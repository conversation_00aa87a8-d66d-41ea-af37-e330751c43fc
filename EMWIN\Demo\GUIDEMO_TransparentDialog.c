/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : GUIDEMO_TransparentDialog.c
Purpose     : Demo of a semi transparent dialog
---------------------------END-OF-HEADER------------------------------
*/

/**
  ******************************************************************************
  * @file    GUIDEMO_TransparentDialog.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Demo of a semi transparent dialog
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


#include "GUIDEMO.h"

#if (SHOW_GUIDEMO_TRANSPARENTDIALOG && GUI_WINSUPPORT && GUI_SUPPORT_MEMDEV)

/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define APP_TIMER (WM_USER + 0)
#define APP_INIT  (WM_USER + 1)
#define PERIOD        40
#define DURATION   10000

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/
static GUI_CONST_STORAGE GUI_COLOR ColorsMap_400x320[] = {
     0xEEEEEE,0x99CCFF,0xCCFFCC,0xFFFFFF
    ,0xCCCCCC,0x0000FF,0x888888,0x99CC99
    ,0x000000,0x33FFFF,0x444444,0xDDDDDD
    ,0xBBBBBB,0x777777,0xAAAAAA,0x555555
    ,0x00FFFF,0x666666,0x999999,0x660000
    ,0x669966,0xCCCCFF,0xCC0033,0xFF0066
    ,0xCCFFFF,0x9999FF,0x6666FF,0x99FFFF
};

static GUI_CONST_STORAGE GUI_LOGPALETTE PalMap_400x320 = {
  28,	/* number of entries */
  0, 	/* No transparency */
  &ColorsMap_400x320[0]
};

static GUI_CONST_STORAGE unsigned char acMap_400x320[] = {
  /* RLE: 007 Pixels @ 000,000*/ 7, 0x07,
  /* RLE: 001 Pixels @ 007,000*/ 1, 0x14,
  /* RLE: 010 Pixels @ 008,000*/ 10, 0x07,
  /* RLE: 001 Pixels @ 018,000*/ 1, 0x14,
  /* RLE: 014 Pixels @ 019,000*/ 14, 0x07,
  /* ABS: 008 Pixels @ 033,000*/ 0, 8, 0x14, 0x07, 0x14, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 006 Pixels @ 041,000*/ 6, 0x07,
  /* ABS: 005 Pixels @ 047,000*/ 0, 5, 0x14, 0x07, 0x07, 0x07, 0x02,
  /* RLE: 005 Pixels @ 052,000*/ 5, 0x01,
  /* RLE: 003 Pixels @ 057,000*/ 3, 0x04,
  /* RLE: 008 Pixels @ 060,000*/ 8, 0x03,
  /* ABS: 002 Pixels @ 068,000*/ 0, 2, 0x04, 0x04,
  /* RLE: 090 Pixels @ 070,000*/ 90, 0x00,
  /* ABS: 004 Pixels @ 160,000*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 164,000*/ 39, 0x01,
  /* ABS: 002 Pixels @ 203,000*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 205,000*/ 9, 0x03,
  /* ABS: 002 Pixels @ 214,000*/ 0, 2, 0x04, 0x04,
  /* RLE: 057 Pixels @ 216,000*/ 57, 0x01,
  /* RLE: 001 Pixels @ 273,000*/ 1, 0x04,
  /* RLE: 019 Pixels @ 274,000*/ 19, 0x03,
  /* RLE: 003 Pixels @ 293,000*/ 3, 0x04,
  /* RLE: 058 Pixels @ 296,000*/ 58, 0x01,
  /* ABS: 002 Pixels @ 354,000*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 356,000*/ 8, 0x03,
  /* ABS: 002 Pixels @ 364,000*/ 0, 2, 0x04, 0x04,
  /* RLE: 034 Pixels @ 366,000*/ 34, 0x00,
  /* RLE: 006 Pixels @ 000,001*/ 6, 0x07,
  /* ABS: 003 Pixels @ 006,001*/ 0, 3, 0x14, 0x07, 0x14,
  /* RLE: 005 Pixels @ 009,001*/ 5, 0x07,
  /* RLE: 001 Pixels @ 014,001*/ 1, 0x14,
  /* RLE: 007 Pixels @ 015,001*/ 7, 0x07,
  /* RLE: 003 Pixels @ 022,001*/ 3, 0x14,
  /* RLE: 009 Pixels @ 025,001*/ 9, 0x07,
  /* RLE: 001 Pixels @ 034,001*/ 1, 0x14,
  /* RLE: 004 Pixels @ 035,001*/ 4, 0x07,
  /* ABS: 002 Pixels @ 039,001*/ 0, 2, 0x14, 0x14,
  /* RLE: 007 Pixels @ 041,001*/ 7, 0x07,
  /* ABS: 003 Pixels @ 048,001*/ 0, 3, 0x14, 0x07, 0x02,
  /* RLE: 008 Pixels @ 051,001*/ 8, 0x01,
  /* ABS: 002 Pixels @ 059,001*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 061,001*/ 8, 0x03,
  /* ABS: 002 Pixels @ 069,001*/ 0, 2, 0x04, 0x04,
  /* RLE: 090 Pixels @ 071,001*/ 90, 0x00,
  /* ABS: 005 Pixels @ 161,001*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 166,001*/ 38, 0x01,
  /* ABS: 002 Pixels @ 204,001*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 206,001*/ 9, 0x03,
  /* ABS: 002 Pixels @ 215,001*/ 0, 2, 0x04, 0x04,
  /* RLE: 056 Pixels @ 217,001*/ 56, 0x01,
  /* RLE: 001 Pixels @ 273,001*/ 1, 0x04,
  /* RLE: 015 Pixels @ 274,001*/ 15, 0x03,
  /* RLE: 003 Pixels @ 289,001*/ 3, 0x04,
  /* RLE: 063 Pixels @ 292,001*/ 63, 0x01,
  /* ABS: 002 Pixels @ 355,001*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 357,001*/ 8, 0x03,
  /* ABS: 002 Pixels @ 365,001*/ 0, 2, 0x04, 0x04,
  /* RLE: 033 Pixels @ 367,001*/ 33, 0x00,
  /* RLE: 004 Pixels @ 000,002*/ 4, 0x07,
  /* ABS: 006 Pixels @ 004,002*/ 0, 6, 0x14, 0x14, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 011 Pixels @ 010,002*/ 11, 0x07,
  /* ABS: 005 Pixels @ 021,002*/ 0, 5, 0x14, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 009 Pixels @ 026,002*/ 9, 0x07,
  /* ABS: 005 Pixels @ 035,002*/ 0, 5, 0x14, 0x07, 0x07, 0x07, 0x14,
  /* RLE: 009 Pixels @ 040,002*/ 9, 0x07,
  /* RLE: 011 Pixels @ 049,002*/ 11, 0x01,
  /* ABS: 002 Pixels @ 060,002*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 062,002*/ 8, 0x03,
  /* ABS: 002 Pixels @ 070,002*/ 0, 2, 0x04, 0x04,
  /* RLE: 090 Pixels @ 072,002*/ 90, 0x00,
  /* ABS: 005 Pixels @ 162,002*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 167,002*/ 38, 0x01,
  /* ABS: 002 Pixels @ 205,002*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 207,002*/ 9, 0x03,
  /* ABS: 002 Pixels @ 216,002*/ 0, 2, 0x04, 0x04,
  /* RLE: 055 Pixels @ 218,002*/ 55, 0x01,
  /* RLE: 001 Pixels @ 273,002*/ 1, 0x04,
  /* RLE: 011 Pixels @ 274,002*/ 11, 0x03,
  /* RLE: 003 Pixels @ 285,002*/ 3, 0x04,
  /* RLE: 068 Pixels @ 288,002*/ 68, 0x01,
  /* ABS: 002 Pixels @ 356,002*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 358,002*/ 8, 0x03,
  /* ABS: 002 Pixels @ 366,002*/ 0, 2, 0x04, 0x04,
  /* RLE: 032 Pixels @ 368,002*/ 32, 0x00,
  /* RLE: 003 Pixels @ 000,003*/ 3, 0x07,
  /* RLE: 001 Pixels @ 003,003*/ 1, 0x14,
  /* RLE: 005 Pixels @ 004,003*/ 5, 0x07,
  /* ABS: 002 Pixels @ 009,003*/ 0, 2, 0x14, 0x14,
  /* RLE: 010 Pixels @ 011,003*/ 10, 0x07,
  /* ABS: 006 Pixels @ 021,003*/ 0, 6, 0x14, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 009 Pixels @ 027,003*/ 9, 0x07,
  /* ABS: 004 Pixels @ 036,003*/ 0, 4, 0x14, 0x07, 0x14, 0x14,
  /* RLE: 008 Pixels @ 040,003*/ 8, 0x07,
  /* RLE: 013 Pixels @ 048,003*/ 13, 0x01,
  /* ABS: 002 Pixels @ 061,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 063,003*/ 8, 0x03,
  /* ABS: 002 Pixels @ 071,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 090 Pixels @ 073,003*/ 90, 0x00,
  /* ABS: 005 Pixels @ 163,003*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 168,003*/ 38, 0x01,
  /* ABS: 002 Pixels @ 206,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 208,003*/ 9, 0x03,
  /* ABS: 002 Pixels @ 217,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 054 Pixels @ 219,003*/ 54, 0x01,
  /* ABS: 002 Pixels @ 273,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 275,003*/ 6, 0x03,
  /* RLE: 003 Pixels @ 281,003*/ 3, 0x04,
  /* RLE: 073 Pixels @ 284,003*/ 73, 0x01,
  /* ABS: 002 Pixels @ 357,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 359,003*/ 8, 0x03,
  /* ABS: 002 Pixels @ 367,003*/ 0, 2, 0x04, 0x04,
  /* RLE: 031 Pixels @ 369,003*/ 31, 0x00,
  /* RLE: 004 Pixels @ 000,004*/ 4, 0x07,
  /* RLE: 001 Pixels @ 004,004*/ 1, 0x14,
  /* RLE: 004 Pixels @ 005,004*/ 4, 0x07,
  /* ABS: 002 Pixels @ 009,004*/ 0, 2, 0x14, 0x14,
  /* RLE: 009 Pixels @ 011,004*/ 9, 0x07,
  /* RLE: 001 Pixels @ 020,004*/ 1, 0x14,
  /* RLE: 004 Pixels @ 021,004*/ 4, 0x07,
  /* ABS: 002 Pixels @ 025,004*/ 0, 2, 0x14, 0x14,
  /* RLE: 011 Pixels @ 027,004*/ 11, 0x07,
  /* RLE: 001 Pixels @ 038,004*/ 1, 0x14,
  /* RLE: 008 Pixels @ 039,004*/ 8, 0x07,
  /* RLE: 015 Pixels @ 047,004*/ 15, 0x01,
  /* ABS: 002 Pixels @ 062,004*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 064,004*/ 8, 0x03,
  /* ABS: 002 Pixels @ 072,004*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 074,004*/ 91, 0x00,
  /* ABS: 004 Pixels @ 165,004*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 169,004*/ 38, 0x01,
  /* ABS: 002 Pixels @ 207,004*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 209,004*/ 9, 0x03,
  /* ABS: 002 Pixels @ 218,004*/ 0, 2, 0x04, 0x04,
  /* RLE: 054 Pixels @ 220,004*/ 54, 0x01,
  /* RLE: 006 Pixels @ 274,004*/ 6, 0x04,
  /* RLE: 079 Pixels @ 280,004*/ 79, 0x01,
  /* RLE: 001 Pixels @ 359,004*/ 1, 0x04,
  /* RLE: 008 Pixels @ 360,004*/ 8, 0x03,
  /* ABS: 002 Pixels @ 368,004*/ 0, 2, 0x04, 0x04,
  /* RLE: 030 Pixels @ 370,004*/ 30, 0x00,
  /* RLE: 003 Pixels @ 000,005*/ 3, 0x07,
  /* RLE: 001 Pixels @ 003,005*/ 1, 0x14,
  /* RLE: 006 Pixels @ 004,005*/ 6, 0x07,
  /* ABS: 002 Pixels @ 010,005*/ 0, 2, 0x14, 0x14,
  /* RLE: 009 Pixels @ 012,005*/ 9, 0x07,
  /* ABS: 005 Pixels @ 021,005*/ 0, 5, 0x14, 0x07, 0x07, 0x07, 0x14,
  /* RLE: 005 Pixels @ 026,005*/ 5, 0x07,
  /* RLE: 001 Pixels @ 031,005*/ 1, 0x14,
  /* RLE: 006 Pixels @ 032,005*/ 6, 0x07,
  /* RLE: 001 Pixels @ 038,005*/ 1, 0x14,
  /* RLE: 007 Pixels @ 039,005*/ 7, 0x07,
  /* RLE: 017 Pixels @ 046,005*/ 17, 0x01,
  /* ABS: 002 Pixels @ 063,005*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 065,005*/ 8, 0x03,
  /* ABS: 002 Pixels @ 073,005*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 075,005*/ 91, 0x00,
  /* ABS: 004 Pixels @ 166,005*/ 0, 4, 0x0D, 0x06, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 170,005*/ 38, 0x01,
  /* ABS: 002 Pixels @ 208,005*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 210,005*/ 9, 0x03,
  /* ABS: 002 Pixels @ 219,005*/ 0, 2, 0x04, 0x04,
  /* RLE: 138 Pixels @ 221,005*/ 138, 0x01,
  /* ABS: 002 Pixels @ 359,005*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 361,005*/ 8, 0x03,
  /* ABS: 002 Pixels @ 369,005*/ 0, 2, 0x04, 0x04,
  /* RLE: 029 Pixels @ 371,005*/ 29, 0x00,
  /* RLE: 003 Pixels @ 000,006*/ 3, 0x07,
  /* RLE: 001 Pixels @ 003,006*/ 1, 0x14,
  /* RLE: 005 Pixels @ 004,006*/ 5, 0x07,
  /* ABS: 002 Pixels @ 009,006*/ 0, 2, 0x14, 0x14,
  /* RLE: 011 Pixels @ 011,006*/ 11, 0x07,
  /* ABS: 004 Pixels @ 022,006*/ 0, 4, 0x14, 0x07, 0x14, 0x14,
  /* RLE: 012 Pixels @ 026,006*/ 12, 0x07,
  /* RLE: 001 Pixels @ 038,006*/ 1, 0x14,
  /* RLE: 006 Pixels @ 039,006*/ 6, 0x07,
  /* RLE: 019 Pixels @ 045,006*/ 19, 0x01,
  /* ABS: 002 Pixels @ 064,006*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 066,006*/ 8, 0x03,
  /* ABS: 002 Pixels @ 074,006*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 076,006*/ 91, 0x00,
  /* ABS: 004 Pixels @ 167,006*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 171,006*/ 38, 0x01,
  /* ABS: 002 Pixels @ 209,006*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 211,006*/ 9, 0x03,
  /* ABS: 002 Pixels @ 220,006*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 222,006*/ 137, 0x01,
  /* RLE: 003 Pixels @ 359,006*/ 3, 0x04,
  /* RLE: 008 Pixels @ 362,006*/ 8, 0x03,
  /* ABS: 002 Pixels @ 370,006*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 372,006*/ 28, 0x00,
  /* RLE: 004 Pixels @ 000,007*/ 4, 0x07,
  /* ABS: 006 Pixels @ 004,007*/ 0, 6, 0x14, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 008 Pixels @ 010,007*/ 8, 0x07,
  /* RLE: 001 Pixels @ 018,007*/ 1, 0x14,
  /* RLE: 005 Pixels @ 019,007*/ 5, 0x07,
  /* RLE: 001 Pixels @ 024,007*/ 1, 0x14,
  /* RLE: 009 Pixels @ 025,007*/ 9, 0x07,
  /* RLE: 001 Pixels @ 034,007*/ 1, 0x14,
  /* RLE: 009 Pixels @ 035,007*/ 9, 0x07,
  /* RLE: 021 Pixels @ 044,007*/ 21, 0x01,
  /* ABS: 002 Pixels @ 065,007*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 067,007*/ 8, 0x03,
  /* ABS: 002 Pixels @ 075,007*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 077,007*/ 91, 0x00,
  /* ABS: 004 Pixels @ 168,007*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 039 Pixels @ 172,007*/ 39, 0x01,
  /* ABS: 002 Pixels @ 211,007*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 213,007*/ 9, 0x03,
  /* ABS: 002 Pixels @ 222,007*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 224,007*/ 137, 0x01,
  /* ABS: 002 Pixels @ 361,007*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 363,007*/ 8, 0x03,
  /* ABS: 002 Pixels @ 371,007*/ 0, 2, 0x04, 0x04,
  /* RLE: 027 Pixels @ 373,007*/ 27, 0x00,
  /* RLE: 005 Pixels @ 000,008*/ 5, 0x07,
  /* ABS: 004 Pixels @ 005,008*/ 0, 4, 0x14, 0x07, 0x07, 0x14,
  /* RLE: 015 Pixels @ 009,008*/ 15, 0x07,
  /* RLE: 001 Pixels @ 024,008*/ 1, 0x14,
  /* RLE: 017 Pixels @ 025,008*/ 17, 0x07,
  /* RLE: 001 Pixels @ 042,008*/ 1, 0x14,
  /* RLE: 023 Pixels @ 043,008*/ 23, 0x01,
  /* ABS: 002 Pixels @ 066,008*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 068,008*/ 8, 0x03,
  /* ABS: 002 Pixels @ 076,008*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 078,008*/ 91, 0x00,
  /* ABS: 005 Pixels @ 169,008*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 174,008*/ 38, 0x01,
  /* ABS: 002 Pixels @ 212,008*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 214,008*/ 9, 0x03,
  /* ABS: 002 Pixels @ 223,008*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 225,008*/ 137, 0x01,
  /* ABS: 002 Pixels @ 362,008*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 364,008*/ 8, 0x03,
  /* ABS: 002 Pixels @ 372,008*/ 0, 2, 0x04, 0x04,
  /* RLE: 026 Pixels @ 374,008*/ 26, 0x00,
  /* RLE: 008 Pixels @ 000,009*/ 8, 0x07,
  /* RLE: 001 Pixels @ 008,009*/ 1, 0x14,
  /* RLE: 015 Pixels @ 009,009*/ 15, 0x07,
  /* RLE: 001 Pixels @ 024,009*/ 1, 0x14,
  /* RLE: 017 Pixels @ 025,009*/ 17, 0x07,
  /* RLE: 025 Pixels @ 042,009*/ 25, 0x01,
  /* ABS: 002 Pixels @ 067,009*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 069,009*/ 8, 0x03,
  /* ABS: 002 Pixels @ 077,009*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 079,009*/ 91, 0x00,
  /* RLE: 001 Pixels @ 170,009*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 171,009*/ 4, 0x06,
  /* RLE: 038 Pixels @ 175,009*/ 38, 0x01,
  /* ABS: 002 Pixels @ 213,009*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 215,009*/ 9, 0x03,
  /* ABS: 002 Pixels @ 224,009*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 226,009*/ 137, 0x01,
  /* ABS: 002 Pixels @ 363,009*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 365,009*/ 8, 0x03,
  /* ABS: 002 Pixels @ 373,009*/ 0, 2, 0x04, 0x04,
  /* RLE: 025 Pixels @ 375,009*/ 25, 0x00,
  /* RLE: 008 Pixels @ 000,010*/ 8, 0x07,
  /* ABS: 002 Pixels @ 008,010*/ 0, 2, 0x14, 0x14,
  /* RLE: 030 Pixels @ 010,010*/ 30, 0x07,
  /* RLE: 028 Pixels @ 040,010*/ 28, 0x01,
  /* ABS: 002 Pixels @ 068,010*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 070,010*/ 8, 0x03,
  /* ABS: 002 Pixels @ 078,010*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 080,010*/ 91, 0x00,
  /* ABS: 005 Pixels @ 171,010*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 176,010*/ 38, 0x01,
  /* ABS: 002 Pixels @ 214,010*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 216,010*/ 9, 0x03,
  /* ABS: 002 Pixels @ 225,010*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 227,010*/ 137, 0x01,
  /* ABS: 002 Pixels @ 364,010*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 366,010*/ 8, 0x03,
  /* ABS: 002 Pixels @ 374,010*/ 0, 2, 0x04, 0x04,
  /* RLE: 024 Pixels @ 376,010*/ 24, 0x00,
  /* RLE: 013 Pixels @ 000,011*/ 13, 0x07,
  /* RLE: 001 Pixels @ 013,011*/ 1, 0x14,
  /* RLE: 025 Pixels @ 014,011*/ 25, 0x07,
  /* RLE: 030 Pixels @ 039,011*/ 30, 0x01,
  /* ABS: 002 Pixels @ 069,011*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 071,011*/ 8, 0x03,
  /* ABS: 002 Pixels @ 079,011*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 081,011*/ 92, 0x00,
  /* ABS: 004 Pixels @ 173,011*/ 0, 4, 0x0D, 0x06, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 177,011*/ 38, 0x01,
  /* ABS: 002 Pixels @ 215,011*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 217,011*/ 9, 0x03,
  /* ABS: 002 Pixels @ 226,011*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 228,011*/ 137, 0x01,
  /* ABS: 002 Pixels @ 365,011*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 367,011*/ 8, 0x03,
  /* ABS: 002 Pixels @ 375,011*/ 0, 2, 0x04, 0x04,
  /* RLE: 023 Pixels @ 377,011*/ 23, 0x00,
  /* RLE: 011 Pixels @ 000,012*/ 11, 0x07,
  /* RLE: 001 Pixels @ 011,012*/ 1, 0x14,
  /* RLE: 026 Pixels @ 012,012*/ 26, 0x07,
  /* RLE: 032 Pixels @ 038,012*/ 32, 0x01,
  /* ABS: 002 Pixels @ 070,012*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 072,012*/ 8, 0x03,
  /* ABS: 002 Pixels @ 080,012*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 082,012*/ 92, 0x00,
  /* ABS: 004 Pixels @ 174,012*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 038 Pixels @ 178,012*/ 38, 0x01,
  /* ABS: 002 Pixels @ 216,012*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 218,012*/ 9, 0x03,
  /* ABS: 002 Pixels @ 227,012*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 229,012*/ 137, 0x01,
  /* ABS: 002 Pixels @ 366,012*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 368,012*/ 8, 0x03,
  /* ABS: 002 Pixels @ 376,012*/ 0, 2, 0x04, 0x04,
  /* RLE: 022 Pixels @ 378,012*/ 22, 0x00,
  /* RLE: 006 Pixels @ 000,013*/ 6, 0x07,
  /* RLE: 001 Pixels @ 006,013*/ 1, 0x14,
  /* RLE: 005 Pixels @ 007,013*/ 5, 0x07,
  /* RLE: 001 Pixels @ 012,013*/ 1, 0x14,
  /* RLE: 007 Pixels @ 013,013*/ 7, 0x07,
  /* RLE: 003 Pixels @ 020,013*/ 3, 0x14,
  /* RLE: 014 Pixels @ 023,013*/ 14, 0x07,
  /* RLE: 034 Pixels @ 037,013*/ 34, 0x01,
  /* ABS: 002 Pixels @ 071,013*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 073,013*/ 8, 0x03,
  /* ABS: 002 Pixels @ 081,013*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 083,013*/ 91, 0x00,
  /* ABS: 004 Pixels @ 174,013*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 178,013*/ 39, 0x01,
  /* RLE: 003 Pixels @ 217,013*/ 3, 0x04,
  /* RLE: 008 Pixels @ 220,013*/ 8, 0x03,
  /* RLE: 003 Pixels @ 228,013*/ 3, 0x04,
  /* RLE: 136 Pixels @ 231,013*/ 136, 0x01,
  /* ABS: 002 Pixels @ 367,013*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 369,013*/ 8, 0x03,
  /* ABS: 002 Pixels @ 377,013*/ 0, 2, 0x04, 0x04,
  /* RLE: 021 Pixels @ 379,013*/ 21, 0x00,
  /* RLE: 019 Pixels @ 000,014*/ 19, 0x07,
  /* ABS: 005 Pixels @ 019,014*/ 0, 5, 0x14, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 011 Pixels @ 024,014*/ 11, 0x07,
  /* ABS: 002 Pixels @ 035,014*/ 0, 2, 0x08, 0x08,
  /* RLE: 036 Pixels @ 037,014*/ 36, 0x01,
  /* RLE: 001 Pixels @ 073,014*/ 1, 0x04,
  /* RLE: 008 Pixels @ 074,014*/ 8, 0x03,
  /* ABS: 002 Pixels @ 082,014*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 084,014*/ 91, 0x00,
  /* RLE: 001 Pixels @ 175,014*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 176,014*/ 4, 0x06,
  /* RLE: 039 Pixels @ 180,014*/ 39, 0x01,
  /* ABS: 002 Pixels @ 219,014*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 221,014*/ 9, 0x03,
  /* ABS: 002 Pixels @ 230,014*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 232,014*/ 136, 0x01,
  /* ABS: 002 Pixels @ 368,014*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 370,014*/ 8, 0x03,
  /* ABS: 002 Pixels @ 378,014*/ 0, 2, 0x04, 0x04,
  /* RLE: 020 Pixels @ 380,014*/ 20, 0x00,
  /* RLE: 019 Pixels @ 000,015*/ 19, 0x07,
  /* ABS: 006 Pixels @ 019,015*/ 0, 6, 0x14, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 006 Pixels @ 025,015*/ 6, 0x07,
  /* ABS: 005 Pixels @ 031,015*/ 0, 5, 0x14, 0x07, 0x07, 0x08, 0x02,
  /* RLE: 037 Pixels @ 036,015*/ 37, 0x01,
  /* ABS: 002 Pixels @ 073,015*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 075,015*/ 8, 0x03,
  /* ABS: 002 Pixels @ 083,015*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 085,015*/ 91, 0x00,
  /* ABS: 005 Pixels @ 176,015*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 039 Pixels @ 181,015*/ 39, 0x01,
  /* ABS: 002 Pixels @ 220,015*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 222,015*/ 9, 0x03,
  /* ABS: 002 Pixels @ 231,015*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 233,015*/ 136, 0x01,
  /* ABS: 002 Pixels @ 369,015*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 371,015*/ 8, 0x03,
  /* ABS: 002 Pixels @ 379,015*/ 0, 2, 0x04, 0x04,
  /* RLE: 019 Pixels @ 381,015*/ 19, 0x00,
  /* RLE: 001 Pixels @ 000,016*/ 1, 0x14,
  /* RLE: 017 Pixels @ 001,016*/ 17, 0x07,
  /* RLE: 001 Pixels @ 018,016*/ 1, 0x14,
  /* RLE: 004 Pixels @ 019,016*/ 4, 0x07,
  /* ABS: 002 Pixels @ 023,016*/ 0, 2, 0x14, 0x14,
  /* RLE: 007 Pixels @ 025,016*/ 7, 0x07,
  /* ABS: 003 Pixels @ 032,016*/ 0, 3, 0x14, 0x07, 0x02,
  /* RLE: 038 Pixels @ 035,016*/ 38, 0x01,
  /* RLE: 003 Pixels @ 073,016*/ 3, 0x04,
  /* RLE: 008 Pixels @ 076,016*/ 8, 0x03,
  /* ABS: 002 Pixels @ 084,016*/ 0, 2, 0x04, 0x04,
  /* RLE: 091 Pixels @ 086,016*/ 91, 0x00,
  /* ABS: 005 Pixels @ 177,016*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x0D,
  /* RLE: 039 Pixels @ 182,016*/ 39, 0x01,
  /* ABS: 002 Pixels @ 221,016*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 223,016*/ 9, 0x03,
  /* ABS: 002 Pixels @ 232,016*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 234,016*/ 136, 0x01,
  /* ABS: 002 Pixels @ 370,016*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 372,016*/ 8, 0x03,
  /* ABS: 002 Pixels @ 380,016*/ 0, 2, 0x04, 0x04,
  /* RLE: 018 Pixels @ 382,016*/ 18, 0x00,
  /* RLE: 007 Pixels @ 000,017*/ 7, 0x07,
  /* RLE: 001 Pixels @ 007,017*/ 1, 0x14,
  /* RLE: 011 Pixels @ 008,017*/ 11, 0x07,
  /* ABS: 005 Pixels @ 019,017*/ 0, 5, 0x14, 0x07, 0x07, 0x07, 0x14,
  /* RLE: 009 Pixels @ 024,017*/ 9, 0x07,
  /* RLE: 042 Pixels @ 033,017*/ 42, 0x01,
  /* ABS: 002 Pixels @ 075,017*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 077,017*/ 8, 0x03,
  /* ABS: 002 Pixels @ 085,017*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 087,017*/ 92, 0x00,
  /* ABS: 004 Pixels @ 179,017*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 039 Pixels @ 183,017*/ 39, 0x01,
  /* ABS: 002 Pixels @ 222,017*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 224,017*/ 9, 0x03,
  /* ABS: 002 Pixels @ 233,017*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 235,017*/ 136, 0x01,
  /* ABS: 002 Pixels @ 371,017*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 373,017*/ 8, 0x03,
  /* ABS: 002 Pixels @ 381,017*/ 0, 2, 0x04, 0x04,
  /* RLE: 017 Pixels @ 383,017*/ 17, 0x00,
  /* RLE: 006 Pixels @ 000,018*/ 6, 0x07,
  /* ABS: 003 Pixels @ 006,018*/ 0, 3, 0x14, 0x07, 0x14,
  /* RLE: 005 Pixels @ 009,018*/ 5, 0x07,
  /* RLE: 001 Pixels @ 014,018*/ 1, 0x14,
  /* RLE: 005 Pixels @ 015,018*/ 5, 0x07,
  /* ABS: 004 Pixels @ 020,018*/ 0, 4, 0x14, 0x07, 0x14, 0x14,
  /* RLE: 008 Pixels @ 024,018*/ 8, 0x07,
  /* RLE: 044 Pixels @ 032,018*/ 44, 0x01,
  /* ABS: 002 Pixels @ 076,018*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 078,018*/ 8, 0x03,
  /* ABS: 002 Pixels @ 086,018*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 088,018*/ 92, 0x00,
  /* ABS: 004 Pixels @ 180,018*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 184,018*/ 39, 0x01,
  /* ABS: 002 Pixels @ 223,018*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 225,018*/ 9, 0x03,
  /* ABS: 002 Pixels @ 234,018*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 236,018*/ 136, 0x01,
  /* ABS: 002 Pixels @ 372,018*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 374,018*/ 8, 0x03,
  /* ABS: 002 Pixels @ 382,018*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 384,018*/ 16, 0x00,
  /* RLE: 004 Pixels @ 000,019*/ 4, 0x07,
  /* ABS: 006 Pixels @ 004,019*/ 0, 6, 0x14, 0x14, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 012 Pixels @ 010,019*/ 12, 0x07,
  /* RLE: 001 Pixels @ 022,019*/ 1, 0x14,
  /* RLE: 008 Pixels @ 023,019*/ 8, 0x07,
  /* RLE: 046 Pixels @ 031,019*/ 46, 0x01,
  /* ABS: 002 Pixels @ 077,019*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 079,019*/ 8, 0x03,
  /* ABS: 002 Pixels @ 087,019*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 089,019*/ 92, 0x00,
  /* ABS: 004 Pixels @ 181,019*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 040 Pixels @ 185,019*/ 40, 0x01,
  /* ABS: 002 Pixels @ 225,019*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 227,019*/ 8, 0x03,
  /* ABS: 002 Pixels @ 235,019*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 237,019*/ 136, 0x01,
  /* ABS: 002 Pixels @ 373,019*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 375,019*/ 8, 0x03,
  /* ABS: 002 Pixels @ 383,019*/ 0, 2, 0x04, 0x04,
  /* RLE: 015 Pixels @ 385,019*/ 15, 0x00,
  /* RLE: 003 Pixels @ 000,020*/ 3, 0x07,
  /* RLE: 001 Pixels @ 003,020*/ 1, 0x14,
  /* RLE: 005 Pixels @ 004,020*/ 5, 0x07,
  /* ABS: 002 Pixels @ 009,020*/ 0, 2, 0x14, 0x14,
  /* RLE: 011 Pixels @ 011,020*/ 11, 0x07,
  /* RLE: 001 Pixels @ 022,020*/ 1, 0x14,
  /* RLE: 007 Pixels @ 023,020*/ 7, 0x07,
  /* RLE: 048 Pixels @ 030,020*/ 48, 0x01,
  /* ABS: 002 Pixels @ 078,020*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 080,020*/ 8, 0x03,
  /* ABS: 002 Pixels @ 088,020*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 090,020*/ 92, 0x00,
  /* ABS: 005 Pixels @ 182,020*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 039 Pixels @ 187,020*/ 39, 0x01,
  /* ABS: 002 Pixels @ 226,020*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 228,020*/ 9, 0x03,
  /* ABS: 002 Pixels @ 237,020*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 239,020*/ 135, 0x01,
  /* ABS: 002 Pixels @ 374,020*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 376,020*/ 9, 0x03,
  /* ABS: 002 Pixels @ 385,020*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 387,020*/ 13, 0x00,
  /* RLE: 004 Pixels @ 000,021*/ 4, 0x07,
  /* RLE: 001 Pixels @ 004,021*/ 1, 0x14,
  /* RLE: 004 Pixels @ 005,021*/ 4, 0x07,
  /* ABS: 002 Pixels @ 009,021*/ 0, 2, 0x14, 0x14,
  /* RLE: 011 Pixels @ 011,021*/ 11, 0x07,
  /* RLE: 001 Pixels @ 022,021*/ 1, 0x14,
  /* RLE: 006 Pixels @ 023,021*/ 6, 0x07,
  /* RLE: 050 Pixels @ 029,021*/ 50, 0x01,
  /* ABS: 002 Pixels @ 079,021*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 081,021*/ 8, 0x03,
  /* ABS: 002 Pixels @ 089,021*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 091,021*/ 92, 0x00,
  /* ABS: 005 Pixels @ 183,021*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 039 Pixels @ 188,021*/ 39, 0x01,
  /* ABS: 002 Pixels @ 227,021*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 229,021*/ 9, 0x03,
  /* ABS: 002 Pixels @ 238,021*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 240,021*/ 135, 0x01,
  /* ABS: 002 Pixels @ 375,021*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 377,021*/ 9, 0x03,
  /* ABS: 002 Pixels @ 386,021*/ 0, 2, 0x04, 0x04,
  /* RLE: 012 Pixels @ 388,021*/ 12, 0x00,
  /* RLE: 003 Pixels @ 000,022*/ 3, 0x07,
  /* RLE: 001 Pixels @ 003,022*/ 1, 0x14,
  /* RLE: 006 Pixels @ 004,022*/ 6, 0x07,
  /* ABS: 002 Pixels @ 010,022*/ 0, 2, 0x14, 0x14,
  /* RLE: 016 Pixels @ 012,022*/ 16, 0x07,
  /* RLE: 052 Pixels @ 028,022*/ 52, 0x01,
  /* ABS: 002 Pixels @ 080,022*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 082,022*/ 8, 0x03,
  /* ABS: 002 Pixels @ 090,022*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 092,022*/ 92, 0x00,
  /* ABS: 005 Pixels @ 184,022*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 039 Pixels @ 189,022*/ 39, 0x01,
  /* ABS: 002 Pixels @ 228,022*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 230,022*/ 9, 0x03,
  /* ABS: 002 Pixels @ 239,022*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 241,022*/ 135, 0x01,
  /* ABS: 002 Pixels @ 376,022*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 378,022*/ 9, 0x03,
  /* ABS: 002 Pixels @ 387,022*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 389,022*/ 11, 0x00,
  /* RLE: 003 Pixels @ 000,023*/ 3, 0x07,
  /* RLE: 001 Pixels @ 003,023*/ 1, 0x14,
  /* RLE: 005 Pixels @ 004,023*/ 5, 0x07,
  /* ABS: 002 Pixels @ 009,023*/ 0, 2, 0x14, 0x14,
  /* RLE: 015 Pixels @ 011,023*/ 15, 0x07,
  /* RLE: 001 Pixels @ 026,023*/ 1, 0x14,
  /* RLE: 054 Pixels @ 027,023*/ 54, 0x01,
  /* ABS: 002 Pixels @ 081,023*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 083,023*/ 8, 0x03,
  /* ABS: 002 Pixels @ 091,023*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 093,023*/ 93, 0x00,
  /* ABS: 004 Pixels @ 186,023*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 190,023*/ 39, 0x01,
  /* ABS: 002 Pixels @ 229,023*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 231,023*/ 9, 0x03,
  /* RLE: 001 Pixels @ 240,023*/ 1, 0x04,
  /* RLE: 136 Pixels @ 241,023*/ 136, 0x01,
  /* ABS: 002 Pixels @ 377,023*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 379,023*/ 9, 0x03,
  /* ABS: 002 Pixels @ 388,023*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 390,023*/ 10, 0x00,
  /* RLE: 004 Pixels @ 000,024*/ 4, 0x07,
  /* ABS: 006 Pixels @ 004,024*/ 0, 6, 0x14, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 016 Pixels @ 010,024*/ 16, 0x07,
  /* RLE: 056 Pixels @ 026,024*/ 56, 0x01,
  /* ABS: 002 Pixels @ 082,024*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 084,024*/ 8, 0x03,
  /* ABS: 002 Pixels @ 092,024*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 094,024*/ 93, 0x00,
  /* ABS: 004 Pixels @ 187,024*/ 0, 4, 0x0D, 0x06, 0x06, 0x0D,
  /* RLE: 039 Pixels @ 191,024*/ 39, 0x01,
  /* ABS: 002 Pixels @ 230,024*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 232,024*/ 9, 0x03,
  /* RLE: 137 Pixels @ 241,024*/ 137, 0x01,
  /* ABS: 002 Pixels @ 378,024*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 380,024*/ 9, 0x03,
  /* ABS: 002 Pixels @ 389,024*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 391,024*/ 9, 0x00,
  /* RLE: 005 Pixels @ 000,025*/ 5, 0x07,
  /* ABS: 004 Pixels @ 005,025*/ 0, 4, 0x14, 0x07, 0x07, 0x14,
  /* RLE: 008 Pixels @ 009,025*/ 8, 0x07,
  /* RLE: 001 Pixels @ 017,025*/ 1, 0x14,
  /* RLE: 006 Pixels @ 018,025*/ 6, 0x07,
  /* RLE: 059 Pixels @ 024,025*/ 59, 0x01,
  /* ABS: 002 Pixels @ 083,025*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 085,025*/ 8, 0x03,
  /* ABS: 002 Pixels @ 093,025*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 095,025*/ 93, 0x00,
  /* ABS: 004 Pixels @ 188,025*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 039 Pixels @ 192,025*/ 39, 0x01,
  /* ABS: 002 Pixels @ 231,025*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 233,025*/ 9, 0x03,
  /* ABS: 002 Pixels @ 242,025*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 244,025*/ 135, 0x01,
  /* RLE: 003 Pixels @ 379,025*/ 3, 0x04,
  /* RLE: 008 Pixels @ 382,025*/ 8, 0x03,
  /* ABS: 002 Pixels @ 390,025*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 392,025*/ 8, 0x00,
  /* RLE: 008 Pixels @ 000,026*/ 8, 0x07,
  /* RLE: 001 Pixels @ 008,026*/ 1, 0x14,
  /* RLE: 014 Pixels @ 009,026*/ 14, 0x07,
  /* RLE: 061 Pixels @ 023,026*/ 61, 0x01,
  /* ABS: 002 Pixels @ 084,026*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 086,026*/ 8, 0x03,
  /* ABS: 002 Pixels @ 094,026*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 096,026*/ 93, 0x00,
  /* ABS: 004 Pixels @ 189,026*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 039 Pixels @ 193,026*/ 39, 0x01,
  /* ABS: 002 Pixels @ 232,026*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 234,026*/ 9, 0x03,
  /* ABS: 002 Pixels @ 243,026*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 245,026*/ 136, 0x01,
  /* ABS: 002 Pixels @ 381,026*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 383,026*/ 8, 0x03,
  /* ABS: 002 Pixels @ 391,026*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 393,026*/ 7, 0x00,
  /* RLE: 008 Pixels @ 000,027*/ 8, 0x07,
  /* ABS: 002 Pixels @ 008,027*/ 0, 2, 0x14, 0x14,
  /* RLE: 012 Pixels @ 010,027*/ 12, 0x07,
  /* RLE: 063 Pixels @ 022,027*/ 63, 0x01,
  /* ABS: 002 Pixels @ 085,027*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 087,027*/ 8, 0x03,
  /* ABS: 002 Pixels @ 095,027*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 097,027*/ 93, 0x00,
  /* ABS: 005 Pixels @ 190,027*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 195,027*/ 38, 0x01,
  /* ABS: 002 Pixels @ 233,027*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 235,027*/ 9, 0x03,
  /* ABS: 002 Pixels @ 244,027*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 246,027*/ 136, 0x01,
  /* ABS: 002 Pixels @ 382,027*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 384,027*/ 8, 0x03,
  /* ABS: 002 Pixels @ 392,027*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 394,027*/ 6, 0x00,
  /* RLE: 013 Pixels @ 000,028*/ 13, 0x07,
  /* RLE: 001 Pixels @ 013,028*/ 1, 0x14,
  /* RLE: 007 Pixels @ 014,028*/ 7, 0x07,
  /* RLE: 065 Pixels @ 021,028*/ 65, 0x01,
  /* ABS: 002 Pixels @ 086,028*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 088,028*/ 8, 0x03,
  /* ABS: 002 Pixels @ 096,028*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 098,028*/ 93, 0x00,
  /* RLE: 001 Pixels @ 191,028*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 192,028*/ 4, 0x06,
  /* RLE: 038 Pixels @ 196,028*/ 38, 0x01,
  /* ABS: 002 Pixels @ 234,028*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 236,028*/ 9, 0x03,
  /* ABS: 002 Pixels @ 245,028*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 247,028*/ 136, 0x01,
  /* ABS: 002 Pixels @ 383,028*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 385,028*/ 8, 0x03,
  /* ABS: 002 Pixels @ 393,028*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 395,028*/ 5, 0x00,
  /* RLE: 019 Pixels @ 000,029*/ 19, 0x07,
  /* ABS: 002 Pixels @ 019,029*/ 0, 2, 0x08, 0x08,
  /* RLE: 066 Pixels @ 021,029*/ 66, 0x01,
  /* ABS: 002 Pixels @ 087,029*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 089,029*/ 8, 0x03,
  /* ABS: 002 Pixels @ 097,029*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 099,029*/ 93, 0x00,
  /* ABS: 005 Pixels @ 192,029*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 197,029*/ 38, 0x01,
  /* ABS: 002 Pixels @ 235,029*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 237,029*/ 9, 0x03,
  /* ABS: 002 Pixels @ 246,029*/ 0, 2, 0x04, 0x04,
  /* RLE: 136 Pixels @ 248,029*/ 136, 0x01,
  /* ABS: 002 Pixels @ 384,029*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 386,029*/ 8, 0x03,
  /* ABS: 002 Pixels @ 394,029*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 396,029*/ 4, 0x00,
  /* RLE: 007 Pixels @ 000,030*/ 7, 0x07,
  /* RLE: 001 Pixels @ 007,030*/ 1, 0x14,
  /* RLE: 010 Pixels @ 008,030*/ 10, 0x07,
  /* ABS: 002 Pixels @ 018,030*/ 0, 2, 0x08, 0x02,
  /* RLE: 068 Pixels @ 020,030*/ 68, 0x01,
  /* ABS: 002 Pixels @ 088,030*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 090,030*/ 9, 0x03,
  /* ABS: 002 Pixels @ 099,030*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 101,030*/ 93, 0x00,
  /* ABS: 004 Pixels @ 194,030*/ 0, 4, 0x0D, 0x06, 0x06, 0x0D,
  /* RLE: 039 Pixels @ 198,030*/ 39, 0x01,
  /* ABS: 002 Pixels @ 237,030*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 239,030*/ 9, 0x03,
  /* ABS: 002 Pixels @ 248,030*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 250,030*/ 135, 0x01,
  /* ABS: 002 Pixels @ 385,030*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 387,030*/ 8, 0x03,
  /* ABS: 008 Pixels @ 395,030*/ 0, 8, 0x04, 0x04, 0x00, 0x00, 0x00, 0x14, 0x14, 0x14,
  /* RLE: 013 Pixels @ 003,031*/ 13, 0x07,
  /* RLE: 004 Pixels @ 016,031*/ 4, 0x02,
  /* RLE: 069 Pixels @ 020,031*/ 69, 0x01,
  /* ABS: 002 Pixels @ 089,031*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 091,031*/ 9, 0x03,
  /* ABS: 002 Pixels @ 100,031*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 102,031*/ 93, 0x00,
  /* ABS: 004 Pixels @ 195,031*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 039 Pixels @ 199,031*/ 39, 0x01,
  /* ABS: 002 Pixels @ 238,031*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 240,031*/ 9, 0x03,
  /* ABS: 002 Pixels @ 249,031*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 251,031*/ 135, 0x01,
  /* ABS: 002 Pixels @ 386,031*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 388,031*/ 8, 0x03,
  /* ABS: 008 Pixels @ 396,031*/ 0, 8, 0x04, 0x04, 0x00, 0x00, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 008 Pixels @ 004,032*/ 8, 0x07,
  /* ABS: 003 Pixels @ 012,032*/ 0, 3, 0x14, 0x08, 0x08,
  /* RLE: 006 Pixels @ 015,032*/ 6, 0x02,
  /* RLE: 069 Pixels @ 021,032*/ 69, 0x01,
  /* ABS: 002 Pixels @ 090,032*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 092,032*/ 9, 0x03,
  /* ABS: 002 Pixels @ 101,032*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 103,032*/ 93, 0x00,
  /* ABS: 004 Pixels @ 196,032*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 039 Pixels @ 200,032*/ 39, 0x01,
  /* ABS: 002 Pixels @ 239,032*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 241,032*/ 9, 0x03,
  /* ABS: 002 Pixels @ 250,032*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 252,032*/ 135, 0x01,
  /* ABS: 002 Pixels @ 387,032*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 389,032*/ 8, 0x03,
  /* ABS: 008 Pixels @ 397,032*/ 0, 8, 0x04, 0x04, 0x00, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 007 Pixels @ 005,033*/ 7, 0x07,
  /* RLE: 001 Pixels @ 012,033*/ 1, 0x08,
  /* RLE: 008 Pixels @ 013,033*/ 8, 0x02,
  /* RLE: 001 Pixels @ 021,033*/ 1, 0x08,
  /* RLE: 069 Pixels @ 022,033*/ 69, 0x01,
  /* ABS: 002 Pixels @ 091,033*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 093,033*/ 9, 0x03,
  /* ABS: 002 Pixels @ 102,033*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 104,033*/ 93, 0x00,
  /* RLE: 001 Pixels @ 197,033*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 198,033*/ 4, 0x06,
  /* RLE: 038 Pixels @ 202,033*/ 38, 0x01,
  /* ABS: 002 Pixels @ 240,033*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 242,033*/ 9, 0x03,
  /* ABS: 002 Pixels @ 251,033*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 253,033*/ 135, 0x01,
  /* ABS: 002 Pixels @ 388,033*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 390,033*/ 8, 0x03,
  /* ABS: 007 Pixels @ 398,033*/ 0, 7, 0x04, 0x04, 0x07, 0x07, 0x07, 0x14, 0x14,
  /* RLE: 005 Pixels @ 005,034*/ 5, 0x07,
  /* RLE: 011 Pixels @ 010,034*/ 11, 0x02,
  /* RLE: 001 Pixels @ 021,034*/ 1, 0x08,
  /* RLE: 070 Pixels @ 022,034*/ 70, 0x01,
  /* ABS: 002 Pixels @ 092,034*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 094,034*/ 9, 0x03,
  /* ABS: 002 Pixels @ 103,034*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 105,034*/ 93, 0x00,
  /* RLE: 001 Pixels @ 198,034*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 199,034*/ 4, 0x06,
  /* RLE: 038 Pixels @ 203,034*/ 38, 0x01,
  /* ABS: 002 Pixels @ 241,034*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 243,034*/ 9, 0x03,
  /* ABS: 002 Pixels @ 252,034*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 254,034*/ 135, 0x01,
  /* ABS: 002 Pixels @ 389,034*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 391,034*/ 8, 0x03,
  /* ABS: 010 Pixels @ 399,034*/ 0, 10, 0x04, 0x07, 0x07, 0x07, 0x14, 0x14, 0x07, 0x07, 0x08, 0x08,
  /* RLE: 012 Pixels @ 009,035*/ 12, 0x02,
  /* RLE: 001 Pixels @ 021,035*/ 1, 0x08,
  /* RLE: 071 Pixels @ 022,035*/ 71, 0x01,
  /* RLE: 003 Pixels @ 093,035*/ 3, 0x04,
  /* RLE: 008 Pixels @ 096,035*/ 8, 0x03,
  /* ABS: 002 Pixels @ 104,035*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 106,035*/ 93, 0x00,
  /* ABS: 005 Pixels @ 199,035*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 204,035*/ 38, 0x01,
  /* ABS: 002 Pixels @ 242,035*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 244,035*/ 9, 0x03,
  /* ABS: 002 Pixels @ 253,035*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 255,035*/ 135, 0x01,
  /* ABS: 002 Pixels @ 390,035*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 392,035*/ 8, 0x03,
  /* ABS: 007 Pixels @ 000,036*/ 0, 7, 0x07, 0x07, 0x14, 0x14, 0x07, 0x07, 0x08,
  /* RLE: 014 Pixels @ 007,036*/ 14, 0x02,
  /* RLE: 074 Pixels @ 021,036*/ 74, 0x01,
  /* ABS: 002 Pixels @ 095,036*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 097,036*/ 8, 0x03,
  /* ABS: 002 Pixels @ 105,036*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 107,036*/ 94, 0x00,
  /* ABS: 004 Pixels @ 201,036*/ 0, 4, 0x0D, 0x03, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 205,036*/ 38, 0x01,
  /* RLE: 003 Pixels @ 243,036*/ 3, 0x04,
  /* RLE: 008 Pixels @ 246,036*/ 8, 0x03,
  /* RLE: 003 Pixels @ 254,036*/ 3, 0x04,
  /* RLE: 134 Pixels @ 257,036*/ 134, 0x01,
  /* ABS: 002 Pixels @ 391,036*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 393,036*/ 7, 0x03,
  /* ABS: 004 Pixels @ 000,037*/ 0, 4, 0x07, 0x07, 0x14, 0x07,
  /* RLE: 017 Pixels @ 004,037*/ 17, 0x02,
  /* RLE: 075 Pixels @ 021,037*/ 75, 0x01,
  /* ABS: 002 Pixels @ 096,037*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 098,037*/ 8, 0x03,
  /* ABS: 002 Pixels @ 106,037*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 108,037*/ 94, 0x00,
  /* ABS: 004 Pixels @ 202,037*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 039 Pixels @ 206,037*/ 39, 0x01,
  /* ABS: 002 Pixels @ 245,037*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 247,037*/ 9, 0x03,
  /* ABS: 002 Pixels @ 256,037*/ 0, 2, 0x04, 0x04,
  /* RLE: 134 Pixels @ 258,037*/ 134, 0x01,
  /* ABS: 002 Pixels @ 392,037*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 394,037*/ 6, 0x03,
  /* ABS: 003 Pixels @ 000,038*/ 0, 3, 0x07, 0x08, 0x08,
  /* RLE: 019 Pixels @ 003,038*/ 19, 0x02,
  /* RLE: 075 Pixels @ 022,038*/ 75, 0x01,
  /* ABS: 002 Pixels @ 097,038*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 099,038*/ 8, 0x03,
  /* ABS: 002 Pixels @ 107,038*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 109,038*/ 94, 0x00,
  /* ABS: 004 Pixels @ 203,038*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 207,038*/ 39, 0x01,
  /* ABS: 002 Pixels @ 246,038*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 248,038*/ 9, 0x03,
  /* ABS: 002 Pixels @ 257,038*/ 0, 2, 0x04, 0x04,
  /* RLE: 134 Pixels @ 259,038*/ 134, 0x01,
  /* ABS: 002 Pixels @ 393,038*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 395,038*/ 5, 0x03,
  /* RLE: 001 Pixels @ 000,039*/ 1, 0x08,
  /* RLE: 021 Pixels @ 001,039*/ 21, 0x02,
  /* RLE: 001 Pixels @ 022,039*/ 1, 0x08,
  /* RLE: 075 Pixels @ 023,039*/ 75, 0x01,
  /* ABS: 002 Pixels @ 098,039*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 100,039*/ 8, 0x03,
  /* ABS: 002 Pixels @ 108,039*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 110,039*/ 94, 0x00,
  /* ABS: 004 Pixels @ 204,039*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 208,039*/ 39, 0x01,
  /* ABS: 002 Pixels @ 247,039*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 249,039*/ 9, 0x03,
  /* ABS: 002 Pixels @ 258,039*/ 0, 2, 0x04, 0x04,
  /* RLE: 134 Pixels @ 260,039*/ 134, 0x01,
  /* ABS: 002 Pixels @ 394,039*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 396,039*/ 4, 0x03,
  /* RLE: 022 Pixels @ 000,040*/ 22, 0x02,
  /* RLE: 001 Pixels @ 022,040*/ 1, 0x08,
  /* RLE: 076 Pixels @ 023,040*/ 76, 0x01,
  /* ABS: 002 Pixels @ 099,040*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 101,040*/ 8, 0x03,
  /* ABS: 002 Pixels @ 109,040*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 111,040*/ 94, 0x00,
  /* RLE: 001 Pixels @ 205,040*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 206,040*/ 4, 0x06,
  /* RLE: 038 Pixels @ 210,040*/ 38, 0x01,
  /* ABS: 002 Pixels @ 248,040*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 250,040*/ 9, 0x03,
  /* ABS: 002 Pixels @ 259,040*/ 0, 2, 0x04, 0x04,
  /* RLE: 134 Pixels @ 261,040*/ 134, 0x01,
  /* ABS: 005 Pixels @ 395,040*/ 0, 5, 0x04, 0x04, 0x03, 0x03, 0x03,
  /* RLE: 021 Pixels @ 000,041*/ 21, 0x02,
  /* RLE: 005 Pixels @ 021,041*/ 5, 0x04,
  /* RLE: 074 Pixels @ 026,041*/ 74, 0x01,
  /* ABS: 002 Pixels @ 100,041*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 102,041*/ 8, 0x03,
  /* ABS: 002 Pixels @ 110,041*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 112,041*/ 94, 0x00,
  /* ABS: 005 Pixels @ 206,041*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 211,041*/ 38, 0x01,
  /* ABS: 002 Pixels @ 249,041*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 251,041*/ 9, 0x03,
  /* ABS: 002 Pixels @ 260,041*/ 0, 2, 0x04, 0x04,
  /* RLE: 134 Pixels @ 262,041*/ 134, 0x01,
  /* ABS: 004 Pixels @ 396,041*/ 0, 4, 0x04, 0x04, 0x03, 0x03,
  /* RLE: 020 Pixels @ 000,042*/ 20, 0x02,
  /* ABS: 007 Pixels @ 020,042*/ 0, 7, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 074 Pixels @ 027,042*/ 74, 0x01,
  /* ABS: 002 Pixels @ 101,042*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 103,042*/ 8, 0x03,
  /* ABS: 002 Pixels @ 111,042*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 113,042*/ 94, 0x00,
  /* ABS: 005 Pixels @ 207,042*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x0D,
  /* RLE: 039 Pixels @ 212,042*/ 39, 0x01,
  /* ABS: 002 Pixels @ 251,042*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 253,042*/ 8, 0x03,
  /* ABS: 002 Pixels @ 261,042*/ 0, 2, 0x04, 0x04,
  /* RLE: 134 Pixels @ 263,042*/ 134, 0x01,
  /* ABS: 003 Pixels @ 397,042*/ 0, 3, 0x04, 0x04, 0x03,
  /* RLE: 020 Pixels @ 000,043*/ 20, 0x02,
  /* RLE: 001 Pixels @ 020,043*/ 1, 0x04,
  /* RLE: 005 Pixels @ 021,043*/ 5, 0x03,
  /* RLE: 001 Pixels @ 026,043*/ 1, 0x04,
  /* RLE: 075 Pixels @ 027,043*/ 75, 0x01,
  /* ABS: 002 Pixels @ 102,043*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 104,043*/ 8, 0x03,
  /* ABS: 002 Pixels @ 112,043*/ 0, 2, 0x04, 0x04,
  /* RLE: 095 Pixels @ 114,043*/ 95, 0x00,
  /* ABS: 004 Pixels @ 209,043*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 039 Pixels @ 213,043*/ 39, 0x01,
  /* ABS: 002 Pixels @ 252,043*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 254,043*/ 9, 0x03,
  /* ABS: 002 Pixels @ 263,043*/ 0, 2, 0x04, 0x04,
  /* RLE: 133 Pixels @ 265,043*/ 133, 0x01,
  /* ABS: 002 Pixels @ 398,043*/ 0, 2, 0x04, 0x04,
  /* RLE: 019 Pixels @ 000,044*/ 19, 0x02,
  /* ABS: 002 Pixels @ 019,044*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 021,044*/ 6, 0x03,
  /* RLE: 076 Pixels @ 027,044*/ 76, 0x01,
  /* ABS: 002 Pixels @ 103,044*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 105,044*/ 8, 0x03,
  /* ABS: 002 Pixels @ 113,044*/ 0, 2, 0x04, 0x04,
  /* RLE: 095 Pixels @ 115,044*/ 95, 0x00,
  /* ABS: 004 Pixels @ 210,044*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 214,044*/ 39, 0x01,
  /* ABS: 002 Pixels @ 253,044*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 255,044*/ 9, 0x03,
  /* ABS: 002 Pixels @ 264,044*/ 0, 2, 0x04, 0x04,
  /* RLE: 133 Pixels @ 266,044*/ 133, 0x01,
  /* RLE: 001 Pixels @ 399,044*/ 1, 0x04,
  /* RLE: 020 Pixels @ 000,045*/ 20, 0x02,
  /* RLE: 001 Pixels @ 020,045*/ 1, 0x04,
  /* RLE: 006 Pixels @ 021,045*/ 6, 0x03,
  /* RLE: 001 Pixels @ 027,045*/ 1, 0x04,
  /* RLE: 076 Pixels @ 028,045*/ 76, 0x01,
  /* ABS: 002 Pixels @ 104,045*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 106,045*/ 8, 0x03,
  /* ABS: 002 Pixels @ 114,045*/ 0, 2, 0x04, 0x04,
  /* RLE: 095 Pixels @ 116,045*/ 95, 0x00,
  /* ABS: 004 Pixels @ 211,045*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 039 Pixels @ 215,045*/ 39, 0x01,
  /* ABS: 002 Pixels @ 254,045*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 256,045*/ 9, 0x03,
  /* ABS: 002 Pixels @ 265,045*/ 0, 2, 0x04, 0x04,
  /* RLE: 133 Pixels @ 267,045*/ 133, 0x01,
  /* RLE: 020 Pixels @ 000,046*/ 20, 0x02,
  /* RLE: 001 Pixels @ 020,046*/ 1, 0x04,
  /* RLE: 006 Pixels @ 021,046*/ 6, 0x03,
  /* RLE: 001 Pixels @ 027,046*/ 1, 0x04,
  /* RLE: 077 Pixels @ 028,046*/ 77, 0x01,
  /* ABS: 002 Pixels @ 105,046*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 107,046*/ 8, 0x03,
  /* ABS: 002 Pixels @ 115,046*/ 0, 2, 0x04, 0x04,
  /* RLE: 062 Pixels @ 117,046*/ 62, 0x00,
  /* ABS: 002 Pixels @ 179,046*/ 0, 2, 0x03, 0x03,
  /* RLE: 031 Pixels @ 181,046*/ 31, 0x00,
  /* ABS: 005 Pixels @ 212,046*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 217,046*/ 38, 0x01,
  /* ABS: 002 Pixels @ 255,046*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 257,046*/ 9, 0x03,
  /* RLE: 001 Pixels @ 266,046*/ 1, 0x04,
  /* RLE: 133 Pixels @ 267,046*/ 133, 0x01,
  /* RLE: 020 Pixels @ 000,047*/ 20, 0x02,
  /* RLE: 001 Pixels @ 020,047*/ 1, 0x04,
  /* RLE: 006 Pixels @ 021,047*/ 6, 0x03,
  /* RLE: 001 Pixels @ 027,047*/ 1, 0x04,
  /* RLE: 078 Pixels @ 028,047*/ 78, 0x01,
  /* ABS: 002 Pixels @ 106,047*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 108,047*/ 8, 0x03,
  /* ABS: 002 Pixels @ 116,047*/ 0, 2, 0x04, 0x04,
  /* RLE: 060 Pixels @ 118,047*/ 60, 0x00,
  /* ABS: 004 Pixels @ 178,047*/ 0, 4, 0x03, 0x0B, 0x0E, 0x03,
  /* RLE: 031 Pixels @ 182,047*/ 31, 0x00,
  /* ABS: 005 Pixels @ 213,047*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 218,047*/ 38, 0x01,
  /* ABS: 002 Pixels @ 256,047*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 258,047*/ 9, 0x03,
  /* RLE: 133 Pixels @ 267,047*/ 133, 0x01,
  /* RLE: 020 Pixels @ 000,048*/ 20, 0x02,
  /* RLE: 001 Pixels @ 020,048*/ 1, 0x04,
  /* RLE: 006 Pixels @ 021,048*/ 6, 0x03,
  /* RLE: 001 Pixels @ 027,048*/ 1, 0x04,
  /* RLE: 079 Pixels @ 028,048*/ 79, 0x01,
  /* ABS: 002 Pixels @ 107,048*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 109,048*/ 8, 0x03,
  /* ABS: 002 Pixels @ 117,048*/ 0, 2, 0x04, 0x04,
  /* RLE: 054 Pixels @ 119,048*/ 54, 0x00,
  /* ABS: 009 Pixels @ 173,048*/ 0, 9, 0x03, 0x00, 0x03, 0x00, 0x03, 0x0C, 0x0D, 0x0E, 0x03,
  /* RLE: 004 Pixels @ 182,048*/ 4, 0x00,
  /* RLE: 001 Pixels @ 186,048*/ 1, 0x0B,
  /* RLE: 027 Pixels @ 187,048*/ 27, 0x00,
  /* ABS: 005 Pixels @ 214,048*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 219,048*/ 38, 0x01,
  /* ABS: 002 Pixels @ 257,048*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 259,048*/ 9, 0x03,
  /* ABS: 002 Pixels @ 268,048*/ 0, 2, 0x04, 0x04,
  /* RLE: 130 Pixels @ 270,048*/ 130, 0x01,
  /* RLE: 020 Pixels @ 000,049*/ 20, 0x02,
  /* RLE: 001 Pixels @ 020,049*/ 1, 0x04,
  /* RLE: 006 Pixels @ 021,049*/ 6, 0x03,
  /* RLE: 001 Pixels @ 027,049*/ 1, 0x04,
  /* RLE: 080 Pixels @ 028,049*/ 80, 0x01,
  /* ABS: 002 Pixels @ 108,049*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 110,049*/ 8, 0x03,
  /* ABS: 002 Pixels @ 118,049*/ 0, 2, 0x04, 0x04,
  /* RLE: 053 Pixels @ 120,049*/ 53, 0x00,
  /* ABS: 014 Pixels @ 173,049*/ 0, 14, 0x03, 0x0E, 0x0E, 0x00, 0x03, 0x0F, 0x08, 0x03, 0x03, 0x00, 0x03, 0x00, 0x03, 0x03,
  /* RLE: 029 Pixels @ 187,049*/ 29, 0x00,
  /* ABS: 004 Pixels @ 216,049*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 220,049*/ 38, 0x01,
  /* ABS: 002 Pixels @ 258,049*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 260,049*/ 9, 0x03,
  /* ABS: 002 Pixels @ 269,049*/ 0, 2, 0x04, 0x04,
  /* RLE: 129 Pixels @ 271,049*/ 129, 0x01,
  /* RLE: 021 Pixels @ 000,050*/ 21, 0x02,
  /* RLE: 007 Pixels @ 021,050*/ 7, 0x03,
  /* RLE: 001 Pixels @ 028,050*/ 1, 0x04,
  /* RLE: 080 Pixels @ 029,050*/ 80, 0x01,
  /* ABS: 002 Pixels @ 109,050*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 111,050*/ 8, 0x03,
  /* ABS: 002 Pixels @ 119,050*/ 0, 2, 0x04, 0x04,
  /* RLE: 052 Pixels @ 121,050*/ 52, 0x00,
  /* ABS: 014 Pixels @ 173,050*/ 0, 14, 0x03, 0x0C, 0x08, 0x08, 0x00, 0x04, 0x08, 0x04, 0x03, 0x00, 0x03, 0x11, 0x12, 0x03,
  /* RLE: 030 Pixels @ 187,050*/ 30, 0x00,
  /* ABS: 004 Pixels @ 217,050*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 221,050*/ 38, 0x01,
  /* ABS: 002 Pixels @ 259,050*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 261,050*/ 9, 0x03,
  /* ABS: 002 Pixels @ 270,050*/ 0, 2, 0x04, 0x04,
  /* RLE: 128 Pixels @ 272,050*/ 128, 0x01,
  /* RLE: 021 Pixels @ 000,051*/ 21, 0x02,
  /* RLE: 001 Pixels @ 021,051*/ 1, 0x04,
  /* RLE: 006 Pixels @ 022,051*/ 6, 0x03,
  /* RLE: 001 Pixels @ 028,051*/ 1, 0x04,
  /* RLE: 081 Pixels @ 029,051*/ 81, 0x01,
  /* ABS: 002 Pixels @ 110,051*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 112,051*/ 8, 0x03,
  /* ABS: 002 Pixels @ 120,051*/ 0, 2, 0x04, 0x04,
  /* RLE: 050 Pixels @ 122,051*/ 50, 0x00,
  /* ABS: 015 Pixels @ 172,051*/ 0, 15, 0x04, 0x03, 0x0E, 0x08, 0x11, 0x03, 0x03, 0x06, 0x0F, 0x03, 0x03, 0x03, 0x0B, 0x00, 0x03,
  /* RLE: 031 Pixels @ 187,051*/ 31, 0x00,
  /* ABS: 004 Pixels @ 218,051*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 222,051*/ 38, 0x01,
  /* ABS: 002 Pixels @ 260,051*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 262,051*/ 9, 0x03,
  /* ABS: 002 Pixels @ 271,051*/ 0, 2, 0x04, 0x04,
  /* RLE: 127 Pixels @ 273,051*/ 127, 0x01,
  /* RLE: 021 Pixels @ 000,052*/ 21, 0x02,
  /* RLE: 001 Pixels @ 021,052*/ 1, 0x04,
  /* RLE: 006 Pixels @ 022,052*/ 6, 0x03,
  /* RLE: 001 Pixels @ 028,052*/ 1, 0x04,
  /* RLE: 082 Pixels @ 029,052*/ 82, 0x01,
  /* ABS: 002 Pixels @ 111,052*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 113,052*/ 8, 0x03,
  /* ABS: 002 Pixels @ 121,052*/ 0, 2, 0x04, 0x04,
  /* RLE: 048 Pixels @ 123,052*/ 48, 0x00,
  /* RLE: 004 Pixels @ 171,052*/ 4, 0x03,
  /* ABS: 011 Pixels @ 175,052*/ 0, 11, 0x0B, 0x08, 0x0C, 0x03, 0x00, 0x08, 0x0E, 0x03, 0x00, 0x03, 0x03,
  /* RLE: 033 Pixels @ 186,052*/ 33, 0x00,
  /* ABS: 005 Pixels @ 219,052*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 037 Pixels @ 224,052*/ 37, 0x01,
  /* ABS: 002 Pixels @ 261,052*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 263,052*/ 9, 0x03,
  /* ABS: 002 Pixels @ 272,052*/ 0, 2, 0x04, 0x04,
  /* RLE: 126 Pixels @ 274,052*/ 126, 0x01,
  /* RLE: 021 Pixels @ 000,053*/ 21, 0x02,
  /* RLE: 001 Pixels @ 021,053*/ 1, 0x04,
  /* RLE: 006 Pixels @ 022,053*/ 6, 0x03,
  /* RLE: 001 Pixels @ 028,053*/ 1, 0x04,
  /* RLE: 083 Pixels @ 029,053*/ 83, 0x01,
  /* ABS: 002 Pixels @ 112,053*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 114,053*/ 8, 0x03,
  /* ABS: 002 Pixels @ 122,053*/ 0, 2, 0x04, 0x04,
  /* RLE: 046 Pixels @ 124,053*/ 46, 0x00,
  /* ABS: 015 Pixels @ 170,053*/ 0, 15, 0x03, 0x0B, 0x06, 0x12, 0x03, 0x03, 0x0E, 0x08, 0x04, 0x04, 0x04, 0x00, 0x03, 0x04, 0x04,
  /* RLE: 035 Pixels @ 185,053*/ 35, 0x00,
  /* ABS: 005 Pixels @ 220,053*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 225,053*/ 38, 0x01,
  /* ABS: 002 Pixels @ 263,053*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 265,053*/ 9, 0x03,
  /* ABS: 002 Pixels @ 274,053*/ 0, 2, 0x04, 0x04,
  /* RLE: 124 Pixels @ 276,053*/ 124, 0x01,
  /* RLE: 021 Pixels @ 000,054*/ 21, 0x02,
  /* RLE: 001 Pixels @ 021,054*/ 1, 0x04,
  /* RLE: 006 Pixels @ 022,054*/ 6, 0x03,
  /* RLE: 001 Pixels @ 028,054*/ 1, 0x04,
  /* RLE: 084 Pixels @ 029,054*/ 84, 0x01,
  /* ABS: 002 Pixels @ 113,054*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 115,054*/ 8, 0x03,
  /* ABS: 002 Pixels @ 123,054*/ 0, 2, 0x04, 0x04,
  /* RLE: 044 Pixels @ 125,054*/ 44, 0x00,
  /* ABS: 011 Pixels @ 169,054*/ 0, 11, 0x03, 0x0E, 0x11, 0x0C, 0x0B, 0x03, 0x03, 0x03, 0x11, 0x08, 0x04,
  /* RLE: 004 Pixels @ 180,054*/ 4, 0x03,
  /* RLE: 001 Pixels @ 184,054*/ 1, 0x04,
  /* RLE: 036 Pixels @ 185,054*/ 36, 0x00,
  /* ABS: 005 Pixels @ 221,054*/ 0, 5, 0x06, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 226,054*/ 38, 0x01,
  /* ABS: 002 Pixels @ 264,054*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 266,054*/ 9, 0x03,
  /* ABS: 002 Pixels @ 275,054*/ 0, 2, 0x04, 0x04,
  /* RLE: 123 Pixels @ 277,054*/ 123, 0x01,
  /* RLE: 021 Pixels @ 000,055*/ 21, 0x02,
  /* RLE: 001 Pixels @ 021,055*/ 1, 0x04,
  /* RLE: 007 Pixels @ 022,055*/ 7, 0x03,
  /* RLE: 085 Pixels @ 029,055*/ 85, 0x01,
  /* ABS: 002 Pixels @ 114,055*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 116,055*/ 8, 0x03,
  /* ABS: 002 Pixels @ 124,055*/ 0, 2, 0x04, 0x04,
  /* RLE: 039 Pixels @ 126,055*/ 39, 0x00,
  /* ABS: 014 Pixels @ 165,055*/ 0, 14, 0x03, 0x03, 0x00, 0x03, 0x03, 0x08, 0x0C, 0x0E, 0x11, 0x08, 0x0E, 0x03, 0x03, 0x00,
  /* RLE: 006 Pixels @ 179,055*/ 6, 0x03,
  /* RLE: 038 Pixels @ 185,055*/ 38, 0x00,
  /* ABS: 004 Pixels @ 223,055*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 227,055*/ 38, 0x01,
  /* ABS: 002 Pixels @ 265,055*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 267,055*/ 9, 0x03,
  /* ABS: 002 Pixels @ 276,055*/ 0, 2, 0x04, 0x04,
  /* RLE: 122 Pixels @ 278,055*/ 122, 0x01,
  /* RLE: 022 Pixels @ 000,056*/ 22, 0x02,
  /* RLE: 001 Pixels @ 022,056*/ 1, 0x04,
  /* RLE: 006 Pixels @ 023,056*/ 6, 0x03,
  /* RLE: 001 Pixels @ 029,056*/ 1, 0x04,
  /* RLE: 085 Pixels @ 030,056*/ 85, 0x01,
  /* ABS: 002 Pixels @ 115,056*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 117,056*/ 8, 0x03,
  /* ABS: 002 Pixels @ 125,056*/ 0, 2, 0x04, 0x04,
  /* RLE: 037 Pixels @ 127,056*/ 37, 0x00,
  /* ABS: 012 Pixels @ 164,056*/ 0, 12, 0x03, 0x00, 0x0F, 0x08, 0x0E, 0x03, 0x0E, 0x08, 0x0F, 0x0E, 0x06, 0x11,
  /* RLE: 009 Pixels @ 176,056*/ 9, 0x03,
  /* RLE: 001 Pixels @ 185,056*/ 1, 0x04,
  /* RLE: 038 Pixels @ 186,056*/ 38, 0x00,
  /* ABS: 004 Pixels @ 224,056*/ 0, 4, 0x0D, 0x06, 0x03, 0x0D,
  /* RLE: 038 Pixels @ 228,056*/ 38, 0x01,
  /* ABS: 002 Pixels @ 266,056*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 268,056*/ 9, 0x03,
  /* ABS: 002 Pixels @ 277,056*/ 0, 2, 0x04, 0x04,
  /* RLE: 121 Pixels @ 279,056*/ 121, 0x01,
  /* RLE: 022 Pixels @ 000,057*/ 22, 0x02,
  /* RLE: 001 Pixels @ 022,057*/ 1, 0x04,
  /* RLE: 006 Pixels @ 023,057*/ 6, 0x03,
  /* RLE: 001 Pixels @ 029,057*/ 1, 0x04,
  /* RLE: 086 Pixels @ 030,057*/ 86, 0x01,
  /* ABS: 002 Pixels @ 116,057*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 118,057*/ 8, 0x03,
  /* ABS: 002 Pixels @ 126,057*/ 0, 2, 0x04, 0x04,
  /* RLE: 034 Pixels @ 128,057*/ 34, 0x00,
  /* ABS: 016 Pixels @ 162,057*/ 0, 16, 0x04, 0x03, 0x0E, 0x06, 0x0C, 0x0C, 0x08, 0x04, 0x03, 0x03, 0x00, 0x00, 0x0D, 0x0E, 0x03, 0x04,
  /* RLE: 007 Pixels @ 178,057*/ 7, 0x03,
  /* RLE: 001 Pixels @ 185,057*/ 1, 0x04,
  /* RLE: 039 Pixels @ 186,057*/ 39, 0x00,
  /* ABS: 004 Pixels @ 225,057*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 229,057*/ 38, 0x01,
  /* ABS: 002 Pixels @ 267,057*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 269,057*/ 9, 0x03,
  /* ABS: 002 Pixels @ 278,057*/ 0, 2, 0x04, 0x04,
  /* RLE: 120 Pixels @ 280,057*/ 120, 0x01,
  /* RLE: 022 Pixels @ 000,058*/ 22, 0x02,
  /* RLE: 001 Pixels @ 022,058*/ 1, 0x04,
  /* RLE: 007 Pixels @ 023,058*/ 7, 0x03,
  /* RLE: 001 Pixels @ 030,058*/ 1, 0x00,
  /* RLE: 086 Pixels @ 031,058*/ 86, 0x01,
  /* ABS: 002 Pixels @ 117,058*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 119,058*/ 8, 0x03,
  /* ABS: 002 Pixels @ 127,058*/ 0, 2, 0x04, 0x04,
  /* RLE: 031 Pixels @ 129,058*/ 31, 0x00,
  /* RLE: 004 Pixels @ 160,058*/ 4, 0x03,
  /* ABS: 015 Pixels @ 164,058*/ 0, 15, 0x11, 0x08, 0x03, 0x03, 0x06, 0x0F, 0x03, 0x0C, 0x08, 0x08, 0x0E, 0x03, 0x03, 0x00, 0x04,
  /* RLE: 007 Pixels @ 179,058*/ 7, 0x03,
  /* RLE: 040 Pixels @ 186,058*/ 40, 0x00,
  /* ABS: 004 Pixels @ 226,058*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 230,058*/ 38, 0x01,
  /* ABS: 002 Pixels @ 268,058*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 270,058*/ 9, 0x03,
  /* ABS: 002 Pixels @ 279,058*/ 0, 2, 0x04, 0x04,
  /* RLE: 119 Pixels @ 281,058*/ 119, 0x01,
  /* RLE: 021 Pixels @ 000,059*/ 21, 0x02,
  /* RLE: 001 Pixels @ 021,059*/ 1, 0x18,
  /* RLE: 005 Pixels @ 022,059*/ 5, 0x03,
  /* ABS: 005 Pixels @ 027,059*/ 0, 5, 0x00, 0x04, 0x0E, 0x03, 0x18,
  /* RLE: 086 Pixels @ 032,059*/ 86, 0x01,
  /* ABS: 002 Pixels @ 118,059*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 120,059*/ 9, 0x03,
  /* ABS: 002 Pixels @ 129,059*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 131,059*/ 28, 0x00,
  /* ABS: 012 Pixels @ 159,059*/ 0, 12, 0x03, 0x0B, 0x06, 0x0E, 0x00, 0x0B, 0x08, 0x0C, 0x03, 0x00, 0x0F, 0x0E,
  /* RLE: 005 Pixels @ 171,059*/ 5, 0x03,
  /* RLE: 003 Pixels @ 176,059*/ 3, 0x00,
  /* RLE: 001 Pixels @ 179,059*/ 1, 0x04,
  /* RLE: 006 Pixels @ 180,059*/ 6, 0x03,
  /* RLE: 001 Pixels @ 186,059*/ 1, 0x04,
  /* RLE: 040 Pixels @ 187,059*/ 40, 0x00,
  /* RLE: 001 Pixels @ 227,059*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 228,059*/ 4, 0x06,
  /* RLE: 037 Pixels @ 232,059*/ 37, 0x01,
  /* RLE: 003 Pixels @ 269,059*/ 3, 0x04,
  /* RLE: 008 Pixels @ 272,059*/ 8, 0x03,
  /* RLE: 003 Pixels @ 280,059*/ 3, 0x04,
  /* RLE: 117 Pixels @ 283,059*/ 117, 0x01,
  /* RLE: 021 Pixels @ 000,060*/ 21, 0x02,
  /* ABS: 005 Pixels @ 021,060*/ 0, 5, 0x03, 0x00, 0x0C, 0x12, 0x0D,
  /* RLE: 004 Pixels @ 026,060*/ 4, 0x08,
  /* ABS: 002 Pixels @ 030,060*/ 0, 2, 0x0B, 0x15,
  /* RLE: 083 Pixels @ 032,060*/ 83, 0x01,
  /* ABS: 005 Pixels @ 115,060*/ 0, 5, 0x15, 0x03, 0x00, 0x18, 0x00,
  /* RLE: 010 Pixels @ 120,060*/ 10, 0x03,
  /* ABS: 002 Pixels @ 130,060*/ 0, 2, 0x04, 0x04,
  /* RLE: 026 Pixels @ 132,060*/ 26, 0x00,
  /* ABS: 015 Pixels @ 158,060*/ 0, 15, 0x03, 0x04, 0x0F, 0x0C, 0x0D, 0x0F, 0x03, 0x12, 0x0F, 0x00, 0x03, 0x04, 0x00, 0x03, 0x04,
  /* RLE: 005 Pixels @ 173,060*/ 5, 0x00,
  /* ABS: 002 Pixels @ 178,060*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 180,060*/ 6, 0x03,
  /* RLE: 001 Pixels @ 186,060*/ 1, 0x04,
  /* RLE: 041 Pixels @ 187,060*/ 41, 0x00,
  /* RLE: 001 Pixels @ 228,060*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 229,060*/ 4, 0x06,
  /* RLE: 038 Pixels @ 233,060*/ 38, 0x01,
  /* ABS: 002 Pixels @ 271,060*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 273,060*/ 9, 0x03,
  /* ABS: 002 Pixels @ 282,060*/ 0, 2, 0x04, 0x04,
  /* RLE: 116 Pixels @ 284,060*/ 116, 0x01,
  /* RLE: 021 Pixels @ 000,061*/ 21, 0x02,
  /* ABS: 011 Pixels @ 021,061*/ 0, 11, 0x03, 0x06, 0x08, 0x08, 0x06, 0x0E, 0x04, 0x0B, 0x03, 0x03, 0x15,
  /* RLE: 083 Pixels @ 032,061*/ 83, 0x01,
  /* ABS: 007 Pixels @ 115,061*/ 0, 7, 0x03, 0x0B, 0x04, 0x03, 0x03, 0x0C, 0x0B,
  /* RLE: 009 Pixels @ 122,061*/ 9, 0x03,
  /* ABS: 002 Pixels @ 131,061*/ 0, 2, 0x04, 0x04,
  /* RLE: 023 Pixels @ 133,061*/ 23, 0x00,
  /* ABS: 014 Pixels @ 156,061*/ 0, 14, 0x04, 0x04, 0x03, 0x06, 0x0E, 0x00, 0x06, 0x12, 0x00, 0x00, 0x0F, 0x12, 0x03, 0x03,
  /* RLE: 009 Pixels @ 170,061*/ 9, 0x00,
  /* RLE: 001 Pixels @ 179,061*/ 1, 0x04,
  /* RLE: 007 Pixels @ 180,061*/ 7, 0x03,
  /* RLE: 042 Pixels @ 187,061*/ 42, 0x00,
  /* RLE: 001 Pixels @ 229,061*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 230,061*/ 4, 0x06,
  /* RLE: 038 Pixels @ 234,061*/ 38, 0x01,
  /* ABS: 002 Pixels @ 272,061*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 274,061*/ 9, 0x03,
  /* ABS: 002 Pixels @ 283,061*/ 0, 2, 0x04, 0x04,
  /* RLE: 115 Pixels @ 285,061*/ 115, 0x01,
  /* RLE: 021 Pixels @ 000,062*/ 21, 0x02,
  /* ABS: 010 Pixels @ 021,062*/ 0, 10, 0x03, 0x00, 0x00, 0x03, 0x03, 0x03, 0x00, 0x00, 0x03, 0x04,
  /* RLE: 084 Pixels @ 031,062*/ 84, 0x01,
  /* ABS: 007 Pixels @ 115,062*/ 0, 7, 0x03, 0x06, 0x0F, 0x00, 0x00, 0x08, 0x0B,
  /* RLE: 010 Pixels @ 122,062*/ 10, 0x03,
  /* ABS: 002 Pixels @ 132,062*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 134,062*/ 16, 0x00,
  /* RLE: 001 Pixels @ 150,062*/ 1, 0x03,
  /* RLE: 004 Pixels @ 151,062*/ 4, 0x00,
  /* RLE: 004 Pixels @ 155,062*/ 4, 0x03,
  /* ABS: 011 Pixels @ 159,062*/ 0, 11, 0x0E, 0x0F, 0x11, 0x0B, 0x04, 0x06, 0x03, 0x0B, 0x00, 0x03, 0x04,
  /* RLE: 010 Pixels @ 170,062*/ 10, 0x00,
  /* RLE: 001 Pixels @ 180,062*/ 1, 0x04,
  /* RLE: 006 Pixels @ 181,062*/ 6, 0x03,
  /* RLE: 001 Pixels @ 187,062*/ 1, 0x04,
  /* RLE: 043 Pixels @ 188,062*/ 43, 0x00,
  /* ABS: 004 Pixels @ 231,062*/ 0, 4, 0x0D, 0x03, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 235,062*/ 38, 0x01,
  /* ABS: 002 Pixels @ 273,062*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 275,062*/ 9, 0x03,
  /* ABS: 002 Pixels @ 284,062*/ 0, 2, 0x04, 0x04,
  /* RLE: 114 Pixels @ 286,062*/ 114, 0x01,
  /* RLE: 021 Pixels @ 000,063*/ 21, 0x02,
  /* ABS: 010 Pixels @ 021,063*/ 0, 10, 0x18, 0x03, 0x04, 0x0E, 0x06, 0x0F, 0x08, 0x08, 0x03, 0x04,
  /* RLE: 084 Pixels @ 031,063*/ 84, 0x01,
  /* ABS: 007 Pixels @ 115,063*/ 0, 7, 0x03, 0x00, 0x0F, 0x0D, 0x00, 0x08, 0x00,
  /* RLE: 011 Pixels @ 122,063*/ 11, 0x03,
  /* ABS: 002 Pixels @ 133,063*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 135,063*/ 14, 0x00,
  /* ABS: 017 Pixels @ 149,063*/ 0, 17, 0x03, 0x0C, 0x03, 0x00, 0x00, 0x03, 0x0B, 0x06, 0x12, 0x03, 0x00, 0x08, 0x12, 0x0B, 0x06, 0x0E, 0x03,
  /* RLE: 014 Pixels @ 166,063*/ 14, 0x00,
  /* RLE: 001 Pixels @ 180,063*/ 1, 0x04,
  /* RLE: 006 Pixels @ 181,063*/ 6, 0x03,
  /* RLE: 001 Pixels @ 187,063*/ 1, 0x04,
  /* RLE: 044 Pixels @ 188,063*/ 44, 0x00,
  /* ABS: 004 Pixels @ 232,063*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 038 Pixels @ 236,063*/ 38, 0x01,
  /* ABS: 002 Pixels @ 274,063*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 276,063*/ 9, 0x03,
  /* ABS: 002 Pixels @ 285,063*/ 0, 2, 0x04, 0x04,
  /* RLE: 113 Pixels @ 287,063*/ 113, 0x01,
  /* RLE: 022 Pixels @ 000,064*/ 22, 0x02,
  /* ABS: 009 Pixels @ 022,064*/ 0, 9, 0x03, 0x06, 0x08, 0x06, 0x0E, 0x0E, 0x0D, 0x03, 0x00,
  /* RLE: 084 Pixels @ 031,064*/ 84, 0x01,
  /* ABS: 009 Pixels @ 115,064*/ 0, 9, 0x00, 0x03, 0x0B, 0x08, 0x0D, 0x08, 0x08, 0x0E, 0x00,
  /* RLE: 010 Pixels @ 124,064*/ 10, 0x03,
  /* ABS: 002 Pixels @ 134,064*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 136,064*/ 13, 0x00,
  /* ABS: 018 Pixels @ 149,064*/ 0, 18, 0x0B, 0x08, 0x0C, 0x03, 0x03, 0x0E, 0x11, 0x0C, 0x0B, 0x03, 0x03, 0x0B, 0x11, 0x0F, 0x0E, 0x03, 0x00, 0x04,
  /* RLE: 012 Pixels @ 167,064*/ 12, 0x00,
  /* ABS: 002 Pixels @ 179,064*/ 0, 2, 0x0B, 0x04,
  /* RLE: 007 Pixels @ 181,064*/ 7, 0x03,
  /* RLE: 045 Pixels @ 188,064*/ 45, 0x00,
  /* ABS: 004 Pixels @ 233,064*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 237,064*/ 38, 0x01,
  /* ABS: 002 Pixels @ 275,064*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 277,064*/ 9, 0x03,
  /* ABS: 002 Pixels @ 286,064*/ 0, 2, 0x04, 0x04,
  /* RLE: 112 Pixels @ 288,064*/ 112, 0x01,
  /* RLE: 022 Pixels @ 000,065*/ 22, 0x02,
  /* RLE: 006 Pixels @ 022,065*/ 6, 0x03,
  /* ABS: 003 Pixels @ 028,065*/ 0, 3, 0x06, 0x0C, 0x03,
  /* RLE: 082 Pixels @ 031,065*/ 82, 0x01,
  /* ABS: 014 Pixels @ 113,065*/ 0, 14, 0x15, 0x03, 0x03, 0x0B, 0x00, 0x0E, 0x08, 0x0D, 0x0C, 0x11, 0x08, 0x0E, 0x03, 0x04,
  /* RLE: 008 Pixels @ 127,065*/ 8, 0x03,
  /* ABS: 002 Pixels @ 135,065*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 137,065*/ 8, 0x00,
  /* RLE: 005 Pixels @ 145,065*/ 5, 0x03,
  /* ABS: 010 Pixels @ 150,065*/ 0, 10, 0x0E, 0x08, 0x00, 0x03, 0x08, 0x0C, 0x0E, 0x11, 0x08, 0x0E,
  /* RLE: 005 Pixels @ 160,065*/ 5, 0x03,
  /* RLE: 016 Pixels @ 165,065*/ 16, 0x00,
  /* RLE: 001 Pixels @ 181,065*/ 1, 0x04,
  /* RLE: 006 Pixels @ 182,065*/ 6, 0x03,
  /* RLE: 001 Pixels @ 188,065*/ 1, 0x04,
  /* RLE: 045 Pixels @ 189,065*/ 45, 0x00,
  /* RLE: 001 Pixels @ 234,065*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 235,065*/ 4, 0x06,
  /* RLE: 038 Pixels @ 239,065*/ 38, 0x01,
  /* ABS: 002 Pixels @ 277,065*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 279,065*/ 8, 0x03,
  /* ABS: 002 Pixels @ 287,065*/ 0, 2, 0x04, 0x04,
  /* RLE: 111 Pixels @ 289,065*/ 111, 0x01,
  /* RLE: 022 Pixels @ 000,066*/ 22, 0x02,
  /* ABS: 009 Pixels @ 022,066*/ 0, 9, 0x00, 0x03, 0x03, 0x0B, 0x04, 0x0E, 0x0F, 0x0C, 0x03,
  /* RLE: 081 Pixels @ 031,066*/ 81, 0x01,
  /* ABS: 016 Pixels @ 112,066*/ 0, 16, 0x04, 0x03, 0x00, 0x0D, 0x08, 0x0F, 0x03, 0x0D, 0x0F, 0x03, 0x03, 0x0B, 0x03, 0x00, 0x04, 0x04,
  /* RLE: 008 Pixels @ 128,066*/ 8, 0x03,
  /* ABS: 002 Pixels @ 136,066*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 138,066*/ 6, 0x00,
  /* ABS: 020 Pixels @ 144,066*/ 0, 20, 0x03, 0x03, 0x0D, 0x00, 0x03, 0x03, 0x03, 0x11, 0x06, 0x03, 0x0E, 0x08, 0x0F, 0x0E, 0x06, 0x11, 0x03, 0x04, 0x04, 0x04,
  /* RLE: 017 Pixels @ 164,066*/ 17, 0x00,
  /* RLE: 001 Pixels @ 181,066*/ 1, 0x04,
  /* RLE: 006 Pixels @ 182,066*/ 6, 0x03,
  /* RLE: 001 Pixels @ 188,066*/ 1, 0x04,
  /* RLE: 046 Pixels @ 189,066*/ 46, 0x00,
  /* RLE: 001 Pixels @ 235,066*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 236,066*/ 4, 0x06,
  /* RLE: 038 Pixels @ 240,066*/ 38, 0x01,
  /* ABS: 002 Pixels @ 278,066*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 280,066*/ 9, 0x03,
  /* ABS: 002 Pixels @ 289,066*/ 0, 2, 0x04, 0x04,
  /* RLE: 109 Pixels @ 291,066*/ 109, 0x01,
  /* RLE: 022 Pixels @ 000,067*/ 22, 0x02,
  /* ABS: 002 Pixels @ 022,067*/ 0, 2, 0x00, 0x0B,
  /* RLE: 005 Pixels @ 024,067*/ 5, 0x08,
  /* ABS: 003 Pixels @ 029,067*/ 0, 3, 0x00, 0x03, 0x04,
  /* RLE: 077 Pixels @ 032,067*/ 77, 0x01,
  /* ABS: 014 Pixels @ 109,067*/ 0, 14, 0x15, 0x00, 0x00, 0x00, 0x03, 0x06, 0x12, 0x03, 0x00, 0x03, 0x00, 0x08, 0x0E, 0x03,
  /* RLE: 004 Pixels @ 123,067*/ 4, 0x00,
  /* ABS: 002 Pixels @ 127,067*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 129,067*/ 8, 0x03,
  /* ABS: 002 Pixels @ 137,067*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 139,067*/ 4, 0x00,
  /* ABS: 020 Pixels @ 143,067*/ 0, 20, 0x03, 0x0E, 0x00, 0x0C, 0x03, 0x0C, 0x00, 0x03, 0x0B, 0x08, 0x0C, 0x03, 0x03, 0x00, 0x00, 0x0D, 0x0E, 0x03, 0x04, 0x0B,
  /* RLE: 018 Pixels @ 163,067*/ 18, 0x00,
  /* RLE: 001 Pixels @ 181,067*/ 1, 0x04,
  /* RLE: 007 Pixels @ 182,067*/ 7, 0x03,
  /* RLE: 047 Pixels @ 189,067*/ 47, 0x00,
  /* ABS: 005 Pixels @ 236,067*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 241,067*/ 38, 0x01,
  /* ABS: 002 Pixels @ 279,067*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 281,067*/ 9, 0x03,
  /* ABS: 002 Pixels @ 290,067*/ 0, 2, 0x04, 0x04,
  /* RLE: 108 Pixels @ 292,067*/ 108, 0x01,
  /* RLE: 022 Pixels @ 000,068*/ 22, 0x02,
  /* ABS: 010 Pixels @ 022,068*/ 0, 10, 0x18, 0x03, 0x0C, 0x04, 0x0B, 0x03, 0x0C, 0x0D, 0x03, 0x00,
  /* RLE: 075 Pixels @ 032,068*/ 75, 0x01,
  /* ABS: 017 Pixels @ 107,068*/ 0, 17, 0x15, 0x03, 0x03, 0x0B, 0x00, 0x03, 0x03, 0x08, 0x0C, 0x03, 0x03, 0x0C, 0x06, 0x0B, 0x03, 0x03, 0x04,
  /* RLE: 004 Pixels @ 124,068*/ 4, 0x00,
  /* ABS: 002 Pixels @ 128,068*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 130,068*/ 8, 0x03,
  /* ABS: 022 Pixels @ 138,068*/ 0, 22, 0x04, 0x04, 0x00, 0x03, 0x03, 0x03, 0x0E, 0x00, 0x03, 0x03, 0x0F, 0x12, 0x03, 0x03, 0x12, 0x0F, 0x03, 0x0C, 0x08, 0x08, 0x0E, 0x03,
  /* RLE: 021 Pixels @ 160,068*/ 21, 0x00,
  /* ABS: 002 Pixels @ 181,068*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 183,068*/ 6, 0x03,
  /* RLE: 001 Pixels @ 189,068*/ 1, 0x04,
  /* RLE: 048 Pixels @ 190,068*/ 48, 0x00,
  /* ABS: 004 Pixels @ 238,068*/ 0, 4, 0x0D, 0x03, 0x06, 0x0D,
  /* RLE: 038 Pixels @ 242,068*/ 38, 0x01,
  /* ABS: 002 Pixels @ 280,068*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 282,068*/ 9, 0x03,
  /* ABS: 002 Pixels @ 291,068*/ 0, 2, 0x04, 0x04,
  /* RLE: 107 Pixels @ 293,068*/ 107, 0x01,
  /* RLE: 023 Pixels @ 000,069*/ 23, 0x02,
  /* RLE: 005 Pixels @ 023,069*/ 5, 0x03,
  /* ABS: 004 Pixels @ 028,069*/ 0, 4, 0x04, 0x08, 0x00, 0x00,
  /* RLE: 071 Pixels @ 032,069*/ 71, 0x01,
  /* ABS: 020 Pixels @ 103,069*/ 0, 20, 0x15, 0x00, 0x00, 0x15, 0x03, 0x00, 0x0D, 0x08, 0x08, 0x0F, 0x00, 0x0D, 0x0F, 0x03, 0x03, 0x0E, 0x11, 0x03, 0x00, 0x04,
  /* RLE: 006 Pixels @ 123,069*/ 6, 0x00,
  /* ABS: 002 Pixels @ 129,069*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 131,069*/ 10, 0x03,
  /* ABS: 014 Pixels @ 141,069*/ 0, 14, 0x00, 0x0F, 0x00, 0x03, 0x0E, 0x00, 0x03, 0x0C, 0x08, 0x0B, 0x03, 0x00, 0x0F, 0x12,
  /* RLE: 005 Pixels @ 155,069*/ 5, 0x03,
  /* RLE: 022 Pixels @ 160,069*/ 22, 0x00,
  /* RLE: 001 Pixels @ 182,069*/ 1, 0x04,
  /* RLE: 006 Pixels @ 183,069*/ 6, 0x03,
  /* RLE: 001 Pixels @ 189,069*/ 1, 0x04,
  /* RLE: 049 Pixels @ 190,069*/ 49, 0x00,
  /* ABS: 004 Pixels @ 239,069*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 038 Pixels @ 243,069*/ 38, 0x01,
  /* ABS: 002 Pixels @ 281,069*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 283,069*/ 9, 0x03,
  /* RLE: 001 Pixels @ 292,069*/ 1, 0x04,
  /* RLE: 105 Pixels @ 293,069*/ 105, 0x01,
  /* ABS: 002 Pixels @ 398,069*/ 0, 2, 0x15, 0x00,
  /* RLE: 023 Pixels @ 000,070*/ 23, 0x02,
  /* ABS: 009 Pixels @ 023,070*/ 0, 9, 0x03, 0x04, 0x0E, 0x0D, 0x08, 0x08, 0x0F, 0x03, 0x00,
  /* RLE: 071 Pixels @ 032,070*/ 71, 0x01,
  /* ABS: 018 Pixels @ 103,070*/ 0, 18, 0x03, 0x00, 0x0B, 0x03, 0x03, 0x12, 0x0D, 0x03, 0x00, 0x0F, 0x0D, 0x0B, 0x08, 0x11, 0x0E, 0x08, 0x04, 0x03,
  /* RLE: 009 Pixels @ 121,070*/ 9, 0x00,
  /* ABS: 002 Pixels @ 130,070*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 132,070*/ 9, 0x03,
  /* ABS: 016 Pixels @ 141,070*/ 0, 16, 0x00, 0x0F, 0x06, 0x03, 0x0F, 0x06, 0x03, 0x03, 0x06, 0x0F, 0x03, 0x03, 0x0B, 0x00, 0x03, 0x04,
  /* RLE: 024 Pixels @ 157,070*/ 24, 0x00,
  /* ABS: 002 Pixels @ 181,070*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 183,070*/ 6, 0x03,
  /* RLE: 001 Pixels @ 189,070*/ 1, 0x04,
  /* RLE: 050 Pixels @ 190,070*/ 50, 0x00,
  /* ABS: 004 Pixels @ 240,070*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 244,070*/ 38, 0x01,
  /* ABS: 002 Pixels @ 282,070*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 284,070*/ 9, 0x03,
  /* RLE: 105 Pixels @ 293,070*/ 105, 0x01,
  /* ABS: 002 Pixels @ 398,070*/ 0, 2, 0x03, 0x00,
  /* RLE: 023 Pixels @ 000,071*/ 23, 0x02,
  /* ABS: 009 Pixels @ 023,071*/ 0, 9, 0x03, 0x0C, 0x0F, 0x06, 0x0C, 0x04, 0x03, 0x03, 0x04,
  /* RLE: 071 Pixels @ 032,071*/ 71, 0x01,
  /* ABS: 017 Pixels @ 103,071*/ 0, 17, 0x03, 0x0F, 0x0F, 0x03, 0x03, 0x08, 0x0C, 0x03, 0x03, 0x0C, 0x08, 0x03, 0x0B, 0x12, 0x06, 0x0B, 0x03,
  /* RLE: 011 Pixels @ 120,071*/ 11, 0x00,
  /* ABS: 002 Pixels @ 131,071*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 133,071*/ 5, 0x03,
  /* ABS: 018 Pixels @ 138,071*/ 0, 18, 0x0C, 0x03, 0x03, 0x03, 0x04, 0x08, 0x04, 0x0B, 0x08, 0x0C, 0x03, 0x04, 0x08, 0x0E, 0x03, 0x03, 0x00, 0x0B,
  /* RLE: 027 Pixels @ 156,071*/ 27, 0x00,
  /* RLE: 007 Pixels @ 183,071*/ 7, 0x03,
  /* RLE: 001 Pixels @ 190,071*/ 1, 0x04,
  /* RLE: 050 Pixels @ 191,071*/ 50, 0x00,
  /* RLE: 001 Pixels @ 241,071*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 242,071*/ 4, 0x06,
  /* RLE: 037 Pixels @ 246,071*/ 37, 0x01,
  /* ABS: 002 Pixels @ 283,071*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 285,071*/ 9, 0x03,
  /* ABS: 002 Pixels @ 294,071*/ 0, 2, 0x04, 0x04,
  /* RLE: 102 Pixels @ 296,071*/ 102, 0x01,
  /* ABS: 002 Pixels @ 398,071*/ 0, 2, 0x03, 0x0E,
  /* RLE: 023 Pixels @ 000,072*/ 23, 0x02,
  /* RLE: 001 Pixels @ 023,072*/ 1, 0x00,
  /* RLE: 008 Pixels @ 024,072*/ 8, 0x03,
  /* RLE: 001 Pixels @ 032,072*/ 1, 0x04,
  /* RLE: 066 Pixels @ 033,072*/ 66, 0x01,
  /* ABS: 015 Pixels @ 099,072*/ 0, 15, 0x15, 0x03, 0x03, 0x00, 0x03, 0x0B, 0x08, 0x12, 0x03, 0x0D, 0x11, 0x03, 0x03, 0x0C, 0x0F,
  /* RLE: 005 Pixels @ 114,072*/ 5, 0x03,
  /* RLE: 013 Pixels @ 119,072*/ 13, 0x00,
  /* ABS: 022 Pixels @ 132,072*/ 0, 22, 0x04, 0x04, 0x03, 0x03, 0x03, 0x00, 0x08, 0x0E, 0x03, 0x03, 0x0C, 0x08, 0x0F, 0x03, 0x06, 0x0F, 0x04, 0x0D, 0x0C, 0x00, 0x03, 0x04,
  /* RLE: 029 Pixels @ 154,072*/ 29, 0x00,
  /* RLE: 001 Pixels @ 183,072*/ 1, 0x04,
  /* RLE: 006 Pixels @ 184,072*/ 6, 0x03,
  /* RLE: 001 Pixels @ 190,072*/ 1, 0x04,
  /* RLE: 051 Pixels @ 191,072*/ 51, 0x00,
  /* ABS: 005 Pixels @ 242,072*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 037 Pixels @ 247,072*/ 37, 0x01,
  /* ABS: 002 Pixels @ 284,072*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 286,072*/ 9, 0x03,
  /* ABS: 002 Pixels @ 295,072*/ 0, 2, 0x04, 0x04,
  /* RLE: 100 Pixels @ 297,072*/ 100, 0x01,
  /* ABS: 003 Pixels @ 397,072*/ 0, 3, 0x00, 0x03, 0x03,
  /* RLE: 025 Pixels @ 000,073*/ 25, 0x02,
  /* RLE: 001 Pixels @ 025,073*/ 1, 0x04,
  /* RLE: 006 Pixels @ 026,073*/ 6, 0x03,
  /* RLE: 001 Pixels @ 032,073*/ 1, 0x04,
  /* RLE: 066 Pixels @ 033,073*/ 66, 0x01,
  /* ABS: 018 Pixels @ 099,073*/ 0, 18, 0x03, 0x0B, 0x0C, 0x03, 0x03, 0x03, 0x0E, 0x08, 0x0C, 0x00, 0x08, 0x11, 0x06, 0x08, 0x0C, 0x03, 0x04, 0x04,
  /* RLE: 016 Pixels @ 117,073*/ 16, 0x00,
  /* ABS: 019 Pixels @ 133,073*/ 0, 19, 0x04, 0x04, 0x03, 0x03, 0x03, 0x0E, 0x08, 0x0C, 0x06, 0x0F, 0x0E, 0x08, 0x12, 0x03, 0x11, 0x08, 0x0C, 0x03, 0x03,
  /* RLE: 031 Pixels @ 152,073*/ 31, 0x00,
  /* RLE: 001 Pixels @ 183,073*/ 1, 0x04,
  /* RLE: 006 Pixels @ 184,073*/ 6, 0x03,
  /* RLE: 001 Pixels @ 190,073*/ 1, 0x04,
  /* RLE: 052 Pixels @ 191,073*/ 52, 0x00,
  /* ABS: 005 Pixels @ 243,073*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 248,073*/ 37, 0x01,
  /* ABS: 002 Pixels @ 285,073*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 287,073*/ 9, 0x03,
  /* ABS: 002 Pixels @ 296,073*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 298,073*/ 94, 0x01,
  /* RLE: 003 Pixels @ 392,073*/ 3, 0x18,
  /* ABS: 005 Pixels @ 395,073*/ 0, 5, 0x15, 0x03, 0x03, 0x06, 0x0E,
  /* RLE: 025 Pixels @ 000,074*/ 25, 0x02,
  /* RLE: 001 Pixels @ 025,074*/ 1, 0x04,
  /* RLE: 007 Pixels @ 026,074*/ 7, 0x03,
  /* RLE: 001 Pixels @ 033,074*/ 1, 0x00,
  /* RLE: 065 Pixels @ 034,074*/ 65, 0x01,
  /* ABS: 017 Pixels @ 099,074*/ 0, 17, 0x03, 0x12, 0x08, 0x0B, 0x03, 0x0B, 0x0F, 0x08, 0x08, 0x00, 0x0B, 0x12, 0x12, 0x0B, 0x03, 0x00, 0x04,
  /* RLE: 018 Pixels @ 116,074*/ 18, 0x00,
  /* ABS: 016 Pixels @ 134,074*/ 0, 16, 0x04, 0x04, 0x03, 0x03, 0x03, 0x11, 0x08, 0x06, 0x00, 0x03, 0x0C, 0x08, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 033 Pixels @ 150,074*/ 33, 0x00,
  /* RLE: 001 Pixels @ 183,074*/ 1, 0x0B,
  /* RLE: 007 Pixels @ 184,074*/ 7, 0x03,
  /* RLE: 001 Pixels @ 191,074*/ 1, 0x04,
  /* RLE: 053 Pixels @ 192,074*/ 53, 0x00,
  /* ABS: 004 Pixels @ 245,074*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 249,074*/ 37, 0x01,
  /* ABS: 002 Pixels @ 286,074*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 288,074*/ 9, 0x03,
  /* ABS: 002 Pixels @ 297,074*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 299,074*/ 93, 0x01,
  /* ABS: 008 Pixels @ 392,074*/ 0, 8, 0x03, 0x04, 0x03, 0x03, 0x0B, 0x12, 0x0F, 0x0B,
  /* RLE: 024 Pixels @ 000,075*/ 24, 0x02,
  /* ABS: 002 Pixels @ 024,075*/ 0, 2, 0x18, 0x00,
  /* RLE: 004 Pixels @ 026,075*/ 4, 0x03,
  /* ABS: 005 Pixels @ 030,075*/ 0, 5, 0x00, 0x0B, 0x0C, 0x03, 0x15,
  /* RLE: 064 Pixels @ 035,075*/ 64, 0x01,
  /* ABS: 010 Pixels @ 099,075*/ 0, 10, 0x00, 0x03, 0x0F, 0x0F, 0x0E, 0x08, 0x0E, 0x00, 0x0F, 0x0D,
  /* RLE: 004 Pixels @ 109,075*/ 4, 0x03,
  /* RLE: 022 Pixels @ 113,075*/ 22, 0x00,
  /* RLE: 003 Pixels @ 135,075*/ 3, 0x04,
  /* ABS: 011 Pixels @ 138,075*/ 0, 11, 0x03, 0x00, 0x08, 0x0C, 0x03, 0x03, 0x03, 0x11, 0x0C, 0x03, 0x04,
  /* RLE: 035 Pixels @ 149,075*/ 35, 0x00,
  /* RLE: 001 Pixels @ 184,075*/ 1, 0x04,
  /* RLE: 006 Pixels @ 185,075*/ 6, 0x03,
  /* RLE: 001 Pixels @ 191,075*/ 1, 0x04,
  /* RLE: 054 Pixels @ 192,075*/ 54, 0x00,
  /* ABS: 004 Pixels @ 246,075*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 250,075*/ 37, 0x01,
  /* ABS: 002 Pixels @ 287,075*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 289,075*/ 9, 0x03,
  /* ABS: 002 Pixels @ 298,075*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 300,075*/ 92, 0x01,
  /* ABS: 008 Pixels @ 392,075*/ 0, 8, 0x03, 0x0F, 0x04, 0x04, 0x00, 0x08, 0x0D, 0x03,
  /* RLE: 024 Pixels @ 000,076*/ 24, 0x02,
  /* ABS: 005 Pixels @ 024,076*/ 0, 5, 0x03, 0x00, 0x0C, 0x0E, 0x06,
  /* RLE: 004 Pixels @ 029,076*/ 4, 0x08,
  /* ABS: 002 Pixels @ 033,076*/ 0, 2, 0x0B, 0x15,
  /* RLE: 064 Pixels @ 035,076*/ 64, 0x01,
  /* ABS: 014 Pixels @ 099,076*/ 0, 14, 0x04, 0x03, 0x0B, 0x08, 0x08, 0x04, 0x03, 0x03, 0x04, 0x08, 0x0E, 0x03, 0x04, 0x04,
  /* RLE: 026 Pixels @ 113,076*/ 26, 0x00,
  /* ABS: 004 Pixels @ 139,076*/ 0, 4, 0x03, 0x12, 0x08, 0x00,
  /* RLE: 004 Pixels @ 143,076*/ 4, 0x03,
  /* RLE: 036 Pixels @ 147,076*/ 36, 0x00,
  /* ABS: 002 Pixels @ 183,076*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 185,076*/ 6, 0x03,
  /* RLE: 001 Pixels @ 191,076*/ 1, 0x04,
  /* RLE: 055 Pixels @ 192,076*/ 55, 0x00,
  /* ABS: 004 Pixels @ 247,076*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 038 Pixels @ 251,076*/ 38, 0x01,
  /* ABS: 002 Pixels @ 289,076*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 291,076*/ 9, 0x03,
  /* ABS: 002 Pixels @ 300,076*/ 0, 2, 0x04, 0x04,
  /* RLE: 090 Pixels @ 302,076*/ 90, 0x01,
  /* ABS: 008 Pixels @ 392,076*/ 0, 8, 0x03, 0x11, 0x08, 0x0D, 0x03, 0x0E, 0x08, 0x00,
  /* RLE: 024 Pixels @ 000,077*/ 24, 0x02,
  /* ABS: 011 Pixels @ 024,077*/ 0, 11, 0x03, 0x06, 0x08, 0x08, 0x0D, 0x08, 0x0E, 0x0B, 0x00, 0x03, 0x15,
  /* RLE: 062 Pixels @ 035,077*/ 62, 0x01,
  /* ABS: 015 Pixels @ 097,077*/ 0, 15, 0x04, 0x04, 0x03, 0x03, 0x03, 0x0E, 0x08, 0x0C, 0x03, 0x03, 0x03, 0x0C, 0x03, 0x00, 0x04,
  /* RLE: 027 Pixels @ 112,077*/ 27, 0x00,
  /* ABS: 006 Pixels @ 139,077*/ 0, 6, 0x03, 0x03, 0x0F, 0x06, 0x03, 0x0B,
  /* RLE: 040 Pixels @ 145,077*/ 40, 0x00,
  /* RLE: 007 Pixels @ 185,077*/ 7, 0x03,
  /* RLE: 001 Pixels @ 192,077*/ 1, 0x04,
  /* RLE: 055 Pixels @ 193,077*/ 55, 0x00,
  /* ABS: 004 Pixels @ 248,077*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 252,077*/ 38, 0x01,
  /* ABS: 002 Pixels @ 290,077*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 292,077*/ 9, 0x03,
  /* ABS: 002 Pixels @ 301,077*/ 0, 2, 0x04, 0x04,
  /* RLE: 050 Pixels @ 303,077*/ 50, 0x01,
  /* RLE: 005 Pixels @ 353,077*/ 5, 0x04,
  /* RLE: 034 Pixels @ 358,077*/ 34, 0x01,
  /* ABS: 008 Pixels @ 392,077*/ 0, 8, 0x03, 0x11, 0x0F, 0x06, 0x03, 0x03, 0x0D, 0x0F,
  /* RLE: 024 Pixels @ 000,078*/ 24, 0x02,
  /* ABS: 010 Pixels @ 024,078*/ 0, 10, 0x03, 0x0B, 0x00, 0x03, 0x03, 0x08, 0x04, 0x03, 0x03, 0x00,
  /* RLE: 060 Pixels @ 034,078*/ 60, 0x01,
  /* ABS: 004 Pixels @ 094,078*/ 0, 4, 0x15, 0x00, 0x03, 0x00,
  /* RLE: 005 Pixels @ 098,078*/ 5, 0x03,
  /* ABS: 005 Pixels @ 103,078*/ 0, 5, 0x0D, 0x08, 0x00, 0x03, 0x03,
  /* RLE: 032 Pixels @ 108,078*/ 32, 0x00,
  /* ABS: 004 Pixels @ 140,078*/ 0, 4, 0x03, 0x0B, 0x00, 0x03,
  /* RLE: 041 Pixels @ 144,078*/ 41, 0x00,
  /* RLE: 001 Pixels @ 185,078*/ 1, 0x04,
  /* RLE: 006 Pixels @ 186,078*/ 6, 0x03,
  /* RLE: 001 Pixels @ 192,078*/ 1, 0x04,
  /* RLE: 056 Pixels @ 193,078*/ 56, 0x00,
  /* ABS: 005 Pixels @ 249,078*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 037 Pixels @ 254,078*/ 37, 0x01,
  /* ABS: 002 Pixels @ 291,078*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 293,078*/ 9, 0x03,
  /* ABS: 002 Pixels @ 302,078*/ 0, 2, 0x04, 0x04,
  /* RLE: 048 Pixels @ 304,078*/ 48, 0x01,
  /* ABS: 002 Pixels @ 352,078*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 354,078*/ 5, 0x03,
  /* ABS: 002 Pixels @ 359,078*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 361,078*/ 28, 0x01,
  /* RLE: 005 Pixels @ 389,078*/ 5, 0x03,
  /* ABS: 006 Pixels @ 394,078*/ 0, 6, 0x0C, 0x08, 0x0C, 0x03, 0x00, 0x08,
  /* RLE: 024 Pixels @ 000,079*/ 24, 0x02,
  /* ABS: 007 Pixels @ 024,079*/ 0, 7, 0x18, 0x03, 0x00, 0x03, 0x03, 0x0D, 0x0E,
  /* RLE: 004 Pixels @ 031,079*/ 4, 0x03,
  /* RLE: 058 Pixels @ 035,079*/ 58, 0x01,
  /* ABS: 005 Pixels @ 093,079*/ 0, 5, 0x15, 0x03, 0x00, 0x04, 0x00,
  /* RLE: 005 Pixels @ 098,079*/ 5, 0x03,
  /* ABS: 006 Pixels @ 103,079*/ 0, 6, 0x00, 0x0F, 0x12, 0x03, 0x04, 0x04,
  /* RLE: 032 Pixels @ 109,079*/ 32, 0x00,
  /* ABS: 002 Pixels @ 141,079*/ 0, 2, 0x03, 0x03,
  /* RLE: 006 Pixels @ 143,079*/ 6, 0x00,
  /* RLE: 001 Pixels @ 149,079*/ 1, 0x08,
  /* RLE: 035 Pixels @ 150,079*/ 35, 0x00,
  /* RLE: 001 Pixels @ 185,079*/ 1, 0x04,
  /* RLE: 006 Pixels @ 186,079*/ 6, 0x03,
  /* RLE: 001 Pixels @ 192,079*/ 1, 0x04,
  /* RLE: 057 Pixels @ 193,079*/ 57, 0x00,
  /* ABS: 005 Pixels @ 250,079*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 255,079*/ 37, 0x01,
  /* ABS: 002 Pixels @ 292,079*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 294,079*/ 9, 0x03,
  /* ABS: 002 Pixels @ 303,079*/ 0, 2, 0x04, 0x04,
  /* RLE: 047 Pixels @ 305,079*/ 47, 0x01,
  /* RLE: 001 Pixels @ 352,079*/ 1, 0x04,
  /* RLE: 009 Pixels @ 353,079*/ 9, 0x03,
  /* ABS: 002 Pixels @ 362,079*/ 0, 2, 0x04, 0x04,
  /* RLE: 024 Pixels @ 364,079*/ 24, 0x01,
  /* ABS: 012 Pixels @ 388,079*/ 0, 12, 0x03, 0x03, 0x06, 0x08, 0x12, 0x03, 0x03, 0x12, 0x08, 0x0E, 0x0C, 0x0B,
  /* RLE: 025 Pixels @ 000,080*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,080*/ 0, 10, 0x00, 0x03, 0x03, 0x03, 0x12, 0x0D, 0x0C, 0x12, 0x0D, 0x03,
  /* RLE: 057 Pixels @ 035,080*/ 57, 0x01,
  /* ABS: 007 Pixels @ 092,080*/ 0, 7, 0x18, 0x03, 0x00, 0x0F, 0x08, 0x0F, 0x00,
  /* RLE: 005 Pixels @ 099,080*/ 5, 0x03,
  /* ABS: 003 Pixels @ 104,080*/ 0, 3, 0x0B, 0x03, 0x03,
  /* RLE: 042 Pixels @ 107,080*/ 42, 0x00,
  /* RLE: 001 Pixels @ 149,080*/ 1, 0x08,
  /* RLE: 035 Pixels @ 150,080*/ 35, 0x00,
  /* RLE: 001 Pixels @ 185,080*/ 1, 0x0B,
  /* RLE: 007 Pixels @ 186,080*/ 7, 0x03,
  /* RLE: 001 Pixels @ 193,080*/ 1, 0x04,
  /* RLE: 057 Pixels @ 194,080*/ 57, 0x00,
  /* ABS: 005 Pixels @ 251,080*/ 0, 5, 0x06, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 256,080*/ 37, 0x01,
  /* ABS: 002 Pixels @ 293,080*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 295,080*/ 9, 0x03,
  /* ABS: 002 Pixels @ 304,080*/ 0, 2, 0x04, 0x04,
  /* RLE: 046 Pixels @ 306,080*/ 46, 0x01,
  /* RLE: 001 Pixels @ 352,080*/ 1, 0x04,
  /* RLE: 012 Pixels @ 353,080*/ 12, 0x03,
  /* ABS: 002 Pixels @ 365,080*/ 0, 2, 0x04, 0x04,
  /* RLE: 020 Pixels @ 367,080*/ 20, 0x01,
  /* ABS: 013 Pixels @ 387,080*/ 0, 13, 0x15, 0x03, 0x11, 0x0E, 0x0B, 0x00, 0x00, 0x03, 0x03, 0x0D, 0x0F, 0x04, 0x03,
  /* RLE: 025 Pixels @ 000,081*/ 25, 0x02,
  /* ABS: 004 Pixels @ 025,081*/ 0, 4, 0x03, 0x0E, 0x06, 0x0F,
  /* RLE: 004 Pixels @ 029,081*/ 4, 0x08,
  /* ABS: 002 Pixels @ 033,081*/ 0, 2, 0x06, 0x03,
  /* RLE: 051 Pixels @ 035,081*/ 51, 0x01,
  /* ABS: 013 Pixels @ 086,081*/ 0, 13, 0x15, 0x00, 0x03, 0x18, 0x01, 0x03, 0x03, 0x04, 0x0E, 0x0C, 0x00, 0x0F, 0x0D,
  /* RLE: 005 Pixels @ 099,081*/ 5, 0x03,
  /* RLE: 046 Pixels @ 104,081*/ 46, 0x00,
  /* RLE: 001 Pixels @ 150,081*/ 1, 0x08,
  /* RLE: 035 Pixels @ 151,081*/ 35, 0x00,
  /* RLE: 001 Pixels @ 186,081*/ 1, 0x04,
  /* RLE: 006 Pixels @ 187,081*/ 6, 0x03,
  /* RLE: 001 Pixels @ 193,081*/ 1, 0x04,
  /* RLE: 059 Pixels @ 194,081*/ 59, 0x00,
  /* ABS: 004 Pixels @ 253,081*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 257,081*/ 37, 0x01,
  /* ABS: 002 Pixels @ 294,081*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 296,081*/ 9, 0x03,
  /* ABS: 002 Pixels @ 305,081*/ 0, 2, 0x04, 0x04,
  /* RLE: 045 Pixels @ 307,081*/ 45, 0x01,
  /* RLE: 001 Pixels @ 352,081*/ 1, 0x04,
  /* RLE: 015 Pixels @ 353,081*/ 15, 0x03,
  /* ABS: 002 Pixels @ 368,081*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 370,081*/ 10, 0x01,
  /* RLE: 003 Pixels @ 380,081*/ 3, 0x15,
  /* RLE: 004 Pixels @ 383,081*/ 4, 0x01,
  /* ABS: 008 Pixels @ 387,081*/ 0, 8, 0x00, 0x00, 0x08, 0x0C, 0x0D, 0x08, 0x08, 0x0E,
  /* RLE: 004 Pixels @ 395,081*/ 4, 0x03,
  /* RLE: 001 Pixels @ 399,081*/ 1, 0x00,
  /* RLE: 025 Pixels @ 000,082*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,082*/ 0, 10, 0x03, 0x0D, 0x11, 0x06, 0x0C, 0x0B, 0x00, 0x03, 0x03, 0x00,
  /* RLE: 051 Pixels @ 035,082*/ 51, 0x01,
  /* ABS: 020 Pixels @ 086,082*/ 0, 20, 0x03, 0x00, 0x0C, 0x03, 0x03, 0x03, 0x0D, 0x08, 0x08, 0x0C, 0x03, 0x0B, 0x08, 0x0E, 0x03, 0x03, 0x04, 0x04, 0x04, 0x0B,
  /* RLE: 080 Pixels @ 106,082*/ 80, 0x00,
  /* RLE: 001 Pixels @ 186,082*/ 1, 0x04,
  /* RLE: 006 Pixels @ 187,082*/ 6, 0x03,
  /* RLE: 001 Pixels @ 193,082*/ 1, 0x04,
  /* RLE: 060 Pixels @ 194,082*/ 60, 0x00,
  /* ABS: 004 Pixels @ 254,082*/ 0, 4, 0x0D, 0x06, 0x03, 0x0D,
  /* RLE: 037 Pixels @ 258,082*/ 37, 0x01,
  /* RLE: 003 Pixels @ 295,082*/ 3, 0x04,
  /* RLE: 008 Pixels @ 298,082*/ 8, 0x03,
  /* RLE: 003 Pixels @ 306,082*/ 3, 0x04,
  /* RLE: 043 Pixels @ 309,082*/ 43, 0x01,
  /* ABS: 002 Pixels @ 352,082*/ 0, 2, 0x04, 0x04,
  /* RLE: 017 Pixels @ 354,082*/ 17, 0x03,
  /* ABS: 002 Pixels @ 371,082*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 373,082*/ 6, 0x01,
  /* ABS: 005 Pixels @ 379,082*/ 0, 5, 0x15, 0x03, 0x00, 0x03, 0x18,
  /* RLE: 005 Pixels @ 384,082*/ 5, 0x03,
  /* ABS: 011 Pixels @ 389,082*/ 0, 11, 0x0D, 0x08, 0x0D, 0x04, 0x0E, 0x06, 0x03, 0x03, 0x04, 0x04, 0x04,
  /* RLE: 025 Pixels @ 000,083*/ 25, 0x02,
  /* RLE: 004 Pixels @ 025,083*/ 4, 0x03,
  /* RLE: 001 Pixels @ 029,083*/ 1, 0x00,
  /* RLE: 004 Pixels @ 030,083*/ 4, 0x03,
  /* RLE: 001 Pixels @ 034,083*/ 1, 0x04,
  /* RLE: 051 Pixels @ 035,083*/ 51, 0x01,
  /* ABS: 017 Pixels @ 086,083*/ 0, 17, 0x03, 0x06, 0x08, 0x00, 0x0B, 0x06, 0x12, 0x03, 0x0D, 0x0F, 0x00, 0x03, 0x0E, 0x08, 0x00, 0x00, 0x04,
  /* RLE: 083 Pixels @ 103,083*/ 83, 0x00,
  /* RLE: 001 Pixels @ 186,083*/ 1, 0x04,
  /* RLE: 007 Pixels @ 187,083*/ 7, 0x03,
  /* RLE: 061 Pixels @ 194,083*/ 61, 0x00,
  /* ABS: 004 Pixels @ 255,083*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 259,083*/ 38, 0x01,
  /* ABS: 002 Pixels @ 297,083*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 299,083*/ 9, 0x03,
  /* ABS: 002 Pixels @ 308,083*/ 0, 2, 0x04, 0x04,
  /* RLE: 043 Pixels @ 310,083*/ 43, 0x01,
  /* ABS: 002 Pixels @ 353,083*/ 0, 2, 0x04, 0x04,
  /* RLE: 019 Pixels @ 355,083*/ 19, 0x03,
  /* ABS: 026 Pixels @ 374,083*/ 0, 26, 0x04, 0x04, 0x01, 0x01, 0x01, 0x15, 0x00, 0x0F, 0x04, 0x03, 0x03, 0x06, 0x08, 0x11, 0x00, 0x03, 0x03, 0x0B, 0x0B, 0x0F, 0x0C, 0x03, 0x04, 0x04, 0x01, 0x01,
  /* RLE: 025 Pixels @ 000,084*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,084*/ 0, 10, 0x00, 0x03, 0x0C, 0x0F, 0x08, 0x0F, 0x04, 0x03, 0x03, 0x04,
  /* RLE: 051 Pixels @ 035,084*/ 51, 0x01,
  /* ABS: 015 Pixels @ 086,084*/ 0, 15, 0x03, 0x00, 0x0F, 0x11, 0x0C, 0x08, 0x0E, 0x03, 0x00, 0x0F, 0x06, 0x03, 0x03, 0x0B, 0x03,
  /* RLE: 085 Pixels @ 101,084*/ 85, 0x00,
  /* ABS: 002 Pixels @ 186,084*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 188,084*/ 6, 0x03,
  /* RLE: 001 Pixels @ 194,084*/ 1, 0x04,
  /* RLE: 061 Pixels @ 195,084*/ 61, 0x00,
  /* ABS: 005 Pixels @ 256,084*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 261,084*/ 37, 0x01,
  /* ABS: 002 Pixels @ 298,084*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 300,084*/ 9, 0x03,
  /* ABS: 002 Pixels @ 309,084*/ 0, 2, 0x04, 0x04,
  /* RLE: 044 Pixels @ 311,084*/ 44, 0x01,
  /* RLE: 003 Pixels @ 355,084*/ 3, 0x04,
  /* RLE: 023 Pixels @ 358,084*/ 23, 0x03,
  /* ABS: 019 Pixels @ 381,084*/ 0, 19, 0x0F, 0x12, 0x03, 0x06, 0x06, 0x00, 0x0D, 0x0F, 0x03, 0x0C, 0x08, 0x0F, 0x0C, 0x03, 0x00, 0x04, 0x01, 0x01, 0x01,
  /* RLE: 025 Pixels @ 000,085*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,085*/ 0, 10, 0x03, 0x0B, 0x08, 0x06, 0x0C, 0x12, 0x08, 0x00, 0x03, 0x04,
  /* RLE: 051 Pixels @ 035,085*/ 51, 0x01,
  /* ABS: 013 Pixels @ 086,085*/ 0, 13, 0x0B, 0x03, 0x0B, 0x08, 0x12, 0x12, 0x08, 0x00, 0x03, 0x04, 0x08, 0x04, 0x03,
  /* RLE: 053 Pixels @ 099,085*/ 53, 0x00,
  /* RLE: 001 Pixels @ 152,085*/ 1, 0x08,
  /* RLE: 034 Pixels @ 153,085*/ 34, 0x00,
  /* RLE: 001 Pixels @ 187,085*/ 1, 0x04,
  /* RLE: 006 Pixels @ 188,085*/ 6, 0x03,
  /* RLE: 001 Pixels @ 194,085*/ 1, 0x04,
  /* RLE: 062 Pixels @ 195,085*/ 62, 0x00,
  /* RLE: 001 Pixels @ 257,085*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 258,085*/ 4, 0x06,
  /* RLE: 037 Pixels @ 262,085*/ 37, 0x01,
  /* ABS: 002 Pixels @ 299,085*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 301,085*/ 9, 0x03,
  /* ABS: 002 Pixels @ 310,085*/ 0, 2, 0x04, 0x04,
  /* RLE: 046 Pixels @ 312,085*/ 46, 0x01,
  /* RLE: 003 Pixels @ 358,085*/ 3, 0x04,
  /* RLE: 016 Pixels @ 361,085*/ 16, 0x03,
  /* ABS: 013 Pixels @ 377,085*/ 0, 13, 0x00, 0x0D, 0x00, 0x03, 0x0C, 0x08, 0x00, 0x08, 0x04, 0x0C, 0x0F, 0x04, 0x00,
  /* RLE: 004 Pixels @ 390,085*/ 4, 0x03,
  /* RLE: 001 Pixels @ 394,085*/ 1, 0x00,
  /* RLE: 005 Pixels @ 395,085*/ 5, 0x01,
  /* RLE: 025 Pixels @ 000,086*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,086*/ 0, 10, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x0D, 0x0C, 0x03, 0x04,
  /* RLE: 050 Pixels @ 035,086*/ 50, 0x01,
  /* ABS: 013 Pixels @ 085,086*/ 0, 13, 0x04, 0x04, 0x03, 0x03, 0x0E, 0x08, 0x04, 0x0F, 0x11, 0x03, 0x03, 0x04, 0x03,
  /* RLE: 008 Pixels @ 098,086*/ 8, 0x00,
  /* RLE: 001 Pixels @ 106,086*/ 1, 0x0B,
  /* RLE: 045 Pixels @ 107,086*/ 45, 0x00,
  /* RLE: 001 Pixels @ 152,086*/ 1, 0x08,
  /* RLE: 034 Pixels @ 153,086*/ 34, 0x00,
  /* RLE: 001 Pixels @ 187,086*/ 1, 0x04,
  /* RLE: 007 Pixels @ 188,086*/ 7, 0x03,
  /* RLE: 063 Pixels @ 195,086*/ 63, 0x00,
  /* ABS: 005 Pixels @ 258,086*/ 0, 5, 0x06, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 263,086*/ 37, 0x01,
  /* ABS: 002 Pixels @ 300,086*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 302,086*/ 9, 0x03,
  /* ABS: 002 Pixels @ 311,086*/ 0, 2, 0x04, 0x04,
  /* RLE: 048 Pixels @ 313,086*/ 48, 0x01,
  /* RLE: 003 Pixels @ 361,086*/ 3, 0x04,
  /* RLE: 013 Pixels @ 364,086*/ 13, 0x03,
  /* ABS: 017 Pixels @ 377,086*/ 0, 17, 0x11, 0x08, 0x0F, 0x0B, 0x00, 0x08, 0x0C, 0x11, 0x08, 0x12, 0x03, 0x0E, 0x06, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 006 Pixels @ 394,086*/ 6, 0x01,
  /* RLE: 025 Pixels @ 000,087*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,087*/ 0, 10, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x0F, 0x0C, 0x03, 0x04,
  /* RLE: 048 Pixels @ 035,087*/ 48, 0x01,
  /* RLE: 003 Pixels @ 083,087*/ 3, 0x04,
  /* RLE: 004 Pixels @ 086,087*/ 4, 0x03,
  /* ABS: 006 Pixels @ 090,087*/ 0, 6, 0x0D, 0x0F, 0x00, 0x08, 0x0C, 0x03,
  /* RLE: 013 Pixels @ 096,087*/ 13, 0x00,
  /* RLE: 001 Pixels @ 109,087*/ 1, 0x0B,
  /* RLE: 042 Pixels @ 110,087*/ 42, 0x00,
  /* RLE: 001 Pixels @ 152,087*/ 1, 0x08,
  /* RLE: 035 Pixels @ 153,087*/ 35, 0x00,
  /* RLE: 001 Pixels @ 188,087*/ 1, 0x04,
  /* RLE: 006 Pixels @ 189,087*/ 6, 0x03,
  /* RLE: 001 Pixels @ 195,087*/ 1, 0x04,
  /* RLE: 064 Pixels @ 196,087*/ 64, 0x00,
  /* ABS: 004 Pixels @ 260,087*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 264,087*/ 37, 0x01,
  /* ABS: 002 Pixels @ 301,087*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 303,087*/ 9, 0x03,
  /* ABS: 002 Pixels @ 312,087*/ 0, 2, 0x04, 0x04,
  /* RLE: 050 Pixels @ 314,087*/ 50, 0x01,
  /* RLE: 003 Pixels @ 364,087*/ 3, 0x04,
  /* RLE: 007 Pixels @ 367,087*/ 7, 0x03,
  /* ABS: 019 Pixels @ 374,087*/ 0, 19, 0x00, 0x03, 0x03, 0x0C, 0x08, 0x06, 0x08, 0x04, 0x12, 0x11, 0x0B, 0x08, 0x12, 0x0C, 0x0F, 0x0C, 0x03, 0x04, 0x04,
  /* RLE: 007 Pixels @ 393,087*/ 7, 0x01,
  /* RLE: 025 Pixels @ 000,088*/ 25, 0x02,
  /* ABS: 010 Pixels @ 025,088*/ 0, 10, 0x03, 0x00, 0x0F, 0x11, 0x06, 0x0F, 0x0F, 0x00, 0x03, 0x03,
  /* RLE: 047 Pixels @ 035,088*/ 47, 0x01,
  /* ABS: 002 Pixels @ 082,088*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 084,088*/ 6, 0x03,
  /* ABS: 005 Pixels @ 090,088*/ 0, 5, 0x00, 0x08, 0x12, 0x0B, 0x03,
  /* RLE: 013 Pixels @ 095,088*/ 13, 0x00,
  /* RLE: 001 Pixels @ 108,088*/ 1, 0x0B,
  /* RLE: 076 Pixels @ 109,088*/ 76, 0x00,
  /* ABS: 004 Pixels @ 185,088*/ 0, 4, 0x0B, 0x00, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 189,088*/ 6, 0x03,
  /* RLE: 001 Pixels @ 195,088*/ 1, 0x04,
  /* RLE: 065 Pixels @ 196,088*/ 65, 0x00,
  /* ABS: 004 Pixels @ 261,088*/ 0, 4, 0x0D, 0x03, 0x03, 0x0D,
  /* RLE: 038 Pixels @ 265,088*/ 38, 0x01,
  /* ABS: 002 Pixels @ 303,088*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 305,088*/ 8, 0x03,
  /* ABS: 002 Pixels @ 313,088*/ 0, 2, 0x04, 0x04,
  /* RLE: 052 Pixels @ 315,088*/ 52, 0x01,
  /* RLE: 003 Pixels @ 367,088*/ 3, 0x04,
  /* RLE: 003 Pixels @ 370,088*/ 3, 0x03,
  /* ABS: 018 Pixels @ 373,088*/ 0, 18, 0x00, 0x08, 0x0D, 0x03, 0x00, 0x08, 0x04, 0x0E, 0x08, 0x11, 0x08, 0x0B, 0x0B, 0x0D, 0x0D, 0x04, 0x03, 0x00,
  /* RLE: 009 Pixels @ 391,088*/ 9, 0x01,
  /* RLE: 025 Pixels @ 000,089*/ 25, 0x02,
  /* ABS: 011 Pixels @ 025,089*/ 0, 11, 0x0B, 0x03, 0x00, 0x12, 0x06, 0x12, 0x00, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 044 Pixels @ 036,089*/ 44, 0x01,
  /* RLE: 003 Pixels @ 080,089*/ 3, 0x04,
  /* RLE: 008 Pixels @ 083,089*/ 8, 0x03,
  /* ABS: 003 Pixels @ 091,089*/ 0, 3, 0x0B, 0x03, 0x03,
  /* RLE: 015 Pixels @ 094,089*/ 15, 0x00,
  /* RLE: 001 Pixels @ 109,089*/ 1, 0x0B,
  /* RLE: 078 Pixels @ 110,089*/ 78, 0x00,
  /* RLE: 001 Pixels @ 188,089*/ 1, 0x04,
  /* RLE: 007 Pixels @ 189,089*/ 7, 0x03,
  /* RLE: 066 Pixels @ 196,089*/ 66, 0x00,
  /* ABS: 004 Pixels @ 262,089*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 266,089*/ 38, 0x01,
  /* ABS: 002 Pixels @ 304,089*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 306,089*/ 9, 0x03,
  /* ABS: 002 Pixels @ 315,089*/ 0, 2, 0x04, 0x04,
  /* RLE: 053 Pixels @ 317,089*/ 53, 0x01,
  /* ABS: 015 Pixels @ 370,089*/ 0, 15, 0x04, 0x04, 0x00, 0x03, 0x04, 0x08, 0x0D, 0x03, 0x06, 0x12, 0x03, 0x04, 0x08, 0x08, 0x0E,
  /* RLE: 004 Pixels @ 385,089*/ 4, 0x03,
  /* RLE: 001 Pixels @ 389,089*/ 1, 0x00,
  /* RLE: 010 Pixels @ 390,089*/ 10, 0x01,
  /* RLE: 026 Pixels @ 000,090*/ 26, 0x02,
  /* ABS: 010 Pixels @ 026,090*/ 0, 10, 0x18, 0x03, 0x03, 0x0C, 0x0F, 0x08, 0x08, 0x0C, 0x03, 0x00,
  /* RLE: 043 Pixels @ 036,090*/ 43, 0x01,
  /* ABS: 002 Pixels @ 079,090*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 081,090*/ 10, 0x03,
  /* ABS: 003 Pixels @ 091,090*/ 0, 3, 0x00, 0x00, 0x04,
  /* RLE: 095 Pixels @ 094,090*/ 95, 0x00,
  /* RLE: 001 Pixels @ 189,090*/ 1, 0x04,
  /* RLE: 006 Pixels @ 190,090*/ 6, 0x03,
  /* RLE: 001 Pixels @ 196,090*/ 1, 0x04,
  /* RLE: 066 Pixels @ 197,090*/ 66, 0x00,
  /* RLE: 001 Pixels @ 263,090*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 264,090*/ 4, 0x06,
  /* RLE: 037 Pixels @ 268,090*/ 37, 0x01,
  /* ABS: 002 Pixels @ 305,090*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 307,090*/ 9, 0x03,
  /* ABS: 002 Pixels @ 316,090*/ 0, 2, 0x04, 0x04,
  /* RLE: 055 Pixels @ 318,090*/ 55, 0x01,
  /* ABS: 015 Pixels @ 373,090*/ 0, 15, 0x00, 0x03, 0x04, 0x0F, 0x0D, 0x0E, 0x08, 0x00, 0x03, 0x0B, 0x0F, 0x0C, 0x03, 0x04, 0x04,
  /* RLE: 012 Pixels @ 388,090*/ 12, 0x01,
  /* RLE: 027 Pixels @ 000,091*/ 27, 0x02,
  /* ABS: 009 Pixels @ 027,091*/ 0, 9, 0x03, 0x04, 0x08, 0x06, 0x0C, 0x0E, 0x08, 0x00, 0x03,
  /* RLE: 041 Pixels @ 036,091*/ 41, 0x01,
  /* ABS: 002 Pixels @ 077,091*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 079,091*/ 11, 0x03,
  /* ABS: 002 Pixels @ 090,091*/ 0, 2, 0x04, 0x04,
  /* RLE: 062 Pixels @ 092,091*/ 62, 0x00,
  /* RLE: 001 Pixels @ 154,091*/ 1, 0x08,
  /* RLE: 034 Pixels @ 155,091*/ 34, 0x00,
  /* RLE: 001 Pixels @ 189,091*/ 1, 0x04,
  /* RLE: 006 Pixels @ 190,091*/ 6, 0x03,
  /* RLE: 001 Pixels @ 196,091*/ 1, 0x04,
  /* RLE: 067 Pixels @ 197,091*/ 67, 0x00,
  /* RLE: 001 Pixels @ 264,091*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 265,091*/ 4, 0x06,
  /* RLE: 037 Pixels @ 269,091*/ 37, 0x01,
  /* ABS: 002 Pixels @ 306,091*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 308,091*/ 9, 0x03,
  /* ABS: 002 Pixels @ 317,091*/ 0, 2, 0x04, 0x04,
  /* RLE: 055 Pixels @ 319,091*/ 55, 0x01,
  /* ABS: 007 Pixels @ 374,091*/ 0, 7, 0x18, 0x03, 0x00, 0x0F, 0x08, 0x08, 0x0C,
  /* RLE: 004 Pixels @ 381,091*/ 4, 0x03,
  /* ABS: 002 Pixels @ 385,091*/ 0, 2, 0x00, 0x04,
  /* RLE: 013 Pixels @ 387,091*/ 13, 0x01,
  /* RLE: 027 Pixels @ 000,092*/ 27, 0x02,
  /* ABS: 009 Pixels @ 027,092*/ 0, 9, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x06, 0x0C, 0x00,
  /* RLE: 039 Pixels @ 036,092*/ 39, 0x01,
  /* RLE: 003 Pixels @ 075,092*/ 3, 0x04,
  /* RLE: 011 Pixels @ 078,092*/ 11, 0x03,
  /* ABS: 002 Pixels @ 089,092*/ 0, 2, 0x04, 0x04,
  /* RLE: 017 Pixels @ 091,092*/ 17, 0x00,
  /* RLE: 001 Pixels @ 108,092*/ 1, 0x0B,
  /* RLE: 046 Pixels @ 109,092*/ 46, 0x00,
  /* RLE: 001 Pixels @ 155,092*/ 1, 0x08,
  /* RLE: 032 Pixels @ 156,092*/ 32, 0x00,
  /* ABS: 002 Pixels @ 188,092*/ 0, 2, 0x0B, 0x04,
  /* RLE: 007 Pixels @ 190,092*/ 7, 0x03,
  /* RLE: 068 Pixels @ 197,092*/ 68, 0x00,
  /* ABS: 005 Pixels @ 265,092*/ 0, 5, 0x06, 0x0D, 0x06, 0x06, 0x0D,
  /* RLE: 037 Pixels @ 270,092*/ 37, 0x01,
  /* ABS: 002 Pixels @ 307,092*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 309,092*/ 9, 0x03,
  /* RLE: 001 Pixels @ 318,092*/ 1, 0x04,
  /* RLE: 056 Pixels @ 319,092*/ 56, 0x01,
  /* ABS: 010 Pixels @ 375,092*/ 0, 10, 0x15, 0x03, 0x00, 0x11, 0x08, 0x11, 0x03, 0x04, 0x04, 0x04,
  /* RLE: 015 Pixels @ 385,092*/ 15, 0x01,
  /* RLE: 027 Pixels @ 000,093*/ 27, 0x02,
  /* ABS: 009 Pixels @ 027,093*/ 0, 9, 0x03, 0x0C, 0x11, 0x03, 0x03, 0x00, 0x0F, 0x04, 0x03,
  /* RLE: 038 Pixels @ 036,093*/ 38, 0x01,
  /* ABS: 002 Pixels @ 074,093*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 076,093*/ 11, 0x03,
  /* ABS: 002 Pixels @ 087,093*/ 0, 2, 0x04, 0x04,
  /* RLE: 066 Pixels @ 089,093*/ 66, 0x00,
  /* RLE: 001 Pixels @ 155,093*/ 1, 0x08,
  /* RLE: 034 Pixels @ 156,093*/ 34, 0x00,
  /* RLE: 001 Pixels @ 190,093*/ 1, 0x04,
  /* RLE: 006 Pixels @ 191,093*/ 6, 0x03,
  /* RLE: 001 Pixels @ 197,093*/ 1, 0x04,
  /* RLE: 069 Pixels @ 198,093*/ 69, 0x00,
  /* ABS: 004 Pixels @ 267,093*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 271,093*/ 37, 0x01,
  /* ABS: 002 Pixels @ 308,093*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 310,093*/ 9, 0x03,
  /* RLE: 057 Pixels @ 319,093*/ 57, 0x01,
  /* ABS: 006 Pixels @ 376,093*/ 0, 6, 0x15, 0x03, 0x03, 0x06, 0x0B, 0x03,
  /* RLE: 018 Pixels @ 382,093*/ 18, 0x01,
  /* RLE: 027 Pixels @ 000,094*/ 27, 0x02,
  /* ABS: 011 Pixels @ 027,094*/ 0, 11, 0x03, 0x00, 0x0F, 0x08, 0x03, 0x00, 0x06, 0x03, 0x03, 0x03, 0x00,
  /* RLE: 034 Pixels @ 038,094*/ 34, 0x01,
  /* RLE: 003 Pixels @ 072,094*/ 3, 0x04,
  /* RLE: 011 Pixels @ 075,094*/ 11, 0x03,
  /* ABS: 002 Pixels @ 086,094*/ 0, 2, 0x04, 0x04,
  /* RLE: 102 Pixels @ 088,094*/ 102, 0x00,
  /* RLE: 001 Pixels @ 190,094*/ 1, 0x04,
  /* RLE: 006 Pixels @ 191,094*/ 6, 0x03,
  /* RLE: 001 Pixels @ 197,094*/ 1, 0x04,
  /* RLE: 070 Pixels @ 198,094*/ 70, 0x00,
  /* ABS: 004 Pixels @ 268,094*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 037 Pixels @ 272,094*/ 37, 0x01,
  /* ABS: 002 Pixels @ 309,094*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 311,094*/ 9, 0x03,
  /* ABS: 002 Pixels @ 320,094*/ 0, 2, 0x04, 0x04,
  /* RLE: 056 Pixels @ 322,094*/ 56, 0x01,
  /* ABS: 004 Pixels @ 378,094*/ 0, 4, 0x00, 0x03, 0x03, 0x15,
  /* RLE: 018 Pixels @ 382,094*/ 18, 0x01,
  /* RLE: 027 Pixels @ 000,095*/ 27, 0x02,
  /* ABS: 012 Pixels @ 027,095*/ 0, 12, 0x0B, 0x03, 0x00, 0x0C, 0x03, 0x03, 0x03, 0x00, 0x0C, 0x0E, 0x03, 0x15,
  /* RLE: 032 Pixels @ 039,095*/ 32, 0x01,
  /* ABS: 002 Pixels @ 071,095*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 073,095*/ 11, 0x03,
  /* ABS: 002 Pixels @ 084,095*/ 0, 2, 0x04, 0x04,
  /* RLE: 023 Pixels @ 086,095*/ 23, 0x00,
  /* RLE: 001 Pixels @ 109,095*/ 1, 0x0B,
  /* RLE: 080 Pixels @ 110,095*/ 80, 0x00,
  /* RLE: 001 Pixels @ 190,095*/ 1, 0x04,
  /* RLE: 007 Pixels @ 191,095*/ 7, 0x03,
  /* RLE: 071 Pixels @ 198,095*/ 71, 0x00,
  /* ABS: 004 Pixels @ 269,095*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 273,095*/ 37, 0x01,
  /* ABS: 002 Pixels @ 310,095*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 312,095*/ 9, 0x03,
  /* ABS: 002 Pixels @ 321,095*/ 0, 2, 0x04, 0x04,
  /* RLE: 077 Pixels @ 323,095*/ 77, 0x01,
  /* RLE: 028 Pixels @ 000,096*/ 28, 0x02,
  /* ABS: 005 Pixels @ 028,096*/ 0, 5, 0x03, 0x0B, 0x0C, 0x12, 0x11,
  /* RLE: 004 Pixels @ 033,096*/ 4, 0x08,
  /* ABS: 002 Pixels @ 037,096*/ 0, 2, 0x00, 0x15,
  /* RLE: 030 Pixels @ 039,096*/ 30, 0x01,
  /* ABS: 002 Pixels @ 069,096*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 071,096*/ 11, 0x03,
  /* RLE: 003 Pixels @ 082,096*/ 3, 0x04,
  /* RLE: 105 Pixels @ 085,096*/ 105, 0x00,
  /* ABS: 002 Pixels @ 190,096*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 192,096*/ 6, 0x03,
  /* RLE: 001 Pixels @ 198,096*/ 1, 0x04,
  /* RLE: 071 Pixels @ 199,096*/ 71, 0x00,
  /* ABS: 004 Pixels @ 270,096*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 274,096*/ 37, 0x01,
  /* ABS: 002 Pixels @ 311,096*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 313,096*/ 9, 0x03,
  /* ABS: 002 Pixels @ 322,096*/ 0, 2, 0x04, 0x04,
  /* RLE: 076 Pixels @ 324,096*/ 76, 0x01,
  /* RLE: 028 Pixels @ 000,097*/ 28, 0x02,
  /* ABS: 011 Pixels @ 028,097*/ 0, 11, 0x03, 0x06, 0x08, 0x08, 0x08, 0x12, 0x0B, 0x00, 0x03, 0x03, 0x15,
  /* RLE: 028 Pixels @ 039,097*/ 28, 0x01,
  /* RLE: 003 Pixels @ 067,097*/ 3, 0x04,
  /* RLE: 011 Pixels @ 070,097*/ 11, 0x03,
  /* ABS: 002 Pixels @ 081,097*/ 0, 2, 0x04, 0x04,
  /* RLE: 074 Pixels @ 083,097*/ 74, 0x00,
  /* RLE: 001 Pixels @ 157,097*/ 1, 0x08,
  /* RLE: 033 Pixels @ 158,097*/ 33, 0x00,
  /* RLE: 001 Pixels @ 191,097*/ 1, 0x04,
  /* RLE: 006 Pixels @ 192,097*/ 6, 0x03,
  /* RLE: 001 Pixels @ 198,097*/ 1, 0x04,
  /* RLE: 072 Pixels @ 199,097*/ 72, 0x00,
  /* RLE: 001 Pixels @ 271,097*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 272,097*/ 4, 0x06,
  /* RLE: 036 Pixels @ 276,097*/ 36, 0x01,
  /* ABS: 002 Pixels @ 312,097*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 314,097*/ 9, 0x03,
  /* ABS: 002 Pixels @ 323,097*/ 0, 2, 0x04, 0x04,
  /* RLE: 075 Pixels @ 325,097*/ 75, 0x01,
  /* RLE: 028 Pixels @ 000,098*/ 28, 0x02,
  /* ABS: 009 Pixels @ 028,098*/ 0, 9, 0x03, 0x00, 0x03, 0x00, 0x08, 0x08, 0x0E, 0x03, 0x00,
  /* RLE: 030 Pixels @ 037,098*/ 30, 0x01,
  /* RLE: 001 Pixels @ 067,098*/ 1, 0x04,
  /* RLE: 011 Pixels @ 068,098*/ 11, 0x03,
  /* ABS: 002 Pixels @ 079,098*/ 0, 2, 0x04, 0x04,
  /* RLE: 027 Pixels @ 081,098*/ 27, 0x00,
  /* RLE: 001 Pixels @ 108,098*/ 1, 0x0B,
  /* RLE: 048 Pixels @ 109,098*/ 48, 0x00,
  /* RLE: 001 Pixels @ 157,098*/ 1, 0x08,
  /* RLE: 032 Pixels @ 158,098*/ 32, 0x00,
  /* ABS: 002 Pixels @ 190,098*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 192,098*/ 6, 0x03,
  /* RLE: 001 Pixels @ 198,098*/ 1, 0x04,
  /* RLE: 073 Pixels @ 199,098*/ 73, 0x00,
  /* ABS: 005 Pixels @ 272,098*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 036 Pixels @ 277,098*/ 36, 0x01,
  /* ABS: 002 Pixels @ 313,098*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 315,098*/ 9, 0x03,
  /* ABS: 002 Pixels @ 324,098*/ 0, 2, 0x04, 0x04,
  /* RLE: 074 Pixels @ 326,098*/ 74, 0x01,
  /* RLE: 028 Pixels @ 000,099*/ 28, 0x02,
  /* ABS: 009 Pixels @ 028,099*/ 0, 9, 0x00, 0x03, 0x00, 0x0F, 0x11, 0x0C, 0x0F, 0x0C, 0x03,
  /* RLE: 029 Pixels @ 037,099*/ 29, 0x01,
  /* ABS: 002 Pixels @ 066,099*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 068,099*/ 10, 0x03,
  /* ABS: 002 Pixels @ 078,099*/ 0, 2, 0x04, 0x04,
  /* RLE: 078 Pixels @ 080,099*/ 78, 0x00,
  /* RLE: 001 Pixels @ 158,099*/ 1, 0x08,
  /* RLE: 033 Pixels @ 159,099*/ 33, 0x00,
  /* RLE: 007 Pixels @ 192,099*/ 7, 0x03,
  /* RLE: 001 Pixels @ 199,099*/ 1, 0x04,
  /* RLE: 073 Pixels @ 200,099*/ 73, 0x00,
  /* ABS: 005 Pixels @ 273,099*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 278,099*/ 37, 0x01,
  /* ABS: 002 Pixels @ 315,099*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 317,099*/ 9, 0x03,
  /* ABS: 002 Pixels @ 326,099*/ 0, 2, 0x04, 0x04,
  /* RLE: 072 Pixels @ 328,099*/ 72, 0x01,
  /* RLE: 028 Pixels @ 000,100*/ 28, 0x02,
  /* ABS: 010 Pixels @ 028,100*/ 0, 10, 0x18, 0x00, 0x08, 0x0D, 0x03, 0x03, 0x03, 0x04, 0x03, 0x04,
  /* RLE: 027 Pixels @ 038,100*/ 27, 0x01,
  /* ABS: 002 Pixels @ 065,100*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 067,100*/ 9, 0x03,
  /* ABS: 002 Pixels @ 076,100*/ 0, 2, 0x04, 0x04,
  /* RLE: 114 Pixels @ 078,100*/ 114, 0x00,
  /* RLE: 001 Pixels @ 192,100*/ 1, 0x04,
  /* RLE: 006 Pixels @ 193,100*/ 6, 0x03,
  /* RLE: 001 Pixels @ 199,100*/ 1, 0x04,
  /* RLE: 075 Pixels @ 200,100*/ 75, 0x00,
  /* ABS: 004 Pixels @ 275,100*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 279,100*/ 37, 0x01,
  /* ABS: 002 Pixels @ 316,100*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 318,100*/ 9, 0x03,
  /* ABS: 002 Pixels @ 327,100*/ 0, 2, 0x04, 0x04,
  /* RLE: 071 Pixels @ 329,100*/ 71, 0x01,
  /* RLE: 028 Pixels @ 000,101*/ 28, 0x02,
  /* ABS: 003 Pixels @ 028,101*/ 0, 3, 0x18, 0x03, 0x0E,
  /* RLE: 006 Pixels @ 031,101*/ 6, 0x03,
  /* RLE: 001 Pixels @ 037,101*/ 1, 0x04,
  /* RLE: 026 Pixels @ 038,101*/ 26, 0x01,
  /* ABS: 002 Pixels @ 064,101*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 066,101*/ 8, 0x03,
  /* RLE: 003 Pixels @ 074,101*/ 3, 0x04,
  /* RLE: 033 Pixels @ 077,101*/ 33, 0x00,
  /* RLE: 001 Pixels @ 110,101*/ 1, 0x0B,
  /* RLE: 081 Pixels @ 111,101*/ 81, 0x00,
  /* RLE: 001 Pixels @ 192,101*/ 1, 0x04,
  /* RLE: 006 Pixels @ 193,101*/ 6, 0x03,
  /* RLE: 001 Pixels @ 199,101*/ 1, 0x04,
  /* RLE: 076 Pixels @ 200,101*/ 76, 0x00,
  /* ABS: 004 Pixels @ 276,101*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 280,101*/ 37, 0x01,
  /* ABS: 002 Pixels @ 317,101*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 319,101*/ 9, 0x03,
  /* ABS: 002 Pixels @ 328,101*/ 0, 2, 0x04, 0x04,
  /* RLE: 070 Pixels @ 330,101*/ 70, 0x01,
  /* RLE: 029 Pixels @ 000,102*/ 29, 0x02,
  /* ABS: 002 Pixels @ 029,102*/ 0, 2, 0x03, 0x00,
  /* RLE: 006 Pixels @ 031,102*/ 6, 0x03,
  /* RLE: 001 Pixels @ 037,102*/ 1, 0x04,
  /* RLE: 025 Pixels @ 038,102*/ 25, 0x01,
  /* ABS: 002 Pixels @ 063,102*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 065,102*/ 8, 0x03,
  /* ABS: 002 Pixels @ 073,102*/ 0, 2, 0x04, 0x04,
  /* RLE: 117 Pixels @ 075,102*/ 117, 0x00,
  /* RLE: 001 Pixels @ 192,102*/ 1, 0x0B,
  /* RLE: 007 Pixels @ 193,102*/ 7, 0x03,
  /* RLE: 001 Pixels @ 200,102*/ 1, 0x04,
  /* RLE: 076 Pixels @ 201,102*/ 76, 0x00,
  /* ABS: 004 Pixels @ 277,102*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 281,102*/ 37, 0x01,
  /* ABS: 002 Pixels @ 318,102*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 320,102*/ 9, 0x03,
  /* ABS: 002 Pixels @ 329,102*/ 0, 2, 0x04, 0x04,
  /* RLE: 069 Pixels @ 331,102*/ 69, 0x01,
  /* RLE: 030 Pixels @ 000,103*/ 30, 0x02,
  /* RLE: 001 Pixels @ 030,103*/ 1, 0x04,
  /* RLE: 006 Pixels @ 031,103*/ 6, 0x03,
  /* RLE: 001 Pixels @ 037,103*/ 1, 0x04,
  /* RLE: 024 Pixels @ 038,103*/ 24, 0x01,
  /* ABS: 002 Pixels @ 062,103*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 064,103*/ 8, 0x03,
  /* RLE: 001 Pixels @ 072,103*/ 1, 0x04,
  /* RLE: 028 Pixels @ 073,103*/ 28, 0x00,
  /* ABS: 006 Pixels @ 101,103*/ 0, 6, 0x03, 0x03, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 052 Pixels @ 107,103*/ 52, 0x00,
  /* RLE: 001 Pixels @ 159,103*/ 1, 0x08,
  /* RLE: 033 Pixels @ 160,103*/ 33, 0x00,
  /* RLE: 001 Pixels @ 193,103*/ 1, 0x04,
  /* RLE: 006 Pixels @ 194,103*/ 6, 0x03,
  /* RLE: 001 Pixels @ 200,103*/ 1, 0x04,
  /* RLE: 077 Pixels @ 201,103*/ 77, 0x00,
  /* ABS: 005 Pixels @ 278,103*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 283,103*/ 36, 0x01,
  /* ABS: 002 Pixels @ 319,103*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 321,103*/ 9, 0x03,
  /* ABS: 002 Pixels @ 330,103*/ 0, 2, 0x04, 0x04,
  /* RLE: 068 Pixels @ 332,103*/ 68, 0x01,
  /* RLE: 030 Pixels @ 000,104*/ 30, 0x02,
  /* RLE: 001 Pixels @ 030,104*/ 1, 0x04,
  /* RLE: 006 Pixels @ 031,104*/ 6, 0x03,
  /* RLE: 001 Pixels @ 037,104*/ 1, 0x04,
  /* RLE: 023 Pixels @ 038,104*/ 23, 0x01,
  /* ABS: 002 Pixels @ 061,104*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 063,104*/ 8, 0x03,
  /* ABS: 002 Pixels @ 071,104*/ 0, 2, 0x04, 0x04,
  /* RLE: 027 Pixels @ 073,104*/ 27, 0x00,
  /* ABS: 007 Pixels @ 100,104*/ 0, 7, 0x03, 0x04, 0x0B, 0x03, 0x03, 0x12, 0x0B,
  /* RLE: 053 Pixels @ 107,104*/ 53, 0x00,
  /* RLE: 001 Pixels @ 160,104*/ 1, 0x08,
  /* RLE: 032 Pixels @ 161,104*/ 32, 0x00,
  /* RLE: 001 Pixels @ 193,104*/ 1, 0x04,
  /* RLE: 006 Pixels @ 194,104*/ 6, 0x03,
  /* RLE: 001 Pixels @ 200,104*/ 1, 0x04,
  /* RLE: 078 Pixels @ 201,104*/ 78, 0x00,
  /* ABS: 005 Pixels @ 279,104*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 036 Pixels @ 284,104*/ 36, 0x01,
  /* ABS: 002 Pixels @ 320,104*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 322,104*/ 9, 0x03,
  /* ABS: 002 Pixels @ 331,104*/ 0, 2, 0x04, 0x04,
  /* RLE: 067 Pixels @ 333,104*/ 67, 0x01,
  /* RLE: 031 Pixels @ 000,105*/ 31, 0x02,
  /* RLE: 007 Pixels @ 031,105*/ 7, 0x03,
  /* RLE: 001 Pixels @ 038,105*/ 1, 0x04,
  /* RLE: 021 Pixels @ 039,105*/ 21, 0x01,
  /* ABS: 002 Pixels @ 060,105*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 062,105*/ 8, 0x03,
  /* ABS: 002 Pixels @ 070,105*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 072,105*/ 28, 0x00,
  /* ABS: 008 Pixels @ 100,105*/ 0, 8, 0x03, 0x11, 0x0F, 0x03, 0x0B, 0x08, 0x03, 0x03,
  /* RLE: 052 Pixels @ 108,105*/ 52, 0x00,
  /* RLE: 001 Pixels @ 160,105*/ 1, 0x08,
  /* RLE: 033 Pixels @ 161,105*/ 33, 0x00,
  /* RLE: 007 Pixels @ 194,105*/ 7, 0x03,
  /* RLE: 001 Pixels @ 201,105*/ 1, 0x04,
  /* RLE: 078 Pixels @ 202,105*/ 78, 0x00,
  /* ABS: 005 Pixels @ 280,105*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 285,105*/ 36, 0x01,
  /* RLE: 003 Pixels @ 321,105*/ 3, 0x04,
  /* RLE: 008 Pixels @ 324,105*/ 8, 0x03,
  /* RLE: 003 Pixels @ 332,105*/ 3, 0x04,
  /* RLE: 065 Pixels @ 335,105*/ 65, 0x01,
  /* RLE: 031 Pixels @ 000,106*/ 31, 0x02,
  /* RLE: 001 Pixels @ 031,106*/ 1, 0x04,
  /* RLE: 006 Pixels @ 032,106*/ 6, 0x03,
  /* RLE: 001 Pixels @ 038,106*/ 1, 0x04,
  /* RLE: 020 Pixels @ 039,106*/ 20, 0x01,
  /* ABS: 002 Pixels @ 059,106*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 061,106*/ 8, 0x03,
  /* ABS: 002 Pixels @ 069,106*/ 0, 2, 0x04, 0x04,
  /* RLE: 029 Pixels @ 071,106*/ 29, 0x00,
  /* ABS: 010 Pixels @ 100,106*/ 0, 10, 0x03, 0x00, 0x08, 0x06, 0x00, 0x08, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 083 Pixels @ 110,106*/ 83, 0x00,
  /* ABS: 002 Pixels @ 193,106*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 195,106*/ 6, 0x03,
  /* RLE: 001 Pixels @ 201,106*/ 1, 0x04,
  /* RLE: 080 Pixels @ 202,106*/ 80, 0x00,
  /* ABS: 004 Pixels @ 282,106*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 286,106*/ 37, 0x01,
  /* ABS: 002 Pixels @ 323,106*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 325,106*/ 9, 0x03,
  /* ABS: 002 Pixels @ 334,106*/ 0, 2, 0x04, 0x04,
  /* RLE: 064 Pixels @ 336,106*/ 64, 0x01,
  /* RLE: 031 Pixels @ 000,107*/ 31, 0x02,
  /* RLE: 001 Pixels @ 031,107*/ 1, 0x04,
  /* RLE: 006 Pixels @ 032,107*/ 6, 0x03,
  /* RLE: 001 Pixels @ 038,107*/ 1, 0x04,
  /* RLE: 019 Pixels @ 039,107*/ 19, 0x01,
  /* ABS: 002 Pixels @ 058,107*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 060,107*/ 8, 0x03,
  /* ABS: 002 Pixels @ 068,107*/ 0, 2, 0x04, 0x04,
  /* RLE: 030 Pixels @ 070,107*/ 30, 0x00,
  /* ABS: 011 Pixels @ 100,107*/ 0, 11, 0x03, 0x03, 0x0C, 0x08, 0x0F, 0x08, 0x08, 0x06, 0x0B, 0x03, 0x03,
  /* RLE: 083 Pixels @ 111,107*/ 83, 0x00,
  /* RLE: 001 Pixels @ 194,107*/ 1, 0x04,
  /* RLE: 006 Pixels @ 195,107*/ 6, 0x03,
  /* RLE: 001 Pixels @ 201,107*/ 1, 0x04,
  /* RLE: 081 Pixels @ 202,107*/ 81, 0x00,
  /* ABS: 004 Pixels @ 283,107*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 037 Pixels @ 287,107*/ 37, 0x01,
  /* ABS: 002 Pixels @ 324,107*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 326,107*/ 9, 0x03,
  /* ABS: 002 Pixels @ 335,107*/ 0, 2, 0x04, 0x04,
  /* RLE: 063 Pixels @ 337,107*/ 63, 0x01,
  /* RLE: 031 Pixels @ 000,108*/ 31, 0x02,
  /* RLE: 001 Pixels @ 031,108*/ 1, 0x04,
  /* RLE: 006 Pixels @ 032,108*/ 6, 0x03,
  /* RLE: 001 Pixels @ 038,108*/ 1, 0x04,
  /* RLE: 018 Pixels @ 039,108*/ 18, 0x01,
  /* ABS: 002 Pixels @ 057,108*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 059,108*/ 8, 0x03,
  /* ABS: 002 Pixels @ 067,108*/ 0, 2, 0x04, 0x04,
  /* RLE: 029 Pixels @ 069,108*/ 29, 0x00,
  /* ABS: 013 Pixels @ 098,108*/ 0, 13, 0x03, 0x03, 0x04, 0x00, 0x03, 0x12, 0x08, 0x06, 0x04, 0x11, 0x08, 0x0E, 0x03,
  /* RLE: 084 Pixels @ 111,108*/ 84, 0x00,
  /* RLE: 007 Pixels @ 195,108*/ 7, 0x03,
  /* RLE: 001 Pixels @ 202,108*/ 1, 0x04,
  /* RLE: 081 Pixels @ 203,108*/ 81, 0x00,
  /* ABS: 004 Pixels @ 284,108*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 288,108*/ 37, 0x01,
  /* ABS: 002 Pixels @ 325,108*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 327,108*/ 9, 0x03,
  /* ABS: 002 Pixels @ 336,108*/ 0, 2, 0x04, 0x04,
  /* RLE: 062 Pixels @ 338,108*/ 62, 0x01,
  /* RLE: 031 Pixels @ 000,109*/ 31, 0x02,
  /* RLE: 001 Pixels @ 031,109*/ 1, 0x04,
  /* RLE: 006 Pixels @ 032,109*/ 6, 0x03,
  /* RLE: 001 Pixels @ 038,109*/ 1, 0x04,
  /* RLE: 017 Pixels @ 039,109*/ 17, 0x01,
  /* ABS: 002 Pixels @ 056,109*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 058,109*/ 8, 0x03,
  /* ABS: 002 Pixels @ 066,109*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 068,109*/ 28, 0x00,
  /* ABS: 015 Pixels @ 096,109*/ 0, 15, 0x04, 0x03, 0x00, 0x0F, 0x08, 0x0D, 0x03, 0x03, 0x11, 0x0F, 0x03, 0x03, 0x00, 0x03, 0x03,
  /* RLE: 051 Pixels @ 111,109*/ 51, 0x00,
  /* ABS: 004 Pixels @ 162,109*/ 0, 4, 0x08, 0x00, 0x00, 0x0B,
  /* RLE: 029 Pixels @ 166,109*/ 29, 0x00,
  /* RLE: 001 Pixels @ 195,109*/ 1, 0x04,
  /* RLE: 006 Pixels @ 196,109*/ 6, 0x03,
  /* RLE: 001 Pixels @ 202,109*/ 1, 0x04,
  /* RLE: 082 Pixels @ 203,109*/ 82, 0x00,
  /* ABS: 005 Pixels @ 285,109*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 290,109*/ 36, 0x01,
  /* ABS: 002 Pixels @ 326,109*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 328,109*/ 9, 0x03,
  /* ABS: 002 Pixels @ 337,109*/ 0, 2, 0x04, 0x04,
  /* RLE: 061 Pixels @ 339,109*/ 61, 0x01,
  /* RLE: 031 Pixels @ 000,110*/ 31, 0x02,
  /* RLE: 001 Pixels @ 031,110*/ 1, 0x04,
  /* RLE: 007 Pixels @ 032,110*/ 7, 0x03,
  /* RLE: 016 Pixels @ 039,110*/ 16, 0x01,
  /* ABS: 002 Pixels @ 055,110*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 057,110*/ 8, 0x03,
  /* ABS: 002 Pixels @ 065,110*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 067,110*/ 28, 0x00,
  /* ABS: 013 Pixels @ 095,110*/ 0, 13, 0x04, 0x04, 0x03, 0x0F, 0x0E, 0x03, 0x00, 0x03, 0x03, 0x00, 0x08, 0x0E, 0x03,
  /* RLE: 054 Pixels @ 108,110*/ 54, 0x00,
  /* ABS: 003 Pixels @ 162,110*/ 0, 3, 0x08, 0x00, 0x0B,
  /* RLE: 029 Pixels @ 165,110*/ 29, 0x00,
  /* ABS: 002 Pixels @ 194,110*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 196,110*/ 6, 0x03,
  /* RLE: 001 Pixels @ 202,110*/ 1, 0x04,
  /* RLE: 083 Pixels @ 203,110*/ 83, 0x00,
  /* ABS: 005 Pixels @ 286,110*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x0D,
  /* RLE: 036 Pixels @ 291,110*/ 36, 0x01,
  /* ABS: 002 Pixels @ 327,110*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 329,110*/ 9, 0x03,
  /* ABS: 002 Pixels @ 338,110*/ 0, 2, 0x04, 0x04,
  /* RLE: 060 Pixels @ 340,110*/ 60, 0x01,
  /* RLE: 032 Pixels @ 000,111*/ 32, 0x02,
  /* RLE: 001 Pixels @ 032,111*/ 1, 0x04,
  /* RLE: 006 Pixels @ 033,111*/ 6, 0x03,
  /* RLE: 001 Pixels @ 039,111*/ 1, 0x04,
  /* RLE: 014 Pixels @ 040,111*/ 14, 0x01,
  /* ABS: 002 Pixels @ 054,111*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 056,111*/ 8, 0x03,
  /* ABS: 002 Pixels @ 064,111*/ 0, 2, 0x04, 0x04,
  /* RLE: 029 Pixels @ 066,111*/ 29, 0x00,
  /* RLE: 003 Pixels @ 095,111*/ 3, 0x03,
  /* ABS: 010 Pixels @ 098,111*/ 0, 10, 0x08, 0x04, 0x03, 0x03, 0x0C, 0x06, 0x03, 0x0B, 0x03, 0x03,
  /* RLE: 055 Pixels @ 108,111*/ 55, 0x00,
  /* ABS: 004 Pixels @ 163,111*/ 0, 4, 0x08, 0x00, 0x00, 0x0B,
  /* RLE: 028 Pixels @ 167,111*/ 28, 0x00,
  /* RLE: 001 Pixels @ 195,111*/ 1, 0x0B,
  /* RLE: 007 Pixels @ 196,111*/ 7, 0x03,
  /* RLE: 084 Pixels @ 203,111*/ 84, 0x00,
  /* RLE: 001 Pixels @ 287,111*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 288,111*/ 4, 0x06,
  /* RLE: 037 Pixels @ 292,111*/ 37, 0x01,
  /* ABS: 002 Pixels @ 329,111*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 331,111*/ 8, 0x03,
  /* ABS: 002 Pixels @ 339,111*/ 0, 2, 0x04, 0x04,
  /* RLE: 059 Pixels @ 341,111*/ 59, 0x01,
  /* RLE: 032 Pixels @ 000,112*/ 32, 0x02,
  /* RLE: 001 Pixels @ 032,112*/ 1, 0x04,
  /* RLE: 006 Pixels @ 033,112*/ 6, 0x03,
  /* RLE: 001 Pixels @ 039,112*/ 1, 0x04,
  /* RLE: 013 Pixels @ 040,112*/ 13, 0x01,
  /* ABS: 002 Pixels @ 053,112*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 055,112*/ 8, 0x03,
  /* ABS: 002 Pixels @ 063,112*/ 0, 2, 0x04, 0x04,
  /* RLE: 024 Pixels @ 065,112*/ 24, 0x00,
  /* ABS: 016 Pixels @ 089,112*/ 0, 16, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x04, 0x0C, 0x00, 0x11, 0x11, 0x03, 0x03, 0x0E, 0x0D, 0x03,
  /* RLE: 058 Pixels @ 105,112*/ 58, 0x00,
  /* ABS: 003 Pixels @ 163,112*/ 0, 3, 0x0B, 0x00, 0x0B,
  /* RLE: 027 Pixels @ 166,112*/ 27, 0x00,
  /* ABS: 004 Pixels @ 193,112*/ 0, 4, 0x0B, 0x00, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 197,112*/ 6, 0x03,
  /* RLE: 001 Pixels @ 203,112*/ 1, 0x04,
  /* RLE: 085 Pixels @ 204,112*/ 85, 0x00,
  /* RLE: 004 Pixels @ 289,112*/ 4, 0x06,
  /* RLE: 037 Pixels @ 293,112*/ 37, 0x01,
  /* ABS: 002 Pixels @ 330,112*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 332,112*/ 9, 0x03,
  /* ABS: 002 Pixels @ 341,112*/ 0, 2, 0x04, 0x04,
  /* RLE: 057 Pixels @ 343,112*/ 57, 0x01,
  /* RLE: 032 Pixels @ 000,113*/ 32, 0x02,
  /* RLE: 001 Pixels @ 032,113*/ 1, 0x04,
  /* RLE: 006 Pixels @ 033,113*/ 6, 0x03,
  /* RLE: 001 Pixels @ 039,113*/ 1, 0x04,
  /* RLE: 012 Pixels @ 040,113*/ 12, 0x01,
  /* ABS: 002 Pixels @ 052,113*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 054,113*/ 8, 0x03,
  /* ABS: 002 Pixels @ 062,113*/ 0, 2, 0x04, 0x04,
  /* RLE: 024 Pixels @ 064,113*/ 24, 0x00,
  /* ABS: 017 Pixels @ 088,113*/ 0, 17, 0x03, 0x0C, 0x00, 0x03, 0x03, 0x00, 0x0F, 0x08, 0x08, 0x0F, 0x00, 0x08, 0x11, 0x06, 0x08, 0x0B, 0x03,
  /* RLE: 061 Pixels @ 105,113*/ 61, 0x00,
  /* RLE: 001 Pixels @ 166,113*/ 1, 0x0B,
  /* RLE: 029 Pixels @ 167,113*/ 29, 0x00,
  /* RLE: 001 Pixels @ 196,113*/ 1, 0x04,
  /* RLE: 006 Pixels @ 197,113*/ 6, 0x03,
  /* RLE: 001 Pixels @ 203,113*/ 1, 0x04,
  /* RLE: 086 Pixels @ 204,113*/ 86, 0x00,
  /* ABS: 004 Pixels @ 290,113*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 294,113*/ 37, 0x01,
  /* ABS: 002 Pixels @ 331,113*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 333,113*/ 9, 0x03,
  /* ABS: 002 Pixels @ 342,113*/ 0, 2, 0x04, 0x04,
  /* RLE: 056 Pixels @ 344,113*/ 56, 0x01,
  /* RLE: 032 Pixels @ 000,114*/ 32, 0x02,
  /* RLE: 001 Pixels @ 032,114*/ 1, 0x04,
  /* RLE: 006 Pixels @ 033,114*/ 6, 0x03,
  /* RLE: 001 Pixels @ 039,114*/ 1, 0x04,
  /* RLE: 011 Pixels @ 040,114*/ 11, 0x01,
  /* ABS: 002 Pixels @ 051,114*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 053,114*/ 8, 0x03,
  /* ABS: 002 Pixels @ 061,114*/ 0, 2, 0x04, 0x04,
  /* RLE: 026 Pixels @ 063,114*/ 26, 0x00,
  /* ABS: 015 Pixels @ 089,114*/ 0, 15, 0x0F, 0x11, 0x03, 0x03, 0x11, 0x12, 0x03, 0x00, 0x0F, 0x11, 0x0B, 0x12, 0x12, 0x0B, 0x03,
  /* RLE: 058 Pixels @ 104,114*/ 58, 0x00,
  /* RLE: 001 Pixels @ 162,114*/ 1, 0x0B,
  /* RLE: 033 Pixels @ 163,114*/ 33, 0x00,
  /* RLE: 001 Pixels @ 196,114*/ 1, 0x04,
  /* RLE: 007 Pixels @ 197,114*/ 7, 0x03,
  /* RLE: 087 Pixels @ 204,114*/ 87, 0x00,
  /* ABS: 004 Pixels @ 291,114*/ 0, 4, 0x06, 0x06, 0x03, 0x0D,
  /* RLE: 037 Pixels @ 295,114*/ 37, 0x01,
  /* ABS: 002 Pixels @ 332,114*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 334,114*/ 9, 0x03,
  /* ABS: 002 Pixels @ 343,114*/ 0, 2, 0x04, 0x04,
  /* RLE: 055 Pixels @ 345,114*/ 55, 0x01,
  /* RLE: 032 Pixels @ 000,115*/ 32, 0x02,
  /* RLE: 001 Pixels @ 032,115*/ 1, 0x04,
  /* RLE: 006 Pixels @ 033,115*/ 6, 0x03,
  /* RLE: 001 Pixels @ 039,115*/ 1, 0x04,
  /* RLE: 010 Pixels @ 040,115*/ 10, 0x01,
  /* ABS: 002 Pixels @ 050,115*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 052,115*/ 8, 0x03,
  /* ABS: 002 Pixels @ 060,115*/ 0, 2, 0x04, 0x04,
  /* RLE: 022 Pixels @ 062,115*/ 22, 0x00,
  /* RLE: 005 Pixels @ 084,115*/ 5, 0x03,
  /* ABS: 010 Pixels @ 089,115*/ 0, 10, 0x0B, 0x08, 0x12, 0x03, 0x08, 0x0C, 0x03, 0x03, 0x0C, 0x08,
  /* RLE: 004 Pixels @ 099,115*/ 4, 0x03,
  /* RLE: 061 Pixels @ 103,115*/ 61, 0x00,
  /* ABS: 004 Pixels @ 164,115*/ 0, 4, 0x08, 0x00, 0x00, 0x0B,
  /* RLE: 029 Pixels @ 168,115*/ 29, 0x00,
  /* RLE: 001 Pixels @ 197,115*/ 1, 0x04,
  /* RLE: 006 Pixels @ 198,115*/ 6, 0x03,
  /* RLE: 001 Pixels @ 204,115*/ 1, 0x04,
  /* RLE: 087 Pixels @ 205,115*/ 87, 0x00,
  /* ABS: 004 Pixels @ 292,115*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 296,115*/ 37, 0x01,
  /* ABS: 002 Pixels @ 333,115*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 335,115*/ 9, 0x03,
  /* RLE: 001 Pixels @ 344,115*/ 1, 0x04,
  /* RLE: 055 Pixels @ 345,115*/ 55, 0x01,
  /* RLE: 032 Pixels @ 000,116*/ 32, 0x02,
  /* RLE: 001 Pixels @ 032,116*/ 1, 0x04,
  /* RLE: 007 Pixels @ 033,116*/ 7, 0x03,
  /* RLE: 001 Pixels @ 040,116*/ 1, 0x04,
  /* RLE: 007 Pixels @ 041,116*/ 7, 0x01,
  /* ABS: 002 Pixels @ 048,116*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 050,116*/ 10, 0x03,
  /* ABS: 002 Pixels @ 060,116*/ 0, 2, 0x04, 0x04,
  /* RLE: 022 Pixels @ 062,116*/ 22, 0x00,
  /* ABS: 017 Pixels @ 084,116*/ 0, 17, 0x03, 0x04, 0x0C, 0x03, 0x03, 0x03, 0x0E, 0x08, 0x0C, 0x11, 0x11, 0x03, 0x03, 0x0E, 0x0F, 0x03, 0x04,
  /* RLE: 062 Pixels @ 101,116*/ 62, 0x00,
  /* ABS: 004 Pixels @ 163,116*/ 0, 4, 0x0B, 0x00, 0x08, 0x0B,
  /* RLE: 029 Pixels @ 167,116*/ 29, 0x00,
  /* ABS: 002 Pixels @ 196,116*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 198,116*/ 6, 0x03,
  /* RLE: 001 Pixels @ 204,116*/ 1, 0x04,
  /* RLE: 088 Pixels @ 205,116*/ 88, 0x00,
  /* RLE: 005 Pixels @ 293,116*/ 5, 0x06,
  /* RLE: 036 Pixels @ 298,116*/ 36, 0x01,
  /* ABS: 002 Pixels @ 334,116*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 336,116*/ 9, 0x03,
  /* RLE: 009 Pixels @ 345,116*/ 9, 0x0A,
  /* RLE: 046 Pixels @ 354,116*/ 46, 0x01,
  /* RLE: 033 Pixels @ 000,117*/ 33, 0x02,
  /* RLE: 001 Pixels @ 033,117*/ 1, 0x04,
  /* RLE: 006 Pixels @ 034,117*/ 6, 0x03,
  /* RLE: 001 Pixels @ 040,117*/ 1, 0x04,
  /* RLE: 005 Pixels @ 041,117*/ 5, 0x01,
  /* RLE: 003 Pixels @ 046,117*/ 3, 0x04,
  /* RLE: 013 Pixels @ 049,117*/ 13, 0x03,
  /* ABS: 002 Pixels @ 062,117*/ 0, 2, 0x04, 0x04,
  /* RLE: 020 Pixels @ 064,117*/ 20, 0x00,
  /* ABS: 017 Pixels @ 084,117*/ 0, 17, 0x03, 0x0D, 0x08, 0x00, 0x03, 0x0C, 0x08, 0x0F, 0x08, 0x04, 0x08, 0x11, 0x06, 0x08, 0x04, 0x03, 0x0B,
  /* RLE: 064 Pixels @ 101,117*/ 64, 0x00,
  /* ABS: 003 Pixels @ 165,117*/ 0, 3, 0x08, 0x00, 0x0B,
  /* RLE: 029 Pixels @ 168,117*/ 29, 0x00,
  /* RLE: 001 Pixels @ 197,117*/ 1, 0x0B,
  /* RLE: 007 Pixels @ 198,117*/ 7, 0x03,
  /* RLE: 089 Pixels @ 205,117*/ 89, 0x00,
  /* RLE: 001 Pixels @ 294,117*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 295,117*/ 4, 0x06,
  /* RLE: 029 Pixels @ 299,117*/ 29, 0x01,
  /* RLE: 016 Pixels @ 328,117*/ 16, 0x0A,
  /* RLE: 010 Pixels @ 344,117*/ 10, 0x05,
  /* RLE: 006 Pixels @ 354,117*/ 6, 0x0A,
  /* RLE: 040 Pixels @ 360,117*/ 40, 0x01,
  /* RLE: 033 Pixels @ 000,118*/ 33, 0x02,
  /* RLE: 001 Pixels @ 033,118*/ 1, 0x04,
  /* RLE: 007 Pixels @ 034,118*/ 7, 0x03,
  /* ABS: 006 Pixels @ 041,118*/ 0, 6, 0x04, 0x01, 0x01, 0x04, 0x04, 0x04,
  /* RLE: 018 Pixels @ 047,118*/ 18, 0x03,
  /* ABS: 002 Pixels @ 065,118*/ 0, 2, 0x04, 0x04,
  /* RLE: 017 Pixels @ 067,118*/ 17, 0x00,
  /* ABS: 015 Pixels @ 084,118*/ 0, 15, 0x03, 0x00, 0x0F, 0x11, 0x06, 0x08, 0x0C, 0x00, 0x0F, 0x11, 0x0B, 0x12, 0x0E, 0x0B, 0x03,
  /* RLE: 098 Pixels @ 099,118*/ 98, 0x00,
  /* ABS: 002 Pixels @ 197,118*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 199,118*/ 6, 0x03,
  /* RLE: 001 Pixels @ 205,118*/ 1, 0x04,
  /* RLE: 089 Pixels @ 206,118*/ 89, 0x00,
  /* ABS: 005 Pixels @ 295,118*/ 0, 5, 0x06, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 011 Pixels @ 300,118*/ 11, 0x01,
  /* ABS: 003 Pixels @ 311,118*/ 0, 3, 0x00, 0x00, 0x0B,
  /* RLE: 014 Pixels @ 314,118*/ 14, 0x0A,
  /* ABS: 003 Pixels @ 328,118*/ 0, 3, 0x05, 0x05, 0x1A,
  /* RLE: 005 Pixels @ 331,118*/ 5, 0x03,
  /* ABS: 002 Pixels @ 336,118*/ 0, 2, 0x15, 0x15,
  /* RLE: 004 Pixels @ 338,118*/ 4, 0x03,
  /* RLE: 001 Pixels @ 342,118*/ 1, 0x19,
  /* RLE: 017 Pixels @ 343,118*/ 17, 0x05,
  /* RLE: 006 Pixels @ 360,118*/ 6, 0x0A,
  /* RLE: 034 Pixels @ 366,118*/ 34, 0x01,
  /* RLE: 033 Pixels @ 000,119*/ 33, 0x02,
  /* ABS: 002 Pixels @ 033,119*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 035,119*/ 6, 0x03,
  /* ABS: 004 Pixels @ 041,119*/ 0, 4, 0x04, 0x01, 0x04, 0x04,
  /* RLE: 022 Pixels @ 045,119*/ 22, 0x03,
  /* ABS: 002 Pixels @ 067,119*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 069,119*/ 14, 0x00,
  /* ABS: 015 Pixels @ 083,119*/ 0, 15, 0x04, 0x0B, 0x03, 0x04, 0x08, 0x08, 0x00, 0x03, 0x03, 0x0B, 0x08, 0x0E, 0x03, 0x03, 0x03,
  /* RLE: 068 Pixels @ 098,119*/ 68, 0x00,
  /* ABS: 004 Pixels @ 166,119*/ 0, 4, 0x0B, 0x00, 0x00, 0x0B,
  /* RLE: 028 Pixels @ 170,119*/ 28, 0x00,
  /* RLE: 001 Pixels @ 198,119*/ 1, 0x04,
  /* RLE: 006 Pixels @ 199,119*/ 6, 0x03,
  /* RLE: 001 Pixels @ 205,119*/ 1, 0x04,
  /* RLE: 068 Pixels @ 206,119*/ 68, 0x00,
  /* ABS: 003 Pixels @ 274,119*/ 0, 3, 0x03, 0x00, 0x00,
  /* RLE: 004 Pixels @ 277,119*/ 4, 0x03,
  /* RLE: 016 Pixels @ 281,119*/ 16, 0x00,
  /* RLE: 001 Pixels @ 297,119*/ 1, 0x0D,
  /* RLE: 012 Pixels @ 298,119*/ 12, 0x0A,
  /* ABS: 005 Pixels @ 310,119*/ 0, 5, 0x04, 0x03, 0x12, 0x03, 0x15,
  /* RLE: 009 Pixels @ 315,119*/ 9, 0x03,
  /* RLE: 001 Pixels @ 324,119*/ 1, 0x00,
  /* RLE: 006 Pixels @ 325,119*/ 6, 0x03,
  /* ABS: 013 Pixels @ 331,119*/ 0, 13, 0x00, 0x11, 0x08, 0x08, 0x0E, 0x03, 0x03, 0x06, 0x08, 0x0F, 0x04, 0x03, 0x1A,
  /* RLE: 022 Pixels @ 344,119*/ 22, 0x05,
  /* RLE: 006 Pixels @ 366,119*/ 6, 0x0A,
  /* RLE: 028 Pixels @ 372,119*/ 28, 0x01,
  /* RLE: 034 Pixels @ 000,120*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,120*/ 1, 0x04,
  /* RLE: 006 Pixels @ 035,120*/ 6, 0x03,
  /* ABS: 002 Pixels @ 041,120*/ 0, 2, 0x04, 0x04,
  /* RLE: 027 Pixels @ 043,120*/ 27, 0x03,
  /* ABS: 002 Pixels @ 070,120*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 072,120*/ 10, 0x00,
  /* ABS: 013 Pixels @ 082,120*/ 0, 13, 0x04, 0x04, 0x03, 0x03, 0x03, 0x0E, 0x08, 0x0C, 0x03, 0x03, 0x03, 0x04, 0x03,
  /* RLE: 073 Pixels @ 095,120*/ 73, 0x00,
  /* RLE: 001 Pixels @ 168,120*/ 1, 0x0B,
  /* RLE: 029 Pixels @ 169,120*/ 29, 0x00,
  /* RLE: 001 Pixels @ 198,120*/ 1, 0x04,
  /* RLE: 007 Pixels @ 199,120*/ 7, 0x03,
  /* RLE: 061 Pixels @ 206,120*/ 61, 0x00,
  /* RLE: 004 Pixels @ 267,120*/ 4, 0x03,
  /* ABS: 015 Pixels @ 271,120*/ 0, 15, 0x00, 0x03, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x03, 0x0B, 0x03, 0x00, 0x03, 0x03, 0x00,
  /* RLE: 012 Pixels @ 286,120*/ 12, 0x0A,
  /* RLE: 006 Pixels @ 298,120*/ 6, 0x05,
  /* RLE: 001 Pixels @ 304,120*/ 1, 0x1A,
  /* RLE: 006 Pixels @ 305,120*/ 6, 0x03,
  /* ABS: 033 Pixels @ 311,120*/ 0, 33, 0x0C, 0x08, 0x03, 0x03, 0x0C, 0x0E, 0x0D, 0x0F, 0x00, 0x06, 0x08, 0x08, 0x12, 0x03, 0x00, 0x11, 0x08, 0x08, 0x0E, 0x03, 0x06, 0x06, 0x03, 0x04, 0x0C, 0x03, 0x0E, 0x0F, 0x00, 0x0C, 0x08, 0x00, 0x15,
  /* RLE: 028 Pixels @ 344,120*/ 28, 0x05,
  /* RLE: 006 Pixels @ 372,120*/ 6, 0x0A,
  /* RLE: 022 Pixels @ 378,120*/ 22, 0x01,
  /* RLE: 034 Pixels @ 000,121*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,121*/ 1, 0x04,
  /* RLE: 018 Pixels @ 035,121*/ 18, 0x03,
  /* RLE: 003 Pixels @ 053,121*/ 3, 0x04,
  /* RLE: 016 Pixels @ 056,121*/ 16, 0x03,
  /* ABS: 002 Pixels @ 072,121*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 074,121*/ 6, 0x00,
  /* RLE: 008 Pixels @ 080,121*/ 8, 0x03,
  /* ABS: 007 Pixels @ 088,121*/ 0, 7, 0x0D, 0x08, 0x00, 0x03, 0x00, 0x00, 0x03,
  /* RLE: 071 Pixels @ 095,121*/ 71, 0x00,
  /* ABS: 005 Pixels @ 166,121*/ 0, 5, 0x0B, 0x08, 0x00, 0x00, 0x0B,
  /* RLE: 028 Pixels @ 171,121*/ 28, 0x00,
  /* RLE: 001 Pixels @ 199,121*/ 1, 0x04,
  /* RLE: 006 Pixels @ 200,121*/ 6, 0x03,
  /* RLE: 001 Pixels @ 206,121*/ 1, 0x04,
  /* RLE: 061 Pixels @ 207,121*/ 61, 0x00,
  /* ABS: 017 Pixels @ 268,121*/ 0, 17, 0x06, 0x08, 0x03, 0x0A, 0x03, 0x08, 0x12, 0x03, 0x0B, 0x0C, 0x0D, 0x00, 0x08, 0x0B, 0x03, 0x06, 0x06,
  /* RLE: 006 Pixels @ 285,121*/ 6, 0x03,
  /* ABS: 002 Pixels @ 291,121*/ 0, 2, 0x15, 0x15,
  /* RLE: 004 Pixels @ 293,121*/ 4, 0x03,
  /* ABS: 002 Pixels @ 297,121*/ 0, 2, 0x15, 0x00,
  /* RLE: 006 Pixels @ 299,121*/ 6, 0x03,
  /* ABS: 039 Pixels @ 305,121*/ 0, 39, 0x00, 0x11, 0x08, 0x08, 0x0E, 0x00, 0x11, 0x08, 0x0F, 0x03, 0x0C, 0x08, 0x0E, 0x0B, 0x0C, 0x06, 0x00, 0x0C, 0x08, 0x00, 0x06, 0x06, 0x03, 0x04, 0x04, 0x03, 0x0E, 0x08, 0x0F, 0x06, 0x04, 0x03, 0x06, 0x11, 0x0C,
        0x0C, 0x08, 0x0C, 0x00,
  /* RLE: 034 Pixels @ 344,121*/ 34, 0x05,
  /* RLE: 006 Pixels @ 378,121*/ 6, 0x0A,
  /* RLE: 016 Pixels @ 384,121*/ 16, 0x01,
  /* RLE: 035 Pixels @ 000,122*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,122*/ 1, 0x04,
  /* RLE: 016 Pixels @ 036,122*/ 16, 0x03,
  /* ABS: 007 Pixels @ 052,122*/ 0, 7, 0x04, 0x04, 0x00, 0x00, 0x00, 0x04, 0x04,
  /* RLE: 016 Pixels @ 059,122*/ 16, 0x03,
  /* ABS: 008 Pixels @ 075,122*/ 0, 8, 0x04, 0x04, 0x00, 0x00, 0x03, 0x00, 0x0E, 0x0B,
  /* RLE: 005 Pixels @ 083,122*/ 5, 0x03,
  /* ABS: 005 Pixels @ 088,122*/ 0, 5, 0x00, 0x0F, 0x06, 0x03, 0x04,
  /* RLE: 072 Pixels @ 093,122*/ 72, 0x00,
  /* ABS: 005 Pixels @ 165,122*/ 0, 5, 0x0B, 0x00, 0x08, 0x00, 0x0B,
  /* RLE: 028 Pixels @ 170,122*/ 28, 0x00,
  /* ABS: 002 Pixels @ 198,122*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 200,122*/ 6, 0x03,
  /* RLE: 001 Pixels @ 206,122*/ 1, 0x04,
  /* RLE: 050 Pixels @ 207,122*/ 50, 0x00,
  /* RLE: 010 Pixels @ 257,122*/ 10, 0x0A,
  /* ABS: 008 Pixels @ 267,122*/ 0, 8, 0x03, 0x0E, 0x08, 0x03, 0x05, 0x03, 0x0F, 0x06,
  /* RLE: 008 Pixels @ 275,122*/ 8, 0x03,
  /* ABS: 061 Pixels @ 283,122*/ 0, 61, 0x0E, 0x11, 0x03, 0x00, 0x11, 0x08, 0x08, 0x0E, 0x03, 0x03, 0x06, 0x08, 0x0F, 0x04, 0x03, 0x04, 0x0E, 0x0E, 0x08, 0x0F, 0x0B, 0x03, 0x06, 0x06, 0x03, 0x04, 0x0C, 0x00, 0x06, 0x08, 0x00, 0x03, 0x0C, 0x08, 0x03,
        0x03, 0x03, 0x0B, 0x0E, 0x11, 0x08, 0x0B, 0x0E, 0x08, 0x0F, 0x06, 0x0C, 0x03, 0x03, 0x0B, 0x0E, 0x0D, 0x08, 0x0C, 0x06, 0x11, 0x0C, 0x00, 0x00, 0x00, 0x03,
  /* RLE: 040 Pixels @ 344,122*/ 40, 0x05,
  /* RLE: 004 Pixels @ 384,122*/ 4, 0x0A,
  /* RLE: 012 Pixels @ 388,122*/ 12, 0x01,
  /* RLE: 035 Pixels @ 000,123*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,123*/ 1, 0x04,
  /* RLE: 014 Pixels @ 036,123*/ 14, 0x03,
  /* ABS: 003 Pixels @ 050,123*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 006 Pixels @ 053,123*/ 6, 0x00,
  /* ABS: 002 Pixels @ 059,123*/ 0, 2, 0x04, 0x04,
  /* RLE: 018 Pixels @ 061,123*/ 18, 0x03,
  /* ABS: 005 Pixels @ 079,123*/ 0, 5, 0x0B, 0x08, 0x0F, 0x08, 0x00,
  /* RLE: 005 Pixels @ 084,123*/ 5, 0x03,
  /* ABS: 003 Pixels @ 089,123*/ 0, 3, 0x0B, 0x03, 0x03,
  /* RLE: 076 Pixels @ 092,123*/ 76, 0x00,
  /* ABS: 004 Pixels @ 168,123*/ 0, 4, 0x08, 0x00, 0x00, 0x0B,
  /* RLE: 027 Pixels @ 172,123*/ 27, 0x00,
  /* RLE: 001 Pixels @ 199,123*/ 1, 0x04,
  /* RLE: 007 Pixels @ 200,123*/ 7, 0x03,
  /* RLE: 046 Pixels @ 207,123*/ 46, 0x00,
  /* RLE: 004 Pixels @ 253,123*/ 4, 0x0A,
  /* RLE: 010 Pixels @ 257,123*/ 10, 0x05,
  /* ABS: 077 Pixels @ 267,123*/ 0, 77, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x03, 0x0D, 0x06, 0x03, 0x0C, 0x06, 0x03, 0x03, 0x08, 0x0E, 0x03, 0x0C, 0x08, 0x03, 0x06, 0x06, 0x03, 0x04, 0x04, 0x03, 0x0E, 0x0F, 0x00, 0x0C, 0x08, 0x00, 0x0C, 0x08, 0x0C, 0x00,
        0x0F, 0x12, 0x03, 0x0E, 0x08, 0x0F, 0x06, 0x04, 0x03, 0x0B, 0x08, 0x0B, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x0C, 0x0F, 0x04, 0x04, 0x08, 0x00, 0x03, 0x04, 0x0E, 0x0D, 0x08, 0x0C, 0x0C, 0x0D, 0x00, 0x04, 0x08, 0x04, 0x0C, 0x08, 0x0B, 0x0C, 0x08, 0x04,
        0x15,
  /* RLE: 044 Pixels @ 344,123*/ 44, 0x05,
  /* ABS: 002 Pixels @ 388,123*/ 0, 2, 0x0A, 0x0A,
  /* RLE: 010 Pixels @ 390,123*/ 10, 0x01,
  /* RLE: 036 Pixels @ 000,124*/ 36, 0x02,
  /* RLE: 001 Pixels @ 036,124*/ 1, 0x04,
  /* RLE: 011 Pixels @ 037,124*/ 11, 0x03,
  /* ABS: 002 Pixels @ 048,124*/ 0, 2, 0x04, 0x04,
  /* RLE: 012 Pixels @ 050,124*/ 12, 0x00,
  /* ABS: 002 Pixels @ 062,124*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 064,124*/ 13, 0x03,
  /* ABS: 007 Pixels @ 077,124*/ 0, 7, 0x00, 0x0C, 0x06, 0x04, 0x03, 0x11, 0x11,
  /* RLE: 004 Pixels @ 084,124*/ 4, 0x03,
  /* RLE: 082 Pixels @ 088,124*/ 82, 0x00,
  /* RLE: 001 Pixels @ 170,124*/ 1, 0x0B,
  /* RLE: 029 Pixels @ 171,124*/ 29, 0x00,
  /* RLE: 001 Pixels @ 200,124*/ 1, 0x04,
  /* RLE: 006 Pixels @ 201,124*/ 6, 0x03,
  /* RLE: 001 Pixels @ 207,124*/ 1, 0x04,
  /* RLE: 041 Pixels @ 208,124*/ 41, 0x00,
  /* RLE: 004 Pixels @ 249,124*/ 4, 0x0A,
  /* RLE: 014 Pixels @ 253,124*/ 14, 0x05,
  /* ABS: 002 Pixels @ 267,124*/ 0, 2, 0x03, 0x0C,
  /* RLE: 005 Pixels @ 269,124*/ 5, 0x08,
  /* ABS: 070 Pixels @ 274,124*/ 0, 70, 0x06, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x06, 0x06, 0x03, 0x0C, 0x08, 0x03, 0x0E, 0x08, 0x0F, 0x06, 0x0C, 0x03, 0x06, 0x11, 0x0C, 0x0C, 0x08, 0x0C, 0x0C, 0x08, 0x03, 0x03, 0x06, 0x06, 0x03, 0x03, 0x0B, 0x0E, 0x0D,
        0x08, 0x04, 0x0B, 0x08, 0x00, 0x03, 0x04, 0x08, 0x0B, 0x03, 0x12, 0x11, 0x00, 0x0E, 0x08, 0x0B, 0x0C, 0x0D, 0x00, 0x0B, 0x08, 0x04, 0x00, 0x12, 0x08, 0x11, 0x0C, 0x03, 0x03, 0x0E, 0x0F, 0x11, 0x0C, 0x03, 0x19,
  /* RLE: 046 Pixels @ 344,124*/ 46, 0x05,
  /* RLE: 006 Pixels @ 390,124*/ 6, 0x0A,
  /* RLE: 004 Pixels @ 396,124*/ 4, 0x01,
  /* RLE: 035 Pixels @ 000,125*/ 35, 0x02,
  /* ABS: 002 Pixels @ 035,125*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 037,125*/ 9, 0x03,
  /* RLE: 003 Pixels @ 046,125*/ 3, 0x04,
  /* RLE: 001 Pixels @ 049,125*/ 1, 0x0B,
  /* RLE: 014 Pixels @ 050,125*/ 14, 0x00,
  /* ABS: 002 Pixels @ 064,125*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 066,125*/ 11, 0x03,
  /* ABS: 013 Pixels @ 077,125*/ 0, 13, 0x0F, 0x08, 0x08, 0x0C, 0x03, 0x00, 0x08, 0x12, 0x03, 0x03, 0x04, 0x04, 0x0B,
  /* RLE: 079 Pixels @ 090,125*/ 79, 0x00,
  /* RLE: 001 Pixels @ 169,125*/ 1, 0x0B,
  /* RLE: 030 Pixels @ 170,125*/ 30, 0x00,
  /* RLE: 001 Pixels @ 200,125*/ 1, 0x04,
  /* RLE: 010 Pixels @ 201,125*/ 10, 0x03,
  /* RLE: 034 Pixels @ 211,125*/ 34, 0x00,
  /* RLE: 004 Pixels @ 245,125*/ 4, 0x0A,
  /* RLE: 018 Pixels @ 249,125*/ 18, 0x05,
  /* ABS: 063 Pixels @ 267,125*/ 0, 63, 0x03, 0x0C, 0x08, 0x0E, 0x0B, 0x0B, 0x0D, 0x0F, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x06, 0x06, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x0B, 0x0E, 0x0D, 0x08, 0x0C, 0x06, 0x11, 0x0C, 0x00, 0x0B, 0x00, 0x04, 0x08, 0x00, 0x03,
        0x06, 0x06, 0x03, 0x0C, 0x0D, 0x00, 0x04, 0x08, 0x04, 0x00, 0x08, 0x0C, 0x00, 0x0B, 0x08, 0x00, 0x03, 0x00, 0x11, 0x0F, 0x0E, 0x12, 0x0C, 0x00, 0x12, 0x08, 0x11, 0x0C,
  /* RLE: 006 Pixels @ 330,125*/ 6, 0x03,
  /* ABS: 002 Pixels @ 336,125*/ 0, 2, 0x15, 0x15,
  /* RLE: 004 Pixels @ 338,125*/ 4, 0x03,
  /* RLE: 001 Pixels @ 342,125*/ 1, 0x15,
  /* RLE: 053 Pixels @ 343,125*/ 53, 0x05,
  /* RLE: 004 Pixels @ 396,125*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 000,126*/ 34, 0x02,
  /* ABS: 002 Pixels @ 034,126*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 036,126*/ 9, 0x03,
  /* ABS: 002 Pixels @ 045,126*/ 0, 2, 0x04, 0x04,
  /* RLE: 020 Pixels @ 047,126*/ 20, 0x00,
  /* ABS: 021 Pixels @ 067,126*/ 0, 21, 0x04, 0x04, 0x03, 0x03, 0x03, 0x0C, 0x04, 0x03, 0x0C, 0x06, 0x0E, 0x03, 0x0D, 0x0F, 0x00, 0x03, 0x0C, 0x0F, 0x00, 0x00, 0x04,
  /* RLE: 079 Pixels @ 088,126*/ 79, 0x00,
  /* ABS: 005 Pixels @ 167,126*/ 0, 5, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 027 Pixels @ 172,126*/ 27, 0x00,
  /* ABS: 002 Pixels @ 199,126*/ 0, 2, 0x0B, 0x04,
  /* RLE: 005 Pixels @ 201,126*/ 5, 0x03,
  /* ABS: 005 Pixels @ 206,126*/ 0, 5, 0x00, 0x0B, 0x0C, 0x06, 0x03,
  /* RLE: 029 Pixels @ 211,126*/ 29, 0x00,
  /* RLE: 005 Pixels @ 240,126*/ 5, 0x0A,
  /* RLE: 022 Pixels @ 245,126*/ 22, 0x05,
  /* ABS: 052 Pixels @ 267,126*/ 0, 52, 0x03, 0x0B, 0x08, 0x0B, 0x03, 0x03, 0x0E, 0x08, 0x03, 0x0C, 0x08, 0x00, 0x03, 0x06, 0x06, 0x03, 0x0B, 0x08, 0x00, 0x0C, 0x0D, 0x00, 0x0B, 0x08, 0x04, 0x0C, 0x08, 0x0B, 0x0C, 0x08, 0x04, 0x00, 0x08, 0x0B, 0x03,
        0x0E, 0x08, 0x03, 0x00, 0x12, 0x08, 0x11, 0x0C, 0x03, 0x03, 0x0D, 0x08, 0x04, 0x03, 0x03, 0x03, 0x15,
  /* RLE: 011 Pixels @ 319,126*/ 11, 0x03,
  /* RLE: 001 Pixels @ 330,126*/ 1, 0x15,
  /* RLE: 013 Pixels @ 331,126*/ 13, 0x05,
  /* RLE: 005 Pixels @ 344,126*/ 5, 0x0A,
  /* RLE: 005 Pixels @ 349,126*/ 5, 0x03,
  /* RLE: 046 Pixels @ 354,126*/ 46, 0x05,
  /* RLE: 034 Pixels @ 000,127*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,127*/ 1, 0x04,
  /* RLE: 008 Pixels @ 035,127*/ 8, 0x03,
  /* ABS: 002 Pixels @ 043,127*/ 0, 2, 0x04, 0x04,
  /* RLE: 024 Pixels @ 045,127*/ 24, 0x00,
  /* ABS: 017 Pixels @ 069,127*/ 0, 17, 0x04, 0x04, 0x03, 0x11, 0x0F, 0x00, 0x0C, 0x08, 0x0E, 0x03, 0x00, 0x0F, 0x0D, 0x03, 0x03, 0x00, 0x03,
  /* RLE: 083 Pixels @ 086,127*/ 83, 0x00,
  /* RLE: 001 Pixels @ 169,127*/ 1, 0x08,
  /* RLE: 031 Pixels @ 170,127*/ 31, 0x00,
  /* ABS: 009 Pixels @ 201,127*/ 0, 9, 0x03, 0x00, 0x0C, 0x06, 0x0F, 0x08, 0x08, 0x08, 0x0F,
  /* RLE: 026 Pixels @ 210,127*/ 26, 0x00,
  /* RLE: 004 Pixels @ 236,127*/ 4, 0x0A,
  /* RLE: 027 Pixels @ 240,127*/ 27, 0x05,
  /* ABS: 039 Pixels @ 267,127*/ 0, 39, 0x00, 0x0B, 0x08, 0x0B, 0x00, 0x03, 0x0C, 0x08, 0x03, 0x0B, 0x08, 0x0C, 0x0B, 0x0F, 0x11, 0x03, 0x0B, 0x08, 0x0B, 0x00, 0x12, 0x08, 0x11, 0x0C, 0x03, 0x03, 0x0E, 0x0F, 0x11, 0x0C, 0x03, 0x0B, 0x0F, 0x00, 0x03,
        0x04, 0x12, 0x03, 0x00,
  /* RLE: 004 Pixels @ 306,127*/ 4, 0x03,
  /* ABS: 006 Pixels @ 310,127*/ 0, 6, 0x15, 0x00, 0x03, 0x03, 0x03, 0x19,
  /* RLE: 012 Pixels @ 316,127*/ 12, 0x05,
  /* RLE: 016 Pixels @ 328,127*/ 16, 0x0A,
  /* RLE: 005 Pixels @ 344,127*/ 5, 0x01,
  /* RLE: 001 Pixels @ 349,127*/ 1, 0x04,
  /* RLE: 008 Pixels @ 350,127*/ 8, 0x03,
  /* ABS: 002 Pixels @ 358,127*/ 0, 2, 0x0A, 0x0A,
  /* RLE: 040 Pixels @ 360,127*/ 40, 0x05,
  /* RLE: 034 Pixels @ 000,128*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,128*/ 1, 0x04,
  /* RLE: 007 Pixels @ 035,128*/ 7, 0x03,
  /* RLE: 001 Pixels @ 042,128*/ 1, 0x04,
  /* RLE: 027 Pixels @ 043,128*/ 27, 0x00,
  /* ABS: 013 Pixels @ 070,128*/ 0, 13, 0x0B, 0x03, 0x00, 0x08, 0x0D, 0x03, 0x06, 0x08, 0x00, 0x03, 0x04, 0x08, 0x04,
  /* RLE: 085 Pixels @ 083,128*/ 85, 0x00,
  /* ABS: 006 Pixels @ 168,128*/ 0, 6, 0x0B, 0x00, 0x08, 0x0B, 0x00, 0x0B,
  /* RLE: 024 Pixels @ 174,128*/ 24, 0x00,
  /* ABS: 013 Pixels @ 198,128*/ 0, 13, 0x0B, 0x00, 0x0B, 0x03, 0x12, 0x08, 0x0F, 0x06, 0x0C, 0x0B, 0x00, 0x03, 0x03,
  /* RLE: 021 Pixels @ 211,128*/ 21, 0x00,
  /* RLE: 004 Pixels @ 232,128*/ 4, 0x0A,
  /* RLE: 031 Pixels @ 236,128*/ 31, 0x05,
  /* ABS: 019 Pixels @ 267,128*/ 0, 19, 0x15, 0x0B, 0x08, 0x0B, 0x15, 0x00, 0x0B, 0x0C, 0x03, 0x03, 0x12, 0x08, 0x06, 0x0C, 0x12, 0x03, 0x0B, 0x0F, 0x00,
  /* RLE: 005 Pixels @ 286,128*/ 5, 0x03,
  /* ABS: 002 Pixels @ 291,128*/ 0, 2, 0x15, 0x15,
  /* RLE: 004 Pixels @ 293,128*/ 4, 0x03,
  /* ABS: 008 Pixels @ 297,128*/ 0, 8, 0x15, 0x03, 0x03, 0x03, 0x15, 0x03, 0x00, 0x15,
  /* RLE: 008 Pixels @ 305,128*/ 8, 0x05,
  /* RLE: 015 Pixels @ 313,128*/ 15, 0x0A,
  /* RLE: 021 Pixels @ 328,128*/ 21, 0x01,
  /* ABS: 002 Pixels @ 349,128*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 351,128*/ 8, 0x03,
  /* RLE: 001 Pixels @ 359,128*/ 1, 0x04,
  /* RLE: 006 Pixels @ 360,128*/ 6, 0x0A,
  /* RLE: 034 Pixels @ 366,128*/ 34, 0x05,
  /* RLE: 033 Pixels @ 000,129*/ 33, 0x02,
  /* ABS: 002 Pixels @ 033,129*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 035,129*/ 6, 0x03,
  /* ABS: 003 Pixels @ 041,129*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 028 Pixels @ 044,129*/ 28, 0x00,
  /* ABS: 013 Pixels @ 072,129*/ 0, 13, 0x03, 0x04, 0x08, 0x0E, 0x03, 0x0F, 0x11, 0x03, 0x03, 0x0B, 0x03, 0x00, 0x0B,
  /* RLE: 085 Pixels @ 085,129*/ 85, 0x00,
  /* ABS: 003 Pixels @ 170,129*/ 0, 3, 0x08, 0x00, 0x0B,
  /* RLE: 028 Pixels @ 173,129*/ 28, 0x00,
  /* ABS: 008 Pixels @ 201,129*/ 0, 8, 0x03, 0x00, 0x00, 0x03, 0x03, 0x00, 0x04, 0x03,
  /* RLE: 019 Pixels @ 209,129*/ 19, 0x00,
  /* RLE: 004 Pixels @ 228,129*/ 4, 0x0A,
  /* RLE: 035 Pixels @ 232,129*/ 35, 0x05,
  /* ABS: 010 Pixels @ 267,129*/ 0, 10, 0x19, 0x03, 0x03, 0x03, 0x19, 0x19, 0x00, 0x00, 0x15, 0x15,
  /* RLE: 005 Pixels @ 277,129*/ 5, 0x03,
  /* ABS: 005 Pixels @ 282,129*/ 0, 5, 0x00, 0x03, 0x03, 0x03, 0x1A,
  /* RLE: 011 Pixels @ 287,129*/ 11, 0x05,
  /* RLE: 015 Pixels @ 298,129*/ 15, 0x0A,
  /* RLE: 036 Pixels @ 313,129*/ 36, 0x01,
  /* RLE: 003 Pixels @ 349,129*/ 3, 0x04,
  /* RLE: 008 Pixels @ 352,129*/ 8, 0x03,
  /* RLE: 003 Pixels @ 360,129*/ 3, 0x04,
  /* RLE: 003 Pixels @ 363,129*/ 3, 0x01,
  /* RLE: 006 Pixels @ 366,129*/ 6, 0x0A,
  /* RLE: 028 Pixels @ 372,129*/ 28, 0x05,
  /* RLE: 034 Pixels @ 000,130*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,130*/ 1, 0x04,
  /* RLE: 006 Pixels @ 035,130*/ 6, 0x03,
  /* RLE: 001 Pixels @ 041,130*/ 1, 0x04,
  /* RLE: 030 Pixels @ 042,130*/ 30, 0x00,
  /* ABS: 012 Pixels @ 072,130*/ 0, 12, 0x03, 0x03, 0x12, 0x08, 0x04, 0x0B, 0x08, 0x0C, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 116 Pixels @ 084,130*/ 116, 0x00,
  /* ABS: 010 Pixels @ 200,130*/ 0, 10, 0x0B, 0x03, 0x04, 0x0E, 0x0D, 0x08, 0x08, 0x08, 0x03, 0x0B,
  /* RLE: 013 Pixels @ 210,130*/ 13, 0x00,
  /* RLE: 005 Pixels @ 223,130*/ 5, 0x0A,
  /* RLE: 054 Pixels @ 228,130*/ 54, 0x05,
  /* RLE: 016 Pixels @ 282,130*/ 16, 0x0A,
  /* RLE: 005 Pixels @ 298,130*/ 5, 0x00,
  /* ABS: 011 Pixels @ 303,130*/ 0, 11, 0x0D, 0x03, 0x06, 0x00, 0x00, 0x00, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 314,130*/ 37, 0x01,
  /* ABS: 002 Pixels @ 351,130*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 353,130*/ 9, 0x03,
  /* ABS: 002 Pixels @ 362,130*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 364,130*/ 8, 0x01,
  /* RLE: 006 Pixels @ 372,130*/ 6, 0x0A,
  /* RLE: 022 Pixels @ 378,130*/ 22, 0x05,
  /* RLE: 034 Pixels @ 000,131*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,131*/ 1, 0x04,
  /* RLE: 006 Pixels @ 035,131*/ 6, 0x03,
  /* ABS: 002 Pixels @ 041,131*/ 0, 2, 0x04, 0x0B,
  /* RLE: 030 Pixels @ 043,131*/ 30, 0x00,
  /* ABS: 006 Pixels @ 073,131*/ 0, 6, 0x03, 0x03, 0x11, 0x0F, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 079,131*/ 4, 0x03,
  /* ABS: 002 Pixels @ 083,131*/ 0, 2, 0x04, 0x0B,
  /* RLE: 116 Pixels @ 085,131*/ 116, 0x00,
  /* ABS: 009 Pixels @ 201,131*/ 0, 9, 0x03, 0x12, 0x08, 0x06, 0x0E, 0x0E, 0x0D, 0x00, 0x03,
  /* RLE: 009 Pixels @ 210,131*/ 9, 0x00,
  /* RLE: 004 Pixels @ 219,131*/ 4, 0x0A,
  /* RLE: 044 Pixels @ 223,131*/ 44, 0x05,
  /* RLE: 015 Pixels @ 267,131*/ 15, 0x0A,
  /* RLE: 021 Pixels @ 282,131*/ 21, 0x00,
  /* ABS: 004 Pixels @ 303,131*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 004 Pixels @ 307,131*/ 4, 0x00,
  /* ABS: 004 Pixels @ 311,131*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 315,131*/ 37, 0x01,
  /* ABS: 002 Pixels @ 352,131*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 354,131*/ 9, 0x03,
  /* ABS: 002 Pixels @ 363,131*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 365,131*/ 13, 0x01,
  /* RLE: 006 Pixels @ 378,131*/ 6, 0x0A,
  /* RLE: 016 Pixels @ 384,131*/ 16, 0x05,
  /* RLE: 034 Pixels @ 000,132*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,132*/ 1, 0x04,
  /* RLE: 006 Pixels @ 035,132*/ 6, 0x03,
  /* RLE: 001 Pixels @ 041,132*/ 1, 0x04,
  /* RLE: 032 Pixels @ 042,132*/ 32, 0x00,
  /* ABS: 004 Pixels @ 074,132*/ 0, 4, 0x03, 0x00, 0x08, 0x12,
  /* RLE: 005 Pixels @ 078,132*/ 5, 0x03,
  /* ABS: 003 Pixels @ 083,132*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 084 Pixels @ 086,132*/ 84, 0x00,
  /* ABS: 004 Pixels @ 170,132*/ 0, 4, 0x0B, 0x00, 0x00, 0x0B,
  /* RLE: 026 Pixels @ 174,132*/ 26, 0x00,
  /* RLE: 001 Pixels @ 200,132*/ 1, 0x0B,
  /* RLE: 006 Pixels @ 201,132*/ 6, 0x03,
  /* ABS: 003 Pixels @ 207,132*/ 0, 3, 0x06, 0x0E, 0x03,
  /* RLE: 005 Pixels @ 210,132*/ 5, 0x00,
  /* RLE: 004 Pixels @ 215,132*/ 4, 0x0A,
  /* RLE: 038 Pixels @ 219,132*/ 38, 0x05,
  /* RLE: 010 Pixels @ 257,132*/ 10, 0x0A,
  /* RLE: 037 Pixels @ 267,132*/ 37, 0x00,
  /* RLE: 003 Pixels @ 304,132*/ 3, 0x06,
  /* RLE: 005 Pixels @ 307,132*/ 5, 0x00,
  /* RLE: 004 Pixels @ 312,132*/ 4, 0x06,
  /* RLE: 037 Pixels @ 316,132*/ 37, 0x01,
  /* ABS: 002 Pixels @ 353,132*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 355,132*/ 9, 0x03,
  /* ABS: 002 Pixels @ 364,132*/ 0, 2, 0x04, 0x04,
  /* RLE: 018 Pixels @ 366,132*/ 18, 0x01,
  /* RLE: 004 Pixels @ 384,132*/ 4, 0x0A,
  /* RLE: 012 Pixels @ 388,132*/ 12, 0x05,
  /* RLE: 034 Pixels @ 000,133*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,133*/ 1, 0x04,
  /* RLE: 006 Pixels @ 035,133*/ 6, 0x03,
  /* ABS: 003 Pixels @ 041,133*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 031 Pixels @ 044,133*/ 31, 0x00,
  /* ABS: 002 Pixels @ 075,133*/ 0, 2, 0x03, 0x0B,
  /* RLE: 006 Pixels @ 077,133*/ 6, 0x03,
  /* ABS: 002 Pixels @ 083,133*/ 0, 2, 0x04, 0x04,
  /* RLE: 086 Pixels @ 085,133*/ 86, 0x00,
  /* ABS: 004 Pixels @ 171,133*/ 0, 4, 0x0B, 0x08, 0x00, 0x0B,
  /* RLE: 026 Pixels @ 175,133*/ 26, 0x00,
  /* ABS: 010 Pixels @ 201,133*/ 0, 10, 0x0B, 0x03, 0x03, 0x00, 0x0C, 0x0E, 0x0F, 0x0E, 0x03, 0x04,
  /* RLE: 004 Pixels @ 211,133*/ 4, 0x0A,
  /* RLE: 038 Pixels @ 215,133*/ 38, 0x05,
  /* RLE: 004 Pixels @ 253,133*/ 4, 0x0A,
  /* RLE: 047 Pixels @ 257,133*/ 47, 0x00,
  /* ABS: 003 Pixels @ 304,133*/ 0, 3, 0x0D, 0x06, 0x06,
  /* RLE: 006 Pixels @ 307,133*/ 6, 0x00,
  /* RLE: 005 Pixels @ 313,133*/ 5, 0x06,
  /* RLE: 036 Pixels @ 318,133*/ 36, 0x01,
  /* ABS: 002 Pixels @ 354,133*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 356,133*/ 9, 0x03,
  /* ABS: 002 Pixels @ 365,133*/ 0, 2, 0x04, 0x04,
  /* RLE: 021 Pixels @ 367,133*/ 21, 0x01,
  /* ABS: 002 Pixels @ 388,133*/ 0, 2, 0x0A, 0x0A,
  /* RLE: 010 Pixels @ 390,133*/ 10, 0x05,
  /* RLE: 034 Pixels @ 000,134*/ 34, 0x02,
  /* RLE: 001 Pixels @ 034,134*/ 1, 0x04,
  /* RLE: 006 Pixels @ 035,134*/ 6, 0x03,
  /* ABS: 002 Pixels @ 041,134*/ 0, 2, 0x04, 0x0B,
  /* RLE: 034 Pixels @ 043,134*/ 34, 0x00,
  /* RLE: 007 Pixels @ 077,134*/ 7, 0x03,
  /* ABS: 003 Pixels @ 084,134*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 083 Pixels @ 087,134*/ 83, 0x00,
  /* ABS: 033 Pixels @ 170,134*/ 0, 33, 0x0B, 0x00, 0x08, 0x00, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x00, 0x03,
  /* RLE: 005 Pixels @ 203,134*/ 5, 0x08,
  /* ABS: 003 Pixels @ 208,134*/ 0, 3, 0x0B, 0x03, 0x06,
  /* RLE: 038 Pixels @ 211,134*/ 38, 0x05,
  /* RLE: 004 Pixels @ 249,134*/ 4, 0x0A,
  /* RLE: 051 Pixels @ 253,134*/ 51, 0x00,
  /* ABS: 003 Pixels @ 304,134*/ 0, 3, 0x06, 0x03, 0x06,
  /* RLE: 007 Pixels @ 307,134*/ 7, 0x00,
  /* ABS: 005 Pixels @ 314,134*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 319,134*/ 36, 0x01,
  /* ABS: 002 Pixels @ 355,134*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 357,134*/ 9, 0x03,
  /* ABS: 002 Pixels @ 366,134*/ 0, 2, 0x04, 0x04,
  /* RLE: 022 Pixels @ 368,134*/ 22, 0x01,
  /* RLE: 006 Pixels @ 390,134*/ 6, 0x0A,
  /* RLE: 004 Pixels @ 396,134*/ 4, 0x05,
  /* RLE: 035 Pixels @ 000,135*/ 35, 0x02,
  /* RLE: 007 Pixels @ 035,135*/ 7, 0x03,
  /* RLE: 001 Pixels @ 042,135*/ 1, 0x04,
  /* RLE: 034 Pixels @ 043,135*/ 34, 0x00,
  /* RLE: 001 Pixels @ 077,135*/ 1, 0x04,
  /* RLE: 007 Pixels @ 078,135*/ 7, 0x03,
  /* ABS: 003 Pixels @ 085,135*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 085 Pixels @ 088,135*/ 85, 0x00,
  /* ABS: 038 Pixels @ 173,135*/ 0, 38, 0x08, 0x00, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x03, 0x0E, 0x04, 0x00, 0x03, 0x00,
        0x08, 0x00, 0x15,
  /* RLE: 034 Pixels @ 211,135*/ 34, 0x05,
  /* RLE: 004 Pixels @ 245,135*/ 4, 0x0A,
  /* RLE: 055 Pixels @ 249,135*/ 55, 0x00,
  /* ABS: 003 Pixels @ 304,135*/ 0, 3, 0x0D, 0x03, 0x06,
  /* RLE: 008 Pixels @ 307,135*/ 8, 0x00,
  /* ABS: 005 Pixels @ 315,135*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 320,135*/ 37, 0x01,
  /* ABS: 002 Pixels @ 357,135*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 359,135*/ 9, 0x03,
  /* ABS: 002 Pixels @ 368,135*/ 0, 2, 0x04, 0x04,
  /* RLE: 026 Pixels @ 370,135*/ 26, 0x01,
  /* RLE: 004 Pixels @ 396,135*/ 4, 0x0A,
  /* RLE: 035 Pixels @ 000,136*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,136*/ 1, 0x04,
  /* RLE: 006 Pixels @ 036,136*/ 6, 0x03,
  /* RLE: 001 Pixels @ 042,136*/ 1, 0x04,
  /* RLE: 033 Pixels @ 043,136*/ 33, 0x00,
  /* ABS: 002 Pixels @ 076,136*/ 0, 2, 0x0B, 0x04,
  /* RLE: 008 Pixels @ 078,136*/ 8, 0x03,
  /* ABS: 003 Pixels @ 086,136*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 086 Pixels @ 089,136*/ 86, 0x00,
  /* RLE: 001 Pixels @ 175,136*/ 1, 0x0B,
  /* RLE: 023 Pixels @ 176,136*/ 23, 0x00,
  /* RLE: 003 Pixels @ 199,136*/ 3, 0x0A,
  /* RLE: 001 Pixels @ 202,136*/ 1, 0x00,
  /* RLE: 004 Pixels @ 203,136*/ 4, 0x03,
  /* ABS: 004 Pixels @ 207,136*/ 0, 4, 0x0B, 0x08, 0x00, 0x15,
  /* RLE: 029 Pixels @ 211,136*/ 29, 0x05,
  /* RLE: 005 Pixels @ 240,136*/ 5, 0x0A,
  /* RLE: 059 Pixels @ 245,136*/ 59, 0x00,
  /* ABS: 003 Pixels @ 304,136*/ 0, 3, 0x06, 0x03, 0x06,
  /* RLE: 010 Pixels @ 307,136*/ 10, 0x00,
  /* ABS: 004 Pixels @ 317,136*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 321,136*/ 37, 0x01,
  /* ABS: 002 Pixels @ 358,136*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 360,136*/ 9, 0x03,
  /* ABS: 002 Pixels @ 369,136*/ 0, 2, 0x04, 0x04,
  /* RLE: 029 Pixels @ 371,136*/ 29, 0x01,
  /* RLE: 035 Pixels @ 000,137*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,137*/ 1, 0x04,
  /* RLE: 006 Pixels @ 036,137*/ 6, 0x03,
  /* ABS: 003 Pixels @ 042,137*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 032 Pixels @ 045,137*/ 32, 0x00,
  /* ABS: 002 Pixels @ 077,137*/ 0, 2, 0x0B, 0x04,
  /* RLE: 008 Pixels @ 079,137*/ 8, 0x03,
  /* ABS: 003 Pixels @ 087,137*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 084 Pixels @ 090,137*/ 84, 0x00,
  /* RLE: 001 Pixels @ 174,137*/ 1, 0x0B,
  /* RLE: 020 Pixels @ 175,137*/ 20, 0x00,
  /* RLE: 004 Pixels @ 195,137*/ 4, 0x0A,
  /* RLE: 003 Pixels @ 199,137*/ 3, 0x05,
  /* ABS: 009 Pixels @ 202,137*/ 0, 9, 0x15, 0x00, 0x0E, 0x0D, 0x08, 0x08, 0x0F, 0x00, 0x15,
  /* RLE: 025 Pixels @ 211,137*/ 25, 0x05,
  /* RLE: 004 Pixels @ 236,137*/ 4, 0x0A,
  /* RLE: 064 Pixels @ 240,137*/ 64, 0x00,
  /* ABS: 004 Pixels @ 304,137*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 010 Pixels @ 308,137*/ 10, 0x00,
  /* RLE: 004 Pixels @ 318,137*/ 4, 0x06,
  /* RLE: 037 Pixels @ 322,137*/ 37, 0x01,
  /* ABS: 002 Pixels @ 359,137*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 361,137*/ 9, 0x03,
  /* ABS: 002 Pixels @ 370,137*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 372,137*/ 28, 0x01,
  /* RLE: 035 Pixels @ 000,138*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,138*/ 1, 0x04,
  /* RLE: 006 Pixels @ 036,138*/ 6, 0x03,
  /* ABS: 002 Pixels @ 042,138*/ 0, 2, 0x04, 0x0B,
  /* RLE: 033 Pixels @ 044,138*/ 33, 0x00,
  /* ABS: 003 Pixels @ 077,138*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 007 Pixels @ 080,138*/ 7, 0x03,
  /* ABS: 005 Pixels @ 087,138*/ 0, 5, 0x04, 0x04, 0x00, 0x00, 0x0B,
  /* RLE: 048 Pixels @ 092,138*/ 48, 0x00,
  /* ABS: 033 Pixels @ 140,138*/ 0, 33, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 019 Pixels @ 173,138*/ 19, 0x00,
  /* RLE: 003 Pixels @ 192,138*/ 3, 0x0A,
  /* RLE: 007 Pixels @ 195,138*/ 7, 0x05,
  /* ABS: 009 Pixels @ 202,138*/ 0, 9, 0x15, 0x0B, 0x0F, 0x06, 0x0C, 0x04, 0x03, 0x03, 0x1A,
  /* RLE: 021 Pixels @ 211,138*/ 21, 0x05,
  /* RLE: 004 Pixels @ 232,138*/ 4, 0x0A,
  /* RLE: 069 Pixels @ 236,138*/ 69, 0x00,
  /* RLE: 003 Pixels @ 305,138*/ 3, 0x06,
  /* RLE: 011 Pixels @ 308,138*/ 11, 0x00,
  /* RLE: 004 Pixels @ 319,138*/ 4, 0x06,
  /* RLE: 037 Pixels @ 323,138*/ 37, 0x01,
  /* ABS: 002 Pixels @ 360,138*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 362,138*/ 9, 0x03,
  /* ABS: 002 Pixels @ 371,138*/ 0, 2, 0x04, 0x04,
  /* RLE: 027 Pixels @ 373,138*/ 27, 0x01,
  /* RLE: 035 Pixels @ 000,139*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,139*/ 1, 0x04,
  /* RLE: 006 Pixels @ 036,139*/ 6, 0x03,
  /* ABS: 003 Pixels @ 042,139*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 034 Pixels @ 045,139*/ 34, 0x00,
  /* ABS: 002 Pixels @ 079,139*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 081,139*/ 7, 0x03,
  /* ABS: 003 Pixels @ 088,139*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 048 Pixels @ 091,139*/ 48, 0x00,
  /* ABS: 037 Pixels @ 139,139*/ 0, 37, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B,
        0x00, 0x08,
  /* RLE: 012 Pixels @ 176,139*/ 12, 0x00,
  /* RLE: 004 Pixels @ 188,139*/ 4, 0x0A,
  /* RLE: 010 Pixels @ 192,139*/ 10, 0x05,
  /* RLE: 001 Pixels @ 202,139*/ 1, 0x19,
  /* RLE: 004 Pixels @ 203,139*/ 4, 0x03,
  /* ABS: 002 Pixels @ 207,139*/ 0, 2, 0x00, 0x19,
  /* RLE: 019 Pixels @ 209,139*/ 19, 0x05,
  /* RLE: 004 Pixels @ 228,139*/ 4, 0x0A,
  /* RLE: 073 Pixels @ 232,139*/ 73, 0x00,
  /* ABS: 003 Pixels @ 305,139*/ 0, 3, 0x0D, 0x06, 0x06,
  /* RLE: 012 Pixels @ 308,139*/ 12, 0x00,
  /* ABS: 005 Pixels @ 320,139*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 325,139*/ 36, 0x01,
  /* ABS: 002 Pixels @ 361,139*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 363,139*/ 9, 0x03,
  /* ABS: 002 Pixels @ 372,139*/ 0, 2, 0x04, 0x04,
  /* RLE: 026 Pixels @ 374,139*/ 26, 0x01,
  /* RLE: 035 Pixels @ 000,140*/ 35, 0x02,
  /* RLE: 001 Pixels @ 035,140*/ 1, 0x04,
  /* RLE: 006 Pixels @ 036,140*/ 6, 0x03,
  /* ABS: 002 Pixels @ 042,140*/ 0, 2, 0x04, 0x0B,
  /* RLE: 036 Pixels @ 044,140*/ 36, 0x00,
  /* ABS: 002 Pixels @ 080,140*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 082,140*/ 7, 0x03,
  /* ABS: 002 Pixels @ 089,140*/ 0, 2, 0x04, 0x04,
  /* RLE: 043 Pixels @ 091,140*/ 43, 0x00,
  /* ABS: 005 Pixels @ 134,140*/ 0, 5, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 036 Pixels @ 139,140*/ 36, 0x00,
  /* RLE: 001 Pixels @ 175,140*/ 1, 0x08,
  /* RLE: 008 Pixels @ 176,140*/ 8, 0x00,
  /* RLE: 004 Pixels @ 184,140*/ 4, 0x0A,
  /* RLE: 021 Pixels @ 188,140*/ 21, 0x05,
  /* ABS: 006 Pixels @ 209,140*/ 0, 6, 0x19, 0x15, 0x03, 0x03, 0x00, 0x1A,
  /* RLE: 008 Pixels @ 215,140*/ 8, 0x05,
  /* RLE: 005 Pixels @ 223,140*/ 5, 0x0A,
  /* RLE: 077 Pixels @ 228,140*/ 77, 0x00,
  /* ABS: 003 Pixels @ 305,140*/ 0, 3, 0x06, 0x03, 0x06,
  /* RLE: 013 Pixels @ 308,140*/ 13, 0x00,
  /* ABS: 005 Pixels @ 321,140*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 326,140*/ 36, 0x01,
  /* RLE: 003 Pixels @ 362,140*/ 3, 0x04,
  /* RLE: 008 Pixels @ 365,140*/ 8, 0x03,
  /* RLE: 003 Pixels @ 373,140*/ 3, 0x04,
  /* RLE: 024 Pixels @ 376,140*/ 24, 0x01,
  /* RLE: 036 Pixels @ 000,141*/ 36, 0x02,
  /* RLE: 007 Pixels @ 036,141*/ 7, 0x03,
  /* RLE: 001 Pixels @ 043,141*/ 1, 0x04,
  /* RLE: 037 Pixels @ 044,141*/ 37, 0x00,
  /* RLE: 001 Pixels @ 081,141*/ 1, 0x04,
  /* RLE: 008 Pixels @ 082,141*/ 8, 0x03,
  /* ABS: 004 Pixels @ 090,141*/ 0, 4, 0x04, 0x00, 0x00, 0x0B,
  /* RLE: 041 Pixels @ 094,141*/ 41, 0x00,
  /* ABS: 003 Pixels @ 135,141*/ 0, 3, 0x0B, 0x00, 0x0B,
  /* RLE: 037 Pixels @ 138,141*/ 37, 0x00,
  /* RLE: 001 Pixels @ 175,141*/ 1, 0x08,
  /* RLE: 004 Pixels @ 176,141*/ 4, 0x00,
  /* RLE: 004 Pixels @ 180,141*/ 4, 0x0A,
  /* RLE: 020 Pixels @ 184,141*/ 20, 0x05,
  /* ABS: 002 Pixels @ 204,141*/ 0, 2, 0x1A, 0x15,
  /* RLE: 004 Pixels @ 206,141*/ 4, 0x03,
  /* ABS: 005 Pixels @ 210,141*/ 0, 5, 0x00, 0x0C, 0x12, 0x00, 0x19,
  /* RLE: 004 Pixels @ 215,141*/ 4, 0x05,
  /* RLE: 004 Pixels @ 219,141*/ 4, 0x0A,
  /* RLE: 082 Pixels @ 223,141*/ 82, 0x00,
  /* ABS: 003 Pixels @ 305,141*/ 0, 3, 0x0D, 0x03, 0x06,
  /* RLE: 014 Pixels @ 308,141*/ 14, 0x00,
  /* ABS: 005 Pixels @ 322,141*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 327,141*/ 37, 0x01,
  /* ABS: 002 Pixels @ 364,141*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 366,141*/ 9, 0x03,
  /* ABS: 002 Pixels @ 375,141*/ 0, 2, 0x04, 0x04,
  /* RLE: 023 Pixels @ 377,141*/ 23, 0x01,
  /* RLE: 036 Pixels @ 000,142*/ 36, 0x02,
  /* RLE: 001 Pixels @ 036,142*/ 1, 0x04,
  /* RLE: 006 Pixels @ 037,142*/ 6, 0x03,
  /* RLE: 001 Pixels @ 043,142*/ 1, 0x04,
  /* RLE: 036 Pixels @ 044,142*/ 36, 0x00,
  /* ABS: 003 Pixels @ 080,142*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 007 Pixels @ 083,142*/ 7, 0x03,
  /* ABS: 003 Pixels @ 090,142*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 039 Pixels @ 093,142*/ 39, 0x00,
  /* ABS: 003 Pixels @ 132,142*/ 0, 3, 0x0B, 0x00, 0x0B,
  /* RLE: 042 Pixels @ 135,142*/ 42, 0x00,
  /* RLE: 003 Pixels @ 177,142*/ 3, 0x0A,
  /* RLE: 024 Pixels @ 180,142*/ 24, 0x05,
  /* ABS: 005 Pixels @ 204,142*/ 0, 5, 0x00, 0x00, 0x0C, 0x12, 0x11,
  /* RLE: 004 Pixels @ 209,142*/ 4, 0x08,
  /* ABS: 002 Pixels @ 213,142*/ 0, 2, 0x00, 0x19,
  /* RLE: 004 Pixels @ 215,142*/ 4, 0x0A,
  /* RLE: 086 Pixels @ 219,142*/ 86, 0x00,
  /* ABS: 004 Pixels @ 305,142*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 015 Pixels @ 309,142*/ 15, 0x00,
  /* RLE: 004 Pixels @ 324,142*/ 4, 0x06,
  /* RLE: 037 Pixels @ 328,142*/ 37, 0x01,
  /* ABS: 002 Pixels @ 365,142*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 367,142*/ 9, 0x03,
  /* ABS: 002 Pixels @ 376,142*/ 0, 2, 0x04, 0x04,
  /* RLE: 022 Pixels @ 378,142*/ 22, 0x01,
  /* RLE: 001 Pixels @ 000,143*/ 1, 0x0A,
  /* RLE: 035 Pixels @ 001,143*/ 35, 0x02,
  /* RLE: 001 Pixels @ 036,143*/ 1, 0x04,
  /* RLE: 006 Pixels @ 037,143*/ 6, 0x03,
  /* ABS: 003 Pixels @ 043,143*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 036 Pixels @ 046,143*/ 36, 0x00,
  /* ABS: 002 Pixels @ 082,143*/ 0, 2, 0x0B, 0x04,
  /* RLE: 007 Pixels @ 084,143*/ 7, 0x03,
  /* ABS: 003 Pixels @ 091,143*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 041 Pixels @ 094,143*/ 41, 0x00,
  /* RLE: 001 Pixels @ 135,143*/ 1, 0x0B,
  /* RLE: 037 Pixels @ 136,143*/ 37, 0x00,
  /* RLE: 004 Pixels @ 173,143*/ 4, 0x0A,
  /* RLE: 027 Pixels @ 177,143*/ 27, 0x05,
  /* ABS: 011 Pixels @ 204,143*/ 0, 11, 0x03, 0x12, 0x08, 0x08, 0x06, 0x08, 0x0C, 0x00, 0x03, 0x03, 0x12,
  /* RLE: 091 Pixels @ 215,143*/ 91, 0x00,
  /* ABS: 003 Pixels @ 306,143*/ 0, 3, 0x0D, 0x06, 0x06,
  /* RLE: 016 Pixels @ 309,143*/ 16, 0x00,
  /* RLE: 004 Pixels @ 325,143*/ 4, 0x06,
  /* RLE: 037 Pixels @ 329,143*/ 37, 0x01,
  /* ABS: 002 Pixels @ 366,143*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 368,143*/ 9, 0x03,
  /* ABS: 002 Pixels @ 377,143*/ 0, 2, 0x04, 0x04,
  /* RLE: 021 Pixels @ 379,143*/ 21, 0x01,
  /* RLE: 003 Pixels @ 000,144*/ 3, 0x0A,
  /* RLE: 033 Pixels @ 003,144*/ 33, 0x02,
  /* RLE: 001 Pixels @ 036,144*/ 1, 0x04,
  /* RLE: 006 Pixels @ 037,144*/ 6, 0x03,
  /* ABS: 002 Pixels @ 043,144*/ 0, 2, 0x04, 0x0B,
  /* RLE: 037 Pixels @ 045,144*/ 37, 0x00,
  /* ABS: 003 Pixels @ 082,144*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 007 Pixels @ 085,144*/ 7, 0x03,
  /* ABS: 002 Pixels @ 092,144*/ 0, 2, 0x04, 0x04,
  /* RLE: 075 Pixels @ 094,144*/ 75, 0x00,
  /* RLE: 004 Pixels @ 169,144*/ 4, 0x0A,
  /* RLE: 031 Pixels @ 173,144*/ 31, 0x05,
  /* RLE: 003 Pixels @ 204,144*/ 3, 0x00,
  /* ABS: 007 Pixels @ 207,144*/ 0, 7, 0x03, 0x03, 0x0F, 0x0C, 0x03, 0x03, 0x04,
  /* RLE: 092 Pixels @ 214,144*/ 92, 0x00,
  /* RLE: 003 Pixels @ 306,144*/ 3, 0x06,
  /* RLE: 017 Pixels @ 309,144*/ 17, 0x00,
  /* ABS: 005 Pixels @ 326,144*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 331,144*/ 36, 0x01,
  /* ABS: 002 Pixels @ 367,144*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 369,144*/ 9, 0x03,
  /* ABS: 002 Pixels @ 378,144*/ 0, 2, 0x04, 0x04,
  /* RLE: 020 Pixels @ 380,144*/ 20, 0x01,
  /* ABS: 005 Pixels @ 000,145*/ 0, 5, 0x05, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 031 Pixels @ 005,145*/ 31, 0x02,
  /* RLE: 001 Pixels @ 036,145*/ 1, 0x04,
  /* RLE: 006 Pixels @ 037,145*/ 6, 0x03,
  /* ABS: 003 Pixels @ 043,145*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 038 Pixels @ 046,145*/ 38, 0x00,
  /* RLE: 001 Pixels @ 084,145*/ 1, 0x0B,
  /* RLE: 008 Pixels @ 085,145*/ 8, 0x03,
  /* ABS: 002 Pixels @ 093,145*/ 0, 2, 0x04, 0x0B,
  /* RLE: 070 Pixels @ 095,145*/ 70, 0x00,
  /* RLE: 004 Pixels @ 165,145*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 169,145*/ 34, 0x05,
  /* ABS: 008 Pixels @ 203,145*/ 0, 8, 0x0A, 0x12, 0x04, 0x04, 0x03, 0x03, 0x06, 0x12,
  /* RLE: 004 Pixels @ 211,145*/ 4, 0x03,
  /* RLE: 091 Pixels @ 215,145*/ 91, 0x00,
  /* ABS: 004 Pixels @ 306,145*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 017 Pixels @ 310,145*/ 17, 0x00,
  /* ABS: 005 Pixels @ 327,145*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 332,145*/ 36, 0x01,
  /* ABS: 002 Pixels @ 368,145*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 370,145*/ 9, 0x03,
  /* ABS: 002 Pixels @ 379,145*/ 0, 2, 0x04, 0x04,
  /* RLE: 019 Pixels @ 381,145*/ 19, 0x01,
  /* ABS: 007 Pixels @ 000,146*/ 0, 7, 0x00, 0x00, 0x1A, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 029 Pixels @ 007,146*/ 29, 0x02,
  /* RLE: 001 Pixels @ 036,146*/ 1, 0x04,
  /* RLE: 006 Pixels @ 037,146*/ 6, 0x03,
  /* ABS: 002 Pixels @ 043,146*/ 0, 2, 0x04, 0x0B,
  /* RLE: 039 Pixels @ 045,146*/ 39, 0x00,
  /* ABS: 002 Pixels @ 084,146*/ 0, 2, 0x0B, 0x04,
  /* RLE: 008 Pixels @ 086,146*/ 8, 0x03,
  /* ABS: 003 Pixels @ 094,146*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 037 Pixels @ 097,146*/ 37, 0x00,
  /* ABS: 004 Pixels @ 134,146*/ 0, 4, 0x0B, 0x00, 0x00, 0x00,
  /* RLE: 022 Pixels @ 138,146*/ 22, 0x08,
  /* RLE: 001 Pixels @ 160,146*/ 1, 0x00,
  /* RLE: 004 Pixels @ 161,146*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 165,146*/ 34, 0x05,
  /* RLE: 004 Pixels @ 199,146*/ 4, 0x0A,
  /* RLE: 003 Pixels @ 203,146*/ 3, 0x00,
  /* RLE: 003 Pixels @ 206,146*/ 3, 0x03,
  /* ABS: 005 Pixels @ 209,146*/ 0, 5, 0x0E, 0x11, 0x0E, 0x06, 0x08,
  /* RLE: 093 Pixels @ 214,146*/ 93, 0x00,
  /* ABS: 004 Pixels @ 307,146*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 017 Pixels @ 311,146*/ 17, 0x00,
  /* ABS: 005 Pixels @ 328,146*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 037 Pixels @ 333,146*/ 37, 0x01,
  /* ABS: 002 Pixels @ 370,146*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 372,146*/ 9, 0x03,
  /* ABS: 002 Pixels @ 381,146*/ 0, 2, 0x04, 0x04,
  /* RLE: 017 Pixels @ 383,146*/ 17, 0x01,
  /* ABS: 009 Pixels @ 000,147*/ 0, 9, 0x12, 0x00, 0x03, 0x03, 0x19, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 028 Pixels @ 009,147*/ 28, 0x02,
  /* RLE: 007 Pixels @ 037,147*/ 7, 0x03,
  /* ABS: 004 Pixels @ 044,147*/ 0, 4, 0x04, 0x0B, 0x00, 0x0B,
  /* RLE: 037 Pixels @ 048,147*/ 37, 0x00,
  /* ABS: 002 Pixels @ 085,147*/ 0, 2, 0x0B, 0x04,
  /* RLE: 007 Pixels @ 087,147*/ 7, 0x03,
  /* ABS: 002 Pixels @ 094,147*/ 0, 2, 0x04, 0x04,
  /* RLE: 039 Pixels @ 096,147*/ 39, 0x00,
  /* ABS: 003 Pixels @ 135,147*/ 0, 3, 0x0B, 0x00, 0x08,
  /* RLE: 022 Pixels @ 138,147*/ 22, 0x10,
  /* RLE: 001 Pixels @ 160,147*/ 1, 0x08,
  /* RLE: 034 Pixels @ 161,147*/ 34, 0x05,
  /* RLE: 004 Pixels @ 195,147*/ 4, 0x0A,
  /* RLE: 006 Pixels @ 199,147*/ 6, 0x00,
  /* ABS: 003 Pixels @ 205,147*/ 0, 3, 0x03, 0x0C, 0x06,
  /* RLE: 004 Pixels @ 208,147*/ 4, 0x08,
  /* ABS: 004 Pixels @ 212,147*/ 0, 4, 0x11, 0x12, 0x00, 0x03,
  /* RLE: 092 Pixels @ 216,147*/ 92, 0x00,
  /* ABS: 004 Pixels @ 308,147*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 312,147*/ 18, 0x00,
  /* RLE: 004 Pixels @ 330,147*/ 4, 0x06,
  /* RLE: 037 Pixels @ 334,147*/ 37, 0x01,
  /* ABS: 002 Pixels @ 371,147*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 373,147*/ 9, 0x03,
  /* ABS: 002 Pixels @ 382,147*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 384,147*/ 16, 0x01,
  /* ABS: 011 Pixels @ 000,148*/ 0, 11, 0x08, 0x06, 0x06, 0x0B, 0x03, 0x19, 0x05, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 026 Pixels @ 011,148*/ 26, 0x02,
  /* RLE: 001 Pixels @ 037,148*/ 1, 0x04,
  /* RLE: 006 Pixels @ 038,148*/ 6, 0x03,
  /* RLE: 001 Pixels @ 044,148*/ 1, 0x04,
  /* RLE: 040 Pixels @ 045,148*/ 40, 0x00,
  /* ABS: 003 Pixels @ 085,148*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 007 Pixels @ 088,148*/ 7, 0x03,
  /* ABS: 003 Pixels @ 095,148*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 036 Pixels @ 098,148*/ 36, 0x00,
  /* ABS: 005 Pixels @ 134,148*/ 0, 5, 0x0B, 0x00, 0x08, 0x10, 0x10,
  /* RLE: 020 Pixels @ 139,148*/ 20, 0x08,
  /* ABS: 003 Pixels @ 159,148*/ 0, 3, 0x10, 0x10, 0x08,
  /* RLE: 030 Pixels @ 162,148*/ 30, 0x05,
  /* RLE: 003 Pixels @ 192,148*/ 3, 0x0A,
  /* RLE: 010 Pixels @ 195,148*/ 10, 0x00,
  /* ABS: 006 Pixels @ 205,148*/ 0, 6, 0x03, 0x12, 0x11, 0x12, 0x0C, 0x0B,
  /* RLE: 004 Pixels @ 211,148*/ 4, 0x03,
  /* RLE: 094 Pixels @ 215,148*/ 94, 0x00,
  /* ABS: 004 Pixels @ 309,148*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 018 Pixels @ 313,148*/ 18, 0x00,
  /* RLE: 004 Pixels @ 331,148*/ 4, 0x06,
  /* RLE: 037 Pixels @ 335,148*/ 37, 0x01,
  /* ABS: 002 Pixels @ 372,148*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 374,148*/ 9, 0x03,
  /* ABS: 002 Pixels @ 383,148*/ 0, 2, 0x04, 0x04,
  /* RLE: 015 Pixels @ 385,148*/ 15, 0x01,
  /* ABS: 004 Pixels @ 000,149*/ 0, 4, 0x0D, 0x0B, 0x0C, 0x08,
  /* RLE: 004 Pixels @ 004,149*/ 4, 0x00,
  /* ABS: 005 Pixels @ 008,149*/ 0, 5, 0x19, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 024 Pixels @ 013,149*/ 24, 0x02,
  /* RLE: 001 Pixels @ 037,149*/ 1, 0x04,
  /* RLE: 006 Pixels @ 038,149*/ 6, 0x03,
  /* ABS: 002 Pixels @ 044,149*/ 0, 2, 0x04, 0x0B,
  /* RLE: 041 Pixels @ 046,149*/ 41, 0x00,
  /* ABS: 002 Pixels @ 087,149*/ 0, 2, 0x0B, 0x04,
  /* RLE: 007 Pixels @ 089,149*/ 7, 0x03,
  /* ABS: 003 Pixels @ 096,149*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 036 Pixels @ 099,149*/ 36, 0x00,
  /* ABS: 004 Pixels @ 135,149*/ 0, 4, 0x0B, 0x08, 0x10, 0x08,
  /* RLE: 020 Pixels @ 139,149*/ 20, 0x10,
  /* ABS: 003 Pixels @ 159,149*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 026 Pixels @ 162,149*/ 26, 0x05,
  /* RLE: 004 Pixels @ 188,149*/ 4, 0x0A,
  /* RLE: 014 Pixels @ 192,149*/ 14, 0x00,
  /* RLE: 004 Pixels @ 206,149*/ 4, 0x03,
  /* ABS: 005 Pixels @ 210,149*/ 0, 5, 0x00, 0x0B, 0x03, 0x03, 0x03,
  /* RLE: 095 Pixels @ 215,149*/ 95, 0x00,
  /* ABS: 004 Pixels @ 310,149*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 314,149*/ 18, 0x00,
  /* ABS: 004 Pixels @ 332,149*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 336,149*/ 37, 0x01,
  /* ABS: 002 Pixels @ 373,149*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 375,149*/ 9, 0x03,
  /* ABS: 002 Pixels @ 384,149*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 386,149*/ 14, 0x01,
  /* ABS: 015 Pixels @ 000,150*/ 0, 15, 0x0B, 0x03, 0x0B, 0x08, 0x0B, 0x03, 0x0C, 0x0B, 0x03, 0x03, 0x1A, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 022 Pixels @ 015,150*/ 22, 0x02,
  /* RLE: 001 Pixels @ 037,150*/ 1, 0x04,
  /* RLE: 006 Pixels @ 038,150*/ 6, 0x03,
  /* RLE: 001 Pixels @ 044,150*/ 1, 0x04,
  /* RLE: 042 Pixels @ 045,150*/ 42, 0x00,
  /* ABS: 002 Pixels @ 087,150*/ 0, 2, 0x0B, 0x04,
  /* RLE: 008 Pixels @ 089,150*/ 8, 0x03,
  /* ABS: 004 Pixels @ 097,150*/ 0, 4, 0x04, 0x00, 0x00, 0x0B,
  /* RLE: 032 Pixels @ 101,150*/ 32, 0x00,
  /* ABS: 006 Pixels @ 133,150*/ 0, 6, 0x0B, 0x00, 0x00, 0x08, 0x10, 0x08,
  /* RLE: 020 Pixels @ 139,150*/ 20, 0x10,
  /* ABS: 003 Pixels @ 159,150*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 022 Pixels @ 162,150*/ 22, 0x05,
  /* RLE: 004 Pixels @ 184,150*/ 4, 0x0A,
  /* RLE: 017 Pixels @ 188,150*/ 17, 0x00,
  /* ABS: 011 Pixels @ 205,150*/ 0, 11, 0x03, 0x03, 0x0C, 0x06, 0x0F, 0x08, 0x08, 0x03, 0x04, 0x0B, 0x03,
  /* RLE: 095 Pixels @ 216,150*/ 95, 0x00,
  /* ABS: 005 Pixels @ 311,150*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 017 Pixels @ 316,150*/ 17, 0x00,
  /* ABS: 005 Pixels @ 333,150*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 338,150*/ 36, 0x01,
  /* ABS: 002 Pixels @ 374,150*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 376,150*/ 9, 0x03,
  /* ABS: 002 Pixels @ 385,150*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 387,150*/ 13, 0x01,
  /* ABS: 017 Pixels @ 000,151*/ 0, 17, 0x03, 0x03, 0x06, 0x0F, 0x03, 0x06, 0x11, 0x11, 0x11, 0x00, 0x00, 0x19, 0x03, 0x03, 0x03, 0x0C, 0x0A,
  /* RLE: 020 Pixels @ 017,151*/ 20, 0x02,
  /* RLE: 001 Pixels @ 037,151*/ 1, 0x04,
  /* RLE: 006 Pixels @ 038,151*/ 6, 0x03,
  /* ABS: 003 Pixels @ 044,151*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 041 Pixels @ 047,151*/ 41, 0x00,
  /* ABS: 002 Pixels @ 088,151*/ 0, 2, 0x0B, 0x04,
  /* RLE: 007 Pixels @ 090,151*/ 7, 0x03,
  /* ABS: 003 Pixels @ 097,151*/ 0, 3, 0x04, 0x04, 0x0B,
  /* RLE: 034 Pixels @ 100,151*/ 34, 0x00,
  /* ABS: 008 Pixels @ 134,151*/ 0, 8, 0x0B, 0x00, 0x08, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 005 Pixels @ 142,151*/ 5, 0x10,
  /* RLE: 003 Pixels @ 147,151*/ 3, 0x08,
  /* RLE: 003 Pixels @ 150,151*/ 3, 0x10,
  /* RLE: 004 Pixels @ 153,151*/ 4, 0x08,
  /* ABS: 005 Pixels @ 157,151*/ 0, 5, 0x10, 0x10, 0x08, 0x10, 0x08,
  /* RLE: 018 Pixels @ 162,151*/ 18, 0x05,
  /* RLE: 004 Pixels @ 180,151*/ 4, 0x0A,
  /* RLE: 022 Pixels @ 184,151*/ 22, 0x00,
  /* ABS: 010 Pixels @ 206,151*/ 0, 10, 0x04, 0x08, 0x11, 0x0E, 0x0C, 0x00, 0x03, 0x12, 0x0C, 0x03,
  /* RLE: 096 Pixels @ 216,151*/ 96, 0x00,
  /* ABS: 005 Pixels @ 312,151*/ 0, 5, 0x0D, 0x06, 0x06, 0x0D, 0x06,
  /* RLE: 017 Pixels @ 317,151*/ 17, 0x00,
  /* ABS: 005 Pixels @ 334,151*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 339,151*/ 36, 0x01,
  /* ABS: 002 Pixels @ 375,151*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 377,151*/ 9, 0x03,
  /* ABS: 002 Pixels @ 386,151*/ 0, 2, 0x04, 0x04,
  /* RLE: 012 Pixels @ 388,151*/ 12, 0x01,
  /* ABS: 019 Pixels @ 000,152*/ 0, 19, 0x03, 0x00, 0x08, 0x0C, 0x03, 0x11, 0x0D, 0x03, 0x12, 0x12, 0x03, 0x03, 0x0B, 0x0F, 0x04, 0x19, 0x0A, 0x0A, 0x0A,
  /* RLE: 018 Pixels @ 019,152*/ 18, 0x02,
  /* RLE: 001 Pixels @ 037,152*/ 1, 0x04,
  /* RLE: 007 Pixels @ 038,152*/ 7, 0x03,
  /* RLE: 001 Pixels @ 045,152*/ 1, 0x0B,
  /* RLE: 042 Pixels @ 046,152*/ 42, 0x00,
  /* ABS: 003 Pixels @ 088,152*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 007 Pixels @ 091,152*/ 7, 0x03,
  /* ABS: 002 Pixels @ 098,152*/ 0, 2, 0x04, 0x04,
  /* RLE: 033 Pixels @ 100,152*/ 33, 0x00,
  /* ABS: 009 Pixels @ 133,152*/ 0, 9, 0x0B, 0x00, 0x00, 0x08, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 004 Pixels @ 142,152*/ 4, 0x10,
  /* ABS: 008 Pixels @ 146,152*/ 0, 8, 0x08, 0x10, 0x10, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 005 Pixels @ 154,152*/ 5, 0x10,
  /* ABS: 003 Pixels @ 159,152*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 015 Pixels @ 162,152*/ 15, 0x05,
  /* RLE: 003 Pixels @ 177,152*/ 3, 0x0A,
  /* RLE: 025 Pixels @ 180,152*/ 25, 0x00,
  /* ABS: 003 Pixels @ 205,152*/ 0, 3, 0x03, 0x0E, 0x0D,
  /* RLE: 006 Pixels @ 208,152*/ 6, 0x03,
  /* ABS: 002 Pixels @ 214,152*/ 0, 2, 0x00, 0x03,
  /* RLE: 097 Pixels @ 216,152*/ 97, 0x00,
  /* ABS: 005 Pixels @ 313,152*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 017 Pixels @ 318,152*/ 17, 0x00,
  /* RLE: 005 Pixels @ 335,152*/ 5, 0x06,
  /* RLE: 036 Pixels @ 340,152*/ 36, 0x01,
  /* RLE: 003 Pixels @ 376,152*/ 3, 0x04,
  /* RLE: 008 Pixels @ 379,152*/ 8, 0x03,
  /* RLE: 003 Pixels @ 387,152*/ 3, 0x04,
  /* RLE: 010 Pixels @ 390,152*/ 10, 0x01,
  /* ABS: 021 Pixels @ 000,153*/ 0, 21, 0x03, 0x06, 0x0F, 0x03, 0x00, 0x0B, 0x08, 0x0D, 0x0B, 0x0B, 0x03, 0x0E, 0x08, 0x11, 0x03, 0x03, 0x00, 0x1A, 0x0A, 0x0A, 0x0A,
  /* RLE: 017 Pixels @ 021,153*/ 17, 0x02,
  /* RLE: 001 Pixels @ 038,153*/ 1, 0x04,
  /* RLE: 006 Pixels @ 039,153*/ 6, 0x03,
  /* ABS: 002 Pixels @ 045,153*/ 0, 2, 0x04, 0x0B,
  /* RLE: 043 Pixels @ 047,153*/ 43, 0x00,
  /* ABS: 002 Pixels @ 090,153*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 092,153*/ 7, 0x03,
  /* ABS: 004 Pixels @ 099,153*/ 0, 4, 0x04, 0x04, 0x00, 0x0B,
  /* RLE: 026 Pixels @ 103,153*/ 26, 0x00,
  /* ABS: 013 Pixels @ 129,153*/ 0, 13, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B, 0x0A, 0x08, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 004 Pixels @ 142,153*/ 4, 0x10,
  /* ABS: 006 Pixels @ 146,153*/ 0, 6, 0x08, 0x10, 0x10, 0x10, 0x08, 0x10,
  /* RLE: 004 Pixels @ 152,153*/ 4, 0x08,
  /* RLE: 003 Pixels @ 156,153*/ 3, 0x10,
  /* ABS: 003 Pixels @ 159,153*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 011 Pixels @ 162,153*/ 11, 0x05,
  /* RLE: 004 Pixels @ 173,153*/ 4, 0x0A,
  /* RLE: 028 Pixels @ 177,153*/ 28, 0x00,
  /* ABS: 011 Pixels @ 205,153*/ 0, 11, 0x03, 0x0B, 0x11, 0x03, 0x03, 0x00, 0x0C, 0x0C, 0x04, 0x08, 0x03,
  /* RLE: 099 Pixels @ 216,153*/ 99, 0x00,
  /* ABS: 004 Pixels @ 315,153*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 319,153*/ 18, 0x00,
  /* RLE: 004 Pixels @ 337,153*/ 4, 0x06,
  /* RLE: 037 Pixels @ 341,153*/ 37, 0x01,
  /* ABS: 002 Pixels @ 378,153*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 380,153*/ 9, 0x03,
  /* ABS: 002 Pixels @ 389,153*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 391,153*/ 9, 0x01,
  /* ABS: 023 Pixels @ 000,154*/ 0, 23, 0x00, 0x00, 0x04, 0x03, 0x08, 0x0C, 0x00, 0x0F, 0x0D, 0x03, 0x03, 0x0C, 0x08, 0x08, 0x04, 0x06, 0x00, 0x03, 0x03, 0x1A, 0x0A, 0x0A, 0x0A,
  /* RLE: 015 Pixels @ 023,154*/ 15, 0x02,
  /* RLE: 001 Pixels @ 038,154*/ 1, 0x04,
  /* RLE: 006 Pixels @ 039,154*/ 6, 0x03,
  /* RLE: 001 Pixels @ 045,154*/ 1, 0x04,
  /* RLE: 045 Pixels @ 046,154*/ 45, 0x00,
  /* RLE: 001 Pixels @ 091,154*/ 1, 0x04,
  /* RLE: 008 Pixels @ 092,154*/ 8, 0x03,
  /* ABS: 002 Pixels @ 100,154*/ 0, 2, 0x04, 0x0B,
  /* RLE: 018 Pixels @ 102,154*/ 18, 0x00,
  /* RLE: 001 Pixels @ 120,154*/ 1, 0x0B,
  /* RLE: 004 Pixels @ 121,154*/ 4, 0x00,
  /* ABS: 006 Pixels @ 125,154*/ 0, 6, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 131,154*/ 4, 0x0A,
  /* ABS: 007 Pixels @ 135,154*/ 0, 7, 0x05, 0x08, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 005 Pixels @ 142,154*/ 5, 0x10,
  /* RLE: 003 Pixels @ 147,154*/ 3, 0x08,
  /* ABS: 012 Pixels @ 150,154*/ 0, 12, 0x10, 0x10, 0x08, 0x10, 0x10, 0x10, 0x08, 0x10, 0x10, 0x08, 0x10, 0x08,
  /* RLE: 007 Pixels @ 162,154*/ 7, 0x05,
  /* RLE: 004 Pixels @ 169,154*/ 4, 0x0A,
  /* RLE: 033 Pixels @ 173,154*/ 33, 0x00,
  /* ABS: 002 Pixels @ 206,154*/ 0, 2, 0x03, 0x06,
  /* RLE: 004 Pixels @ 208,154*/ 4, 0x08,
  /* ABS: 004 Pixels @ 212,154*/ 0, 4, 0x06, 0x03, 0x00, 0x03,
  /* RLE: 100 Pixels @ 216,154*/ 100, 0x00,
  /* ABS: 004 Pixels @ 316,154*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 320,154*/ 18, 0x00,
  /* ABS: 004 Pixels @ 338,154*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 342,154*/ 37, 0x01,
  /* ABS: 002 Pixels @ 379,154*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 381,154*/ 9, 0x03,
  /* ABS: 002 Pixels @ 390,154*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 392,154*/ 8, 0x01,
  /* ABS: 022 Pixels @ 000,155*/ 0, 22, 0x0B, 0x0B, 0x04, 0x03, 0x0E, 0x0F, 0x0C, 0x06, 0x0F, 0x03, 0x03, 0x11, 0x11, 0x00, 0x0C, 0x08, 0x11, 0x11, 0x00, 0x00, 0x15, 0x19,
  /* RLE: 004 Pixels @ 022,155*/ 4, 0x0A,
  /* RLE: 012 Pixels @ 026,155*/ 12, 0x02,
  /* RLE: 001 Pixels @ 038,155*/ 1, 0x04,
  /* RLE: 006 Pixels @ 039,155*/ 6, 0x03,
  /* ABS: 002 Pixels @ 045,155*/ 0, 2, 0x04, 0x0B,
  /* RLE: 045 Pixels @ 047,155*/ 45, 0x00,
  /* RLE: 001 Pixels @ 092,155*/ 1, 0x04,
  /* RLE: 008 Pixels @ 093,155*/ 8, 0x03,
  /* RLE: 001 Pixels @ 101,155*/ 1, 0x04,
  /* RLE: 017 Pixels @ 102,155*/ 17, 0x00,
  /* ABS: 008 Pixels @ 119,155*/ 0, 8, 0x0B, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 127,155*/ 4, 0x0A,
  /* RLE: 005 Pixels @ 131,155*/ 5, 0x05,
  /* ABS: 006 Pixels @ 136,155*/ 0, 6, 0x08, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 004 Pixels @ 142,155*/ 4, 0x10,
  /* ABS: 005 Pixels @ 146,155*/ 0, 5, 0x08, 0x10, 0x10, 0x10, 0x08,
  /* RLE: 005 Pixels @ 151,155*/ 5, 0x10,
  /* ABS: 009 Pixels @ 156,155*/ 0, 9, 0x08, 0x10, 0x10, 0x08, 0x10, 0x08, 0x05, 0x05, 0x05,
  /* RLE: 004 Pixels @ 165,155*/ 4, 0x0A,
  /* RLE: 037 Pixels @ 169,155*/ 37, 0x00,
  /* ABS: 005 Pixels @ 206,155*/ 0, 5, 0x03, 0x06, 0x12, 0x0C, 0x0B,
  /* RLE: 005 Pixels @ 211,155*/ 5, 0x03,
  /* RLE: 101 Pixels @ 216,155*/ 101, 0x00,
  /* ABS: 004 Pixels @ 317,155*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 321,155*/ 18, 0x00,
  /* ABS: 005 Pixels @ 339,155*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 344,155*/ 36, 0x01,
  /* ABS: 002 Pixels @ 380,155*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 382,155*/ 9, 0x03,
  /* ABS: 002 Pixels @ 391,155*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 393,155*/ 7, 0x01,
  /* RLE: 003 Pixels @ 000,156*/ 3, 0x02,
  /* ABS: 025 Pixels @ 003,156*/ 0, 25, 0x04, 0x03, 0x0B, 0x12, 0x06, 0x04, 0x03, 0x04, 0x08, 0x04, 0x03, 0x0F, 0x0D, 0x04, 0x12, 0x03, 0x0B, 0x00, 0x03, 0x03, 0x1A, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 010 Pixels @ 028,156*/ 10, 0x02,
  /* RLE: 001 Pixels @ 038,156*/ 1, 0x04,
  /* RLE: 006 Pixels @ 039,156*/ 6, 0x03,
  /* RLE: 001 Pixels @ 045,156*/ 1, 0x04,
  /* RLE: 045 Pixels @ 046,156*/ 45, 0x00,
  /* ABS: 003 Pixels @ 091,156*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 007 Pixels @ 094,156*/ 7, 0x03,
  /* RLE: 001 Pixels @ 101,156*/ 1, 0x04,
  /* RLE: 016 Pixels @ 102,156*/ 16, 0x00,
  /* ABS: 005 Pixels @ 118,156*/ 0, 5, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 123,156*/ 4, 0x0A,
  /* RLE: 009 Pixels @ 127,156*/ 9, 0x05,
  /* ABS: 006 Pixels @ 136,156*/ 0, 6, 0x08, 0x10, 0x08, 0x10, 0x10, 0x08,
  /* RLE: 004 Pixels @ 142,156*/ 4, 0x10,
  /* ABS: 019 Pixels @ 146,156*/ 0, 19, 0x08, 0x10, 0x10, 0x10, 0x08, 0x10, 0x08, 0x10, 0x10, 0x10, 0x08, 0x10, 0x10, 0x08, 0x10, 0x08, 0x0A, 0x0A, 0x0A,
  /* RLE: 042 Pixels @ 165,156*/ 42, 0x00,
  /* RLE: 006 Pixels @ 207,156*/ 6, 0x03,
  /* ABS: 004 Pixels @ 213,156*/ 0, 4, 0x00, 0x0C, 0x12, 0x03,
  /* RLE: 101 Pixels @ 217,156*/ 101, 0x00,
  /* ABS: 005 Pixels @ 318,156*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 017 Pixels @ 323,156*/ 17, 0x00,
  /* ABS: 005 Pixels @ 340,156*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 345,156*/ 36, 0x01,
  /* ABS: 002 Pixels @ 381,156*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 383,156*/ 9, 0x03,
  /* ABS: 002 Pixels @ 392,156*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 394,156*/ 6, 0x01,
  /* RLE: 004 Pixels @ 000,157*/ 4, 0x02,
  /* ABS: 002 Pixels @ 004,157*/ 0, 2, 0x00, 0x00,
  /* RLE: 004 Pixels @ 006,157*/ 4, 0x03,
  /* ABS: 020 Pixels @ 010,157*/ 0, 20, 0x12, 0x11, 0x03, 0x0C, 0x08, 0x0B, 0x03, 0x03, 0x04, 0x08, 0x08, 0x11, 0x00, 0x03, 0x05, 0x05, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 008 Pixels @ 030,157*/ 8, 0x02,
  /* RLE: 001 Pixels @ 038,157*/ 1, 0x04,
  /* RLE: 006 Pixels @ 039,157*/ 6, 0x03,
  /* ABS: 003 Pixels @ 045,157*/ 0, 3, 0x04, 0x00, 0x0B,
  /* RLE: 045 Pixels @ 048,157*/ 45, 0x00,
  /* ABS: 002 Pixels @ 093,157*/ 0, 2, 0x0B, 0x04,
  /* RLE: 006 Pixels @ 095,157*/ 6, 0x03,
  /* RLE: 001 Pixels @ 101,157*/ 1, 0x04,
  /* RLE: 012 Pixels @ 102,157*/ 12, 0x00,
  /* ABS: 009 Pixels @ 114,157*/ 0, 9, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B, 0x0A, 0x0A, 0x0A,
  /* RLE: 013 Pixels @ 123,157*/ 13, 0x05,
  /* ABS: 005 Pixels @ 136,157*/ 0, 5, 0x08, 0x10, 0x08, 0x10, 0x10,
  /* RLE: 005 Pixels @ 141,157*/ 5, 0x08,
  /* ABS: 016 Pixels @ 146,157*/ 0, 16, 0x10, 0x08, 0x08, 0x08, 0x10, 0x10, 0x10, 0x08, 0x08, 0x08, 0x10, 0x10, 0x10, 0x08, 0x10, 0x08,
  /* RLE: 045 Pixels @ 162,157*/ 45, 0x00,
  /* ABS: 009 Pixels @ 207,157*/ 0, 9, 0x03, 0x00, 0x0C, 0x12, 0x11, 0x08, 0x08, 0x08, 0x11,
  /* RLE: 103 Pixels @ 216,157*/ 103, 0x00,
  /* ABS: 005 Pixels @ 319,157*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 017 Pixels @ 324,157*/ 17, 0x00,
  /* RLE: 005 Pixels @ 341,157*/ 5, 0x06,
  /* RLE: 036 Pixels @ 346,157*/ 36, 0x01,
  /* ABS: 002 Pixels @ 382,157*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 384,157*/ 9, 0x03,
  /* ABS: 002 Pixels @ 393,157*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 395,157*/ 5, 0x01,
  /* RLE: 007 Pixels @ 000,158*/ 7, 0x02,
  /* ABS: 025 Pixels @ 007,158*/ 0, 25, 0x0A, 0x0A, 0x03, 0x0E, 0x08, 0x0C, 0x0F, 0x06, 0x03, 0x03, 0x03, 0x00, 0x03, 0x03, 0x12, 0x11, 0x03, 0x19, 0x00, 0x00, 0x19, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 006 Pixels @ 032,158*/ 6, 0x02,
  /* RLE: 001 Pixels @ 038,158*/ 1, 0x04,
  /* RLE: 007 Pixels @ 039,158*/ 7, 0x03,
  /* RLE: 001 Pixels @ 046,158*/ 1, 0x0B,
  /* RLE: 046 Pixels @ 047,158*/ 46, 0x00,
  /* ABS: 003 Pixels @ 093,158*/ 0, 3, 0x0B, 0x04, 0x04,
  /* RLE: 005 Pixels @ 096,158*/ 5, 0x03,
  /* RLE: 001 Pixels @ 101,158*/ 1, 0x04,
  /* RLE: 008 Pixels @ 102,158*/ 8, 0x00,
  /* ABS: 006 Pixels @ 110,158*/ 0, 6, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 116,158*/ 4, 0x0A,
  /* RLE: 016 Pixels @ 120,158*/ 16, 0x05,
  /* ABS: 003 Pixels @ 136,158*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 020 Pixels @ 139,158*/ 20, 0x10,
  /* ABS: 003 Pixels @ 159,158*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 045 Pixels @ 162,158*/ 45, 0x00,
  /* ABS: 010 Pixels @ 207,158*/ 0, 10, 0x03, 0x12, 0x08, 0x11, 0x12, 0x0C, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 103 Pixels @ 217,158*/ 103, 0x00,
  /* ABS: 005 Pixels @ 320,158*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 325,158*/ 18, 0x00,
  /* RLE: 004 Pixels @ 343,158*/ 4, 0x06,
  /* RLE: 037 Pixels @ 347,158*/ 37, 0x01,
  /* ABS: 002 Pixels @ 384,158*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 386,158*/ 8, 0x03,
  /* ABS: 002 Pixels @ 394,158*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 396,158*/ 4, 0x01,
  /* RLE: 009 Pixels @ 000,159*/ 9, 0x02,
  /* ABS: 025 Pixels @ 009,159*/ 0, 25, 0x04, 0x03, 0x0B, 0x00, 0x06, 0x0B, 0x03, 0x03, 0x0D, 0x08, 0x08, 0x06, 0x0F, 0x06, 0x03, 0x03, 0x0C, 0x0B, 0x03, 0x03, 0x1A, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 005 Pixels @ 034,159*/ 5, 0x02,
  /* RLE: 001 Pixels @ 039,159*/ 1, 0x04,
  /* RLE: 006 Pixels @ 040,159*/ 6, 0x03,
  /* ABS: 002 Pixels @ 046,159*/ 0, 2, 0x04, 0x0B,
  /* RLE: 047 Pixels @ 048,159*/ 47, 0x00,
  /* ABS: 017 Pixels @ 095,159*/ 0, 17, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x04, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 112,159*/ 4, 0x0A,
  /* RLE: 020 Pixels @ 116,159*/ 20, 0x05,
  /* ABS: 003 Pixels @ 136,159*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 020 Pixels @ 139,159*/ 20, 0x10,
  /* ABS: 003 Pixels @ 159,159*/ 0, 3, 0x08, 0x10, 0x08,
  /* RLE: 045 Pixels @ 162,159*/ 45, 0x00,
  /* ABS: 010 Pixels @ 207,159*/ 0, 10, 0x03, 0x00, 0x03, 0x03, 0x00, 0x04, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 105 Pixels @ 217,159*/ 105, 0x00,
  /* ABS: 004 Pixels @ 322,159*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 326,159*/ 18, 0x00,
  /* ABS: 004 Pixels @ 344,159*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 348,159*/ 37, 0x01,
  /* ABS: 002 Pixels @ 385,159*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 387,159*/ 9, 0x03,
  /* ABS: 004 Pixels @ 396,159*/ 0, 4, 0x04, 0x04, 0x01, 0x01,
  /* RLE: 010 Pixels @ 000,160*/ 10, 0x02,
  /* ABS: 030 Pixels @ 010,160*/ 0, 30, 0x00, 0x04, 0x03, 0x00, 0x00, 0x15, 0x00, 0x08, 0x0C, 0x03, 0x0C, 0x08, 0x0B, 0x03, 0x06, 0x11, 0x11, 0x11, 0x00, 0x00, 0x05, 0x05, 0x05, 0x0A, 0x0A, 0x0A, 0x02, 0x02, 0x02, 0x04,
  /* RLE: 006 Pixels @ 040,160*/ 6, 0x03,
  /* RLE: 001 Pixels @ 046,160*/ 1, 0x04,
  /* RLE: 048 Pixels @ 047,160*/ 48, 0x00,
  /* ABS: 013 Pixels @ 095,160*/ 0, 13, 0x04, 0x0B, 0x04, 0x0B, 0x04, 0x04, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 108,160*/ 4, 0x0A,
  /* RLE: 024 Pixels @ 112,160*/ 24, 0x05,
  /* ABS: 003 Pixels @ 136,160*/ 0, 3, 0x08, 0x10, 0x10,
  /* RLE: 020 Pixels @ 139,160*/ 20, 0x08,
  /* ABS: 003 Pixels @ 159,160*/ 0, 3, 0x10, 0x10, 0x08,
  /* RLE: 045 Pixels @ 162,160*/ 45, 0x00,
  /* ABS: 010 Pixels @ 207,160*/ 0, 10, 0x03, 0x00, 0x0D, 0x03, 0x11, 0x08, 0x0F, 0x03, 0x03, 0x04,
  /* RLE: 106 Pixels @ 217,160*/ 106, 0x00,
  /* ABS: 004 Pixels @ 323,160*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 018 Pixels @ 327,160*/ 18, 0x00,
  /* ABS: 004 Pixels @ 345,160*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 349,160*/ 37, 0x01,
  /* ABS: 002 Pixels @ 386,160*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 388,160*/ 9, 0x03,
  /* ABS: 003 Pixels @ 397,160*/ 0, 3, 0x04, 0x04, 0x01,
  /* RLE: 013 Pixels @ 000,161*/ 13, 0x02,
  /* ABS: 027 Pixels @ 013,161*/ 0, 27, 0x0A, 0x0A, 0x06, 0x03, 0x06, 0x11, 0x0C, 0x0F, 0x11, 0x03, 0x03, 0x11, 0x0D, 0x03, 0x12, 0x12, 0x03, 0x15, 0x00, 0x19, 0x05, 0x05, 0x0A, 0x0A, 0x0A, 0x02, 0x04,
  /* RLE: 006 Pixels @ 040,161*/ 6, 0x03,
  /* ABS: 002 Pixels @ 046,161*/ 0, 2, 0x04, 0x0B,
  /* RLE: 048 Pixels @ 048,161*/ 48, 0x00,
  /* ABS: 008 Pixels @ 096,161*/ 0, 8, 0x0B, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 104,161*/ 4, 0x0A,
  /* RLE: 029 Pixels @ 108,161*/ 29, 0x05,
  /* RLE: 001 Pixels @ 137,161*/ 1, 0x08,
  /* RLE: 022 Pixels @ 138,161*/ 22, 0x10,
  /* RLE: 001 Pixels @ 160,161*/ 1, 0x08,
  /* RLE: 047 Pixels @ 161,161*/ 47, 0x00,
  /* ABS: 009 Pixels @ 208,161*/ 0, 9, 0x0C, 0x11, 0x00, 0x08, 0x04, 0x0D, 0x04, 0x03, 0x04,
  /* RLE: 107 Pixels @ 217,161*/ 107, 0x00,
  /* ABS: 004 Pixels @ 324,161*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 328,161*/ 18, 0x00,
  /* ABS: 005 Pixels @ 346,161*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 036 Pixels @ 351,161*/ 36, 0x01,
  /* ABS: 002 Pixels @ 387,161*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 389,161*/ 9, 0x03,
  /* ABS: 002 Pixels @ 398,161*/ 0, 2, 0x04, 0x04,
  /* RLE: 015 Pixels @ 000,162*/ 15, 0x02,
  /* ABS: 025 Pixels @ 015,162*/ 0, 25, 0x0A, 0x00, 0x03, 0x0B, 0x0B, 0x08, 0x04, 0x03, 0x00, 0x0B, 0x08, 0x0D, 0x0B, 0x0B, 0x03, 0x0C, 0x0B, 0x03, 0x03, 0x1A, 0x05, 0x05, 0x0A, 0x0A, 0x0A,
  /* RLE: 006 Pixels @ 040,162*/ 6, 0x03,
  /* RLE: 001 Pixels @ 046,162*/ 1, 0x04,
  /* RLE: 048 Pixels @ 047,162*/ 48, 0x00,
  /* ABS: 005 Pixels @ 095,162*/ 0, 5, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 100,162*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 104,162*/ 34, 0x05,
  /* RLE: 022 Pixels @ 138,162*/ 22, 0x08,
  /* RLE: 047 Pixels @ 160,162*/ 47, 0x00,
  /* ABS: 010 Pixels @ 207,162*/ 0, 10, 0x03, 0x0C, 0x0C, 0x0B, 0x08, 0x00, 0x12, 0x0C, 0x03, 0x04,
  /* RLE: 108 Pixels @ 217,162*/ 108, 0x00,
  /* ABS: 004 Pixels @ 325,162*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 018 Pixels @ 329,162*/ 18, 0x00,
  /* RLE: 005 Pixels @ 347,162*/ 5, 0x06,
  /* RLE: 036 Pixels @ 352,162*/ 36, 0x01,
  /* ABS: 002 Pixels @ 388,162*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 390,162*/ 9, 0x03,
  /* RLE: 001 Pixels @ 399,162*/ 1, 0x04,
  /* RLE: 017 Pixels @ 000,163*/ 17, 0x02,
  /* ABS: 023 Pixels @ 017,163*/ 0, 23, 0x18, 0x0B, 0x03, 0x00, 0x00, 0x03, 0x08, 0x0C, 0x00, 0x0F, 0x0D, 0x03, 0x06, 0x11, 0x11, 0x11, 0x00, 0x00, 0x1A, 0x15, 0x15, 0x19, 0x0A,
  /* RLE: 006 Pixels @ 040,163*/ 6, 0x03,
  /* ABS: 004 Pixels @ 046,163*/ 0, 4, 0x04, 0x00, 0x00, 0x0B,
  /* RLE: 041 Pixels @ 050,163*/ 41, 0x00,
  /* ABS: 009 Pixels @ 091,163*/ 0, 9, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B, 0x0A, 0x0A, 0x0A,
  /* RLE: 035 Pixels @ 100,163*/ 35, 0x05,
  /* RLE: 004 Pixels @ 135,163*/ 4, 0x0A,
  /* RLE: 068 Pixels @ 139,163*/ 68, 0x00,
  /* ABS: 010 Pixels @ 207,163*/ 0, 10, 0x03, 0x04, 0x11, 0x04, 0x08, 0x0B, 0x0F, 0x04, 0x03, 0x03,
  /* RLE: 109 Pixels @ 217,163*/ 109, 0x00,
  /* ABS: 005 Pixels @ 326,163*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 017 Pixels @ 331,163*/ 17, 0x00,
  /* RLE: 005 Pixels @ 348,163*/ 5, 0x06,
  /* RLE: 036 Pixels @ 353,163*/ 36, 0x01,
  /* ABS: 002 Pixels @ 389,163*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 391,163*/ 9, 0x03,
  /* RLE: 019 Pixels @ 000,164*/ 19, 0x02,
  /* ABS: 022 Pixels @ 019,164*/ 0, 22, 0x18, 0x0E, 0x0C, 0x03, 0x0E, 0x0F, 0x04, 0x06, 0x0F, 0x03, 0x11, 0x0D, 0x03, 0x12, 0x12, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x1A,
  /* RLE: 006 Pixels @ 041,164*/ 6, 0x03,
  /* ABS: 013 Pixels @ 047,164*/ 0, 13, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 027 Pixels @ 060,164*/ 27, 0x00,
  /* ABS: 006 Pixels @ 087,164*/ 0, 6, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 093,164*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 097,164*/ 34, 0x05,
  /* RLE: 004 Pixels @ 131,164*/ 4, 0x0A,
  /* RLE: 073 Pixels @ 135,164*/ 73, 0x00,
  /* ABS: 010 Pixels @ 208,164*/ 0, 10, 0x03, 0x0D, 0x08, 0x0F, 0x03, 0x04, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 109 Pixels @ 218,164*/ 109, 0x00,
  /* ABS: 005 Pixels @ 327,164*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 332,164*/ 18, 0x00,
  /* RLE: 004 Pixels @ 350,164*/ 4, 0x06,
  /* RLE: 036 Pixels @ 354,164*/ 36, 0x01,
  /* RLE: 003 Pixels @ 390,164*/ 3, 0x04,
  /* RLE: 007 Pixels @ 393,164*/ 7, 0x03,
  /* RLE: 022 Pixels @ 000,165*/ 22, 0x02,
  /* ABS: 021 Pixels @ 022,165*/ 0, 21, 0x04, 0x03, 0x04, 0x12, 0x06, 0x04, 0x00, 0x0B, 0x08, 0x0D, 0x0B, 0x0B, 0x03, 0x0E, 0x08, 0x08, 0x0D, 0x00, 0x00, 0x05, 0x05,
  /* RLE: 004 Pixels @ 043,165*/ 4, 0x03,
  /* RLE: 003 Pixels @ 047,165*/ 3, 0x0A,
  /* ABS: 022 Pixels @ 050,165*/ 0, 22, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B,
  /* RLE: 011 Pixels @ 072,165*/ 11, 0x00,
  /* ABS: 006 Pixels @ 083,165*/ 0, 6, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 089,165*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 093,165*/ 34, 0x05,
  /* RLE: 004 Pixels @ 127,165*/ 4, 0x0A,
  /* RLE: 001 Pixels @ 131,165*/ 1, 0x0B,
  /* RLE: 077 Pixels @ 132,165*/ 77, 0x00,
  /* ABS: 009 Pixels @ 209,165*/ 0, 9, 0x03, 0x0B, 0x03, 0x00, 0x00, 0x0B, 0x03, 0x03, 0x04,
  /* RLE: 110 Pixels @ 218,165*/ 110, 0x00,
  /* ABS: 005 Pixels @ 328,165*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 333,165*/ 18, 0x00,
  /* ABS: 004 Pixels @ 351,165*/ 0, 4, 0x06, 0x03, 0x03, 0x06,
  /* RLE: 037 Pixels @ 355,165*/ 37, 0x01,
  /* ABS: 002 Pixels @ 392,165*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 394,165*/ 6, 0x03,
  /* RLE: 023 Pixels @ 000,166*/ 23, 0x02,
  /* RLE: 003 Pixels @ 023,166*/ 3, 0x00,
  /* ABS: 015 Pixels @ 026,166*/ 0, 15, 0x03, 0x03, 0x08, 0x0C, 0x00, 0x0F, 0x0D, 0x03, 0x04, 0x08, 0x0C, 0x03, 0x06, 0x12, 0x03,
  /* RLE: 009 Pixels @ 041,166*/ 9, 0x05,
  /* RLE: 012 Pixels @ 050,166*/ 12, 0x0A,
  /* ABS: 023 Pixels @ 062,166*/ 0, 23, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x00, 0x0B, 0x00, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 085,166*/ 4, 0x0A,
  /* RLE: 034 Pixels @ 089,166*/ 34, 0x05,
  /* RLE: 004 Pixels @ 123,166*/ 4, 0x0A,
  /* RLE: 082 Pixels @ 127,166*/ 82, 0x00,
  /* ABS: 008 Pixels @ 209,166*/ 0, 8, 0x03, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x06, 0x03,
  /* RLE: 113 Pixels @ 217,166*/ 113, 0x00,
  /* ABS: 004 Pixels @ 330,166*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 334,166*/ 18, 0x00,
  /* ABS: 004 Pixels @ 352,166*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 037 Pixels @ 356,166*/ 37, 0x01,
  /* ABS: 002 Pixels @ 393,166*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 395,166*/ 5, 0x03,
  /* RLE: 026 Pixels @ 000,167*/ 26, 0x02,
  /* ABS: 015 Pixels @ 026,167*/ 0, 15, 0x0A, 0x03, 0x0E, 0x0F, 0x0C, 0x06, 0x0F, 0x03, 0x06, 0x0F, 0x11, 0x0E, 0x12, 0x06, 0x03,
  /* RLE: 021 Pixels @ 041,167*/ 21, 0x05,
  /* RLE: 012 Pixels @ 062,167*/ 12, 0x0A,
  /* ABS: 007 Pixels @ 074,167*/ 0, 7, 0x0B, 0x00, 0x0B, 0x00, 0x0B, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 081,167*/ 4, 0x0A,
  /* RLE: 035 Pixels @ 085,167*/ 35, 0x05,
  /* RLE: 003 Pixels @ 120,167*/ 3, 0x0A,
  /* RLE: 087 Pixels @ 123,167*/ 87, 0x00,
  /* ABS: 007 Pixels @ 210,167*/ 0, 7, 0x04, 0x08, 0x0E, 0x0D, 0x0B, 0x0F, 0x04,
  /* RLE: 114 Pixels @ 217,167*/ 114, 0x00,
  /* ABS: 004 Pixels @ 331,167*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 018 Pixels @ 335,167*/ 18, 0x00,
  /* RLE: 005 Pixels @ 353,167*/ 5, 0x06,
  /* RLE: 036 Pixels @ 358,167*/ 36, 0x01,
  /* ABS: 002 Pixels @ 394,167*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 396,167*/ 4, 0x03,
  /* RLE: 027 Pixels @ 000,168*/ 27, 0x02,
  /* ABS: 014 Pixels @ 027,168*/ 0, 14, 0x00, 0x03, 0x04, 0x12, 0x06, 0x0B, 0x03, 0x06, 0x0E, 0x03, 0x0C, 0x11, 0x0E, 0x03,
  /* RLE: 033 Pixels @ 041,168*/ 33, 0x05,
  /* RLE: 007 Pixels @ 074,168*/ 7, 0x0A,
  /* RLE: 035 Pixels @ 081,168*/ 35, 0x05,
  /* RLE: 004 Pixels @ 116,168*/ 4, 0x0A,
  /* RLE: 089 Pixels @ 120,168*/ 89, 0x00,
  /* ABS: 009 Pixels @ 209,168*/ 0, 9, 0x03, 0x0C, 0x12, 0x03, 0x08, 0x03, 0x06, 0x0C, 0x03,
  /* RLE: 114 Pixels @ 218,168*/ 114, 0x00,
  /* ABS: 004 Pixels @ 332,168*/ 0, 4, 0x0D, 0x0D, 0x03, 0x06,
  /* RLE: 018 Pixels @ 336,168*/ 18, 0x00,
  /* RLE: 005 Pixels @ 354,168*/ 5, 0x06,
  /* RLE: 036 Pixels @ 359,168*/ 36, 0x01,
  /* ABS: 005 Pixels @ 395,168*/ 0, 5, 0x04, 0x04, 0x03, 0x03, 0x03,
  /* RLE: 028 Pixels @ 000,169*/ 28, 0x02,
  /* ABS: 013 Pixels @ 028,169*/ 0, 13, 0x00, 0x03, 0x03, 0x00, 0x00, 0x03, 0x04, 0x0F, 0x0C, 0x0E, 0x00, 0x03, 0x15,
  /* RLE: 071 Pixels @ 041,169*/ 71, 0x05,
  /* RLE: 004 Pixels @ 112,169*/ 4, 0x0A,
  /* RLE: 001 Pixels @ 116,169*/ 1, 0x0B,
  /* RLE: 092 Pixels @ 117,169*/ 92, 0x00,
  /* ABS: 010 Pixels @ 209,169*/ 0, 10, 0x03, 0x04, 0x11, 0x0B, 0x06, 0x12, 0x08, 0x00, 0x03, 0x04,
  /* RLE: 114 Pixels @ 219,169*/ 114, 0x00,
  /* ABS: 005 Pixels @ 333,169*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 017 Pixels @ 338,169*/ 17, 0x00,
  /* RLE: 005 Pixels @ 355,169*/ 5, 0x06,
  /* RLE: 036 Pixels @ 360,169*/ 36, 0x01,
  /* ABS: 004 Pixels @ 396,169*/ 0, 4, 0x04, 0x04, 0x03, 0x03,
  /* RLE: 032 Pixels @ 000,170*/ 32, 0x02,
  /* ABS: 008 Pixels @ 032,170*/ 0, 8, 0x0A, 0x0C, 0x03, 0x04, 0x12, 0x06, 0x0B, 0x15,
  /* RLE: 068 Pixels @ 040,170*/ 68, 0x05,
  /* RLE: 004 Pixels @ 108,170*/ 4, 0x0A,
  /* RLE: 098 Pixels @ 112,170*/ 98, 0x00,
  /* ABS: 009 Pixels @ 210,170*/ 0, 9, 0x03, 0x0D, 0x06, 0x0E, 0x0F, 0x04, 0x03, 0x03, 0x04,
  /* RLE: 115 Pixels @ 219,170*/ 115, 0x00,
  /* RLE: 001 Pixels @ 334,170*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 335,170*/ 4, 0x06,
  /* RLE: 018 Pixels @ 339,170*/ 18, 0x00,
  /* ABS: 004 Pixels @ 357,170*/ 0, 4, 0x06, 0x03, 0x03, 0x06,
  /* RLE: 036 Pixels @ 361,170*/ 36, 0x01,
  /* RLE: 003 Pixels @ 397,170*/ 3, 0x04,
  /* RLE: 034 Pixels @ 000,171*/ 34, 0x02,
  /* ABS: 006 Pixels @ 034,171*/ 0, 6, 0x0E, 0x00, 0x03, 0x03, 0x03, 0x19,
  /* RLE: 064 Pixels @ 040,171*/ 64, 0x05,
  /* RLE: 004 Pixels @ 104,171*/ 4, 0x0A,
  /* RLE: 103 Pixels @ 108,171*/ 103, 0x00,
  /* ABS: 008 Pixels @ 211,171*/ 0, 8, 0x03, 0x00, 0x00, 0x03, 0x03, 0x0B, 0x03, 0x04,
  /* RLE: 116 Pixels @ 219,171*/ 116, 0x00,
  /* ABS: 005 Pixels @ 335,171*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 340,171*/ 18, 0x00,
  /* ABS: 004 Pixels @ 358,171*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 038 Pixels @ 362,171*/ 38, 0x01,
  /* RLE: 036 Pixels @ 000,172*/ 36, 0x02,
  /* RLE: 003 Pixels @ 036,172*/ 3, 0x0A,
  /* RLE: 061 Pixels @ 039,172*/ 61, 0x05,
  /* RLE: 004 Pixels @ 100,172*/ 4, 0x0A,
  /* RLE: 106 Pixels @ 104,172*/ 106, 0x00,
  /* ABS: 008 Pixels @ 210,172*/ 0, 8, 0x03, 0x00, 0x0C, 0x12, 0x11, 0x08, 0x08, 0x03,
  /* RLE: 119 Pixels @ 218,172*/ 119, 0x00,
  /* ABS: 004 Pixels @ 337,172*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 018 Pixels @ 341,172*/ 18, 0x00,
  /* RLE: 005 Pixels @ 359,172*/ 5, 0x06,
  /* RLE: 036 Pixels @ 364,172*/ 36, 0x01,
  /* RLE: 038 Pixels @ 000,173*/ 38, 0x02,
  /* RLE: 003 Pixels @ 038,173*/ 3, 0x0A,
  /* RLE: 056 Pixels @ 041,173*/ 56, 0x05,
  /* RLE: 003 Pixels @ 097,173*/ 3, 0x0A,
  /* RLE: 110 Pixels @ 100,173*/ 110, 0x00,
  /* ABS: 009 Pixels @ 210,173*/ 0, 9, 0x03, 0x12, 0x08, 0x11, 0x12, 0x0E, 0x0D, 0x00, 0x03,
  /* RLE: 119 Pixels @ 219,173*/ 119, 0x00,
  /* ABS: 004 Pixels @ 338,173*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 018 Pixels @ 342,173*/ 18, 0x00,
  /* RLE: 005 Pixels @ 360,173*/ 5, 0x06,
  /* RLE: 035 Pixels @ 365,173*/ 35, 0x01,
  /* RLE: 040 Pixels @ 000,174*/ 40, 0x02,
  /* RLE: 003 Pixels @ 040,174*/ 3, 0x0A,
  /* RLE: 050 Pixels @ 043,174*/ 50, 0x05,
  /* RLE: 004 Pixels @ 093,174*/ 4, 0x0A,
  /* RLE: 113 Pixels @ 097,174*/ 113, 0x00,
  /* ABS: 002 Pixels @ 210,174*/ 0, 2, 0x03, 0x00,
  /* RLE: 004 Pixels @ 212,174*/ 4, 0x03,
  /* ABS: 004 Pixels @ 216,174*/ 0, 4, 0x0E, 0x0C, 0x03, 0x04,
  /* RLE: 119 Pixels @ 220,174*/ 119, 0x00,
  /* ABS: 004 Pixels @ 339,174*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 018 Pixels @ 343,174*/ 18, 0x00,
  /* RLE: 005 Pixels @ 361,174*/ 5, 0x06,
  /* RLE: 034 Pixels @ 366,174*/ 34, 0x01,
  /* RLE: 043 Pixels @ 000,175*/ 43, 0x02,
  /* RLE: 007 Pixels @ 043,175*/ 7, 0x0A,
  /* RLE: 039 Pixels @ 050,175*/ 39, 0x05,
  /* RLE: 004 Pixels @ 089,175*/ 4, 0x0A,
  /* RLE: 118 Pixels @ 093,175*/ 118, 0x00,
  /* RLE: 004 Pixels @ 211,175*/ 4, 0x03,
  /* ABS: 005 Pixels @ 215,175*/ 0, 5, 0x0B, 0x11, 0x06, 0x03, 0x04,
  /* RLE: 120 Pixels @ 220,175*/ 120, 0x00,
  /* RLE: 001 Pixels @ 340,175*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 341,175*/ 4, 0x06,
  /* RLE: 018 Pixels @ 345,175*/ 18, 0x00,
  /* ABS: 004 Pixels @ 363,175*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 033 Pixels @ 367,175*/ 33, 0x01,
  /* RLE: 044 Pixels @ 000,176*/ 44, 0x02,
  /* ABS: 002 Pixels @ 044,176*/ 0, 2, 0x01, 0x04,
  /* RLE: 004 Pixels @ 046,176*/ 4, 0x00,
  /* RLE: 012 Pixels @ 050,176*/ 12, 0x0A,
  /* RLE: 023 Pixels @ 062,176*/ 23, 0x05,
  /* RLE: 004 Pixels @ 085,176*/ 4, 0x0A,
  /* RLE: 122 Pixels @ 089,176*/ 122, 0x00,
  /* ABS: 009 Pixels @ 211,176*/ 0, 9, 0x03, 0x12, 0x11, 0x08, 0x08, 0x08, 0x0B, 0x03, 0x03,
  /* RLE: 121 Pixels @ 220,176*/ 121, 0x00,
  /* ABS: 005 Pixels @ 341,176*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 346,176*/ 18, 0x00,
  /* ABS: 004 Pixels @ 364,176*/ 0, 4, 0x06, 0x03, 0x03, 0x06,
  /* RLE: 032 Pixels @ 368,176*/ 32, 0x01,
  /* RLE: 043 Pixels @ 000,177*/ 43, 0x02,
  /* RLE: 005 Pixels @ 043,177*/ 5, 0x04,
  /* RLE: 014 Pixels @ 048,177*/ 14, 0x00,
  /* RLE: 012 Pixels @ 062,177*/ 12, 0x0A,
  /* RLE: 007 Pixels @ 074,177*/ 7, 0x05,
  /* RLE: 005 Pixels @ 081,177*/ 5, 0x09,
  /* RLE: 001 Pixels @ 086,177*/ 1, 0x0C,
  /* RLE: 124 Pixels @ 087,177*/ 124, 0x00,
  /* ABS: 010 Pixels @ 211,177*/ 0, 10, 0x03, 0x0D, 0x06, 0x0C, 0x04, 0x00, 0x0D, 0x00, 0x0B, 0x03,
  /* RLE: 121 Pixels @ 221,177*/ 121, 0x00,
  /* ABS: 005 Pixels @ 342,177*/ 0, 5, 0x0D, 0x0D, 0x06, 0x0D, 0x06,
  /* RLE: 018 Pixels @ 347,177*/ 18, 0x00,
  /* RLE: 004 Pixels @ 365,177*/ 4, 0x06,
  /* RLE: 031 Pixels @ 369,177*/ 31, 0x01,
  /* RLE: 042 Pixels @ 000,178*/ 42, 0x02,
  /* ABS: 007 Pixels @ 042,178*/ 0, 7, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 025 Pixels @ 049,178*/ 25, 0x00,
  /* RLE: 003 Pixels @ 074,178*/ 3, 0x0A,
  /* RLE: 009 Pixels @ 077,178*/ 9, 0x09,
  /* RLE: 001 Pixels @ 086,178*/ 1, 0x0C,
  /* RLE: 124 Pixels @ 087,178*/ 124, 0x00,
  /* ABS: 011 Pixels @ 211,178*/ 0, 11, 0x03, 0x03, 0x00, 0x0C, 0x06, 0x0F, 0x08, 0x08, 0x08, 0x04, 0x03,
  /* RLE: 122 Pixels @ 222,178*/ 122, 0x00,
  /* ABS: 004 Pixels @ 344,178*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 348,178*/ 18, 0x00,
  /* RLE: 005 Pixels @ 366,178*/ 5, 0x06,
  /* RLE: 029 Pixels @ 371,178*/ 29, 0x01,
  /* RLE: 042 Pixels @ 000,179*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,179*/ 1, 0x04,
  /* RLE: 005 Pixels @ 043,179*/ 5, 0x03,
  /* RLE: 001 Pixels @ 048,179*/ 1, 0x04,
  /* RLE: 028 Pixels @ 049,179*/ 28, 0x00,
  /* RLE: 001 Pixels @ 077,179*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 078,179*/ 9, 0x09,
  /* RLE: 001 Pixels @ 087,179*/ 1, 0x0C,
  /* RLE: 123 Pixels @ 088,179*/ 123, 0x00,
  /* ABS: 011 Pixels @ 211,179*/ 0, 11, 0x03, 0x12, 0x08, 0x08, 0x11, 0x12, 0x08, 0x0C, 0x12, 0x0E, 0x03,
  /* RLE: 034 Pixels @ 222,179*/ 34, 0x00,
  /* RLE: 005 Pixels @ 256,179*/ 5, 0x04,
  /* RLE: 084 Pixels @ 261,179*/ 84, 0x00,
  /* ABS: 004 Pixels @ 345,179*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 018 Pixels @ 349,179*/ 18, 0x00,
  /* RLE: 005 Pixels @ 367,179*/ 5, 0x06,
  /* RLE: 028 Pixels @ 372,179*/ 28, 0x01,
  /* RLE: 041 Pixels @ 000,180*/ 41, 0x02,
  /* ABS: 002 Pixels @ 041,180*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 043,180*/ 6, 0x03,
  /* RLE: 029 Pixels @ 049,180*/ 29, 0x00,
  /* RLE: 009 Pixels @ 078,180*/ 9, 0x09,
  /* RLE: 001 Pixels @ 087,180*/ 1, 0x0C,
  /* RLE: 123 Pixels @ 088,180*/ 123, 0x00,
  /* ABS: 011 Pixels @ 211,180*/ 0, 11, 0x03, 0x0B, 0x00, 0x03, 0x03, 0x03, 0x04, 0x00, 0x0B, 0x04, 0x03,
  /* RLE: 033 Pixels @ 222,180*/ 33, 0x00,
  /* ABS: 007 Pixels @ 255,180*/ 0, 7, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 084 Pixels @ 262,180*/ 84, 0x00,
  /* ABS: 004 Pixels @ 346,180*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 350,180*/ 18, 0x00,
  /* ABS: 005 Pixels @ 368,180*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 027 Pixels @ 373,180*/ 27, 0x01,
  /* RLE: 042 Pixels @ 000,181*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,181*/ 1, 0x04,
  /* RLE: 006 Pixels @ 043,181*/ 6, 0x03,
  /* RLE: 001 Pixels @ 049,181*/ 1, 0x04,
  /* RLE: 028 Pixels @ 050,181*/ 28, 0x00,
  /* RLE: 001 Pixels @ 078,181*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 079,181*/ 9, 0x09,
  /* RLE: 001 Pixels @ 088,181*/ 1, 0x0C,
  /* RLE: 121 Pixels @ 089,181*/ 121, 0x00,
  /* ABS: 011 Pixels @ 210,181*/ 0, 11, 0x04, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x0B, 0x03, 0x03, 0x03,
  /* RLE: 034 Pixels @ 221,181*/ 34, 0x00,
  /* RLE: 001 Pixels @ 255,181*/ 1, 0x04,
  /* RLE: 005 Pixels @ 256,181*/ 5, 0x03,
  /* RLE: 001 Pixels @ 261,181*/ 1, 0x04,
  /* RLE: 085 Pixels @ 262,181*/ 85, 0x00,
  /* RLE: 001 Pixels @ 347,181*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 348,181*/ 4, 0x06,
  /* RLE: 018 Pixels @ 352,181*/ 18, 0x00,
  /* ABS: 004 Pixels @ 370,181*/ 0, 4, 0x06, 0x03, 0x03, 0x06,
  /* RLE: 026 Pixels @ 374,181*/ 26, 0x01,
  /* RLE: 042 Pixels @ 000,182*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,182*/ 1, 0x04,
  /* RLE: 006 Pixels @ 043,182*/ 6, 0x03,
  /* RLE: 001 Pixels @ 049,182*/ 1, 0x04,
  /* RLE: 029 Pixels @ 050,182*/ 29, 0x00,
  /* RLE: 009 Pixels @ 079,182*/ 9, 0x09,
  /* RLE: 001 Pixels @ 088,182*/ 1, 0x0C,
  /* RLE: 117 Pixels @ 089,182*/ 117, 0x00,
  /* RLE: 003 Pixels @ 206,182*/ 3, 0x04,
  /* RLE: 005 Pixels @ 209,182*/ 5, 0x03,
  /* ABS: 006 Pixels @ 214,182*/ 0, 6, 0x0E, 0x08, 0x08, 0x08, 0x06, 0x03,
  /* RLE: 034 Pixels @ 220,182*/ 34, 0x00,
  /* RLE: 001 Pixels @ 254,182*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,182*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,182*/ 1, 0x04,
  /* RLE: 086 Pixels @ 262,182*/ 86, 0x00,
  /* ABS: 005 Pixels @ 348,182*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 353,182*/ 18, 0x00,
  /* RLE: 004 Pixels @ 371,182*/ 4, 0x06,
  /* RLE: 025 Pixels @ 375,182*/ 25, 0x01,
  /* RLE: 042 Pixels @ 000,183*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,183*/ 1, 0x04,
  /* RLE: 006 Pixels @ 043,183*/ 6, 0x03,
  /* RLE: 001 Pixels @ 049,183*/ 1, 0x04,
  /* RLE: 029 Pixels @ 050,183*/ 29, 0x00,
  /* RLE: 001 Pixels @ 079,183*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 080,183*/ 9, 0x09,
  /* RLE: 001 Pixels @ 089,183*/ 1, 0x0C,
  /* RLE: 112 Pixels @ 090,183*/ 112, 0x00,
  /* RLE: 003 Pixels @ 202,183*/ 3, 0x04,
  /* RLE: 008 Pixels @ 205,183*/ 8, 0x03,
  /* ABS: 008 Pixels @ 213,183*/ 0, 8, 0x0C, 0x08, 0x0E, 0x0D, 0x0B, 0x0F, 0x04, 0x03,
  /* RLE: 033 Pixels @ 221,183*/ 33, 0x00,
  /* RLE: 001 Pixels @ 254,183*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,183*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,183*/ 1, 0x04,
  /* RLE: 087 Pixels @ 262,183*/ 87, 0x00,
  /* ABS: 005 Pixels @ 349,183*/ 0, 5, 0x0D, 0x0D, 0x03, 0x0D, 0x06,
  /* RLE: 018 Pixels @ 354,183*/ 18, 0x00,
  /* RLE: 005 Pixels @ 372,183*/ 5, 0x06,
  /* RLE: 023 Pixels @ 377,183*/ 23, 0x01,
  /* RLE: 042 Pixels @ 000,184*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,184*/ 1, 0x04,
  /* RLE: 006 Pixels @ 043,184*/ 6, 0x03,
  /* RLE: 001 Pixels @ 049,184*/ 1, 0x04,
  /* RLE: 030 Pixels @ 050,184*/ 30, 0x00,
  /* RLE: 009 Pixels @ 080,184*/ 9, 0x09,
  /* RLE: 001 Pixels @ 089,184*/ 1, 0x0C,
  /* RLE: 108 Pixels @ 090,184*/ 108, 0x00,
  /* RLE: 003 Pixels @ 198,184*/ 3, 0x04,
  /* RLE: 012 Pixels @ 201,184*/ 12, 0x03,
  /* ABS: 009 Pixels @ 213,184*/ 0, 9, 0x0C, 0x12, 0x03, 0x08, 0x03, 0x06, 0x0C, 0x03, 0x04,
  /* RLE: 032 Pixels @ 222,184*/ 32, 0x00,
  /* RLE: 001 Pixels @ 254,184*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,184*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,184*/ 1, 0x04,
  /* RLE: 089 Pixels @ 262,184*/ 89, 0x00,
  /* ABS: 004 Pixels @ 351,184*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 355,184*/ 18, 0x00,
  /* RLE: 005 Pixels @ 373,184*/ 5, 0x06,
  /* RLE: 022 Pixels @ 378,184*/ 22, 0x01,
  /* RLE: 042 Pixels @ 000,185*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,185*/ 1, 0x04,
  /* RLE: 006 Pixels @ 043,185*/ 6, 0x03,
  /* RLE: 001 Pixels @ 049,185*/ 1, 0x04,
  /* RLE: 030 Pixels @ 050,185*/ 30, 0x00,
  /* RLE: 001 Pixels @ 080,185*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 081,185*/ 9, 0x09,
  /* RLE: 104 Pixels @ 090,185*/ 104, 0x00,
  /* RLE: 003 Pixels @ 194,185*/ 3, 0x04,
  /* RLE: 016 Pixels @ 197,185*/ 16, 0x03,
  /* ABS: 009 Pixels @ 213,185*/ 0, 9, 0x04, 0x11, 0x0B, 0x06, 0x12, 0x08, 0x0B, 0x03, 0x04,
  /* RLE: 032 Pixels @ 222,185*/ 32, 0x00,
  /* RLE: 001 Pixels @ 254,185*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,185*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,185*/ 1, 0x04,
  /* RLE: 090 Pixels @ 262,185*/ 90, 0x00,
  /* ABS: 004 Pixels @ 352,185*/ 0, 4, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 356,185*/ 18, 0x00,
  /* ABS: 005 Pixels @ 374,185*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 021 Pixels @ 379,185*/ 21, 0x01,
  /* RLE: 042 Pixels @ 000,186*/ 42, 0x02,
  /* RLE: 001 Pixels @ 042,186*/ 1, 0x04,
  /* RLE: 007 Pixels @ 043,186*/ 7, 0x03,
  /* RLE: 031 Pixels @ 050,186*/ 31, 0x00,
  /* RLE: 009 Pixels @ 081,186*/ 9, 0x09,
  /* RLE: 001 Pixels @ 090,186*/ 1, 0x0C,
  /* RLE: 099 Pixels @ 091,186*/ 99, 0x00,
  /* RLE: 003 Pixels @ 190,186*/ 3, 0x04,
  /* RLE: 021 Pixels @ 193,186*/ 21, 0x03,
  /* ABS: 005 Pixels @ 214,186*/ 0, 5, 0x0D, 0x06, 0x0E, 0x0F, 0x04,
  /* RLE: 004 Pixels @ 219,186*/ 4, 0x03,
  /* RLE: 031 Pixels @ 223,186*/ 31, 0x00,
  /* RLE: 001 Pixels @ 254,186*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,186*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,186*/ 1, 0x04,
  /* RLE: 091 Pixels @ 262,186*/ 91, 0x00,
  /* ABS: 004 Pixels @ 353,186*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 019 Pixels @ 357,186*/ 19, 0x00,
  /* ABS: 004 Pixels @ 376,186*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 020 Pixels @ 380,186*/ 20, 0x01,
  /* RLE: 043 Pixels @ 000,187*/ 43, 0x02,
  /* RLE: 001 Pixels @ 043,187*/ 1, 0x04,
  /* RLE: 006 Pixels @ 044,187*/ 6, 0x03,
  /* RLE: 001 Pixels @ 050,187*/ 1, 0x04,
  /* RLE: 030 Pixels @ 051,187*/ 30, 0x00,
  /* RLE: 001 Pixels @ 081,187*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 082,187*/ 9, 0x09,
  /* RLE: 095 Pixels @ 091,187*/ 95, 0x00,
  /* RLE: 004 Pixels @ 186,187*/ 4, 0x04,
  /* RLE: 025 Pixels @ 190,187*/ 25, 0x03,
  /* ABS: 008 Pixels @ 215,187*/ 0, 8, 0x00, 0x00, 0x03, 0x03, 0x00, 0x0C, 0x12, 0x03,
  /* RLE: 031 Pixels @ 223,187*/ 31, 0x00,
  /* RLE: 001 Pixels @ 254,187*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,187*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,187*/ 1, 0x04,
  /* RLE: 092 Pixels @ 262,187*/ 92, 0x00,
  /* ABS: 004 Pixels @ 354,187*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 019 Pixels @ 358,187*/ 19, 0x00,
  /* ABS: 004 Pixels @ 377,187*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 019 Pixels @ 381,187*/ 19, 0x01,
  /* RLE: 043 Pixels @ 000,188*/ 43, 0x02,
  /* RLE: 001 Pixels @ 043,188*/ 1, 0x04,
  /* RLE: 006 Pixels @ 044,188*/ 6, 0x03,
  /* RLE: 001 Pixels @ 050,188*/ 1, 0x04,
  /* RLE: 031 Pixels @ 051,188*/ 31, 0x00,
  /* RLE: 009 Pixels @ 082,188*/ 9, 0x09,
  /* RLE: 001 Pixels @ 091,188*/ 1, 0x0C,
  /* RLE: 090 Pixels @ 092,188*/ 90, 0x00,
  /* RLE: 004 Pixels @ 182,188*/ 4, 0x04,
  /* RLE: 024 Pixels @ 186,188*/ 24, 0x03,
  /* RLE: 003 Pixels @ 210,188*/ 3, 0x04,
  /* ABS: 009 Pixels @ 213,188*/ 0, 9, 0x03, 0x00, 0x0C, 0x12, 0x11, 0x08, 0x08, 0x08, 0x11,
  /* RLE: 032 Pixels @ 222,188*/ 32, 0x00,
  /* RLE: 001 Pixels @ 254,188*/ 1, 0x04,
  /* RLE: 006 Pixels @ 255,188*/ 6, 0x03,
  /* RLE: 001 Pixels @ 261,188*/ 1, 0x04,
  /* RLE: 093 Pixels @ 262,188*/ 93, 0x00,
  /* ABS: 005 Pixels @ 355,188*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 360,188*/ 18, 0x00,
  /* RLE: 004 Pixels @ 378,188*/ 4, 0x06,
  /* RLE: 018 Pixels @ 382,188*/ 18, 0x01,
  /* RLE: 043 Pixels @ 000,189*/ 43, 0x02,
  /* RLE: 001 Pixels @ 043,189*/ 1, 0x04,
  /* RLE: 006 Pixels @ 044,189*/ 6, 0x03,
  /* RLE: 001 Pixels @ 050,189*/ 1, 0x04,
  /* RLE: 031 Pixels @ 051,189*/ 31, 0x00,
  /* RLE: 001 Pixels @ 082,189*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 083,189*/ 9, 0x09,
  /* RLE: 086 Pixels @ 092,189*/ 86, 0x00,
  /* RLE: 004 Pixels @ 178,189*/ 4, 0x04,
  /* RLE: 024 Pixels @ 182,189*/ 24, 0x03,
  /* RLE: 004 Pixels @ 206,189*/ 4, 0x04,
  /* RLE: 003 Pixels @ 210,189*/ 3, 0x00,
  /* ABS: 010 Pixels @ 213,189*/ 0, 10, 0x03, 0x12, 0x08, 0x11, 0x12, 0x0C, 0x0B, 0x03, 0x03, 0x03,
  /* RLE: 030 Pixels @ 223,189*/ 30, 0x00,
  /* RLE: 001 Pixels @ 253,189*/ 1, 0x04,
  /* RLE: 007 Pixels @ 254,189*/ 7, 0x03,
  /* RLE: 095 Pixels @ 261,189*/ 95, 0x00,
  /* ABS: 005 Pixels @ 356,189*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 361,189*/ 18, 0x00,
  /* RLE: 005 Pixels @ 379,189*/ 5, 0x06,
  /* RLE: 016 Pixels @ 384,189*/ 16, 0x01,
  /* RLE: 043 Pixels @ 000,190*/ 43, 0x02,
  /* RLE: 001 Pixels @ 043,190*/ 1, 0x04,
  /* RLE: 006 Pixels @ 044,190*/ 6, 0x03,
  /* RLE: 001 Pixels @ 050,190*/ 1, 0x04,
  /* RLE: 032 Pixels @ 051,190*/ 32, 0x00,
  /* RLE: 009 Pixels @ 083,190*/ 9, 0x09,
  /* RLE: 001 Pixels @ 092,190*/ 1, 0x0C,
  /* RLE: 081 Pixels @ 093,190*/ 81, 0x00,
  /* RLE: 004 Pixels @ 174,190*/ 4, 0x04,
  /* RLE: 024 Pixels @ 178,190*/ 24, 0x03,
  /* RLE: 004 Pixels @ 202,190*/ 4, 0x04,
  /* RLE: 007 Pixels @ 206,190*/ 7, 0x00,
  /* ABS: 011 Pixels @ 213,190*/ 0, 11, 0x03, 0x00, 0x03, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 029 Pixels @ 224,190*/ 29, 0x00,
  /* RLE: 001 Pixels @ 253,190*/ 1, 0x04,
  /* RLE: 006 Pixels @ 254,190*/ 6, 0x03,
  /* RLE: 001 Pixels @ 260,190*/ 1, 0x04,
  /* RLE: 096 Pixels @ 261,190*/ 96, 0x00,
  /* ABS: 005 Pixels @ 357,190*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 362,190*/ 18, 0x00,
  /* ABS: 005 Pixels @ 380,190*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 015 Pixels @ 385,190*/ 15, 0x01,
  /* RLE: 043 Pixels @ 000,191*/ 43, 0x02,
  /* RLE: 001 Pixels @ 043,191*/ 1, 0x04,
  /* RLE: 006 Pixels @ 044,191*/ 6, 0x03,
  /* RLE: 001 Pixels @ 050,191*/ 1, 0x04,
  /* RLE: 032 Pixels @ 051,191*/ 32, 0x00,
  /* RLE: 001 Pixels @ 083,191*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 084,191*/ 9, 0x09,
  /* RLE: 077 Pixels @ 093,191*/ 77, 0x00,
  /* RLE: 004 Pixels @ 170,191*/ 4, 0x04,
  /* RLE: 024 Pixels @ 174,191*/ 24, 0x03,
  /* RLE: 004 Pixels @ 198,191*/ 4, 0x04,
  /* RLE: 013 Pixels @ 202,191*/ 13, 0x00,
  /* ABS: 009 Pixels @ 215,191*/ 0, 9, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x06, 0x03, 0x03, 0x04,
  /* RLE: 029 Pixels @ 224,191*/ 29, 0x00,
  /* RLE: 001 Pixels @ 253,191*/ 1, 0x04,
  /* RLE: 006 Pixels @ 254,191*/ 6, 0x03,
  /* RLE: 001 Pixels @ 260,191*/ 1, 0x04,
  /* RLE: 098 Pixels @ 261,191*/ 98, 0x00,
  /* ABS: 004 Pixels @ 359,191*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 363,191*/ 18, 0x00,
  /* ABS: 005 Pixels @ 381,191*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 014 Pixels @ 386,191*/ 14, 0x01,
  /* RLE: 043 Pixels @ 000,192*/ 43, 0x02,
  /* RLE: 001 Pixels @ 043,192*/ 1, 0x04,
  /* RLE: 007 Pixels @ 044,192*/ 7, 0x03,
  /* RLE: 032 Pixels @ 051,192*/ 32, 0x00,
  /* RLE: 001 Pixels @ 083,192*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 084,192*/ 9, 0x09,
  /* RLE: 001 Pixels @ 093,192*/ 1, 0x0C,
  /* RLE: 072 Pixels @ 094,192*/ 72, 0x00,
  /* RLE: 004 Pixels @ 166,192*/ 4, 0x04,
  /* RLE: 024 Pixels @ 170,192*/ 24, 0x03,
  /* RLE: 004 Pixels @ 194,192*/ 4, 0x04,
  /* RLE: 016 Pixels @ 198,192*/ 16, 0x00,
  /* ABS: 010 Pixels @ 214,192*/ 0, 10, 0x03, 0x04, 0x08, 0x12, 0x04, 0x04, 0x0F, 0x04, 0x03, 0x04,
  /* RLE: 029 Pixels @ 224,192*/ 29, 0x00,
  /* RLE: 001 Pixels @ 253,192*/ 1, 0x04,
  /* RLE: 006 Pixels @ 254,192*/ 6, 0x03,
  /* RLE: 001 Pixels @ 260,192*/ 1, 0x04,
  /* RLE: 099 Pixels @ 261,192*/ 99, 0x00,
  /* ABS: 004 Pixels @ 360,192*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 019 Pixels @ 364,192*/ 19, 0x00,
  /* ABS: 004 Pixels @ 383,192*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 013 Pixels @ 387,192*/ 13, 0x01,
  /* RLE: 044 Pixels @ 000,193*/ 44, 0x02,
  /* RLE: 001 Pixels @ 044,193*/ 1, 0x04,
  /* RLE: 006 Pixels @ 045,193*/ 6, 0x03,
  /* RLE: 001 Pixels @ 051,193*/ 1, 0x04,
  /* RLE: 032 Pixels @ 052,193*/ 32, 0x00,
  /* RLE: 001 Pixels @ 084,193*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 085,193*/ 9, 0x09,
  /* RLE: 069 Pixels @ 094,193*/ 69, 0x00,
  /* RLE: 003 Pixels @ 163,193*/ 3, 0x04,
  /* RLE: 025 Pixels @ 166,193*/ 25, 0x03,
  /* RLE: 003 Pixels @ 191,193*/ 3, 0x04,
  /* RLE: 020 Pixels @ 194,193*/ 20, 0x00,
  /* ABS: 011 Pixels @ 214,193*/ 0, 11, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x06, 0x0C, 0x03, 0x03, 0x03,
  /* RLE: 028 Pixels @ 225,193*/ 28, 0x00,
  /* RLE: 001 Pixels @ 253,193*/ 1, 0x04,
  /* RLE: 006 Pixels @ 254,193*/ 6, 0x03,
  /* RLE: 001 Pixels @ 260,193*/ 1, 0x04,
  /* RLE: 100 Pixels @ 261,193*/ 100, 0x00,
  /* ABS: 004 Pixels @ 361,193*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 019 Pixels @ 365,193*/ 19, 0x00,
  /* RLE: 004 Pixels @ 384,193*/ 4, 0x06,
  /* RLE: 012 Pixels @ 388,193*/ 12, 0x01,
  /* RLE: 044 Pixels @ 000,194*/ 44, 0x02,
  /* RLE: 001 Pixels @ 044,194*/ 1, 0x04,
  /* RLE: 006 Pixels @ 045,194*/ 6, 0x03,
  /* RLE: 001 Pixels @ 051,194*/ 1, 0x04,
  /* RLE: 032 Pixels @ 052,194*/ 32, 0x00,
  /* RLE: 001 Pixels @ 084,194*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 085,194*/ 9, 0x09,
  /* RLE: 001 Pixels @ 094,194*/ 1, 0x0C,
  /* RLE: 067 Pixels @ 095,194*/ 67, 0x00,
  /* ABS: 002 Pixels @ 162,194*/ 0, 2, 0x04, 0x04,
  /* RLE: 023 Pixels @ 164,194*/ 23, 0x03,
  /* RLE: 003 Pixels @ 187,194*/ 3, 0x04,
  /* RLE: 024 Pixels @ 190,194*/ 24, 0x00,
  /* ABS: 011 Pixels @ 214,194*/ 0, 11, 0x03, 0x0B, 0x11, 0x00, 0x03, 0x0B, 0x0F, 0x12, 0x0D, 0x0F, 0x03,
  /* RLE: 028 Pixels @ 225,194*/ 28, 0x00,
  /* RLE: 001 Pixels @ 253,194*/ 1, 0x04,
  /* RLE: 006 Pixels @ 254,194*/ 6, 0x03,
  /* RLE: 001 Pixels @ 260,194*/ 1, 0x04,
  /* RLE: 101 Pixels @ 261,194*/ 101, 0x00,
  /* ABS: 005 Pixels @ 362,194*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 367,194*/ 18, 0x00,
  /* RLE: 004 Pixels @ 385,194*/ 4, 0x06,
  /* RLE: 011 Pixels @ 389,194*/ 11, 0x01,
  /* RLE: 044 Pixels @ 000,195*/ 44, 0x02,
  /* RLE: 001 Pixels @ 044,195*/ 1, 0x04,
  /* RLE: 006 Pixels @ 045,195*/ 6, 0x03,
  /* RLE: 001 Pixels @ 051,195*/ 1, 0x04,
  /* RLE: 033 Pixels @ 052,195*/ 33, 0x00,
  /* RLE: 001 Pixels @ 085,195*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 086,195*/ 9, 0x09,
  /* RLE: 065 Pixels @ 095,195*/ 65, 0x00,
  /* RLE: 003 Pixels @ 160,195*/ 3, 0x04,
  /* RLE: 020 Pixels @ 163,195*/ 20, 0x03,
  /* RLE: 003 Pixels @ 183,195*/ 3, 0x04,
  /* RLE: 029 Pixels @ 186,195*/ 29, 0x00,
  /* ABS: 002 Pixels @ 215,195*/ 0, 2, 0x03, 0x0D,
  /* RLE: 004 Pixels @ 217,195*/ 4, 0x08,
  /* ABS: 004 Pixels @ 221,195*/ 0, 4, 0x0F, 0x06, 0x0C, 0x03,
  /* RLE: 028 Pixels @ 225,195*/ 28, 0x00,
  /* RLE: 001 Pixels @ 253,195*/ 1, 0x04,
  /* RLE: 006 Pixels @ 254,195*/ 6, 0x03,
  /* RLE: 001 Pixels @ 260,195*/ 1, 0x04,
  /* RLE: 102 Pixels @ 261,195*/ 102, 0x00,
  /* ABS: 005 Pixels @ 363,195*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 368,195*/ 18, 0x00,
  /* RLE: 004 Pixels @ 386,195*/ 4, 0x06,
  /* RLE: 010 Pixels @ 390,195*/ 10, 0x01,
  /* RLE: 044 Pixels @ 000,196*/ 44, 0x02,
  /* RLE: 001 Pixels @ 044,196*/ 1, 0x04,
  /* RLE: 006 Pixels @ 045,196*/ 6, 0x03,
  /* RLE: 001 Pixels @ 051,196*/ 1, 0x04,
  /* RLE: 033 Pixels @ 052,196*/ 33, 0x00,
  /* RLE: 001 Pixels @ 085,196*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 086,196*/ 9, 0x09,
  /* RLE: 001 Pixels @ 095,196*/ 1, 0x0C,
  /* RLE: 063 Pixels @ 096,196*/ 63, 0x00,
  /* ABS: 002 Pixels @ 159,196*/ 0, 2, 0x04, 0x04,
  /* RLE: 018 Pixels @ 161,196*/ 18, 0x03,
  /* RLE: 003 Pixels @ 179,196*/ 3, 0x04,
  /* RLE: 033 Pixels @ 182,196*/ 33, 0x00,
  /* ABS: 009 Pixels @ 215,196*/ 0, 9, 0x03, 0x11, 0x06, 0x0E, 0x04, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 028 Pixels @ 224,196*/ 28, 0x00,
  /* RLE: 001 Pixels @ 252,196*/ 1, 0x04,
  /* RLE: 007 Pixels @ 253,196*/ 7, 0x03,
  /* RLE: 104 Pixels @ 260,196*/ 104, 0x00,
  /* RLE: 003 Pixels @ 364,196*/ 3, 0x0D,
  /* ABS: 002 Pixels @ 367,196*/ 0, 2, 0x06, 0x06,
  /* RLE: 018 Pixels @ 369,196*/ 18, 0x00,
  /* RLE: 005 Pixels @ 387,196*/ 5, 0x06,
  /* RLE: 008 Pixels @ 392,196*/ 8, 0x01,
  /* RLE: 044 Pixels @ 000,197*/ 44, 0x02,
  /* RLE: 001 Pixels @ 044,197*/ 1, 0x04,
  /* RLE: 006 Pixels @ 045,197*/ 6, 0x03,
  /* RLE: 001 Pixels @ 051,197*/ 1, 0x04,
  /* RLE: 034 Pixels @ 052,197*/ 34, 0x00,
  /* RLE: 001 Pixels @ 086,197*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 087,197*/ 9, 0x09,
  /* RLE: 061 Pixels @ 096,197*/ 61, 0x00,
  /* ABS: 002 Pixels @ 157,197*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 159,197*/ 16, 0x03,
  /* RLE: 003 Pixels @ 175,197*/ 3, 0x04,
  /* RLE: 037 Pixels @ 178,197*/ 37, 0x00,
  /* RLE: 010 Pixels @ 215,197*/ 10, 0x03,
  /* RLE: 001 Pixels @ 225,197*/ 1, 0x04,
  /* RLE: 026 Pixels @ 226,197*/ 26, 0x00,
  /* RLE: 001 Pixels @ 252,197*/ 1, 0x04,
  /* RLE: 006 Pixels @ 253,197*/ 6, 0x03,
  /* RLE: 001 Pixels @ 259,197*/ 1, 0x04,
  /* RLE: 106 Pixels @ 260,197*/ 106, 0x00,
  /* ABS: 004 Pixels @ 366,197*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 370,197*/ 18, 0x00,
  /* RLE: 005 Pixels @ 388,197*/ 5, 0x06,
  /* RLE: 007 Pixels @ 393,197*/ 7, 0x01,
  /* RLE: 044 Pixels @ 000,198*/ 44, 0x02,
  /* RLE: 001 Pixels @ 044,198*/ 1, 0x04,
  /* RLE: 007 Pixels @ 045,198*/ 7, 0x03,
  /* RLE: 034 Pixels @ 052,198*/ 34, 0x00,
  /* RLE: 001 Pixels @ 086,198*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 087,198*/ 9, 0x09,
  /* RLE: 001 Pixels @ 096,198*/ 1, 0x0C,
  /* RLE: 059 Pixels @ 097,198*/ 59, 0x00,
  /* ABS: 002 Pixels @ 156,198*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 158,198*/ 13, 0x03,
  /* RLE: 003 Pixels @ 171,198*/ 3, 0x04,
  /* RLE: 044 Pixels @ 174,198*/ 44, 0x00,
  /* RLE: 001 Pixels @ 218,198*/ 1, 0x04,
  /* RLE: 006 Pixels @ 219,198*/ 6, 0x03,
  /* RLE: 001 Pixels @ 225,198*/ 1, 0x04,
  /* RLE: 026 Pixels @ 226,198*/ 26, 0x00,
  /* RLE: 001 Pixels @ 252,198*/ 1, 0x04,
  /* RLE: 006 Pixels @ 253,198*/ 6, 0x03,
  /* RLE: 001 Pixels @ 259,198*/ 1, 0x04,
  /* RLE: 107 Pixels @ 260,198*/ 107, 0x00,
  /* ABS: 004 Pixels @ 367,198*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 018 Pixels @ 371,198*/ 18, 0x00,
  /* ABS: 005 Pixels @ 389,198*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 006 Pixels @ 394,198*/ 6, 0x01,
  /* RLE: 045 Pixels @ 000,199*/ 45, 0x02,
  /* RLE: 001 Pixels @ 045,199*/ 1, 0x04,
  /* RLE: 006 Pixels @ 046,199*/ 6, 0x03,
  /* RLE: 001 Pixels @ 052,199*/ 1, 0x04,
  /* RLE: 034 Pixels @ 053,199*/ 34, 0x00,
  /* RLE: 001 Pixels @ 087,199*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 088,199*/ 9, 0x09,
  /* RLE: 057 Pixels @ 097,199*/ 57, 0x00,
  /* ABS: 002 Pixels @ 154,199*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 156,199*/ 11, 0x03,
  /* RLE: 003 Pixels @ 167,199*/ 3, 0x04,
  /* RLE: 048 Pixels @ 170,199*/ 48, 0x00,
  /* RLE: 001 Pixels @ 218,199*/ 1, 0x04,
  /* RLE: 006 Pixels @ 219,199*/ 6, 0x03,
  /* RLE: 001 Pixels @ 225,199*/ 1, 0x04,
  /* RLE: 026 Pixels @ 226,199*/ 26, 0x00,
  /* RLE: 001 Pixels @ 252,199*/ 1, 0x04,
  /* RLE: 006 Pixels @ 253,199*/ 6, 0x03,
  /* RLE: 001 Pixels @ 259,199*/ 1, 0x04,
  /* RLE: 108 Pixels @ 260,199*/ 108, 0x00,
  /* ABS: 004 Pixels @ 368,199*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 019 Pixels @ 372,199*/ 19, 0x00,
  /* ABS: 004 Pixels @ 391,199*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 005 Pixels @ 395,199*/ 5, 0x01,
  /* RLE: 045 Pixels @ 000,200*/ 45, 0x02,
  /* RLE: 001 Pixels @ 045,200*/ 1, 0x04,
  /* RLE: 006 Pixels @ 046,200*/ 6, 0x03,
  /* RLE: 001 Pixels @ 052,200*/ 1, 0x04,
  /* RLE: 034 Pixels @ 053,200*/ 34, 0x00,
  /* RLE: 001 Pixels @ 087,200*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 088,200*/ 9, 0x09,
  /* RLE: 001 Pixels @ 097,200*/ 1, 0x0C,
  /* RLE: 054 Pixels @ 098,200*/ 54, 0x00,
  /* RLE: 003 Pixels @ 152,200*/ 3, 0x04,
  /* RLE: 011 Pixels @ 155,200*/ 11, 0x03,
  /* ABS: 002 Pixels @ 166,200*/ 0, 2, 0x04, 0x04,
  /* RLE: 050 Pixels @ 168,200*/ 50, 0x00,
  /* RLE: 001 Pixels @ 218,200*/ 1, 0x04,
  /* RLE: 007 Pixels @ 219,200*/ 7, 0x03,
  /* RLE: 026 Pixels @ 226,200*/ 26, 0x00,
  /* RLE: 001 Pixels @ 252,200*/ 1, 0x04,
  /* RLE: 006 Pixels @ 253,200*/ 6, 0x03,
  /* RLE: 001 Pixels @ 259,200*/ 1, 0x04,
  /* RLE: 109 Pixels @ 260,200*/ 109, 0x00,
  /* ABS: 005 Pixels @ 369,200*/ 0, 5, 0x0D, 0x0D, 0x03, 0x06, 0x06,
  /* RLE: 018 Pixels @ 374,200*/ 18, 0x00,
  /* ABS: 004 Pixels @ 392,200*/ 0, 4, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 004 Pixels @ 396,200*/ 4, 0x01,
  /* RLE: 045 Pixels @ 000,201*/ 45, 0x02,
  /* RLE: 001 Pixels @ 045,201*/ 1, 0x04,
  /* RLE: 006 Pixels @ 046,201*/ 6, 0x03,
  /* RLE: 001 Pixels @ 052,201*/ 1, 0x04,
  /* RLE: 035 Pixels @ 053,201*/ 35, 0x00,
  /* RLE: 001 Pixels @ 088,201*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 089,201*/ 8, 0x09,
  /* RLE: 001 Pixels @ 097,201*/ 1, 0x0C,
  /* RLE: 054 Pixels @ 098,201*/ 54, 0x00,
  /* ABS: 002 Pixels @ 152,201*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 154,201*/ 10, 0x03,
  /* ABS: 002 Pixels @ 164,201*/ 0, 2, 0x04, 0x04,
  /* RLE: 053 Pixels @ 166,201*/ 53, 0x00,
  /* RLE: 001 Pixels @ 219,201*/ 1, 0x04,
  /* RLE: 006 Pixels @ 220,201*/ 6, 0x03,
  /* RLE: 001 Pixels @ 226,201*/ 1, 0x04,
  /* RLE: 025 Pixels @ 227,201*/ 25, 0x00,
  /* RLE: 001 Pixels @ 252,201*/ 1, 0x04,
  /* RLE: 006 Pixels @ 253,201*/ 6, 0x03,
  /* RLE: 001 Pixels @ 259,201*/ 1, 0x04,
  /* RLE: 110 Pixels @ 260,201*/ 110, 0x00,
  /* RLE: 001 Pixels @ 370,201*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 371,201*/ 4, 0x06,
  /* RLE: 018 Pixels @ 375,201*/ 18, 0x00,
  /* RLE: 004 Pixels @ 393,201*/ 4, 0x06,
  /* RLE: 003 Pixels @ 397,201*/ 3, 0x01,
  /* RLE: 045 Pixels @ 000,202*/ 45, 0x02,
  /* RLE: 001 Pixels @ 045,202*/ 1, 0x04,
  /* RLE: 006 Pixels @ 046,202*/ 6, 0x03,
  /* RLE: 001 Pixels @ 052,202*/ 1, 0x04,
  /* RLE: 035 Pixels @ 053,202*/ 35, 0x00,
  /* RLE: 001 Pixels @ 088,202*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 089,202*/ 9, 0x09,
  /* RLE: 001 Pixels @ 098,202*/ 1, 0x0C,
  /* RLE: 053 Pixels @ 099,202*/ 53, 0x00,
  /* RLE: 001 Pixels @ 152,202*/ 1, 0x04,
  /* RLE: 009 Pixels @ 153,202*/ 9, 0x03,
  /* RLE: 003 Pixels @ 162,202*/ 3, 0x04,
  /* RLE: 054 Pixels @ 165,202*/ 54, 0x00,
  /* RLE: 001 Pixels @ 219,202*/ 1, 0x04,
  /* RLE: 006 Pixels @ 220,202*/ 6, 0x03,
  /* RLE: 001 Pixels @ 226,202*/ 1, 0x04,
  /* RLE: 024 Pixels @ 227,202*/ 24, 0x00,
  /* ABS: 002 Pixels @ 251,202*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 253,202*/ 6, 0x03,
  /* RLE: 001 Pixels @ 259,202*/ 1, 0x04,
  /* RLE: 111 Pixels @ 260,202*/ 111, 0x00,
  /* ABS: 005 Pixels @ 371,202*/ 0, 5, 0x0D, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 018 Pixels @ 376,202*/ 18, 0x00,
  /* RLE: 005 Pixels @ 394,202*/ 5, 0x06,
  /* RLE: 001 Pixels @ 399,202*/ 1, 0x01,
  /* RLE: 045 Pixels @ 000,203*/ 45, 0x02,
  /* RLE: 001 Pixels @ 045,203*/ 1, 0x04,
  /* RLE: 006 Pixels @ 046,203*/ 6, 0x03,
  /* RLE: 001 Pixels @ 052,203*/ 1, 0x04,
  /* RLE: 036 Pixels @ 053,203*/ 36, 0x00,
  /* RLE: 001 Pixels @ 089,203*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 090,203*/ 8, 0x09,
  /* RLE: 001 Pixels @ 098,203*/ 1, 0x0C,
  /* RLE: 051 Pixels @ 099,203*/ 51, 0x00,
  /* RLE: 011 Pixels @ 150,203*/ 11, 0x03,
  /* ABS: 002 Pixels @ 161,203*/ 0, 2, 0x04, 0x04,
  /* RLE: 056 Pixels @ 163,203*/ 56, 0x00,
  /* RLE: 001 Pixels @ 219,203*/ 1, 0x04,
  /* RLE: 006 Pixels @ 220,203*/ 6, 0x03,
  /* RLE: 001 Pixels @ 226,203*/ 1, 0x04,
  /* RLE: 024 Pixels @ 227,203*/ 24, 0x00,
  /* RLE: 001 Pixels @ 251,203*/ 1, 0x04,
  /* RLE: 007 Pixels @ 252,203*/ 7, 0x03,
  /* RLE: 114 Pixels @ 259,203*/ 114, 0x00,
  /* ABS: 004 Pixels @ 373,203*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 018 Pixels @ 377,203*/ 18, 0x00,
  /* ABS: 005 Pixels @ 395,203*/ 0, 5, 0x06, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 045 Pixels @ 000,204*/ 45, 0x02,
  /* RLE: 001 Pixels @ 045,204*/ 1, 0x04,
  /* RLE: 007 Pixels @ 046,204*/ 7, 0x03,
  /* RLE: 036 Pixels @ 053,204*/ 36, 0x00,
  /* RLE: 001 Pixels @ 089,204*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 090,204*/ 9, 0x09,
  /* RLE: 050 Pixels @ 099,204*/ 50, 0x00,
  /* ABS: 006 Pixels @ 149,204*/ 0, 6, 0x03, 0x00, 0x0C, 0x0C, 0x04, 0x00,
  /* RLE: 004 Pixels @ 155,204*/ 4, 0x03,
  /* RLE: 003 Pixels @ 159,204*/ 3, 0x04,
  /* RLE: 058 Pixels @ 162,204*/ 58, 0x00,
  /* RLE: 007 Pixels @ 220,204*/ 7, 0x03,
  /* RLE: 001 Pixels @ 227,204*/ 1, 0x04,
  /* RLE: 022 Pixels @ 228,204*/ 22, 0x00,
  /* ABS: 002 Pixels @ 250,204*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 252,204*/ 6, 0x03,
  /* RLE: 001 Pixels @ 258,204*/ 1, 0x04,
  /* RLE: 115 Pixels @ 259,204*/ 115, 0x00,
  /* ABS: 004 Pixels @ 374,204*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 018 Pixels @ 378,204*/ 18, 0x00,
  /* ABS: 004 Pixels @ 396,204*/ 0, 4, 0x06, 0x06, 0x03, 0x06,
  /* RLE: 046 Pixels @ 000,205*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,205*/ 1, 0x04,
  /* RLE: 006 Pixels @ 047,205*/ 6, 0x03,
  /* RLE: 001 Pixels @ 053,205*/ 1, 0x04,
  /* RLE: 036 Pixels @ 054,205*/ 36, 0x00,
  /* RLE: 001 Pixels @ 090,205*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 091,205*/ 8, 0x09,
  /* RLE: 001 Pixels @ 099,205*/ 1, 0x0C,
  /* RLE: 048 Pixels @ 100,205*/ 48, 0x00,
  /* ABS: 003 Pixels @ 148,205*/ 0, 3, 0x03, 0x00, 0x0F,
  /* RLE: 004 Pixels @ 151,205*/ 4, 0x08,
  /* ABS: 005 Pixels @ 155,205*/ 0, 5, 0x06, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 060 Pixels @ 160,205*/ 60, 0x00,
  /* RLE: 001 Pixels @ 220,205*/ 1, 0x04,
  /* RLE: 006 Pixels @ 221,205*/ 6, 0x03,
  /* RLE: 001 Pixels @ 227,205*/ 1, 0x04,
  /* RLE: 022 Pixels @ 228,205*/ 22, 0x00,
  /* RLE: 001 Pixels @ 250,205*/ 1, 0x04,
  /* RLE: 007 Pixels @ 251,205*/ 7, 0x03,
  /* RLE: 001 Pixels @ 258,205*/ 1, 0x04,
  /* RLE: 116 Pixels @ 259,205*/ 116, 0x00,
  /* ABS: 004 Pixels @ 375,205*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 019 Pixels @ 379,205*/ 19, 0x00,
  /* ABS: 002 Pixels @ 398,205*/ 0, 2, 0x06, 0x03,
  /* RLE: 046 Pixels @ 000,206*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,206*/ 1, 0x04,
  /* RLE: 006 Pixels @ 047,206*/ 6, 0x03,
  /* RLE: 001 Pixels @ 053,206*/ 1, 0x04,
  /* RLE: 036 Pixels @ 054,206*/ 36, 0x00,
  /* RLE: 001 Pixels @ 090,206*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 091,206*/ 8, 0x09,
  /* RLE: 001 Pixels @ 099,206*/ 1, 0x0C,
  /* RLE: 050 Pixels @ 100,206*/ 50, 0x00,
  /* ABS: 008 Pixels @ 150,206*/ 0, 8, 0x11, 0x03, 0x03, 0x00, 0x04, 0x04, 0x03, 0x03,
  /* RLE: 062 Pixels @ 158,206*/ 62, 0x00,
  /* RLE: 001 Pixels @ 220,206*/ 1, 0x04,
  /* RLE: 006 Pixels @ 221,206*/ 6, 0x03,
  /* ABS: 002 Pixels @ 227,206*/ 0, 2, 0x04, 0x04,
  /* RLE: 020 Pixels @ 229,206*/ 20, 0x00,
  /* RLE: 001 Pixels @ 249,206*/ 1, 0x04,
  /* RLE: 007 Pixels @ 250,206*/ 7, 0x03,
  /* RLE: 001 Pixels @ 257,206*/ 1, 0x04,
  /* RLE: 118 Pixels @ 258,206*/ 118, 0x00,
  /* RLE: 001 Pixels @ 376,206*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 377,206*/ 4, 0x06,
  /* RLE: 018 Pixels @ 381,206*/ 18, 0x00,
  /* RLE: 001 Pixels @ 399,206*/ 1, 0x06,
  /* RLE: 046 Pixels @ 000,207*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,207*/ 1, 0x04,
  /* RLE: 006 Pixels @ 047,207*/ 6, 0x03,
  /* RLE: 001 Pixels @ 053,207*/ 1, 0x04,
  /* RLE: 037 Pixels @ 054,207*/ 37, 0x00,
  /* RLE: 009 Pixels @ 091,207*/ 9, 0x09,
  /* RLE: 001 Pixels @ 100,207*/ 1, 0x0C,
  /* RLE: 048 Pixels @ 101,207*/ 48, 0x00,
  /* ABS: 002 Pixels @ 149,207*/ 0, 2, 0x03, 0x11,
  /* RLE: 006 Pixels @ 151,207*/ 6, 0x03,
  /* RLE: 001 Pixels @ 157,207*/ 1, 0x04,
  /* RLE: 062 Pixels @ 158,207*/ 62, 0x00,
  /* RLE: 001 Pixels @ 220,207*/ 1, 0x04,
  /* RLE: 007 Pixels @ 221,207*/ 7, 0x03,
  /* RLE: 001 Pixels @ 228,207*/ 1, 0x04,
  /* RLE: 019 Pixels @ 229,207*/ 19, 0x00,
  /* ABS: 002 Pixels @ 248,207*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 250,207*/ 7, 0x03,
  /* RLE: 001 Pixels @ 257,207*/ 1, 0x04,
  /* RLE: 119 Pixels @ 258,207*/ 119, 0x00,
  /* RLE: 001 Pixels @ 377,207*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 378,207*/ 4, 0x06,
  /* RLE: 018 Pixels @ 382,207*/ 18, 0x00,
  /* RLE: 046 Pixels @ 000,208*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,208*/ 1, 0x04,
  /* RLE: 006 Pixels @ 047,208*/ 6, 0x03,
  /* RLE: 001 Pixels @ 053,208*/ 1, 0x04,
  /* RLE: 037 Pixels @ 054,208*/ 37, 0x00,
  /* RLE: 001 Pixels @ 091,208*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 092,208*/ 8, 0x09,
  /* RLE: 001 Pixels @ 100,208*/ 1, 0x0C,
  /* RLE: 047 Pixels @ 101,208*/ 47, 0x00,
  /* ABS: 010 Pixels @ 148,208*/ 0, 10, 0x03, 0x0C, 0x08, 0x0F, 0x06, 0x0C, 0x0C, 0x03, 0x03, 0x04,
  /* RLE: 063 Pixels @ 158,208*/ 63, 0x00,
  /* RLE: 001 Pixels @ 221,208*/ 1, 0x04,
  /* RLE: 006 Pixels @ 222,208*/ 6, 0x03,
  /* ABS: 002 Pixels @ 228,208*/ 0, 2, 0x04, 0x04,
  /* RLE: 018 Pixels @ 230,208*/ 18, 0x00,
  /* RLE: 001 Pixels @ 248,208*/ 1, 0x04,
  /* RLE: 007 Pixels @ 249,208*/ 7, 0x03,
  /* RLE: 001 Pixels @ 256,208*/ 1, 0x04,
  /* RLE: 121 Pixels @ 257,208*/ 121, 0x00,
  /* RLE: 003 Pixels @ 378,208*/ 3, 0x0D,
  /* ABS: 002 Pixels @ 381,208*/ 0, 2, 0x06, 0x06,
  /* RLE: 017 Pixels @ 383,208*/ 17, 0x00,
  /* RLE: 046 Pixels @ 000,209*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,209*/ 1, 0x04,
  /* RLE: 006 Pixels @ 047,209*/ 6, 0x03,
  /* RLE: 001 Pixels @ 053,209*/ 1, 0x04,
  /* RLE: 037 Pixels @ 054,209*/ 37, 0x00,
  /* RLE: 001 Pixels @ 091,209*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 092,209*/ 9, 0x09,
  /* RLE: 049 Pixels @ 101,209*/ 49, 0x00,
  /* ABS: 005 Pixels @ 150,209*/ 0, 5, 0x0C, 0x06, 0x0D, 0x08, 0x08,
  /* RLE: 066 Pixels @ 155,209*/ 66, 0x00,
  /* RLE: 001 Pixels @ 221,209*/ 1, 0x04,
  /* RLE: 007 Pixels @ 222,209*/ 7, 0x03,
  /* RLE: 001 Pixels @ 229,209*/ 1, 0x04,
  /* RLE: 017 Pixels @ 230,209*/ 17, 0x00,
  /* ABS: 002 Pixels @ 247,209*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 249,209*/ 6, 0x03,
  /* ABS: 002 Pixels @ 255,209*/ 0, 2, 0x04, 0x04,
  /* RLE: 123 Pixels @ 257,209*/ 123, 0x00,
  /* ABS: 004 Pixels @ 380,209*/ 0, 4, 0x0D, 0x03, 0x0D, 0x06,
  /* RLE: 016 Pixels @ 384,209*/ 16, 0x00,
  /* RLE: 046 Pixels @ 000,210*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,210*/ 1, 0x04,
  /* RLE: 006 Pixels @ 047,210*/ 6, 0x03,
  /* RLE: 001 Pixels @ 053,210*/ 1, 0x04,
  /* RLE: 038 Pixels @ 054,210*/ 38, 0x00,
  /* RLE: 001 Pixels @ 092,210*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 093,210*/ 8, 0x09,
  /* RLE: 001 Pixels @ 101,210*/ 1, 0x0C,
  /* RLE: 046 Pixels @ 102,210*/ 46, 0x00,
  /* RLE: 006 Pixels @ 148,210*/ 6, 0x03,
  /* ABS: 002 Pixels @ 154,210*/ 0, 2, 0x00, 0x03,
  /* RLE: 065 Pixels @ 156,210*/ 65, 0x00,
  /* ABS: 002 Pixels @ 221,210*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 223,210*/ 7, 0x03,
  /* RLE: 001 Pixels @ 230,210*/ 1, 0x04,
  /* RLE: 016 Pixels @ 231,210*/ 16, 0x00,
  /* RLE: 001 Pixels @ 247,210*/ 1, 0x04,
  /* RLE: 007 Pixels @ 248,210*/ 7, 0x03,
  /* RLE: 001 Pixels @ 255,210*/ 1, 0x04,
  /* RLE: 125 Pixels @ 256,210*/ 125, 0x00,
  /* ABS: 004 Pixels @ 381,210*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 015 Pixels @ 385,210*/ 15, 0x00,
  /* RLE: 047 Pixels @ 000,211*/ 47, 0x02,
  /* RLE: 007 Pixels @ 047,211*/ 7, 0x03,
  /* RLE: 038 Pixels @ 054,211*/ 38, 0x00,
  /* RLE: 001 Pixels @ 092,211*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 093,211*/ 8, 0x09,
  /* RLE: 001 Pixels @ 101,211*/ 1, 0x0C,
  /* RLE: 046 Pixels @ 102,211*/ 46, 0x00,
  /* ABS: 008 Pixels @ 148,211*/ 0, 8, 0x03, 0x0C, 0x08, 0x08, 0x0B, 0x0E, 0x03, 0x03,
  /* RLE: 066 Pixels @ 156,211*/ 66, 0x00,
  /* RLE: 001 Pixels @ 222,211*/ 1, 0x04,
  /* RLE: 007 Pixels @ 223,211*/ 7, 0x03,
  /* ABS: 002 Pixels @ 230,211*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 232,211*/ 14, 0x00,
  /* RLE: 001 Pixels @ 246,211*/ 1, 0x04,
  /* RLE: 007 Pixels @ 247,211*/ 7, 0x03,
  /* ABS: 002 Pixels @ 254,211*/ 0, 2, 0x04, 0x04,
  /* RLE: 126 Pixels @ 256,211*/ 126, 0x00,
  /* ABS: 004 Pixels @ 382,211*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 014 Pixels @ 386,211*/ 14, 0x00,
  /* RLE: 046 Pixels @ 000,212*/ 46, 0x02,
  /* RLE: 001 Pixels @ 046,212*/ 1, 0x00,
  /* RLE: 006 Pixels @ 047,212*/ 6, 0x03,
  /* ABS: 003 Pixels @ 053,212*/ 0, 3, 0x00, 0x04, 0x03,
  /* RLE: 037 Pixels @ 056,212*/ 37, 0x00,
  /* RLE: 009 Pixels @ 093,212*/ 9, 0x09,
  /* RLE: 001 Pixels @ 102,212*/ 1, 0x0C,
  /* RLE: 044 Pixels @ 103,212*/ 44, 0x00,
  /* ABS: 008 Pixels @ 147,212*/ 0, 8, 0x03, 0x00, 0x08, 0x04, 0x0D, 0x04, 0x0F, 0x04,
  /* RLE: 068 Pixels @ 155,212*/ 68, 0x00,
  /* RLE: 001 Pixels @ 223,212*/ 1, 0x04,
  /* RLE: 007 Pixels @ 224,212*/ 7, 0x03,
  /* RLE: 001 Pixels @ 231,212*/ 1, 0x04,
  /* RLE: 013 Pixels @ 232,212*/ 13, 0x00,
  /* ABS: 002 Pixels @ 245,212*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 247,212*/ 7, 0x03,
  /* RLE: 001 Pixels @ 254,212*/ 1, 0x04,
  /* RLE: 128 Pixels @ 255,212*/ 128, 0x00,
  /* ABS: 004 Pixels @ 383,212*/ 0, 4, 0x0D, 0x06, 0x06, 0x06,
  /* RLE: 013 Pixels @ 387,212*/ 13, 0x00,
  /* RLE: 046 Pixels @ 000,213*/ 46, 0x02,
  /* ABS: 009 Pixels @ 046,213*/ 0, 9, 0x03, 0x0B, 0x0C, 0x0E, 0x06, 0x11, 0x08, 0x08, 0x08,
  /* RLE: 038 Pixels @ 055,213*/ 38, 0x00,
  /* RLE: 001 Pixels @ 093,213*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 094,213*/ 8, 0x09,
  /* RLE: 001 Pixels @ 102,213*/ 1, 0x0C,
  /* RLE: 045 Pixels @ 103,213*/ 45, 0x00,
  /* ABS: 008 Pixels @ 148,213*/ 0, 8, 0x0C, 0x06, 0x03, 0x0F, 0x03, 0x0E, 0x0C, 0x03,
  /* RLE: 067 Pixels @ 156,213*/ 67, 0x00,
  /* ABS: 002 Pixels @ 223,213*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 225,213*/ 6, 0x03,
  /* ABS: 002 Pixels @ 231,213*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 233,213*/ 11, 0x00,
  /* ABS: 002 Pixels @ 244,213*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 246,213*/ 7, 0x03,
  /* RLE: 001 Pixels @ 253,213*/ 1, 0x04,
  /* RLE: 130 Pixels @ 254,213*/ 130, 0x00,
  /* RLE: 001 Pixels @ 384,213*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 385,213*/ 4, 0x06,
  /* RLE: 011 Pixels @ 389,213*/ 11, 0x00,
  /* RLE: 046 Pixels @ 000,214*/ 46, 0x02,
  /* ABS: 002 Pixels @ 046,214*/ 0, 2, 0x03, 0x06,
  /* RLE: 004 Pixels @ 048,214*/ 4, 0x08,
  /* ABS: 005 Pixels @ 052,214*/ 0, 5, 0x0E, 0x0C, 0x08, 0x04, 0x03,
  /* RLE: 036 Pixels @ 057,214*/ 36, 0x00,
  /* RLE: 001 Pixels @ 093,214*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 094,214*/ 9, 0x09,
  /* RLE: 044 Pixels @ 103,214*/ 44, 0x00,
  /* ABS: 009 Pixels @ 147,214*/ 0, 9, 0x03, 0x00, 0x08, 0x0E, 0x0F, 0x0B, 0x11, 0x0C, 0x03,
  /* RLE: 068 Pixels @ 156,214*/ 68, 0x00,
  /* RLE: 001 Pixels @ 224,214*/ 1, 0x04,
  /* RLE: 007 Pixels @ 225,214*/ 7, 0x03,
  /* RLE: 001 Pixels @ 232,214*/ 1, 0x04,
  /* RLE: 010 Pixels @ 233,214*/ 10, 0x00,
  /* ABS: 002 Pixels @ 243,214*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 245,214*/ 7, 0x03,
  /* ABS: 002 Pixels @ 252,214*/ 0, 2, 0x04, 0x04,
  /* RLE: 131 Pixels @ 254,214*/ 131, 0x00,
  /* ABS: 005 Pixels @ 385,214*/ 0, 5, 0x0D, 0x06, 0x03, 0x06, 0x06,
  /* RLE: 010 Pixels @ 390,214*/ 10, 0x00,
  /* RLE: 046 Pixels @ 000,215*/ 46, 0x02,
  /* ABS: 011 Pixels @ 046,215*/ 0, 11, 0x03, 0x00, 0x0B, 0x03, 0x00, 0x08, 0x00, 0x03, 0x11, 0x0C, 0x03,
  /* RLE: 037 Pixels @ 057,215*/ 37, 0x00,
  /* RLE: 001 Pixels @ 094,215*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 095,215*/ 8, 0x09,
  /* RLE: 001 Pixels @ 103,215*/ 1, 0x0C,
  /* RLE: 044 Pixels @ 104,215*/ 44, 0x00,
  /* ABS: 007 Pixels @ 148,215*/ 0, 7, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x0D, 0x03,
  /* RLE: 069 Pixels @ 155,215*/ 69, 0x00,
  /* ABS: 002 Pixels @ 224,215*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 226,215*/ 7, 0x03,
  /* RLE: 001 Pixels @ 233,215*/ 1, 0x04,
  /* RLE: 008 Pixels @ 234,215*/ 8, 0x00,
  /* ABS: 002 Pixels @ 242,215*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 244,215*/ 8, 0x03,
  /* RLE: 001 Pixels @ 252,215*/ 1, 0x04,
  /* RLE: 133 Pixels @ 253,215*/ 133, 0x00,
  /* RLE: 001 Pixels @ 386,215*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 387,215*/ 4, 0x06,
  /* RLE: 009 Pixels @ 391,215*/ 9, 0x00,
  /* RLE: 046 Pixels @ 000,216*/ 46, 0x02,
  /* ABS: 011 Pixels @ 046,216*/ 0, 11, 0x00, 0x00, 0x03, 0x03, 0x03, 0x08, 0x04, 0x03, 0x06, 0x0E, 0x03,
  /* RLE: 037 Pixels @ 057,216*/ 37, 0x00,
  /* RLE: 001 Pixels @ 094,216*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 095,216*/ 8, 0x09,
  /* RLE: 001 Pixels @ 103,216*/ 1, 0x0C,
  /* RLE: 044 Pixels @ 104,216*/ 44, 0x00,
  /* RLE: 003 Pixels @ 148,216*/ 3, 0x03,
  /* ABS: 005 Pixels @ 151,216*/ 0, 5, 0x00, 0x0B, 0x03, 0x03, 0x04,
  /* RLE: 069 Pixels @ 156,216*/ 69, 0x00,
  /* RLE: 001 Pixels @ 225,216*/ 1, 0x04,
  /* RLE: 007 Pixels @ 226,216*/ 7, 0x03,
  /* ABS: 002 Pixels @ 233,216*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 235,216*/ 6, 0x00,
  /* ABS: 002 Pixels @ 241,216*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 243,216*/ 8, 0x03,
  /* ABS: 002 Pixels @ 251,216*/ 0, 2, 0x04, 0x04,
  /* RLE: 135 Pixels @ 253,216*/ 135, 0x00,
  /* ABS: 004 Pixels @ 388,216*/ 0, 4, 0x0D, 0x0D, 0x06, 0x06,
  /* RLE: 008 Pixels @ 392,216*/ 8, 0x00,
  /* RLE: 048 Pixels @ 000,217*/ 48, 0x02,
  /* RLE: 003 Pixels @ 048,217*/ 3, 0x03,
  /* ABS: 006 Pixels @ 051,217*/ 0, 6, 0x0D, 0x0C, 0x03, 0x0C, 0x06, 0x03,
  /* RLE: 038 Pixels @ 057,217*/ 38, 0x00,
  /* RLE: 009 Pixels @ 095,217*/ 9, 0x09,
  /* RLE: 001 Pixels @ 104,217*/ 1, 0x0C,
  /* RLE: 042 Pixels @ 105,217*/ 42, 0x00,
  /* ABS: 009 Pixels @ 147,217*/ 0, 9, 0x03, 0x0E, 0x0C, 0x00, 0x06, 0x12, 0x03, 0x03, 0x04,
  /* RLE: 070 Pixels @ 156,217*/ 70, 0x00,
  /* RLE: 001 Pixels @ 226,217*/ 1, 0x04,
  /* RLE: 007 Pixels @ 227,217*/ 7, 0x03,
  /* RLE: 001 Pixels @ 234,217*/ 1, 0x04,
  /* RLE: 005 Pixels @ 235,217*/ 5, 0x00,
  /* ABS: 002 Pixels @ 240,217*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 242,217*/ 8, 0x03,
  /* ABS: 002 Pixels @ 250,217*/ 0, 2, 0x04, 0x04,
  /* RLE: 137 Pixels @ 252,217*/ 137, 0x00,
  /* ABS: 004 Pixels @ 389,217*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 007 Pixels @ 393,217*/ 7, 0x00,
  /* RLE: 048 Pixels @ 000,218*/ 48, 0x02,
  /* RLE: 001 Pixels @ 048,218*/ 1, 0x04,
  /* RLE: 008 Pixels @ 049,218*/ 8, 0x03,
  /* RLE: 038 Pixels @ 057,218*/ 38, 0x00,
  /* RLE: 001 Pixels @ 095,218*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 096,218*/ 8, 0x09,
  /* RLE: 001 Pixels @ 104,218*/ 1, 0x0C,
  /* RLE: 041 Pixels @ 105,218*/ 41, 0x00,
  /* ABS: 009 Pixels @ 146,218*/ 0, 9, 0x03, 0x00, 0x0F, 0x00, 0x11, 0x11, 0x0F, 0x0C, 0x03,
  /* RLE: 071 Pixels @ 155,218*/ 71, 0x00,
  /* ABS: 002 Pixels @ 226,218*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 228,218*/ 6, 0x03,
  /* ABS: 007 Pixels @ 234,218*/ 0, 7, 0x04, 0x04, 0x00, 0x00, 0x00, 0x04, 0x04,
  /* RLE: 008 Pixels @ 241,218*/ 8, 0x03,
  /* ABS: 002 Pixels @ 249,218*/ 0, 2, 0x04, 0x04,
  /* RLE: 139 Pixels @ 251,218*/ 139, 0x00,
  /* ABS: 004 Pixels @ 390,218*/ 0, 4, 0x0D, 0x06, 0x03, 0x06,
  /* RLE: 006 Pixels @ 394,218*/ 6, 0x00,
  /* RLE: 048 Pixels @ 000,219*/ 48, 0x02,
  /* RLE: 001 Pixels @ 048,219*/ 1, 0x00,
  /* RLE: 006 Pixels @ 049,219*/ 6, 0x03,
  /* RLE: 001 Pixels @ 055,219*/ 1, 0x04,
  /* RLE: 039 Pixels @ 056,219*/ 39, 0x00,
  /* RLE: 001 Pixels @ 095,219*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 096,219*/ 9, 0x09,
  /* RLE: 041 Pixels @ 105,219*/ 41, 0x00,
  /* ABS: 009 Pixels @ 146,219*/ 0, 9, 0x03, 0x0C, 0x06, 0x0B, 0x08, 0x04, 0x0C, 0x0C, 0x03,
  /* RLE: 072 Pixels @ 155,219*/ 72, 0x00,
  /* RLE: 001 Pixels @ 227,219*/ 1, 0x04,
  /* RLE: 007 Pixels @ 228,219*/ 7, 0x03,
  /* ABS: 005 Pixels @ 235,219*/ 0, 5, 0x04, 0x00, 0x00, 0x04, 0x04,
  /* RLE: 008 Pixels @ 240,219*/ 8, 0x03,
  /* ABS: 002 Pixels @ 248,219*/ 0, 2, 0x04, 0x04,
  /* RLE: 141 Pixels @ 250,219*/ 141, 0x00,
  /* RLE: 001 Pixels @ 391,219*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 392,219*/ 4, 0x06,
  /* RLE: 004 Pixels @ 396,219*/ 4, 0x00,
  /* RLE: 047 Pixels @ 000,220*/ 47, 0x02,
  /* ABS: 008 Pixels @ 047,220*/ 0, 8, 0x00, 0x03, 0x0C, 0x0F, 0x08, 0x11, 0x0B, 0x03,
  /* RLE: 041 Pixels @ 055,220*/ 41, 0x00,
  /* RLE: 001 Pixels @ 096,220*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 097,220*/ 8, 0x09,
  /* RLE: 001 Pixels @ 105,220*/ 1, 0x0C,
  /* RLE: 040 Pixels @ 106,220*/ 40, 0x00,
  /* ABS: 009 Pixels @ 146,220*/ 0, 9, 0x03, 0x0C, 0x11, 0x0D, 0x11, 0x03, 0x06, 0x04, 0x03,
  /* RLE: 072 Pixels @ 155,220*/ 72, 0x00,
  /* ABS: 002 Pixels @ 227,220*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 229,220*/ 7, 0x03,
  /* RLE: 003 Pixels @ 236,220*/ 3, 0x04,
  /* RLE: 008 Pixels @ 239,220*/ 8, 0x03,
  /* ABS: 002 Pixels @ 247,220*/ 0, 2, 0x04, 0x04,
  /* RLE: 143 Pixels @ 249,220*/ 143, 0x00,
  /* RLE: 001 Pixels @ 392,220*/ 1, 0x0D,
  /* RLE: 004 Pixels @ 393,220*/ 4, 0x06,
  /* RLE: 003 Pixels @ 397,220*/ 3, 0x00,
  /* RLE: 047 Pixels @ 000,221*/ 47, 0x02,
  /* ABS: 007 Pixels @ 047,221*/ 0, 7, 0x03, 0x04, 0x08, 0x06, 0x0C, 0x06, 0x0F,
  /* RLE: 042 Pixels @ 054,221*/ 42, 0x00,
  /* RLE: 001 Pixels @ 096,221*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 097,221*/ 8, 0x09,
  /* RLE: 001 Pixels @ 105,221*/ 1, 0x0C,
  /* RLE: 038 Pixels @ 106,221*/ 38, 0x00,
  /* ABS: 009 Pixels @ 144,221*/ 0, 9, 0x03, 0x00, 0x03, 0x03, 0x06, 0x0F, 0x04, 0x0C, 0x0F,
  /* RLE: 075 Pixels @ 153,221*/ 75, 0x00,
  /* RLE: 001 Pixels @ 228,221*/ 1, 0x04,
  /* RLE: 007 Pixels @ 229,221*/ 7, 0x03,
  /* ABS: 002 Pixels @ 236,221*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 238,221*/ 8, 0x03,
  /* ABS: 002 Pixels @ 246,221*/ 0, 2, 0x04, 0x04,
  /* RLE: 145 Pixels @ 248,221*/ 145, 0x00,
  /* RLE: 003 Pixels @ 393,221*/ 3, 0x0D,
  /* ABS: 004 Pixels @ 396,221*/ 0, 4, 0x06, 0x06, 0x00, 0x00,
  /* RLE: 047 Pixels @ 000,222*/ 47, 0x02,
  /* ABS: 009 Pixels @ 047,222*/ 0, 9, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x11, 0x04, 0x03,
  /* RLE: 041 Pixels @ 056,222*/ 41, 0x00,
  /* RLE: 009 Pixels @ 097,222*/ 9, 0x09,
  /* RLE: 001 Pixels @ 106,222*/ 1, 0x0C,
  /* RLE: 036 Pixels @ 107,222*/ 36, 0x00,
  /* ABS: 004 Pixels @ 143,222*/ 0, 4, 0x03, 0x00, 0x00, 0x00,
  /* RLE: 004 Pixels @ 147,222*/ 4, 0x03,
  /* ABS: 003 Pixels @ 151,222*/ 0, 3, 0x00, 0x00, 0x03,
  /* RLE: 075 Pixels @ 154,222*/ 75, 0x00,
  /* RLE: 001 Pixels @ 229,222*/ 1, 0x04,
  /* RLE: 015 Pixels @ 230,222*/ 15, 0x03,
  /* ABS: 002 Pixels @ 245,222*/ 0, 2, 0x04, 0x04,
  /* RLE: 148 Pixels @ 247,222*/ 148, 0x00,
  /* ABS: 005 Pixels @ 395,222*/ 0, 5, 0x0D, 0x03, 0x0D, 0x06, 0x00,
  /* RLE: 047 Pixels @ 000,223*/ 47, 0x02,
  /* ABS: 010 Pixels @ 047,223*/ 0, 10, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x0F, 0x04, 0x03, 0x04,
  /* RLE: 040 Pixels @ 057,223*/ 40, 0x00,
  /* RLE: 001 Pixels @ 097,223*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 098,223*/ 8, 0x09,
  /* RLE: 001 Pixels @ 106,223*/ 1, 0x0C,
  /* RLE: 036 Pixels @ 107,223*/ 36, 0x00,
  /* ABS: 012 Pixels @ 143,223*/ 0, 12, 0x03, 0x06, 0x08, 0x08, 0x08, 0x0D, 0x06, 0x0C, 0x0C, 0x03, 0x03, 0x04,
  /* RLE: 074 Pixels @ 155,223*/ 74, 0x00,
  /* ABS: 002 Pixels @ 229,223*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 231,223*/ 13, 0x03,
  /* ABS: 002 Pixels @ 244,223*/ 0, 2, 0x04, 0x04,
  /* RLE: 150 Pixels @ 246,223*/ 150, 0x00,
  /* ABS: 004 Pixels @ 396,223*/ 0, 4, 0x0D, 0x03, 0x03, 0x06,
  /* RLE: 047 Pixels @ 000,224*/ 47, 0x02,
  /* ABS: 009 Pixels @ 047,224*/ 0, 9, 0x03, 0x00, 0x08, 0x0D, 0x06, 0x0F, 0x0F, 0x03, 0x03,
  /* RLE: 041 Pixels @ 056,224*/ 41, 0x00,
  /* RLE: 001 Pixels @ 097,224*/ 1, 0x0C,
  /* RLE: 004 Pixels @ 098,224*/ 4, 0x09,
  /* ABS: 002 Pixels @ 102,224*/ 0, 2, 0x1B, 0x18,
  /* RLE: 004 Pixels @ 104,224*/ 4, 0x03,
  /* RLE: 035 Pixels @ 108,224*/ 35, 0x00,
  /* ABS: 012 Pixels @ 143,224*/ 0, 12, 0x03, 0x00, 0x0B, 0x0C, 0x0C, 0x06, 0x0D, 0x08, 0x08, 0x00, 0x03, 0x04,
  /* RLE: 075 Pixels @ 155,224*/ 75, 0x00,
  /* RLE: 001 Pixels @ 230,224*/ 1, 0x04,
  /* RLE: 012 Pixels @ 231,224*/ 12, 0x03,
  /* ABS: 002 Pixels @ 243,224*/ 0, 2, 0x04, 0x04,
  /* RLE: 152 Pixels @ 245,224*/ 152, 0x00,
  /* ABS: 003 Pixels @ 397,224*/ 0, 3, 0x0D, 0x06, 0x06,
  /* RLE: 047 Pixels @ 000,225*/ 47, 0x02,
  /* ABS: 010 Pixels @ 047,225*/ 0, 10, 0x18, 0x03, 0x0B, 0x0D, 0x0F, 0x12, 0x00, 0x03, 0x00, 0x03,
  /* RLE: 041 Pixels @ 057,225*/ 41, 0x00,
  /* ABS: 009 Pixels @ 098,225*/ 0, 9, 0x0B, 0x18, 0x03, 0x03, 0x03, 0x00, 0x04, 0x0E, 0x0D,
  /* RLE: 036 Pixels @ 107,225*/ 36, 0x00,
  /* RLE: 008 Pixels @ 143,225*/ 8, 0x03,
  /* ABS: 003 Pixels @ 151,225*/ 0, 3, 0x00, 0x03, 0x03,
  /* RLE: 076 Pixels @ 154,225*/ 76, 0x00,
  /* ABS: 002 Pixels @ 230,225*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 232,225*/ 10, 0x03,
  /* ABS: 002 Pixels @ 242,225*/ 0, 2, 0x04, 0x04,
  /* RLE: 154 Pixels @ 244,225*/ 154, 0x00,
  /* ABS: 002 Pixels @ 398,225*/ 0, 2, 0x0D, 0x06,
  /* RLE: 048 Pixels @ 000,226*/ 48, 0x02,
  /* ABS: 009 Pixels @ 048,226*/ 0, 9, 0x00, 0x03, 0x0B, 0x0C, 0x12, 0x0D, 0x08, 0x0F, 0x03,
  /* RLE: 041 Pixels @ 057,226*/ 41, 0x00,
  /* ABS: 005 Pixels @ 098,226*/ 0, 5, 0x03, 0x00, 0x0C, 0x12, 0x0F,
  /* RLE: 004 Pixels @ 103,226*/ 4, 0x08,
  /* ABS: 002 Pixels @ 107,226*/ 0, 2, 0x04, 0x03,
  /* RLE: 033 Pixels @ 109,226*/ 33, 0x00,
  /* ABS: 012 Pixels @ 142,226*/ 0, 12, 0x03, 0x00, 0x06, 0x03, 0x12, 0x0E, 0x0C, 0x00, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 077 Pixels @ 154,226*/ 77, 0x00,
  /* RLE: 001 Pixels @ 231,226*/ 1, 0x04,
  /* RLE: 009 Pixels @ 232,226*/ 9, 0x03,
  /* ABS: 002 Pixels @ 241,226*/ 0, 2, 0x04, 0x04,
  /* RLE: 156 Pixels @ 243,226*/ 156, 0x00,
  /* RLE: 001 Pixels @ 399,226*/ 1, 0x0D,
  /* RLE: 049 Pixels @ 000,227*/ 49, 0x02,
  /* ABS: 008 Pixels @ 049,227*/ 0, 8, 0x00, 0x06, 0x08, 0x0F, 0x06, 0x11, 0x06, 0x03,
  /* RLE: 042 Pixels @ 057,227*/ 42, 0x00,
  /* ABS: 009 Pixels @ 099,227*/ 0, 9, 0x12, 0x08, 0x11, 0x0E, 0x0C, 0x0D, 0x08, 0x0C, 0x03,
  /* RLE: 036 Pixels @ 108,227*/ 36, 0x00,
  /* ABS: 003 Pixels @ 144,227*/ 0, 3, 0x0E, 0x03, 0x0D,
  /* RLE: 004 Pixels @ 147,227*/ 4, 0x08,
  /* ABS: 003 Pixels @ 151,227*/ 0, 3, 0x06, 0x03, 0x04,
  /* RLE: 078 Pixels @ 154,227*/ 78, 0x00,
  /* RLE: 001 Pixels @ 232,227*/ 1, 0x04,
  /* RLE: 007 Pixels @ 233,227*/ 7, 0x03,
  /* ABS: 002 Pixels @ 240,227*/ 0, 2, 0x04, 0x04,
  /* RLE: 158 Pixels @ 242,227*/ 158, 0x00,
  /* RLE: 049 Pixels @ 000,228*/ 49, 0x02,
  /* ABS: 002 Pixels @ 049,228*/ 0, 2, 0x03, 0x00,
  /* RLE: 004 Pixels @ 051,228*/ 4, 0x03,
  /* ABS: 003 Pixels @ 055,228*/ 0, 3, 0x08, 0x04, 0x03,
  /* RLE: 040 Pixels @ 058,228*/ 40, 0x00,
  /* ABS: 009 Pixels @ 098,228*/ 0, 9, 0x03, 0x00, 0x03, 0x03, 0x03, 0x0E, 0x08, 0x04, 0x03,
  /* RLE: 035 Pixels @ 107,228*/ 35, 0x00,
  /* ABS: 012 Pixels @ 142,228*/ 0, 12, 0x03, 0x00, 0x0B, 0x03, 0x03, 0x03, 0x00, 0x04, 0x0D, 0x04, 0x03, 0x04,
  /* RLE: 078 Pixels @ 154,228*/ 78, 0x00,
  /* ABS: 002 Pixels @ 232,228*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 234,228*/ 6, 0x03,
  /* ABS: 002 Pixels @ 240,228*/ 0, 2, 0x04, 0x04,
  /* RLE: 158 Pixels @ 242,228*/ 158, 0x00,
  /* RLE: 049 Pixels @ 000,229*/ 49, 0x02,
  /* RLE: 001 Pixels @ 049,229*/ 1, 0x18,
  /* RLE: 005 Pixels @ 050,229*/ 5, 0x03,
  /* RLE: 001 Pixels @ 055,229*/ 1, 0x04,
  /* RLE: 043 Pixels @ 056,229*/ 43, 0x00,
  /* ABS: 006 Pixels @ 099,229*/ 0, 6, 0x0B, 0x03, 0x03, 0x0D, 0x08, 0x04,
  /* RLE: 004 Pixels @ 105,229*/ 4, 0x03,
  /* RLE: 033 Pixels @ 109,229*/ 33, 0x00,
  /* ABS: 003 Pixels @ 142,229*/ 0, 3, 0x03, 0x0E, 0x12,
  /* RLE: 005 Pixels @ 145,229*/ 5, 0x03,
  /* ABS: 004 Pixels @ 150,229*/ 0, 4, 0x0C, 0x0C, 0x03, 0x04,
  /* RLE: 079 Pixels @ 154,229*/ 79, 0x00,
  /* RLE: 001 Pixels @ 233,229*/ 1, 0x04,
  /* RLE: 007 Pixels @ 234,229*/ 7, 0x03,
  /* RLE: 001 Pixels @ 241,229*/ 1, 0x04,
  /* RLE: 091 Pixels @ 242,229*/ 91, 0x00,
  /* RLE: 006 Pixels @ 333,229*/ 6, 0x04,
  /* RLE: 061 Pixels @ 339,229*/ 61, 0x00,
  /* RLE: 049 Pixels @ 000,230*/ 49, 0x02,
  /* RLE: 001 Pixels @ 049,230*/ 1, 0x18,
  /* RLE: 004 Pixels @ 050,230*/ 4, 0x03,
  /* ABS: 003 Pixels @ 054,230*/ 0, 3, 0x00, 0x03, 0x03,
  /* RLE: 043 Pixels @ 057,230*/ 43, 0x00,
  /* ABS: 008 Pixels @ 100,230*/ 0, 8, 0x03, 0x0D, 0x08, 0x0C, 0x0B, 0x0E, 0x06, 0x08,
  /* RLE: 035 Pixels @ 108,230*/ 35, 0x00,
  /* ABS: 011 Pixels @ 143,230*/ 0, 11, 0x03, 0x03, 0x0C, 0x08, 0x06, 0x12, 0x0C, 0x0F, 0x0C, 0x03, 0x04,
  /* RLE: 079 Pixels @ 154,230*/ 79, 0x00,
  /* ABS: 002 Pixels @ 233,230*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 235,230*/ 7, 0x03,
  /* RLE: 001 Pixels @ 242,230*/ 1, 0x04,
  /* RLE: 090 Pixels @ 243,230*/ 90, 0x00,
  /* ABS: 007 Pixels @ 333,230*/ 0, 7, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 060 Pixels @ 340,230*/ 60, 0x00,
  /* RLE: 049 Pixels @ 000,231*/ 49, 0x02,
  /* ABS: 008 Pixels @ 049,231*/ 0, 8, 0x03, 0x00, 0x12, 0x03, 0x0D, 0x08, 0x06, 0x03,
  /* RLE: 042 Pixels @ 057,231*/ 42, 0x00,
  /* ABS: 002 Pixels @ 099,231*/ 0, 2, 0x03, 0x12,
  /* RLE: 005 Pixels @ 101,231*/ 5, 0x08,
  /* ABS: 004 Pixels @ 106,231*/ 0, 4, 0x06, 0x0E, 0x00, 0x03,
  /* RLE: 034 Pixels @ 110,231*/ 34, 0x00,
  /* ABS: 010 Pixels @ 144,231*/ 0, 10, 0x03, 0x0B, 0x0E, 0x06, 0x11, 0x08, 0x0D, 0x03, 0x03, 0x04,
  /* RLE: 080 Pixels @ 154,231*/ 80, 0x00,
  /* RLE: 001 Pixels @ 234,231*/ 1, 0x04,
  /* RLE: 007 Pixels @ 235,231*/ 7, 0x03,
  /* ABS: 002 Pixels @ 242,231*/ 0, 2, 0x04, 0x04,
  /* RLE: 088 Pixels @ 244,231*/ 88, 0x00,
  /* ABS: 002 Pixels @ 332,231*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 334,231*/ 5, 0x03,
  /* RLE: 001 Pixels @ 339,231*/ 1, 0x04,
  /* RLE: 060 Pixels @ 340,231*/ 60, 0x00,
  /* RLE: 049 Pixels @ 000,232*/ 49, 0x02,
  /* ABS: 007 Pixels @ 049,232*/ 0, 7, 0x03, 0x0E, 0x0D, 0x0B, 0x08, 0x04, 0x0F,
  /* RLE: 043 Pixels @ 056,232*/ 43, 0x00,
  /* ABS: 010 Pixels @ 099,232*/ 0, 10, 0x03, 0x0E, 0x11, 0x0E, 0x0C, 0x00, 0x00, 0x04, 0x00, 0x0D,
  /* RLE: 036 Pixels @ 109,232*/ 36, 0x00,
  /* RLE: 008 Pixels @ 145,232*/ 8, 0x03,
  /* RLE: 082 Pixels @ 153,232*/ 82, 0x00,
  /* RLE: 001 Pixels @ 235,232*/ 1, 0x04,
  /* RLE: 007 Pixels @ 236,232*/ 7, 0x03,
  /* RLE: 001 Pixels @ 243,232*/ 1, 0x04,
  /* RLE: 088 Pixels @ 244,232*/ 88, 0x00,
  /* RLE: 001 Pixels @ 332,232*/ 1, 0x04,
  /* RLE: 006 Pixels @ 333,232*/ 6, 0x03,
  /* RLE: 001 Pixels @ 339,232*/ 1, 0x04,
  /* RLE: 060 Pixels @ 340,232*/ 60, 0x00,
  /* RLE: 049 Pixels @ 000,233*/ 49, 0x02,
  /* ABS: 007 Pixels @ 049,233*/ 0, 7, 0x03, 0x06, 0x0C, 0x0B, 0x08, 0x00, 0x06,
  /* RLE: 044 Pixels @ 056,233*/ 44, 0x00,
  /* ABS: 011 Pixels @ 100,233*/ 0, 11, 0x03, 0x00, 0x0C, 0x06, 0x0F, 0x08, 0x08, 0x0B, 0x06, 0x0B, 0x03,
  /* RLE: 033 Pixels @ 111,233*/ 33, 0x00,
  /* ABS: 009 Pixels @ 144,233*/ 0, 9, 0x03, 0x00, 0x0C, 0x0C, 0x0B, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 082 Pixels @ 153,233*/ 82, 0x00,
  /* ABS: 002 Pixels @ 235,233*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 237,233*/ 6, 0x03,
  /* ABS: 002 Pixels @ 243,233*/ 0, 2, 0x04, 0x04,
  /* RLE: 086 Pixels @ 245,233*/ 86, 0x00,
  /* ABS: 002 Pixels @ 331,233*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 333,233*/ 6, 0x03,
  /* RLE: 001 Pixels @ 339,233*/ 1, 0x04,
  /* RLE: 060 Pixels @ 340,233*/ 60, 0x00,
  /* RLE: 049 Pixels @ 000,234*/ 49, 0x02,
  /* ABS: 007 Pixels @ 049,234*/ 0, 7, 0x03, 0x0C, 0x12, 0x0E, 0x08, 0x00, 0x08,
  /* RLE: 044 Pixels @ 056,234*/ 44, 0x00,
  /* ABS: 010 Pixels @ 100,234*/ 0, 10, 0x03, 0x12, 0x08, 0x11, 0x0E, 0x0C, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 033 Pixels @ 110,234*/ 33, 0x00,
  /* ABS: 003 Pixels @ 143,234*/ 0, 3, 0x03, 0x00, 0x0F,
  /* RLE: 004 Pixels @ 146,234*/ 4, 0x08,
  /* ABS: 003 Pixels @ 150,234*/ 0, 3, 0x06, 0x03, 0x04,
  /* RLE: 083 Pixels @ 153,234*/ 83, 0x00,
  /* RLE: 001 Pixels @ 236,234*/ 1, 0x04,
  /* RLE: 007 Pixels @ 237,234*/ 7, 0x03,
  /* RLE: 001 Pixels @ 244,234*/ 1, 0x04,
  /* RLE: 086 Pixels @ 245,234*/ 86, 0x00,
  /* RLE: 001 Pixels @ 331,234*/ 1, 0x04,
  /* RLE: 007 Pixels @ 332,234*/ 7, 0x03,
  /* RLE: 001 Pixels @ 339,234*/ 1, 0x04,
  /* RLE: 060 Pixels @ 340,234*/ 60, 0x00,
  /* RLE: 049 Pixels @ 000,235*/ 49, 0x02,
  /* ABS: 009 Pixels @ 049,235*/ 0, 9, 0x03, 0x00, 0x0F, 0x08, 0x06, 0x03, 0x0C, 0x03, 0x03,
  /* RLE: 044 Pixels @ 058,235*/ 44, 0x00,
  /* ABS: 008 Pixels @ 102,235*/ 0, 8, 0x03, 0x03, 0x00, 0x0B, 0x00, 0x03, 0x03, 0x0C,
  /* RLE: 032 Pixels @ 110,235*/ 32, 0x00,
  /* ABS: 011 Pixels @ 142,235*/ 0, 11, 0x03, 0x00, 0x00, 0x11, 0x03, 0x03, 0x00, 0x04, 0x04, 0x03, 0x04,
  /* RLE: 083 Pixels @ 153,235*/ 83, 0x00,
  /* ABS: 002 Pixels @ 236,235*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 238,235*/ 7, 0x03,
  /* RLE: 001 Pixels @ 245,235*/ 1, 0x04,
  /* RLE: 084 Pixels @ 246,235*/ 84, 0x00,
  /* ABS: 002 Pixels @ 330,235*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 332,235*/ 6, 0x03,
  /* RLE: 001 Pixels @ 338,235*/ 1, 0x04,
  /* RLE: 061 Pixels @ 339,235*/ 61, 0x00,
  /* RLE: 049 Pixels @ 000,236*/ 49, 0x02,
  /* ABS: 010 Pixels @ 049,236*/ 0, 10, 0x18, 0x03, 0x00, 0x0B, 0x03, 0x03, 0x00, 0x0E, 0x00, 0x03,
  /* RLE: 041 Pixels @ 059,236*/ 41, 0x00,
  /* ABS: 009 Pixels @ 100,236*/ 0, 9, 0x0B, 0x18, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x0D, 0x03,
  /* RLE: 032 Pixels @ 109,236*/ 32, 0x00,
  /* ABS: 005 Pixels @ 141,236*/ 0, 5, 0x03, 0x00, 0x00, 0x00, 0x11,
  /* RLE: 006 Pixels @ 146,236*/ 6, 0x03,
  /* RLE: 001 Pixels @ 152,236*/ 1, 0x04,
  /* RLE: 084 Pixels @ 153,236*/ 84, 0x00,
  /* RLE: 001 Pixels @ 237,236*/ 1, 0x04,
  /* RLE: 007 Pixels @ 238,236*/ 7, 0x03,
  /* RLE: 001 Pixels @ 245,236*/ 1, 0x04,
  /* RLE: 084 Pixels @ 246,236*/ 84, 0x00,
  /* RLE: 001 Pixels @ 330,236*/ 1, 0x04,
  /* RLE: 007 Pixels @ 331,236*/ 7, 0x03,
  /* RLE: 001 Pixels @ 338,236*/ 1, 0x04,
  /* RLE: 061 Pixels @ 339,236*/ 61, 0x00,
  /* RLE: 050 Pixels @ 000,237*/ 50, 0x02,
  /* ABS: 010 Pixels @ 050,237*/ 0, 10, 0x03, 0x03, 0x04, 0x0C, 0x12, 0x0D, 0x08, 0x08, 0x0E, 0x03,
  /* RLE: 040 Pixels @ 060,237*/ 40, 0x00,
  /* ABS: 010 Pixels @ 100,237*/ 0, 10, 0x0C, 0x03, 0x04, 0x08, 0x0E, 0x11, 0x0B, 0x11, 0x0C, 0x03,
  /* RLE: 031 Pixels @ 110,237*/ 31, 0x00,
  /* ABS: 012 Pixels @ 141,237*/ 0, 12, 0x03, 0x06, 0x08, 0x08, 0x08, 0x0F, 0x06, 0x0C, 0x0C, 0x03, 0x03, 0x04,
  /* RLE: 085 Pixels @ 153,237*/ 85, 0x00,
  /* RLE: 001 Pixels @ 238,237*/ 1, 0x04,
  /* RLE: 007 Pixels @ 239,237*/ 7, 0x03,
  /* RLE: 001 Pixels @ 246,237*/ 1, 0x04,
  /* RLE: 082 Pixels @ 247,237*/ 82, 0x00,
  /* ABS: 002 Pixels @ 329,237*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 331,237*/ 6, 0x03,
  /* RLE: 001 Pixels @ 337,237*/ 1, 0x04,
  /* RLE: 062 Pixels @ 338,237*/ 62, 0x00,
  /* RLE: 050 Pixels @ 000,238*/ 50, 0x02,
  /* ABS: 010 Pixels @ 050,238*/ 0, 10, 0x03, 0x12, 0x08, 0x08, 0x11, 0x06, 0x08, 0x04, 0x0B, 0x03,
  /* RLE: 041 Pixels @ 060,238*/ 41, 0x00,
  /* ABS: 010 Pixels @ 101,238*/ 0, 10, 0x03, 0x0C, 0x06, 0x03, 0x0F, 0x03, 0x06, 0x0C, 0x03, 0x0C,
  /* RLE: 030 Pixels @ 111,238*/ 30, 0x00,
  /* ABS: 012 Pixels @ 141,238*/ 0, 12, 0x03, 0x00, 0x00, 0x0C, 0x0C, 0x06, 0x0D, 0x08, 0x08, 0x0B, 0x03, 0x04,
  /* RLE: 085 Pixels @ 153,238*/ 85, 0x00,
  /* ABS: 002 Pixels @ 238,238*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 240,238*/ 6, 0x03,
  /* RLE: 001 Pixels @ 246,238*/ 1, 0x04,
  /* RLE: 081 Pixels @ 247,238*/ 81, 0x00,
  /* ABS: 002 Pixels @ 328,238*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 330,238*/ 7, 0x03,
  /* RLE: 001 Pixels @ 337,238*/ 1, 0x04,
  /* RLE: 062 Pixels @ 338,238*/ 62, 0x00,
  /* RLE: 050 Pixels @ 000,239*/ 50, 0x02,
  /* ABS: 009 Pixels @ 050,239*/ 0, 9, 0x03, 0x06, 0x12, 0x03, 0x03, 0x03, 0x06, 0x00, 0x03,
  /* RLE: 043 Pixels @ 059,239*/ 43, 0x00,
  /* ABS: 009 Pixels @ 102,239*/ 0, 9, 0x04, 0x0F, 0x0B, 0x12, 0x06, 0x08, 0x00, 0x18, 0x0C,
  /* RLE: 031 Pixels @ 111,239*/ 31, 0x00,
  /* RLE: 003 Pixels @ 142,239*/ 3, 0x03,
  /* ABS: 007 Pixels @ 145,239*/ 0, 7, 0x00, 0x0C, 0x0C, 0x0B, 0x00, 0x03, 0x03,
  /* RLE: 087 Pixels @ 152,239*/ 87, 0x00,
  /* RLE: 001 Pixels @ 239,239*/ 1, 0x04,
  /* RLE: 007 Pixels @ 240,239*/ 7, 0x03,
  /* RLE: 001 Pixels @ 247,239*/ 1, 0x04,
  /* RLE: 078 Pixels @ 248,239*/ 78, 0x00,
  /* ABS: 002 Pixels @ 326,239*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 328,239*/ 8, 0x03,
  /* RLE: 001 Pixels @ 336,239*/ 1, 0x04,
  /* RLE: 063 Pixels @ 337,239*/ 63, 0x00,
  /* RLE: 050 Pixels @ 000,240*/ 50, 0x02,
  /* ABS: 009 Pixels @ 050,240*/ 0, 9, 0x03, 0x0B, 0x0B, 0x12, 0x03, 0x0D, 0x08, 0x06, 0x03,
  /* RLE: 043 Pixels @ 059,240*/ 43, 0x00,
  /* ABS: 009 Pixels @ 102,240*/ 0, 9, 0x03, 0x06, 0x06, 0x0C, 0x0F, 0x04, 0x03, 0x1B, 0x0C,
  /* RLE: 034 Pixels @ 111,240*/ 34, 0x00,
  /* RLE: 001 Pixels @ 145,240*/ 1, 0x0F,
  /* RLE: 004 Pixels @ 146,240*/ 4, 0x08,
  /* ABS: 002 Pixels @ 150,240*/ 0, 2, 0x06, 0x03,
  /* RLE: 087 Pixels @ 152,240*/ 87, 0x00,
  /* ABS: 002 Pixels @ 239,240*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 241,240*/ 6, 0x03,
  /* RLE: 001 Pixels @ 247,240*/ 1, 0x04,
  /* RLE: 076 Pixels @ 248,240*/ 76, 0x00,
  /* ABS: 002 Pixels @ 324,240*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 326,240*/ 10, 0x03,
  /* RLE: 001 Pixels @ 336,240*/ 1, 0x04,
  /* RLE: 063 Pixels @ 337,240*/ 63, 0x00,
  /* RLE: 050 Pixels @ 000,241*/ 50, 0x02,
  /* ABS: 010 Pixels @ 050,241*/ 0, 10, 0x18, 0x03, 0x0E, 0x0D, 0x0B, 0x08, 0x0C, 0x0F, 0x00, 0x03,
  /* RLE: 041 Pixels @ 060,241*/ 41, 0x00,
  /* ABS: 010 Pixels @ 101,241*/ 0, 10, 0x0C, 0x03, 0x03, 0x00, 0x03, 0x00, 0x0B, 0x03, 0x03, 0x0C,
  /* RLE: 033 Pixels @ 111,241*/ 33, 0x00,
  /* ABS: 008 Pixels @ 144,241*/ 0, 8, 0x0B, 0x11, 0x03, 0x03, 0x0B, 0x04, 0x04, 0x03,
  /* RLE: 088 Pixels @ 152,241*/ 88, 0x00,
  /* RLE: 001 Pixels @ 240,241*/ 1, 0x04,
  /* RLE: 007 Pixels @ 241,241*/ 7, 0x03,
  /* RLE: 001 Pixels @ 248,241*/ 1, 0x04,
  /* RLE: 073 Pixels @ 249,241*/ 73, 0x00,
  /* RLE: 003 Pixels @ 322,241*/ 3, 0x04,
  /* RLE: 010 Pixels @ 325,241*/ 10, 0x03,
  /* RLE: 001 Pixels @ 335,241*/ 1, 0x04,
  /* RLE: 064 Pixels @ 336,241*/ 64, 0x00,
  /* RLE: 051 Pixels @ 000,242*/ 51, 0x02,
  /* ABS: 007 Pixels @ 051,242*/ 0, 7, 0x03, 0x06, 0x0C, 0x00, 0x08, 0x00, 0x06,
  /* RLE: 043 Pixels @ 058,242*/ 43, 0x00,
  /* ABS: 009 Pixels @ 101,242*/ 0, 9, 0x0C, 0x18, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x0D, 0x03,
  /* RLE: 033 Pixels @ 110,242*/ 33, 0x00,
  /* ABS: 003 Pixels @ 143,242*/ 0, 3, 0x03, 0x03, 0x11,
  /* RLE: 005 Pixels @ 146,242*/ 5, 0x03,
  /* RLE: 089 Pixels @ 151,242*/ 89, 0x00,
  /* ABS: 002 Pixels @ 240,242*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 242,242*/ 6, 0x03,
  /* RLE: 001 Pixels @ 248,242*/ 1, 0x04,
  /* RLE: 054 Pixels @ 249,242*/ 54, 0x00,
  /* ABS: 007 Pixels @ 303,242*/ 0, 7, 0x03, 0x00, 0x00, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 010 Pixels @ 310,242*/ 10, 0x00,
  /* RLE: 003 Pixels @ 320,242*/ 3, 0x04,
  /* RLE: 011 Pixels @ 323,242*/ 11, 0x03,
  /* ABS: 002 Pixels @ 334,242*/ 0, 2, 0x04, 0x04,
  /* RLE: 064 Pixels @ 336,242*/ 64, 0x00,
  /* RLE: 051 Pixels @ 000,243*/ 51, 0x02,
  /* ABS: 007 Pixels @ 051,243*/ 0, 7, 0x03, 0x0C, 0x12, 0x0E, 0x08, 0x00, 0x08,
  /* RLE: 045 Pixels @ 058,243*/ 45, 0x00,
  /* ABS: 009 Pixels @ 103,243*/ 0, 9, 0x04, 0x08, 0x12, 0x04, 0x0B, 0x0F, 0x04, 0x03, 0x0B,
  /* RLE: 031 Pixels @ 112,243*/ 31, 0x00,
  /* ABS: 008 Pixels @ 143,243*/ 0, 8, 0x03, 0x0C, 0x08, 0x0F, 0x06, 0x0C, 0x04, 0x03,
  /* RLE: 090 Pixels @ 151,243*/ 90, 0x00,
  /* RLE: 001 Pixels @ 241,243*/ 1, 0x04,
  /* RLE: 007 Pixels @ 242,243*/ 7, 0x03,
  /* RLE: 001 Pixels @ 249,243*/ 1, 0x04,
  /* RLE: 051 Pixels @ 250,243*/ 51, 0x00,
  /* ABS: 009 Pixels @ 301,243*/ 0, 9, 0x03, 0x00, 0x04, 0x03, 0x03, 0x03, 0x04, 0x06, 0x03,
  /* RLE: 009 Pixels @ 310,243*/ 9, 0x00,
  /* ABS: 002 Pixels @ 319,243*/ 0, 2, 0x04, 0x04,
  /* RLE: 012 Pixels @ 321,243*/ 12, 0x03,
  /* ABS: 002 Pixels @ 333,243*/ 0, 2, 0x04, 0x04,
  /* RLE: 065 Pixels @ 335,243*/ 65, 0x00,
  /* RLE: 051 Pixels @ 000,244*/ 51, 0x02,
  /* ABS: 009 Pixels @ 051,244*/ 0, 9, 0x03, 0x00, 0x0F, 0x08, 0x06, 0x03, 0x0C, 0x03, 0x03,
  /* RLE: 042 Pixels @ 060,244*/ 42, 0x00,
  /* ABS: 011 Pixels @ 102,244*/ 0, 11, 0x03, 0x0C, 0x0D, 0x03, 0x03, 0x03, 0x0E, 0x0C, 0x03, 0x00, 0x03,
  /* RLE: 028 Pixels @ 113,244*/ 28, 0x00,
  /* ABS: 010 Pixels @ 141,244*/ 0, 10, 0x03, 0x00, 0x03, 0x0B, 0x0C, 0x06, 0x0D, 0x08, 0x08, 0x0B,
  /* RLE: 090 Pixels @ 151,244*/ 90, 0x00,
  /* ABS: 002 Pixels @ 241,244*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 243,244*/ 6, 0x03,
  /* RLE: 001 Pixels @ 249,244*/ 1, 0x04,
  /* RLE: 046 Pixels @ 250,244*/ 46, 0x00,
  /* RLE: 006 Pixels @ 296,244*/ 6, 0x03,
  /* ABS: 008 Pixels @ 302,244*/ 0, 8, 0x0E, 0x11, 0x0B, 0x00, 0x0D, 0x11, 0x06, 0x03,
  /* RLE: 007 Pixels @ 310,244*/ 7, 0x00,
  /* ABS: 002 Pixels @ 317,244*/ 0, 2, 0x04, 0x04,
  /* RLE: 012 Pixels @ 319,244*/ 12, 0x03,
  /* RLE: 003 Pixels @ 331,244*/ 3, 0x04,
  /* RLE: 066 Pixels @ 334,244*/ 66, 0x00,
  /* RLE: 051 Pixels @ 000,245*/ 51, 0x02,
  /* ABS: 010 Pixels @ 051,245*/ 0, 10, 0x18, 0x03, 0x00, 0x00, 0x03, 0x03, 0x0B, 0x0E, 0x00, 0x03,
  /* RLE: 042 Pixels @ 061,245*/ 42, 0x00,
  /* ABS: 010 Pixels @ 103,245*/ 0, 10, 0x0B, 0x0F, 0x00, 0x03, 0x0B, 0x0F, 0x06, 0x0F, 0x08, 0x03,
  /* RLE: 027 Pixels @ 113,245*/ 27, 0x00,
  /* ABS: 003 Pixels @ 140,245*/ 0, 3, 0x03, 0x00, 0x00,
  /* RLE: 006 Pixels @ 143,245*/ 6, 0x03,
  /* ABS: 002 Pixels @ 149,245*/ 0, 2, 0x00, 0x03,
  /* RLE: 091 Pixels @ 151,245*/ 91, 0x00,
  /* RLE: 001 Pixels @ 242,245*/ 1, 0x04,
  /* RLE: 007 Pixels @ 243,245*/ 7, 0x03,
  /* RLE: 001 Pixels @ 250,245*/ 1, 0x04,
  /* RLE: 044 Pixels @ 251,245*/ 44, 0x00,
  /* ABS: 022 Pixels @ 295,245*/ 0, 22, 0x03, 0x00, 0x12, 0x08, 0x11, 0x00, 0x03, 0x12, 0x08, 0x0F, 0x00, 0x08, 0x06, 0x03, 0x03, 0x00, 0x03, 0x03, 0x03, 0x00, 0x04, 0x04,
  /* RLE: 012 Pixels @ 317,245*/ 12, 0x03,
  /* RLE: 003 Pixels @ 329,245*/ 3, 0x04,
  /* RLE: 068 Pixels @ 332,245*/ 68, 0x00,
  /* RLE: 052 Pixels @ 000,246*/ 52, 0x02,
  /* ABS: 010 Pixels @ 052,246*/ 0, 10, 0x00, 0x03, 0x04, 0x0C, 0x12, 0x0D, 0x08, 0x08, 0x0E, 0x03,
  /* RLE: 040 Pixels @ 062,246*/ 40, 0x00,
  /* ABS: 003 Pixels @ 102,246*/ 0, 3, 0x0B, 0x03, 0x06,
  /* RLE: 004 Pixels @ 105,246*/ 4, 0x08,
  /* ABS: 004 Pixels @ 109,246*/ 0, 4, 0x11, 0x12, 0x0C, 0x03,
  /* RLE: 027 Pixels @ 113,246*/ 27, 0x00,
  /* ABS: 011 Pixels @ 140,246*/ 0, 11, 0x03, 0x06, 0x06, 0x0C, 0x08, 0x0D, 0x06, 0x0C, 0x0C, 0x03, 0x03,
  /* RLE: 091 Pixels @ 151,246*/ 91, 0x00,
  /* ABS: 002 Pixels @ 242,246*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 244,246*/ 6, 0x03,
  /* RLE: 001 Pixels @ 250,246*/ 1, 0x04,
  /* RLE: 043 Pixels @ 251,246*/ 43, 0x00,
  /* ABS: 022 Pixels @ 294,246*/ 0, 22, 0x03, 0x0B, 0x08, 0x12, 0x0C, 0x11, 0x0C, 0x03, 0x0E, 0x0F, 0x0E, 0x03, 0x12, 0x0F, 0x03, 0x00, 0x03, 0x12, 0x11, 0x03, 0x04, 0x04,
  /* RLE: 012 Pixels @ 316,246*/ 12, 0x03,
  /* ABS: 002 Pixels @ 328,246*/ 0, 2, 0x04, 0x04,
  /* RLE: 070 Pixels @ 330,246*/ 70, 0x00,
  /* RLE: 052 Pixels @ 000,247*/ 52, 0x02,
  /* ABS: 010 Pixels @ 052,247*/ 0, 10, 0x03, 0x12, 0x08, 0x08, 0x11, 0x06, 0x08, 0x04, 0x0B, 0x03,
  /* RLE: 041 Pixels @ 062,247*/ 41, 0x00,
  /* ABS: 009 Pixels @ 103,247*/ 0, 9, 0x03, 0x0D, 0x06, 0x0C, 0x0B, 0x00, 0x03, 0x03, 0x03,
  /* RLE: 028 Pixels @ 112,247*/ 28, 0x00,
  /* ABS: 009 Pixels @ 140,247*/ 0, 9, 0x03, 0x00, 0x00, 0x00, 0x0C, 0x06, 0x0D, 0x08, 0x08,
  /* RLE: 094 Pixels @ 149,247*/ 94, 0x00,
  /* RLE: 001 Pixels @ 243,247*/ 1, 0x04,
  /* RLE: 007 Pixels @ 244,247*/ 7, 0x03,
  /* RLE: 001 Pixels @ 251,247*/ 1, 0x04,
  /* RLE: 042 Pixels @ 252,247*/ 42, 0x00,
  /* ABS: 003 Pixels @ 294,247*/ 0, 3, 0x03, 0x06, 0x0D,
  /* RLE: 006 Pixels @ 297,247*/ 6, 0x03,
  /* ABS: 010 Pixels @ 303,247*/ 0, 10, 0x0E, 0x08, 0x00, 0x00, 0x08, 0x04, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 013 Pixels @ 313,247*/ 13, 0x03,
  /* ABS: 002 Pixels @ 326,247*/ 0, 2, 0x04, 0x04,
  /* RLE: 072 Pixels @ 328,247*/ 72, 0x00,
  /* RLE: 052 Pixels @ 000,248*/ 52, 0x02,
  /* ABS: 009 Pixels @ 052,248*/ 0, 9, 0x03, 0x06, 0x12, 0x03, 0x03, 0x03, 0x0E, 0x00, 0x03,
  /* RLE: 042 Pixels @ 061,248*/ 42, 0x00,
  /* RLE: 003 Pixels @ 103,248*/ 3, 0x03,
  /* ABS: 007 Pixels @ 106,248*/ 0, 7, 0x00, 0x04, 0x00, 0x03, 0x03, 0x09, 0x0C,
  /* RLE: 028 Pixels @ 113,248*/ 28, 0x00,
  /* RLE: 007 Pixels @ 141,248*/ 7, 0x03,
  /* ABS: 002 Pixels @ 148,248*/ 0, 2, 0x00, 0x03,
  /* RLE: 093 Pixels @ 150,248*/ 93, 0x00,
  /* ABS: 002 Pixels @ 243,248*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 245,248*/ 6, 0x03,
  /* RLE: 001 Pixels @ 251,248*/ 1, 0x04,
  /* RLE: 042 Pixels @ 252,248*/ 42, 0x00,
  /* ABS: 015 Pixels @ 294,248*/ 0, 15, 0x03, 0x0C, 0x08, 0x0D, 0x0F, 0x08, 0x08, 0x12, 0x03, 0x00, 0x08, 0x0C, 0x00, 0x11, 0x06,
  /* RLE: 015 Pixels @ 309,248*/ 15, 0x03,
  /* ABS: 002 Pixels @ 324,248*/ 0, 2, 0x04, 0x04,
  /* RLE: 074 Pixels @ 326,248*/ 74, 0x00,
  /* RLE: 052 Pixels @ 000,249*/ 52, 0x02,
  /* ABS: 008 Pixels @ 052,249*/ 0, 8, 0x03, 0x0B, 0x00, 0x03, 0x03, 0x03, 0x00, 0x03,
  /* RLE: 044 Pixels @ 060,249*/ 44, 0x00,
  /* ABS: 009 Pixels @ 104,249*/ 0, 9, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x0D, 0x03, 0x18, 0x0C,
  /* RLE: 029 Pixels @ 113,249*/ 29, 0x00,
  /* ABS: 009 Pixels @ 142,249*/ 0, 9, 0x03, 0x0C, 0x08, 0x08, 0x0B, 0x0E, 0x03, 0x03, 0x04,
  /* RLE: 093 Pixels @ 151,249*/ 93, 0x00,
  /* RLE: 001 Pixels @ 244,249*/ 1, 0x04,
  /* RLE: 007 Pixels @ 245,249*/ 7, 0x03,
  /* RLE: 001 Pixels @ 252,249*/ 1, 0x04,
  /* RLE: 039 Pixels @ 253,249*/ 39, 0x00,
  /* ABS: 017 Pixels @ 292,249*/ 0, 17, 0x04, 0x04, 0x03, 0x03, 0x0C, 0x06, 0x12, 0x0C, 0x12, 0x08, 0x04, 0x03, 0x0E, 0x08, 0x0F, 0x04, 0x04,
  /* RLE: 013 Pixels @ 309,249*/ 13, 0x03,
  /* RLE: 003 Pixels @ 322,249*/ 3, 0x04,
  /* RLE: 075 Pixels @ 325,249*/ 75, 0x00,
  /* RLE: 052 Pixels @ 000,250*/ 52, 0x02,
  /* ABS: 009 Pixels @ 052,250*/ 0, 9, 0x03, 0x0B, 0x0C, 0x12, 0x0D, 0x08, 0x0F, 0x03, 0x04,
  /* RLE: 042 Pixels @ 061,250*/ 42, 0x00,
  /* ABS: 010 Pixels @ 103,250*/ 0, 10, 0x03, 0x04, 0x08, 0x0E, 0x11, 0x00, 0x11, 0x0C, 0x03, 0x0C,
  /* RLE: 028 Pixels @ 113,250*/ 28, 0x00,
  /* ABS: 010 Pixels @ 141,250*/ 0, 10, 0x03, 0x00, 0x08, 0x04, 0x0D, 0x0B, 0x0F, 0x0C, 0x03, 0x04,
  /* RLE: 093 Pixels @ 151,250*/ 93, 0x00,
  /* ABS: 002 Pixels @ 244,250*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 246,250*/ 6, 0x03,
  /* RLE: 001 Pixels @ 252,250*/ 1, 0x04,
  /* RLE: 035 Pixels @ 253,250*/ 35, 0x00,
  /* RLE: 007 Pixels @ 288,250*/ 7, 0x03,
  /* RLE: 001 Pixels @ 295,250*/ 1, 0x0B,
  /* RLE: 005 Pixels @ 296,250*/ 5, 0x03,
  /* ABS: 006 Pixels @ 301,250*/ 0, 6, 0x08, 0x0C, 0x03, 0x03, 0x0B, 0x00,
  /* RLE: 013 Pixels @ 307,250*/ 13, 0x03,
  /* RLE: 003 Pixels @ 320,250*/ 3, 0x04,
  /* RLE: 077 Pixels @ 323,250*/ 77, 0x00,
  /* RLE: 052 Pixels @ 000,251*/ 52, 0x02,
  /* ABS: 008 Pixels @ 052,251*/ 0, 8, 0x03, 0x06, 0x08, 0x0F, 0x06, 0x11, 0x06, 0x03,
  /* RLE: 043 Pixels @ 060,251*/ 43, 0x00,
  /* ABS: 010 Pixels @ 103,251*/ 0, 10, 0x03, 0x0C, 0x06, 0x03, 0x0F, 0x03, 0x06, 0x0C, 0x03, 0x0C,
  /* RLE: 029 Pixels @ 113,251*/ 29, 0x00,
  /* ABS: 009 Pixels @ 142,251*/ 0, 9, 0x0C, 0x06, 0x03, 0x0F, 0x03, 0x0E, 0x0C, 0x03, 0x04,
  /* RLE: 094 Pixels @ 151,251*/ 94, 0x00,
  /* RLE: 001 Pixels @ 245,251*/ 1, 0x04,
  /* RLE: 006 Pixels @ 246,251*/ 6, 0x03,
  /* ABS: 002 Pixels @ 252,251*/ 0, 2, 0x04, 0x04,
  /* RLE: 024 Pixels @ 254,251*/ 24, 0x00,
  /* RLE: 003 Pixels @ 278,251*/ 3, 0x03,
  /* RLE: 005 Pixels @ 281,251*/ 5, 0x00,
  /* ABS: 017 Pixels @ 286,251*/ 0, 17, 0x03, 0x03, 0x00, 0x11, 0x08, 0x0C, 0x03, 0x0E, 0x08, 0x08, 0x04, 0x08, 0x0C, 0x04, 0x0D, 0x0F, 0x00,
  /* RLE: 016 Pixels @ 303,251*/ 16, 0x03,
  /* ABS: 002 Pixels @ 319,251*/ 0, 2, 0x04, 0x04,
  /* RLE: 079 Pixels @ 321,251*/ 79, 0x00,
  /* RLE: 052 Pixels @ 000,252*/ 52, 0x02,
  /* ABS: 002 Pixels @ 052,252*/ 0, 2, 0x03, 0x00,
  /* RLE: 004 Pixels @ 054,252*/ 4, 0x03,
  /* ABS: 002 Pixels @ 058,252*/ 0, 2, 0x08, 0x04,
  /* RLE: 043 Pixels @ 060,252*/ 43, 0x00,
  /* ABS: 011 Pixels @ 103,252*/ 0, 11, 0x03, 0x04, 0x0F, 0x0B, 0x12, 0x06, 0x08, 0x0B, 0x18, 0x09, 0x0C,
  /* RLE: 027 Pixels @ 114,252*/ 27, 0x00,
  /* ABS: 010 Pixels @ 141,252*/ 0, 10, 0x03, 0x00, 0x08, 0x0E, 0x0F, 0x0B, 0x11, 0x0C, 0x03, 0x04,
  /* RLE: 094 Pixels @ 151,252*/ 94, 0x00,
  /* RLE: 001 Pixels @ 245,252*/ 1, 0x04,
  /* RLE: 007 Pixels @ 246,252*/ 7, 0x03,
  /* RLE: 001 Pixels @ 253,252*/ 1, 0x04,
  /* RLE: 024 Pixels @ 254,252*/ 24, 0x00,
  /* ABS: 003 Pixels @ 278,252*/ 0, 3, 0x03, 0x06, 0x04,
  /* RLE: 004 Pixels @ 281,252*/ 4, 0x03,
  /* ABS: 017 Pixels @ 285,252*/ 0, 17, 0x00, 0x00, 0x11, 0x06, 0x04, 0x06, 0x08, 0x00, 0x0E, 0x0E, 0x00, 0x03, 0x06, 0x08, 0x08, 0x12, 0x00,
  /* RLE: 015 Pixels @ 302,252*/ 15, 0x03,
  /* ABS: 002 Pixels @ 317,252*/ 0, 2, 0x04, 0x04,
  /* RLE: 081 Pixels @ 319,252*/ 81, 0x00,
  /* RLE: 052 Pixels @ 000,253*/ 52, 0x02,
  /* ABS: 002 Pixels @ 052,253*/ 0, 2, 0x18, 0x00,
  /* RLE: 004 Pixels @ 054,253*/ 4, 0x03,
  /* ABS: 003 Pixels @ 058,253*/ 0, 3, 0x04, 0x00, 0x03,
  /* RLE: 037 Pixels @ 061,253*/ 37, 0x00,
  /* RLE: 005 Pixels @ 098,253*/ 5, 0x03,
  /* ABS: 011 Pixels @ 103,253*/ 0, 11, 0x00, 0x03, 0x06, 0x06, 0x0C, 0x0F, 0x04, 0x03, 0x03, 0x18, 0x0C,
  /* RLE: 026 Pixels @ 114,253*/ 26, 0x00,
  /* ABS: 010 Pixels @ 140,253*/ 0, 10, 0x03, 0x00, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x0D, 0x03, 0x03,
  /* RLE: 096 Pixels @ 150,253*/ 96, 0x00,
  /* RLE: 001 Pixels @ 246,253*/ 1, 0x04,
  /* RLE: 006 Pixels @ 247,253*/ 6, 0x03,
  /* ABS: 002 Pixels @ 253,253*/ 0, 2, 0x04, 0x04,
  /* RLE: 023 Pixels @ 255,253*/ 23, 0x00,
  /* ABS: 015 Pixels @ 278,253*/ 0, 15, 0x03, 0x11, 0x06, 0x00, 0x11, 0x08, 0x0C, 0x03, 0x03, 0x08, 0x06, 0x03, 0x00, 0x08, 0x0C,
  /* RLE: 025 Pixels @ 293,253*/ 25, 0x03,
  /* ABS: 002 Pixels @ 318,253*/ 0, 2, 0x04, 0x04,
  /* RLE: 080 Pixels @ 320,253*/ 80, 0x00,
  /* RLE: 053 Pixels @ 000,254*/ 53, 0x02,
  /* ABS: 003 Pixels @ 053,254*/ 0, 3, 0x03, 0x0B, 0x0B,
  /* RLE: 005 Pixels @ 056,254*/ 5, 0x03,
  /* RLE: 001 Pixels @ 061,254*/ 1, 0x04,
  /* RLE: 033 Pixels @ 062,254*/ 33, 0x00,
  /* RLE: 003 Pixels @ 095,254*/ 3, 0x03,
  /* RLE: 005 Pixels @ 098,254*/ 5, 0x13,
  /* RLE: 003 Pixels @ 103,254*/ 3, 0x03,
  /* ABS: 008 Pixels @ 106,254*/ 0, 8, 0x00, 0x03, 0x03, 0x03, 0x00, 0x04, 0x03, 0x0C,
  /* RLE: 025 Pixels @ 114,254*/ 25, 0x00,
  /* ABS: 011 Pixels @ 139,254*/ 0, 11, 0x03, 0x00, 0x00, 0x00, 0x03, 0x03, 0x0B, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 096 Pixels @ 150,254*/ 96, 0x00,
  /* RLE: 001 Pixels @ 246,254*/ 1, 0x04,
  /* RLE: 007 Pixels @ 247,254*/ 7, 0x03,
  /* RLE: 001 Pixels @ 254,254*/ 1, 0x04,
  /* RLE: 023 Pixels @ 255,254*/ 23, 0x00,
  /* ABS: 015 Pixels @ 278,254*/ 0, 15, 0x03, 0x0C, 0x08, 0x0D, 0x04, 0x12, 0x08, 0x00, 0x03, 0x0E, 0x0F, 0x03, 0x03, 0x12, 0x11,
  /* RLE: 004 Pixels @ 293,254*/ 4, 0x03,
  /* RLE: 014 Pixels @ 297,254*/ 14, 0x04,
  /* RLE: 008 Pixels @ 311,254*/ 8, 0x03,
  /* ABS: 002 Pixels @ 319,254*/ 0, 2, 0x04, 0x04,
  /* RLE: 079 Pixels @ 321,254*/ 79, 0x00,
  /* RLE: 053 Pixels @ 000,255*/ 53, 0x02,
  /* ABS: 003 Pixels @ 053,255*/ 0, 3, 0x03, 0x06, 0x06,
  /* RLE: 005 Pixels @ 056,255*/ 5, 0x03,
  /* RLE: 001 Pixels @ 061,255*/ 1, 0x04,
  /* RLE: 032 Pixels @ 062,255*/ 32, 0x00,
  /* ABS: 004 Pixels @ 094,255*/ 0, 4, 0x03, 0x03, 0x13, 0x13,
  /* RLE: 005 Pixels @ 098,255*/ 5, 0x17,
  /* ABS: 011 Pixels @ 103,255*/ 0, 11, 0x13, 0x13, 0x03, 0x03, 0x0C, 0x06, 0x0F, 0x08, 0x08, 0x03, 0x0C,
  /* RLE: 025 Pixels @ 114,255*/ 25, 0x00,
  /* ABS: 010 Pixels @ 139,255*/ 0, 10, 0x03, 0x06, 0x08, 0x08, 0x08, 0x0D, 0x06, 0x0C, 0x0C, 0x03,
  /* RLE: 098 Pixels @ 149,255*/ 98, 0x00,
  /* RLE: 001 Pixels @ 247,255*/ 1, 0x04,
  /* RLE: 006 Pixels @ 248,255*/ 6, 0x03,
  /* ABS: 002 Pixels @ 254,255*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 256,255*/ 14, 0x00,
  /* ABS: 002 Pixels @ 270,255*/ 0, 2, 0x03, 0x03,
  /* RLE: 004 Pixels @ 272,255*/ 4, 0x00,
  /* RLE: 004 Pixels @ 276,255*/ 4, 0x03,
  /* ABS: 016 Pixels @ 280,255*/ 0, 16, 0x0F, 0x06, 0x03, 0x00, 0x08, 0x0C, 0x03, 0x00, 0x08, 0x04, 0x03, 0x0B, 0x08, 0x00, 0x03, 0x04,
  /* RLE: 014 Pixels @ 296,255*/ 14, 0x00,
  /* ABS: 002 Pixels @ 310,255*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 312,255*/ 8, 0x03,
  /* ABS: 002 Pixels @ 320,255*/ 0, 2, 0x04, 0x04,
  /* RLE: 078 Pixels @ 322,255*/ 78, 0x00,
  /* RLE: 053 Pixels @ 000,256*/ 53, 0x02,
  /* ABS: 003 Pixels @ 053,256*/ 0, 3, 0x03, 0x00, 0x00,
  /* RLE: 005 Pixels @ 056,256*/ 5, 0x03,
  /* RLE: 001 Pixels @ 061,256*/ 1, 0x04,
  /* RLE: 031 Pixels @ 062,256*/ 31, 0x00,
  /* ABS: 003 Pixels @ 093,256*/ 0, 3, 0x03, 0x03, 0x13,
  /* RLE: 007 Pixels @ 096,256*/ 7, 0x17,
  /* ABS: 010 Pixels @ 103,256*/ 0, 10, 0x16, 0x16, 0x13, 0x03, 0x03, 0x0D, 0x0E, 0x0E, 0x06, 0x03,
  /* RLE: 026 Pixels @ 113,256*/ 26, 0x00,
  /* ABS: 009 Pixels @ 139,256*/ 0, 9, 0x03, 0x00, 0x00, 0x0C, 0x0C, 0x06, 0x0D, 0x08, 0x08,
  /* RLE: 099 Pixels @ 148,256*/ 99, 0x00,
  /* RLE: 001 Pixels @ 247,256*/ 1, 0x04,
  /* RLE: 007 Pixels @ 248,256*/ 7, 0x03,
  /* RLE: 001 Pixels @ 255,256*/ 1, 0x04,
  /* RLE: 013 Pixels @ 256,256*/ 13, 0x00,
  /* ABS: 025 Pixels @ 269,256*/ 0, 25, 0x03, 0x04, 0x0C, 0x03, 0x00, 0x03, 0x03, 0x04, 0x06, 0x12, 0x03, 0x0E, 0x0F, 0x03, 0x03, 0x12, 0x11, 0x03, 0x03, 0x0D, 0x0D, 0x03, 0x03, 0x00, 0x03,
  /* RLE: 017 Pixels @ 294,256*/ 17, 0x00,
  /* ABS: 002 Pixels @ 311,256*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 313,256*/ 8, 0x03,
  /* ABS: 002 Pixels @ 321,256*/ 0, 2, 0x04, 0x04,
  /* RLE: 077 Pixels @ 323,256*/ 77, 0x00,
  /* RLE: 053 Pixels @ 000,257*/ 53, 0x02,
  /* ABS: 002 Pixels @ 053,257*/ 0, 2, 0x18, 0x00,
  /* RLE: 006 Pixels @ 055,257*/ 6, 0x03,
  /* RLE: 001 Pixels @ 061,257*/ 1, 0x04,
  /* RLE: 030 Pixels @ 062,257*/ 30, 0x00,
  /* ABS: 008 Pixels @ 092,257*/ 0, 8, 0x03, 0x03, 0x13, 0x17, 0x17, 0x17, 0x03, 0x03,
  /* RLE: 004 Pixels @ 100,257*/ 4, 0x17,
  /* ABS: 003 Pixels @ 104,257*/ 0, 3, 0x16, 0x16, 0x13,
  /* RLE: 004 Pixels @ 107,257*/ 4, 0x03,
  /* ABS: 004 Pixels @ 111,257*/ 0, 4, 0x0E, 0x0E, 0x03, 0x0C,
  /* RLE: 024 Pixels @ 115,257*/ 24, 0x00,
  /* ABS: 002 Pixels @ 139,257*/ 0, 2, 0x03, 0x04,
  /* RLE: 006 Pixels @ 141,257*/ 6, 0x03,
  /* ABS: 002 Pixels @ 147,257*/ 0, 2, 0x00, 0x03,
  /* RLE: 099 Pixels @ 149,257*/ 99, 0x00,
  /* RLE: 001 Pixels @ 248,257*/ 1, 0x04,
  /* RLE: 006 Pixels @ 249,257*/ 6, 0x03,
  /* ABS: 002 Pixels @ 255,257*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 257,257*/ 8, 0x00,
  /* RLE: 003 Pixels @ 265,257*/ 3, 0x03,
  /* ABS: 024 Pixels @ 268,257*/ 0, 24, 0x00, 0x03, 0x0E, 0x08, 0x03, 0x00, 0x03, 0x0D, 0x11, 0x0C, 0x08, 0x0C, 0x00, 0x08, 0x04, 0x03, 0x0B, 0x08, 0x00, 0x03, 0x0B, 0x04, 0x03, 0x0B,
  /* RLE: 020 Pixels @ 292,257*/ 20, 0x00,
  /* ABS: 002 Pixels @ 312,257*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 314,257*/ 8, 0x03,
  /* ABS: 002 Pixels @ 322,257*/ 0, 2, 0x04, 0x04,
  /* RLE: 076 Pixels @ 324,257*/ 76, 0x00,
  /* RLE: 054 Pixels @ 000,258*/ 54, 0x02,
  /* RLE: 001 Pixels @ 054,258*/ 1, 0x04,
  /* RLE: 006 Pixels @ 055,258*/ 6, 0x03,
  /* RLE: 001 Pixels @ 061,258*/ 1, 0x04,
  /* RLE: 030 Pixels @ 062,258*/ 30, 0x00,
  /* ABS: 005 Pixels @ 092,258*/ 0, 5, 0x03, 0x13, 0x16, 0x17, 0x17,
  /* RLE: 004 Pixels @ 097,258*/ 4, 0x03,
  /* RLE: 003 Pixels @ 101,258*/ 3, 0x17,
  /* RLE: 003 Pixels @ 104,258*/ 3, 0x16,
  /* ABS: 008 Pixels @ 107,258*/ 0, 8, 0x13, 0x03, 0x03, 0x0B, 0x0D, 0x06, 0x03, 0x0C,
  /* RLE: 024 Pixels @ 115,258*/ 24, 0x00,
  /* ABS: 003 Pixels @ 139,258*/ 0, 3, 0x03, 0x08, 0x0B,
  /* RLE: 004 Pixels @ 142,258*/ 4, 0x03,
  /* ABS: 004 Pixels @ 146,258*/ 0, 4, 0x00, 0x0E, 0x03, 0x04,
  /* RLE: 098 Pixels @ 150,258*/ 98, 0x00,
  /* RLE: 001 Pixels @ 248,258*/ 1, 0x04,
  /* RLE: 007 Pixels @ 249,258*/ 7, 0x03,
  /* RLE: 001 Pixels @ 256,258*/ 1, 0x04,
  /* RLE: 008 Pixels @ 257,258*/ 8, 0x00,
  /* ABS: 025 Pixels @ 265,258*/ 0, 25, 0x03, 0x06, 0x0C, 0x03, 0x03, 0x00, 0x08, 0x0C, 0x03, 0x03, 0x0D, 0x00, 0x0B, 0x0F, 0x11, 0x03, 0x06, 0x0D, 0x03, 0x03, 0x00, 0x03, 0x00, 0x00, 0x03,
  /* RLE: 023 Pixels @ 290,258*/ 23, 0x00,
  /* ABS: 002 Pixels @ 313,258*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 315,258*/ 8, 0x03,
  /* ABS: 002 Pixels @ 323,258*/ 0, 2, 0x04, 0x04,
  /* RLE: 075 Pixels @ 325,258*/ 75, 0x00,
  /* RLE: 054 Pixels @ 000,259*/ 54, 0x02,
  /* RLE: 001 Pixels @ 054,259*/ 1, 0x04,
  /* RLE: 007 Pixels @ 055,259*/ 7, 0x03,
  /* RLE: 030 Pixels @ 062,259*/ 30, 0x00,
  /* ABS: 005 Pixels @ 092,259*/ 0, 5, 0x03, 0x13, 0x16, 0x17, 0x17,
  /* RLE: 004 Pixels @ 097,259*/ 4, 0x03,
  /* RLE: 004 Pixels @ 101,259*/ 4, 0x17,
  /* ABS: 010 Pixels @ 105,259*/ 0, 10, 0x16, 0x16, 0x13, 0x03, 0x08, 0x08, 0x08, 0x0B, 0x03, 0x0C,
  /* RLE: 024 Pixels @ 115,259*/ 24, 0x00,
  /* ABS: 011 Pixels @ 139,259*/ 0, 11, 0x03, 0x0D, 0x0F, 0x00, 0x03, 0x00, 0x12, 0x08, 0x08, 0x03, 0x04,
  /* RLE: 099 Pixels @ 150,259*/ 99, 0x00,
  /* RLE: 001 Pixels @ 249,259*/ 1, 0x04,
  /* RLE: 006 Pixels @ 250,259*/ 6, 0x03,
  /* ABS: 002 Pixels @ 256,259*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 258,259*/ 7, 0x00,
  /* ABS: 021 Pixels @ 265,259*/ 0, 21, 0x03, 0x11, 0x11, 0x03, 0x03, 0x00, 0x0F, 0x11, 0x03, 0x03, 0x03, 0x0E, 0x0D, 0x0C, 0x08, 0x0B, 0x0B, 0x04, 0x03, 0x03, 0x0B,
  /* RLE: 028 Pixels @ 286,259*/ 28, 0x00,
  /* ABS: 002 Pixels @ 314,259*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 316,259*/ 8, 0x03,
  /* ABS: 002 Pixels @ 324,259*/ 0, 2, 0x04, 0x04,
  /* RLE: 074 Pixels @ 326,259*/ 74, 0x00,
  /* RLE: 055 Pixels @ 000,260*/ 55, 0x02,
  /* RLE: 001 Pixels @ 055,260*/ 1, 0x04,
  /* RLE: 006 Pixels @ 056,260*/ 6, 0x03,
  /* RLE: 001 Pixels @ 062,260*/ 1, 0x04,
  /* RLE: 029 Pixels @ 063,260*/ 29, 0x00,
  /* ABS: 008 Pixels @ 092,260*/ 0, 8, 0x03, 0x13, 0x16, 0x17, 0x17, 0x17, 0x03, 0x03,
  /* RLE: 005 Pixels @ 100,260*/ 5, 0x17,
  /* ABS: 010 Pixels @ 105,260*/ 0, 10, 0x16, 0x16, 0x13, 0x03, 0x0C, 0x0B, 0x03, 0x03, 0x18, 0x0C,
  /* RLE: 024 Pixels @ 115,260*/ 24, 0x00,
  /* ABS: 010 Pixels @ 139,260*/ 0, 10, 0x03, 0x00, 0x0F, 0x0D, 0x06, 0x08, 0x08, 0x12, 0x0B, 0x03,
  /* RLE: 100 Pixels @ 149,260*/ 100, 0x00,
  /* RLE: 001 Pixels @ 249,260*/ 1, 0x04,
  /* RLE: 007 Pixels @ 250,260*/ 7, 0x03,
  /* RLE: 001 Pixels @ 257,260*/ 1, 0x0C,
  /* RLE: 007 Pixels @ 258,260*/ 7, 0x00,
  /* ABS: 018 Pixels @ 265,260*/ 0, 18, 0x03, 0x04, 0x08, 0x04, 0x12, 0x08, 0x0F, 0x08, 0x0B, 0x03, 0x00, 0x08, 0x00, 0x00, 0x08, 0x0D, 0x03, 0x03,
  /* RLE: 032 Pixels @ 283,260*/ 32, 0x00,
  /* ABS: 002 Pixels @ 315,260*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 317,260*/ 8, 0x03,
  /* RLE: 001 Pixels @ 325,260*/ 1, 0x04,
  /* RLE: 074 Pixels @ 326,260*/ 74, 0x00,
  /* RLE: 055 Pixels @ 000,261*/ 55, 0x02,
  /* RLE: 001 Pixels @ 055,261*/ 1, 0x04,
  /* RLE: 006 Pixels @ 056,261*/ 6, 0x03,
  /* RLE: 001 Pixels @ 062,261*/ 1, 0x04,
  /* RLE: 029 Pixels @ 063,261*/ 29, 0x00,
  /* ABS: 003 Pixels @ 092,261*/ 0, 3, 0x03, 0x13, 0x16,
  /* RLE: 009 Pixels @ 095,261*/ 9, 0x17,
  /* RLE: 003 Pixels @ 104,261*/ 3, 0x16,
  /* ABS: 009 Pixels @ 107,261*/ 0, 9, 0x13, 0x03, 0x03, 0x00, 0x0C, 0x00, 0x03, 0x1B, 0x0C,
  /* RLE: 023 Pixels @ 116,261*/ 23, 0x00,
  /* ABS: 009 Pixels @ 139,261*/ 0, 9, 0x03, 0x03, 0x0B, 0x08, 0x08, 0x0C, 0x00, 0x03, 0x03,
  /* RLE: 102 Pixels @ 148,261*/ 102, 0x00,
  /* ABS: 002 Pixels @ 250,261*/ 0, 2, 0x04, 0x03,
  /* RLE: 006 Pixels @ 252,261*/ 6, 0x09,
  /* ABS: 024 Pixels @ 258,261*/ 0, 24, 0x0C, 0x0C, 0x00, 0x00, 0x00, 0x04, 0x04, 0x03, 0x03, 0x0F, 0x08, 0x12, 0x0B, 0x03, 0x0F, 0x12, 0x03, 0x00, 0x08, 0x11, 0x11, 0x00, 0x00, 0x03,
  /* RLE: 034 Pixels @ 282,261*/ 34, 0x00,
  /* ABS: 002 Pixels @ 316,261*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 318,261*/ 8, 0x03,
  /* RLE: 074 Pixels @ 326,261*/ 74, 0x00,
  /* RLE: 055 Pixels @ 000,262*/ 55, 0x02,
  /* RLE: 001 Pixels @ 055,262*/ 1, 0x04,
  /* RLE: 006 Pixels @ 056,262*/ 6, 0x03,
  /* RLE: 001 Pixels @ 062,262*/ 1, 0x04,
  /* RLE: 029 Pixels @ 063,262*/ 29, 0x00,
  /* ABS: 004 Pixels @ 092,262*/ 0, 4, 0x03, 0x13, 0x16, 0x16,
  /* RLE: 007 Pixels @ 096,262*/ 7, 0x17,
  /* RLE: 004 Pixels @ 103,262*/ 4, 0x16,
  /* ABS: 009 Pixels @ 107,262*/ 0, 9, 0x13, 0x03, 0x03, 0x0F, 0x08, 0x0F, 0x00, 0x18, 0x0C,
  /* RLE: 022 Pixels @ 116,262*/ 22, 0x00,
  /* ABS: 011 Pixels @ 138,262*/ 0, 11, 0x03, 0x00, 0x04, 0x00, 0x0C, 0x08, 0x04, 0x03, 0x03, 0x03, 0x0B,
  /* RLE: 101 Pixels @ 149,262*/ 101, 0x00,
  /* ABS: 009 Pixels @ 250,262*/ 0, 9, 0x04, 0x03, 0x1B, 0x18, 0x18, 0x18, 0x09, 0x09, 0x1B,
  /* RLE: 007 Pixels @ 259,262*/ 7, 0x03,
  /* ABS: 015 Pixels @ 266,262*/ 0, 15, 0x0B, 0x0E, 0x08, 0x00, 0x03, 0x03, 0x0E, 0x08, 0x00, 0x03, 0x00, 0x0C, 0x00, 0x03, 0x03,
  /* RLE: 036 Pixels @ 281,262*/ 36, 0x00,
  /* ABS: 002 Pixels @ 317,262*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 319,262*/ 7, 0x03,
  /* RLE: 001 Pixels @ 326,262*/ 1, 0x04,
  /* RLE: 073 Pixels @ 327,262*/ 73, 0x00,
  /* RLE: 055 Pixels @ 000,263*/ 55, 0x02,
  /* RLE: 001 Pixels @ 055,263*/ 1, 0x04,
  /* RLE: 006 Pixels @ 056,263*/ 6, 0x03,
  /* RLE: 001 Pixels @ 062,263*/ 1, 0x04,
  /* RLE: 029 Pixels @ 063,263*/ 29, 0x00,
  /* ABS: 002 Pixels @ 092,263*/ 0, 2, 0x03, 0x13,
  /* RLE: 004 Pixels @ 094,263*/ 4, 0x16,
  /* RLE: 004 Pixels @ 098,263*/ 4, 0x17,
  /* RLE: 005 Pixels @ 102,263*/ 5, 0x16,
  /* ABS: 009 Pixels @ 107,263*/ 0, 9, 0x13, 0x03, 0x0B, 0x08, 0x04, 0x06, 0x0C, 0x03, 0x0C,
  /* RLE: 022 Pixels @ 116,263*/ 22, 0x00,
  /* ABS: 002 Pixels @ 138,263*/ 0, 2, 0x03, 0x12,
  /* RLE: 004 Pixels @ 140,263*/ 4, 0x08,
  /* ABS: 005 Pixels @ 144,263*/ 0, 5, 0x0F, 0x12, 0x0C, 0x00, 0x03,
  /* RLE: 102 Pixels @ 149,263*/ 102, 0x00,
  /* ABS: 027 Pixels @ 251,263*/ 0, 27, 0x09, 0x18, 0x00, 0x04, 0x03, 0x1B, 0x18, 0x03, 0x04, 0x06, 0x06, 0x04, 0x03, 0x0E, 0x08, 0x08, 0x00, 0x08, 0x0C, 0x03, 0x03, 0x00, 0x06, 0x00, 0x03, 0x00, 0x03,
  /* RLE: 040 Pixels @ 278,263*/ 40, 0x00,
  /* ABS: 002 Pixels @ 318,263*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 320,263*/ 6, 0x03,
  /* RLE: 001 Pixels @ 326,263*/ 1, 0x04,
  /* RLE: 073 Pixels @ 327,263*/ 73, 0x00,
  /* RLE: 055 Pixels @ 000,264*/ 55, 0x02,
  /* RLE: 001 Pixels @ 055,264*/ 1, 0x04,
  /* RLE: 006 Pixels @ 056,264*/ 6, 0x03,
  /* RLE: 001 Pixels @ 062,264*/ 1, 0x04,
  /* RLE: 029 Pixels @ 063,264*/ 29, 0x00,
  /* ABS: 003 Pixels @ 092,264*/ 0, 3, 0x03, 0x03, 0x13,
  /* RLE: 011 Pixels @ 095,264*/ 11, 0x16,
  /* ABS: 010 Pixels @ 106,264*/ 0, 10, 0x13, 0x03, 0x03, 0x00, 0x08, 0x0B, 0x0E, 0x0C, 0x03, 0x0C,
  /* RLE: 023 Pixels @ 116,264*/ 23, 0x00,
  /* ABS: 010 Pixels @ 139,264*/ 0, 10, 0x03, 0x0B, 0x04, 0x0C, 0x12, 0x06, 0x08, 0x08, 0x0B, 0x03,
  /* RLE: 102 Pixels @ 149,264*/ 102, 0x00,
  /* ABS: 019 Pixels @ 251,264*/ 0, 19, 0x18, 0x03, 0x0E, 0x11, 0x0B, 0x03, 0x03, 0x0C, 0x0F, 0x0E, 0x12, 0x08, 0x04, 0x0E, 0x0E, 0x00, 0x03, 0x06, 0x11,
  /* RLE: 005 Pixels @ 270,264*/ 5, 0x03,
  /* RLE: 045 Pixels @ 275,264*/ 45, 0x00,
  /* RLE: 007 Pixels @ 320,264*/ 7, 0x03,
  /* RLE: 001 Pixels @ 327,264*/ 1, 0x04,
  /* RLE: 072 Pixels @ 328,264*/ 72, 0x00,
  /* RLE: 055 Pixels @ 000,265*/ 55, 0x02,
  /* RLE: 001 Pixels @ 055,265*/ 1, 0x04,
  /* RLE: 006 Pixels @ 056,265*/ 6, 0x03,
  /* RLE: 001 Pixels @ 062,265*/ 1, 0x04,
  /* RLE: 030 Pixels @ 063,265*/ 30, 0x00,
  /* ABS: 003 Pixels @ 093,265*/ 0, 3, 0x03, 0x03, 0x13,
  /* RLE: 009 Pixels @ 096,265*/ 9, 0x16,
  /* ABS: 011 Pixels @ 105,265*/ 0, 11, 0x13, 0x03, 0x03, 0x11, 0x0C, 0x08, 0x00, 0x11, 0x04, 0x03, 0x0C,
  /* RLE: 021 Pixels @ 116,265*/ 21, 0x00,
  /* ABS: 002 Pixels @ 137,265*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 139,265*/ 9, 0x03,
  /* RLE: 097 Pixels @ 148,265*/ 97, 0x00,
  /* ABS: 019 Pixels @ 245,265*/ 0, 19, 0x03, 0x00, 0x00, 0x00, 0x03, 0x00, 0x04, 0x03, 0x12, 0x08, 0x0F, 0x0B, 0x03, 0x11, 0x0E, 0x03, 0x03, 0x0D, 0x0D,
  /* RLE: 004 Pixels @ 264,265*/ 4, 0x03,
  /* ABS: 004 Pixels @ 268,265*/ 0, 4, 0x0B, 0x04, 0x03, 0x04,
  /* RLE: 048 Pixels @ 272,265*/ 48, 0x00,
  /* RLE: 001 Pixels @ 320,265*/ 1, 0x04,
  /* RLE: 006 Pixels @ 321,265*/ 6, 0x03,
  /* RLE: 001 Pixels @ 327,265*/ 1, 0x04,
  /* RLE: 072 Pixels @ 328,265*/ 72, 0x00,
  /* RLE: 056 Pixels @ 000,266*/ 56, 0x02,
  /* RLE: 007 Pixels @ 056,266*/ 7, 0x03,
  /* RLE: 001 Pixels @ 063,266*/ 1, 0x04,
  /* RLE: 030 Pixels @ 064,266*/ 30, 0x00,
  /* ABS: 004 Pixels @ 094,266*/ 0, 4, 0x03, 0x03, 0x13, 0x13,
  /* RLE: 005 Pixels @ 098,266*/ 5, 0x16,
  /* ABS: 014 Pixels @ 103,266*/ 0, 14, 0x13, 0x13, 0x03, 0x03, 0x03, 0x0D, 0x08, 0x0F, 0x03, 0x0B, 0x03, 0x03, 0x18, 0x0B,
  /* RLE: 018 Pixels @ 117,266*/ 18, 0x00,
  /* RLE: 003 Pixels @ 135,266*/ 3, 0x04,
  /* RLE: 007 Pixels @ 138,266*/ 7, 0x03,
  /* ABS: 002 Pixels @ 145,266*/ 0, 2, 0x04, 0x04,
  /* RLE: 096 Pixels @ 147,266*/ 96, 0x00,
  /* ABS: 021 Pixels @ 243,266*/ 0, 21, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x03, 0x0E, 0x11, 0x0B, 0x0E, 0x0F, 0x0E, 0x03, 0x03, 0x11, 0x12, 0x03, 0x03, 0x0C, 0x0F,
  /* RLE: 005 Pixels @ 264,266*/ 5, 0x03,
  /* RLE: 051 Pixels @ 269,266*/ 51, 0x00,
  /* RLE: 001 Pixels @ 320,266*/ 1, 0x04,
  /* RLE: 007 Pixels @ 321,266*/ 7, 0x03,
  /* RLE: 072 Pixels @ 328,266*/ 72, 0x00,
  /* RLE: 056 Pixels @ 000,267*/ 56, 0x02,
  /* RLE: 001 Pixels @ 056,267*/ 1, 0x04,
  /* RLE: 006 Pixels @ 057,267*/ 6, 0x03,
  /* RLE: 001 Pixels @ 063,267*/ 1, 0x04,
  /* RLE: 028 Pixels @ 064,267*/ 28, 0x00,
  /* ABS: 006 Pixels @ 092,267*/ 0, 6, 0x0B, 0x00, 0x00, 0x03, 0x13, 0x00,
  /* RLE: 005 Pixels @ 098,267*/ 5, 0x13,
  /* RLE: 003 Pixels @ 103,267*/ 3, 0x03,
  /* ABS: 011 Pixels @ 106,267*/ 0, 11, 0x00, 0x03, 0x03, 0x0B, 0x03, 0x03, 0x0B, 0x11, 0x0E, 0x00, 0x03,
  /* RLE: 016 Pixels @ 117,267*/ 16, 0x00,
  /* ABS: 002 Pixels @ 133,267*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 135,267*/ 10, 0x03,
  /* RLE: 001 Pixels @ 145,267*/ 1, 0x04,
  /* RLE: 096 Pixels @ 146,267*/ 96, 0x00,
  /* ABS: 026 Pixels @ 242,267*/ 0, 26, 0x03, 0x00, 0x06, 0x08, 0x08, 0x08, 0x0E, 0x03, 0x12, 0x08, 0x0F, 0x0B, 0x0E, 0x08, 0x00, 0x03, 0x0C, 0x08, 0x0C, 0x0B, 0x11, 0x0E, 0x03, 0x03, 0x04, 0x04,
  /* RLE: 053 Pixels @ 268,267*/ 53, 0x00,
  /* RLE: 001 Pixels @ 321,267*/ 1, 0x04,
  /* RLE: 006 Pixels @ 322,267*/ 6, 0x03,
  /* RLE: 001 Pixels @ 328,267*/ 1, 0x04,
  /* RLE: 071 Pixels @ 329,267*/ 71, 0x00,
  /* RLE: 056 Pixels @ 000,268*/ 56, 0x02,
  /* RLE: 001 Pixels @ 056,268*/ 1, 0x04,
  /* RLE: 006 Pixels @ 057,268*/ 6, 0x03,
  /* RLE: 001 Pixels @ 063,268*/ 1, 0x04,
  /* RLE: 029 Pixels @ 064,268*/ 29, 0x00,
  /* ABS: 006 Pixels @ 093,268*/ 0, 6, 0x0B, 0x03, 0x13, 0x00, 0x13, 0x13,
  /* RLE: 005 Pixels @ 099,268*/ 5, 0x03,
  /* RLE: 004 Pixels @ 104,268*/ 4, 0x00,
  /* ABS: 009 Pixels @ 108,268*/ 0, 9, 0x03, 0x04, 0x0E, 0x11, 0x08, 0x08, 0x08, 0x06, 0x03,
  /* RLE: 013 Pixels @ 117,268*/ 13, 0x00,
  /* RLE: 003 Pixels @ 130,268*/ 3, 0x04,
  /* RLE: 011 Pixels @ 133,268*/ 11, 0x03,
  /* RLE: 001 Pixels @ 144,268*/ 1, 0x04,
  /* RLE: 096 Pixels @ 145,268*/ 96, 0x00,
  /* ABS: 023 Pixels @ 241,268*/ 0, 23, 0x03, 0x03, 0x11, 0x0D, 0x04, 0x00, 0x0E, 0x08, 0x0C, 0x0E, 0x0F, 0x0E, 0x03, 0x00, 0x08, 0x0C, 0x00, 0x03, 0x12, 0x08, 0x08, 0x0E, 0x03,
  /* RLE: 057 Pixels @ 264,268*/ 57, 0x00,
  /* RLE: 001 Pixels @ 321,268*/ 1, 0x04,
  /* RLE: 006 Pixels @ 322,268*/ 6, 0x03,
  /* RLE: 001 Pixels @ 328,268*/ 1, 0x04,
  /* RLE: 071 Pixels @ 329,268*/ 71, 0x00,
  /* RLE: 056 Pixels @ 000,269*/ 56, 0x02,
  /* RLE: 001 Pixels @ 056,269*/ 1, 0x04,
  /* RLE: 006 Pixels @ 057,269*/ 6, 0x03,
  /* RLE: 001 Pixels @ 063,269*/ 1, 0x04,
  /* RLE: 029 Pixels @ 064,269*/ 29, 0x00,
  /* ABS: 007 Pixels @ 093,269*/ 0, 7, 0x03, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03,
  /* RLE: 007 Pixels @ 100,269*/ 7, 0x00,
  /* ABS: 009 Pixels @ 107,269*/ 0, 9, 0x03, 0x0E, 0x08, 0x0F, 0x06, 0x0C, 0x0F, 0x04, 0x03,
  /* RLE: 012 Pixels @ 116,269*/ 12, 0x00,
  /* RLE: 003 Pixels @ 128,269*/ 3, 0x04,
  /* RLE: 012 Pixels @ 131,269*/ 12, 0x03,
  /* ABS: 002 Pixels @ 143,269*/ 0, 2, 0x04, 0x04,
  /* RLE: 096 Pixels @ 145,269*/ 96, 0x00,
  /* ABS: 017 Pixels @ 241,269*/ 0, 17, 0x03, 0x04, 0x08, 0x0B, 0x03, 0x03, 0x03, 0x11, 0x0F, 0x03, 0x0E, 0x08, 0x00, 0x03, 0x0E, 0x08, 0x0F,
  /* RLE: 005 Pixels @ 258,269*/ 5, 0x03,
  /* RLE: 059 Pixels @ 263,269*/ 59, 0x00,
  /* RLE: 007 Pixels @ 322,269*/ 7, 0x03,
  /* RLE: 001 Pixels @ 329,269*/ 1, 0x04,
  /* RLE: 070 Pixels @ 330,269*/ 70, 0x00,
  /* RLE: 056 Pixels @ 000,270*/ 56, 0x02,
  /* RLE: 001 Pixels @ 056,270*/ 1, 0x04,
  /* RLE: 006 Pixels @ 057,270*/ 6, 0x03,
  /* RLE: 001 Pixels @ 063,270*/ 1, 0x04,
  /* RLE: 029 Pixels @ 064,270*/ 29, 0x00,
  /* ABS: 007 Pixels @ 093,270*/ 0, 7, 0x03, 0x13, 0x0B, 0x13, 0x13, 0x03, 0x03,
  /* RLE: 007 Pixels @ 100,270*/ 7, 0x00,
  /* ABS: 011 Pixels @ 107,270*/ 0, 11, 0x03, 0x0E, 0x06, 0x03, 0x03, 0x03, 0x00, 0x0C, 0x03, 0x09, 0x0C,
  /* RLE: 008 Pixels @ 118,270*/ 8, 0x00,
  /* ABS: 002 Pixels @ 126,270*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 128,270*/ 14, 0x03,
  /* ABS: 002 Pixels @ 142,270*/ 0, 2, 0x04, 0x04,
  /* RLE: 094 Pixels @ 144,270*/ 94, 0x00,
  /* ABS: 021 Pixels @ 238,270*/ 0, 21, 0x04, 0x04, 0x03, 0x03, 0x0C, 0x08, 0x00, 0x03, 0x03, 0x03, 0x0C, 0x08, 0x00, 0x00, 0x08, 0x0C, 0x00, 0x03, 0x0B, 0x00, 0x03,
  /* RLE: 004 Pixels @ 259,270*/ 4, 0x09,
  /* RLE: 001 Pixels @ 263,270*/ 1, 0x0C,
  /* RLE: 058 Pixels @ 264,270*/ 58, 0x00,
  /* RLE: 001 Pixels @ 322,270*/ 1, 0x04,
  /* RLE: 006 Pixels @ 323,270*/ 6, 0x03,
  /* RLE: 001 Pixels @ 329,270*/ 1, 0x04,
  /* RLE: 070 Pixels @ 330,270*/ 70, 0x00,
  /* RLE: 056 Pixels @ 000,271*/ 56, 0x02,
  /* RLE: 001 Pixels @ 056,271*/ 1, 0x04,
  /* RLE: 006 Pixels @ 057,271*/ 6, 0x03,
  /* RLE: 001 Pixels @ 063,271*/ 1, 0x04,
  /* RLE: 029 Pixels @ 064,271*/ 29, 0x00,
  /* ABS: 006 Pixels @ 093,271*/ 0, 6, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03,
  /* RLE: 008 Pixels @ 099,271*/ 8, 0x00,
  /* ABS: 011 Pixels @ 107,271*/ 0, 11, 0x03, 0x00, 0x0B, 0x0E, 0x0D, 0x08, 0x08, 0x08, 0x03, 0x1B, 0x0C,
  /* RLE: 005 Pixels @ 118,271*/ 5, 0x00,
  /* RLE: 003 Pixels @ 123,271*/ 3, 0x04,
  /* RLE: 015 Pixels @ 126,271*/ 15, 0x03,
  /* ABS: 002 Pixels @ 141,271*/ 0, 2, 0x04, 0x04,
  /* RLE: 093 Pixels @ 143,271*/ 93, 0x00,
  /* ABS: 002 Pixels @ 236,271*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 238,271*/ 4, 0x03,
  /* ABS: 021 Pixels @ 242,271*/ 0, 21, 0x04, 0x08, 0x04, 0x03, 0x03, 0x03, 0x00, 0x08, 0x00, 0x03, 0x0E, 0x08, 0x0F, 0x03, 0x18, 0x18, 0x1B, 0x09, 0x09, 0x09, 0x0C,
  /* RLE: 059 Pixels @ 263,271*/ 59, 0x00,
  /* RLE: 001 Pixels @ 322,271*/ 1, 0x04,
  /* RLE: 007 Pixels @ 323,271*/ 7, 0x03,
  /* RLE: 070 Pixels @ 330,271*/ 70, 0x00,
  /* RLE: 057 Pixels @ 000,272*/ 57, 0x02,
  /* RLE: 007 Pixels @ 057,272*/ 7, 0x03,
  /* RLE: 001 Pixels @ 064,272*/ 1, 0x04,
  /* RLE: 027 Pixels @ 065,272*/ 27, 0x00,
  /* ABS: 011 Pixels @ 092,272*/ 0, 11, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03, 0x03, 0x00, 0x0B, 0x00, 0x00,
  /* RLE: 007 Pixels @ 103,272*/ 7, 0x03,
  /* ABS: 013 Pixels @ 110,272*/ 0, 13, 0x08, 0x06, 0x0E, 0x0E, 0x11, 0x00, 0x18, 0x0C, 0x00, 0x00, 0x00, 0x04, 0x04,
  /* RLE: 016 Pixels @ 123,272*/ 16, 0x03,
  /* ABS: 002 Pixels @ 139,272*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 141,272*/ 92, 0x00,
  /* ABS: 002 Pixels @ 233,272*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 235,272*/ 8, 0x03,
  /* ABS: 013 Pixels @ 243,272*/ 0, 13, 0x0F, 0x11, 0x03, 0x03, 0x03, 0x12, 0x08, 0x03, 0x03, 0x03, 0x0B, 0x00, 0x03,
  /* RLE: 006 Pixels @ 256,272*/ 6, 0x09,
  /* RLE: 001 Pixels @ 262,272*/ 1, 0x0C,
  /* RLE: 060 Pixels @ 263,272*/ 60, 0x00,
  /* RLE: 001 Pixels @ 323,272*/ 1, 0x04,
  /* RLE: 006 Pixels @ 324,272*/ 6, 0x03,
  /* RLE: 001 Pixels @ 330,272*/ 1, 0x04,
  /* RLE: 069 Pixels @ 331,272*/ 69, 0x00,
  /* RLE: 057 Pixels @ 000,273*/ 57, 0x02,
  /* RLE: 001 Pixels @ 057,273*/ 1, 0x04,
  /* RLE: 006 Pixels @ 058,273*/ 6, 0x03,
  /* RLE: 001 Pixels @ 064,273*/ 1, 0x04,
  /* RLE: 026 Pixels @ 065,273*/ 26, 0x00,
  /* ABS: 007 Pixels @ 091,273*/ 0, 7, 0x03, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03,
  /* RLE: 004 Pixels @ 098,273*/ 4, 0x00,
  /* ABS: 002 Pixels @ 102,273*/ 0, 2, 0x03, 0x03,
  /* RLE: 005 Pixels @ 104,273*/ 5, 0x13,
  /* RLE: 005 Pixels @ 109,273*/ 5, 0x03,
  /* ABS: 007 Pixels @ 114,273*/ 0, 7, 0x06, 0x0C, 0x03, 0x0C, 0x00, 0x04, 0x04,
  /* RLE: 015 Pixels @ 121,273*/ 15, 0x03,
  /* RLE: 003 Pixels @ 136,273*/ 3, 0x04,
  /* RLE: 092 Pixels @ 139,273*/ 92, 0x00,
  /* ABS: 002 Pixels @ 231,273*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 233,273*/ 10, 0x03,
  /* ABS: 013 Pixels @ 243,273*/ 0, 13, 0x0B, 0x08, 0x11, 0x0E, 0x06, 0x08, 0x04, 0x03, 0x00, 0x00, 0x00, 0x18, 0x1B,
  /* RLE: 006 Pixels @ 256,273*/ 6, 0x09,
  /* RLE: 001 Pixels @ 262,273*/ 1, 0x0C,
  /* RLE: 060 Pixels @ 263,273*/ 60, 0x00,
  /* RLE: 001 Pixels @ 323,273*/ 1, 0x04,
  /* RLE: 006 Pixels @ 324,273*/ 6, 0x03,
  /* RLE: 001 Pixels @ 330,273*/ 1, 0x04,
  /* RLE: 069 Pixels @ 331,273*/ 69, 0x00,
  /* RLE: 057 Pixels @ 000,274*/ 57, 0x02,
  /* RLE: 001 Pixels @ 057,274*/ 1, 0x04,
  /* RLE: 006 Pixels @ 058,274*/ 6, 0x03,
  /* RLE: 001 Pixels @ 064,274*/ 1, 0x04,
  /* RLE: 026 Pixels @ 065,274*/ 26, 0x00,
  /* ABS: 011 Pixels @ 091,274*/ 0, 11, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03, 0x03, 0x00, 0x00, 0x0B, 0x03,
  /* RLE: 009 Pixels @ 102,274*/ 9, 0x13,
  /* ABS: 008 Pixels @ 111,274*/ 0, 8, 0x03, 0x1B, 0x03, 0x00, 0x00, 0x18, 0x0C, 0x04,
  /* RLE: 015 Pixels @ 119,274*/ 15, 0x03,
  /* ABS: 002 Pixels @ 134,274*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 136,274*/ 92, 0x00,
  /* ABS: 002 Pixels @ 228,274*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 230,274*/ 14, 0x03,
  /* ABS: 006 Pixels @ 244,274*/ 0, 6, 0x0B, 0x12, 0x06, 0x12, 0x0B, 0x03,
  /* RLE: 004 Pixels @ 250,274*/ 4, 0x00,
  /* RLE: 001 Pixels @ 254,274*/ 1, 0x0C,
  /* RLE: 006 Pixels @ 255,274*/ 6, 0x09,
  /* ABS: 003 Pixels @ 261,274*/ 0, 3, 0x03, 0x03, 0x04,
  /* RLE: 060 Pixels @ 264,274*/ 60, 0x00,
  /* RLE: 007 Pixels @ 324,274*/ 7, 0x03,
  /* RLE: 001 Pixels @ 331,274*/ 1, 0x04,
  /* RLE: 068 Pixels @ 332,274*/ 68, 0x00,
  /* RLE: 057 Pixels @ 000,275*/ 57, 0x02,
  /* RLE: 001 Pixels @ 057,275*/ 1, 0x04,
  /* RLE: 006 Pixels @ 058,275*/ 6, 0x03,
  /* RLE: 001 Pixels @ 064,275*/ 1, 0x04,
  /* RLE: 025 Pixels @ 065,275*/ 25, 0x00,
  /* ABS: 011 Pixels @ 090,275*/ 0, 11, 0x03, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03, 0x00, 0x00, 0x00, 0x03,
  /* RLE: 011 Pixels @ 101,275*/ 11, 0x13,
  /* RLE: 001 Pixels @ 112,275*/ 1, 0x03,
  /* RLE: 004 Pixels @ 113,275*/ 4, 0x1B,
  /* RLE: 001 Pixels @ 117,275*/ 1, 0x09,
  /* RLE: 014 Pixels @ 118,275*/ 14, 0x03,
  /* ABS: 002 Pixels @ 132,275*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 134,275*/ 92, 0x00,
  /* ABS: 002 Pixels @ 226,275*/ 0, 2, 0x04, 0x04,
  /* RLE: 021 Pixels @ 228,275*/ 21, 0x03,
  /* RLE: 007 Pixels @ 249,275*/ 7, 0x00,
  /* RLE: 001 Pixels @ 256,275*/ 1, 0x0C,
  /* RLE: 006 Pixels @ 257,275*/ 6, 0x03,
  /* RLE: 001 Pixels @ 263,275*/ 1, 0x04,
  /* RLE: 060 Pixels @ 264,275*/ 60, 0x00,
  /* RLE: 001 Pixels @ 324,275*/ 1, 0x04,
  /* RLE: 006 Pixels @ 325,275*/ 6, 0x03,
  /* RLE: 001 Pixels @ 331,275*/ 1, 0x04,
  /* RLE: 068 Pixels @ 332,275*/ 68, 0x00,
  /* RLE: 057 Pixels @ 000,276*/ 57, 0x02,
  /* RLE: 001 Pixels @ 057,276*/ 1, 0x04,
  /* RLE: 006 Pixels @ 058,276*/ 6, 0x03,
  /* RLE: 001 Pixels @ 064,276*/ 1, 0x04,
  /* RLE: 024 Pixels @ 065,276*/ 24, 0x00,
  /* ABS: 012 Pixels @ 089,276*/ 0, 12, 0x0B, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03, 0x03, 0x00, 0x00, 0x00, 0x03,
  /* RLE: 011 Pixels @ 101,276*/ 11, 0x13,
  /* RLE: 001 Pixels @ 112,276*/ 1, 0x03,
  /* RLE: 005 Pixels @ 113,276*/ 5, 0x09,
  /* RLE: 011 Pixels @ 118,276*/ 11, 0x03,
  /* RLE: 003 Pixels @ 129,276*/ 3, 0x04,
  /* RLE: 091 Pixels @ 132,276*/ 91, 0x00,
  /* ABS: 002 Pixels @ 223,276*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 225,276*/ 16, 0x03,
  /* ABS: 002 Pixels @ 241,276*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 243,276*/ 13, 0x00,
  /* RLE: 001 Pixels @ 256,276*/ 1, 0x04,
  /* RLE: 007 Pixels @ 257,276*/ 7, 0x03,
  /* RLE: 060 Pixels @ 264,276*/ 60, 0x00,
  /* RLE: 001 Pixels @ 324,276*/ 1, 0x04,
  /* RLE: 007 Pixels @ 325,276*/ 7, 0x03,
  /* RLE: 068 Pixels @ 332,276*/ 68, 0x00,
  /* RLE: 057 Pixels @ 000,277*/ 57, 0x02,
  /* RLE: 001 Pixels @ 057,277*/ 1, 0x04,
  /* RLE: 006 Pixels @ 058,277*/ 6, 0x03,
  /* RLE: 001 Pixels @ 064,277*/ 1, 0x04,
  /* RLE: 024 Pixels @ 065,277*/ 24, 0x00,
  /* ABS: 007 Pixels @ 089,277*/ 0, 7, 0x03, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03,
  /* RLE: 004 Pixels @ 096,277*/ 4, 0x00,
  /* RLE: 001 Pixels @ 100,277*/ 1, 0x03,
  /* RLE: 011 Pixels @ 101,277*/ 11, 0x13,
  /* RLE: 001 Pixels @ 112,277*/ 1, 0x03,
  /* RLE: 005 Pixels @ 113,277*/ 5, 0x09,
  /* RLE: 009 Pixels @ 118,277*/ 9, 0x03,
  /* ABS: 002 Pixels @ 127,277*/ 0, 2, 0x04, 0x04,
  /* RLE: 092 Pixels @ 129,277*/ 92, 0x00,
  /* ABS: 002 Pixels @ 221,277*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 223,277*/ 16, 0x03,
  /* ABS: 002 Pixels @ 239,277*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 241,277*/ 16, 0x00,
  /* RLE: 001 Pixels @ 257,277*/ 1, 0x04,
  /* RLE: 006 Pixels @ 258,277*/ 6, 0x03,
  /* RLE: 001 Pixels @ 264,277*/ 1, 0x04,
  /* RLE: 060 Pixels @ 265,277*/ 60, 0x00,
  /* RLE: 001 Pixels @ 325,277*/ 1, 0x04,
  /* RLE: 006 Pixels @ 326,277*/ 6, 0x03,
  /* RLE: 001 Pixels @ 332,277*/ 1, 0x04,
  /* RLE: 067 Pixels @ 333,277*/ 67, 0x00,
  /* RLE: 058 Pixels @ 000,278*/ 58, 0x02,
  /* RLE: 007 Pixels @ 058,278*/ 7, 0x03,
  /* RLE: 001 Pixels @ 065,278*/ 1, 0x04,
  /* RLE: 023 Pixels @ 066,278*/ 23, 0x00,
  /* ABS: 007 Pixels @ 089,278*/ 0, 7, 0x03, 0x13, 0x0B, 0x13, 0x13, 0x03, 0x03,
  /* RLE: 004 Pixels @ 096,278*/ 4, 0x00,
  /* RLE: 001 Pixels @ 100,278*/ 1, 0x03,
  /* RLE: 011 Pixels @ 101,278*/ 11, 0x13,
  /* RLE: 001 Pixels @ 112,278*/ 1, 0x03,
  /* RLE: 005 Pixels @ 113,278*/ 5, 0x09,
  /* RLE: 006 Pixels @ 118,278*/ 6, 0x03,
  /* RLE: 003 Pixels @ 124,278*/ 3, 0x04,
  /* RLE: 091 Pixels @ 127,278*/ 91, 0x00,
  /* ABS: 002 Pixels @ 218,278*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 220,278*/ 16, 0x03,
  /* ABS: 002 Pixels @ 236,278*/ 0, 2, 0x04, 0x04,
  /* RLE: 019 Pixels @ 238,278*/ 19, 0x00,
  /* RLE: 001 Pixels @ 257,278*/ 1, 0x04,
  /* RLE: 006 Pixels @ 258,278*/ 6, 0x03,
  /* RLE: 001 Pixels @ 264,278*/ 1, 0x04,
  /* RLE: 060 Pixels @ 265,278*/ 60, 0x00,
  /* RLE: 001 Pixels @ 325,278*/ 1, 0x04,
  /* RLE: 006 Pixels @ 326,278*/ 6, 0x03,
  /* RLE: 001 Pixels @ 332,278*/ 1, 0x04,
  /* RLE: 067 Pixels @ 333,278*/ 67, 0x00,
  /* RLE: 058 Pixels @ 000,279*/ 58, 0x02,
  /* RLE: 001 Pixels @ 058,279*/ 1, 0x04,
  /* RLE: 006 Pixels @ 059,279*/ 6, 0x03,
  /* RLE: 001 Pixels @ 065,279*/ 1, 0x04,
  /* RLE: 022 Pixels @ 066,279*/ 22, 0x00,
  /* ABS: 012 Pixels @ 088,279*/ 0, 12, 0x03, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03, 0x00, 0x04, 0x04, 0x03, 0x03,
  /* RLE: 011 Pixels @ 100,279*/ 11, 0x13,
  /* ABS: 002 Pixels @ 111,279*/ 0, 2, 0x03, 0x03,
  /* RLE: 005 Pixels @ 113,279*/ 5, 0x09,
  /* RLE: 004 Pixels @ 118,279*/ 4, 0x03,
  /* RLE: 003 Pixels @ 122,279*/ 3, 0x04,
  /* RLE: 090 Pixels @ 125,279*/ 90, 0x00,
  /* RLE: 003 Pixels @ 215,279*/ 3, 0x04,
  /* RLE: 016 Pixels @ 218,279*/ 16, 0x03,
  /* ABS: 002 Pixels @ 234,279*/ 0, 2, 0x04, 0x04,
  /* RLE: 021 Pixels @ 236,279*/ 21, 0x00,
  /* RLE: 001 Pixels @ 257,279*/ 1, 0x04,
  /* RLE: 007 Pixels @ 258,279*/ 7, 0x03,
  /* RLE: 061 Pixels @ 265,279*/ 61, 0x00,
  /* RLE: 007 Pixels @ 326,279*/ 7, 0x03,
  /* RLE: 001 Pixels @ 333,279*/ 1, 0x04,
  /* RLE: 066 Pixels @ 334,279*/ 66, 0x00,
  /* RLE: 058 Pixels @ 000,280*/ 58, 0x02,
  /* RLE: 001 Pixels @ 058,280*/ 1, 0x04,
  /* RLE: 006 Pixels @ 059,280*/ 6, 0x03,
  /* RLE: 001 Pixels @ 065,280*/ 1, 0x04,
  /* RLE: 021 Pixels @ 066,280*/ 21, 0x00,
  /* ABS: 013 Pixels @ 087,280*/ 0, 13, 0x03, 0x03, 0x13, 0x00, 0x13, 0x13, 0x03, 0x04, 0x03, 0x03, 0x03, 0x13, 0x13,
  /* RLE: 004 Pixels @ 100,280*/ 4, 0x03,
  /* RLE: 005 Pixels @ 104,280*/ 5, 0x13,
  /* RLE: 003 Pixels @ 109,280*/ 3, 0x03,
  /* RLE: 007 Pixels @ 112,280*/ 7, 0x09,
  /* ABS: 003 Pixels @ 119,280*/ 0, 3, 0x03, 0x04, 0x04,
  /* RLE: 090 Pixels @ 122,280*/ 90, 0x00,
  /* RLE: 003 Pixels @ 212,280*/ 3, 0x04,
  /* RLE: 016 Pixels @ 215,280*/ 16, 0x03,
  /* ABS: 002 Pixels @ 231,280*/ 0, 2, 0x04, 0x04,
  /* RLE: 025 Pixels @ 233,280*/ 25, 0x00,
  /* RLE: 001 Pixels @ 258,280*/ 1, 0x04,
  /* RLE: 006 Pixels @ 259,280*/ 6, 0x03,
  /* RLE: 001 Pixels @ 265,280*/ 1, 0x04,
  /* RLE: 060 Pixels @ 266,280*/ 60, 0x00,
  /* RLE: 001 Pixels @ 326,280*/ 1, 0x04,
  /* RLE: 006 Pixels @ 327,280*/ 6, 0x03,
  /* RLE: 001 Pixels @ 333,280*/ 1, 0x04,
  /* RLE: 066 Pixels @ 334,280*/ 66, 0x00,
  /* RLE: 058 Pixels @ 000,281*/ 58, 0x02,
  /* RLE: 001 Pixels @ 058,281*/ 1, 0x04,
  /* RLE: 006 Pixels @ 059,281*/ 6, 0x03,
  /* RLE: 001 Pixels @ 065,281*/ 1, 0x04,
  /* RLE: 021 Pixels @ 066,281*/ 21, 0x00,
  /* ABS: 011 Pixels @ 087,281*/ 0, 11, 0x03, 0x03, 0x13, 0x04, 0x13, 0x13, 0x03, 0x03, 0x03, 0x13, 0x13,
  /* RLE: 013 Pixels @ 098,281*/ 13, 0x03,
  /* RLE: 008 Pixels @ 111,281*/ 8, 0x09,
  /* RLE: 001 Pixels @ 119,281*/ 1, 0x0C,
  /* RLE: 091 Pixels @ 120,281*/ 91, 0x00,
  /* ABS: 002 Pixels @ 211,281*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 213,281*/ 16, 0x03,
  /* ABS: 002 Pixels @ 229,281*/ 0, 2, 0x04, 0x04,
  /* RLE: 027 Pixels @ 231,281*/ 27, 0x00,
  /* RLE: 001 Pixels @ 258,281*/ 1, 0x04,
  /* RLE: 006 Pixels @ 259,281*/ 6, 0x03,
  /* RLE: 001 Pixels @ 265,281*/ 1, 0x04,
  /* RLE: 060 Pixels @ 266,281*/ 60, 0x00,
  /* RLE: 001 Pixels @ 326,281*/ 1, 0x04,
  /* RLE: 007 Pixels @ 327,281*/ 7, 0x03,
  /* RLE: 066 Pixels @ 334,281*/ 66, 0x00,
  /* RLE: 058 Pixels @ 000,282*/ 58, 0x02,
  /* RLE: 001 Pixels @ 058,282*/ 1, 0x04,
  /* RLE: 006 Pixels @ 059,282*/ 6, 0x03,
  /* RLE: 001 Pixels @ 065,282*/ 1, 0x04,
  /* RLE: 016 Pixels @ 066,282*/ 16, 0x00,
  /* RLE: 004 Pixels @ 082,282*/ 4, 0x04,
  /* ABS: 010 Pixels @ 086,282*/ 0, 10, 0x03, 0x03, 0x13, 0x03, 0x13, 0x13, 0x13, 0x03, 0x13, 0x13,
  /* RLE: 015 Pixels @ 096,282*/ 15, 0x03,
  /* RLE: 008 Pixels @ 111,282*/ 8, 0x09,
  /* RLE: 001 Pixels @ 119,282*/ 1, 0x0C,
  /* RLE: 090 Pixels @ 120,282*/ 90, 0x00,
  /* ABS: 002 Pixels @ 210,282*/ 0, 2, 0x04, 0x04,
  /* RLE: 014 Pixels @ 212,282*/ 14, 0x03,
  /* ABS: 002 Pixels @ 226,282*/ 0, 2, 0x04, 0x04,
  /* RLE: 031 Pixels @ 228,282*/ 31, 0x00,
  /* RLE: 007 Pixels @ 259,282*/ 7, 0x03,
  /* RLE: 001 Pixels @ 266,282*/ 1, 0x04,
  /* RLE: 060 Pixels @ 267,282*/ 60, 0x00,
  /* RLE: 001 Pixels @ 327,282*/ 1, 0x04,
  /* RLE: 006 Pixels @ 328,282*/ 6, 0x03,
  /* RLE: 001 Pixels @ 334,282*/ 1, 0x04,
  /* RLE: 065 Pixels @ 335,282*/ 65, 0x00,
  /* RLE: 058 Pixels @ 000,283*/ 58, 0x02,
  /* RLE: 001 Pixels @ 058,283*/ 1, 0x04,
  /* RLE: 006 Pixels @ 059,283*/ 6, 0x03,
  /* RLE: 001 Pixels @ 065,283*/ 1, 0x04,
  /* RLE: 011 Pixels @ 066,283*/ 11, 0x00,
  /* RLE: 005 Pixels @ 077,283*/ 5, 0x04,
  /* RLE: 006 Pixels @ 082,283*/ 6, 0x03,
  /* ABS: 002 Pixels @ 088,283*/ 0, 2, 0x13, 0x03,
  /* RLE: 004 Pixels @ 090,283*/ 4, 0x13,
  /* RLE: 016 Pixels @ 094,283*/ 16, 0x03,
  /* RLE: 001 Pixels @ 110,283*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 111,283*/ 8, 0x09,
  /* RLE: 001 Pixels @ 119,283*/ 1, 0x0C,
  /* RLE: 089 Pixels @ 120,283*/ 89, 0x00,
  /* ABS: 002 Pixels @ 209,283*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 211,283*/ 13, 0x03,
  /* ABS: 002 Pixels @ 224,283*/ 0, 2, 0x04, 0x04,
  /* RLE: 033 Pixels @ 226,283*/ 33, 0x00,
  /* RLE: 001 Pixels @ 259,283*/ 1, 0x04,
  /* RLE: 006 Pixels @ 260,283*/ 6, 0x03,
  /* RLE: 001 Pixels @ 266,283*/ 1, 0x04,
  /* RLE: 060 Pixels @ 267,283*/ 60, 0x00,
  /* RLE: 001 Pixels @ 327,283*/ 1, 0x04,
  /* RLE: 006 Pixels @ 328,283*/ 6, 0x03,
  /* RLE: 001 Pixels @ 334,283*/ 1, 0x04,
  /* RLE: 065 Pixels @ 335,283*/ 65, 0x00,
  /* RLE: 059 Pixels @ 000,284*/ 59, 0x02,
  /* RLE: 007 Pixels @ 059,284*/ 7, 0x03,
  /* RLE: 001 Pixels @ 066,284*/ 1, 0x04,
  /* RLE: 006 Pixels @ 067,284*/ 6, 0x00,
  /* RLE: 004 Pixels @ 073,284*/ 4, 0x04,
  /* RLE: 011 Pixels @ 077,284*/ 11, 0x03,
  /* RLE: 004 Pixels @ 088,284*/ 4, 0x13,
  /* RLE: 014 Pixels @ 092,284*/ 14, 0x03,
  /* RLE: 004 Pixels @ 106,284*/ 4, 0x04,
  /* ABS: 002 Pixels @ 110,284*/ 0, 2, 0x00, 0x0C,
  /* RLE: 008 Pixels @ 112,284*/ 8, 0x09,
  /* RLE: 001 Pixels @ 120,284*/ 1, 0x0C,
  /* RLE: 087 Pixels @ 121,284*/ 87, 0x00,
  /* ABS: 002 Pixels @ 208,284*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 210,284*/ 11, 0x03,
  /* ABS: 002 Pixels @ 221,284*/ 0, 2, 0x04, 0x04,
  /* RLE: 036 Pixels @ 223,284*/ 36, 0x00,
  /* RLE: 001 Pixels @ 259,284*/ 1, 0x04,
  /* RLE: 007 Pixels @ 260,284*/ 7, 0x03,
  /* RLE: 061 Pixels @ 267,284*/ 61, 0x00,
  /* RLE: 007 Pixels @ 328,284*/ 7, 0x03,
  /* RLE: 001 Pixels @ 335,284*/ 1, 0x04,
  /* RLE: 064 Pixels @ 336,284*/ 64, 0x00,
  /* RLE: 059 Pixels @ 000,285*/ 59, 0x02,
  /* RLE: 001 Pixels @ 059,285*/ 1, 0x04,
  /* RLE: 006 Pixels @ 060,285*/ 6, 0x03,
  /* ABS: 002 Pixels @ 066,285*/ 0, 2, 0x04, 0x00,
  /* RLE: 004 Pixels @ 068,285*/ 4, 0x04,
  /* RLE: 029 Pixels @ 072,285*/ 29, 0x03,
  /* RLE: 004 Pixels @ 101,285*/ 4, 0x04,
  /* RLE: 006 Pixels @ 105,285*/ 6, 0x00,
  /* RLE: 001 Pixels @ 111,285*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 112,285*/ 8, 0x09,
  /* RLE: 001 Pixels @ 120,285*/ 1, 0x0C,
  /* RLE: 086 Pixels @ 121,285*/ 86, 0x00,
  /* ABS: 002 Pixels @ 207,285*/ 0, 2, 0x04, 0x04,
  /* RLE: 010 Pixels @ 209,285*/ 10, 0x03,
  /* ABS: 002 Pixels @ 219,285*/ 0, 2, 0x04, 0x04,
  /* RLE: 039 Pixels @ 221,285*/ 39, 0x00,
  /* RLE: 001 Pixels @ 260,285*/ 1, 0x04,
  /* RLE: 006 Pixels @ 261,285*/ 6, 0x03,
  /* RLE: 001 Pixels @ 267,285*/ 1, 0x04,
  /* RLE: 060 Pixels @ 268,285*/ 60, 0x00,
  /* RLE: 001 Pixels @ 328,285*/ 1, 0x04,
  /* RLE: 006 Pixels @ 329,285*/ 6, 0x03,
  /* RLE: 065 Pixels @ 335,285*/ 65, 0x00,
  /* RLE: 057 Pixels @ 000,286*/ 57, 0x02,
  /* RLE: 001 Pixels @ 057,286*/ 1, 0x18,
  /* RLE: 008 Pixels @ 058,286*/ 8, 0x03,
  /* ABS: 002 Pixels @ 066,286*/ 0, 2, 0x04, 0x04,
  /* RLE: 028 Pixels @ 068,286*/ 28, 0x03,
  /* RLE: 005 Pixels @ 096,286*/ 5, 0x04,
  /* RLE: 010 Pixels @ 101,286*/ 10, 0x00,
  /* RLE: 001 Pixels @ 111,286*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 112,286*/ 8, 0x09,
  /* RLE: 001 Pixels @ 120,286*/ 1, 0x0C,
  /* RLE: 085 Pixels @ 121,286*/ 85, 0x00,
  /* ABS: 002 Pixels @ 206,286*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 208,286*/ 8, 0x03,
  /* ABS: 002 Pixels @ 216,286*/ 0, 2, 0x04, 0x04,
  /* RLE: 042 Pixels @ 218,286*/ 42, 0x00,
  /* RLE: 001 Pixels @ 260,286*/ 1, 0x04,
  /* RLE: 006 Pixels @ 261,286*/ 6, 0x03,
  /* RLE: 001 Pixels @ 267,286*/ 1, 0x04,
  /* RLE: 060 Pixels @ 268,286*/ 60, 0x00,
  /* RLE: 001 Pixels @ 328,286*/ 1, 0x04,
  /* RLE: 004 Pixels @ 329,286*/ 4, 0x03,
  /* RLE: 003 Pixels @ 333,286*/ 3, 0x00,
  /* ABS: 002 Pixels @ 336,286*/ 0, 2, 0x03, 0x03,
  /* RLE: 062 Pixels @ 338,286*/ 62, 0x00,
  /* RLE: 034 Pixels @ 000,287*/ 34, 0x02,
  /* ABS: 002 Pixels @ 034,287*/ 0, 2, 0x00, 0x00,
  /* RLE: 006 Pixels @ 036,287*/ 6, 0x03,
  /* ABS: 019 Pixels @ 042,287*/ 0, 19, 0x00, 0x18, 0x00, 0x03, 0x00, 0x18, 0x02, 0x02, 0x00, 0x00, 0x00, 0x18, 0x02, 0x03, 0x03, 0x03, 0x00, 0x0C, 0x00,
  /* RLE: 031 Pixels @ 061,287*/ 31, 0x03,
  /* RLE: 004 Pixels @ 092,287*/ 4, 0x04,
  /* RLE: 015 Pixels @ 096,287*/ 15, 0x00,
  /* RLE: 001 Pixels @ 111,287*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 112,287*/ 8, 0x09,
  /* RLE: 001 Pixels @ 120,287*/ 1, 0x0C,
  /* RLE: 084 Pixels @ 121,287*/ 84, 0x00,
  /* ABS: 002 Pixels @ 205,287*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 207,287*/ 8, 0x03,
  /* ABS: 002 Pixels @ 215,287*/ 0, 2, 0x04, 0x04,
  /* RLE: 043 Pixels @ 217,287*/ 43, 0x00,
  /* RLE: 001 Pixels @ 260,287*/ 1, 0x04,
  /* RLE: 007 Pixels @ 261,287*/ 7, 0x03,
  /* RLE: 062 Pixels @ 268,287*/ 62, 0x00,
  /* ABS: 008 Pixels @ 330,287*/ 0, 8, 0x03, 0x0B, 0x11, 0x08, 0x08, 0x08, 0x06, 0x03,
  /* RLE: 062 Pixels @ 338,287*/ 62, 0x00,
  /* RLE: 034 Pixels @ 000,288*/ 34, 0x02,
  /* ABS: 013 Pixels @ 034,288*/ 0, 13, 0x03, 0x04, 0x00, 0x0E, 0x04, 0x00, 0x08, 0x00, 0x03, 0x03, 0x00, 0x0B, 0x00,
  /* RLE: 004 Pixels @ 047,288*/ 4, 0x03,
  /* ABS: 011 Pixels @ 051,288*/ 0, 11, 0x0B, 0x00, 0x03, 0x00, 0x03, 0x11, 0x06, 0x0F, 0x0F, 0x08, 0x00,
  /* RLE: 025 Pixels @ 062,288*/ 25, 0x03,
  /* RLE: 004 Pixels @ 087,288*/ 4, 0x04,
  /* RLE: 020 Pixels @ 091,288*/ 20, 0x00,
  /* RLE: 001 Pixels @ 111,288*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 112,288*/ 8, 0x09,
  /* RLE: 001 Pixels @ 120,288*/ 1, 0x0C,
  /* RLE: 083 Pixels @ 121,288*/ 83, 0x00,
  /* ABS: 002 Pixels @ 204,288*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 206,288*/ 8, 0x03,
  /* ABS: 002 Pixels @ 214,288*/ 0, 2, 0x04, 0x04,
  /* RLE: 045 Pixels @ 216,288*/ 45, 0x00,
  /* RLE: 001 Pixels @ 261,288*/ 1, 0x04,
  /* RLE: 006 Pixels @ 262,288*/ 6, 0x03,
  /* RLE: 001 Pixels @ 268,288*/ 1, 0x04,
  /* RLE: 060 Pixels @ 269,288*/ 60, 0x00,
  /* ABS: 010 Pixels @ 329,288*/ 0, 10, 0x03, 0x0B, 0x08, 0x11, 0x0C, 0x0B, 0x04, 0x0F, 0x0C, 0x03,
  /* RLE: 061 Pixels @ 339,288*/ 61, 0x00,
  /* RLE: 025 Pixels @ 000,289*/ 25, 0x02,
  /* ABS: 005 Pixels @ 025,289*/ 0, 5, 0x18, 0x03, 0x03, 0x03, 0x18,
  /* RLE: 004 Pixels @ 030,289*/ 4, 0x02,
  /* ABS: 028 Pixels @ 034,289*/ 0, 28, 0x03, 0x08, 0x00, 0x0C, 0x04, 0x03, 0x08, 0x0C, 0x03, 0x0E, 0x08, 0x0D, 0x08, 0x0C, 0x03, 0x04, 0x08, 0x08, 0x08, 0x04, 0x03, 0x03, 0x0D, 0x0F, 0x03, 0x00, 0x08, 0x0C,
  /* RLE: 021 Pixels @ 062,289*/ 21, 0x03,
  /* RLE: 004 Pixels @ 083,289*/ 4, 0x04,
  /* RLE: 025 Pixels @ 087,289*/ 25, 0x00,
  /* RLE: 001 Pixels @ 112,289*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 113,289*/ 8, 0x09,
  /* RLE: 001 Pixels @ 121,289*/ 1, 0x0C,
  /* RLE: 082 Pixels @ 122,289*/ 82, 0x00,
  /* ABS: 002 Pixels @ 204,289*/ 0, 2, 0x04, 0x04,
  /* RLE: 007 Pixels @ 206,289*/ 7, 0x03,
  /* ABS: 002 Pixels @ 213,289*/ 0, 2, 0x04, 0x04,
  /* RLE: 046 Pixels @ 215,289*/ 46, 0x00,
  /* RLE: 001 Pixels @ 261,289*/ 1, 0x04,
  /* RLE: 006 Pixels @ 262,289*/ 6, 0x03,
  /* RLE: 001 Pixels @ 268,289*/ 1, 0x04,
  /* RLE: 060 Pixels @ 269,289*/ 60, 0x00,
  /* ABS: 003 Pixels @ 329,289*/ 0, 3, 0x03, 0x06, 0x11,
  /* RLE: 004 Pixels @ 332,289*/ 4, 0x03,
  /* ABS: 003 Pixels @ 336,289*/ 0, 3, 0x0C, 0x0F, 0x03,
  /* RLE: 061 Pixels @ 339,289*/ 61, 0x00,
  /* RLE: 015 Pixels @ 000,290*/ 15, 0x02,
  /* ABS: 005 Pixels @ 015,290*/ 0, 5, 0x00, 0x03, 0x03, 0x03, 0x00,
  /* RLE: 005 Pixels @ 020,290*/ 5, 0x02,
  /* ABS: 008 Pixels @ 025,290*/ 0, 8, 0x03, 0x0B, 0x08, 0x00, 0x03, 0x03, 0x03, 0x18,
  /* RLE: 005 Pixels @ 033,290*/ 5, 0x03,
  /* ABS: 024 Pixels @ 038,290*/ 0, 24, 0x0C, 0x00, 0x08, 0x0C, 0x03, 0x08, 0x0C, 0x03, 0x00, 0x03, 0x03, 0x0F, 0x0E, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x06, 0x0D, 0x03, 0x03, 0x08, 0x0E,
  /* RLE: 016 Pixels @ 062,290*/ 16, 0x03,
  /* RLE: 004 Pixels @ 078,290*/ 4, 0x04,
  /* RLE: 030 Pixels @ 082,290*/ 30, 0x00,
  /* RLE: 001 Pixels @ 112,290*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 113,290*/ 8, 0x09,
  /* RLE: 001 Pixels @ 121,290*/ 1, 0x0C,
  /* RLE: 082 Pixels @ 122,290*/ 82, 0x00,
  /* RLE: 001 Pixels @ 204,290*/ 1, 0x04,
  /* RLE: 007 Pixels @ 205,290*/ 7, 0x03,
  /* ABS: 002 Pixels @ 212,290*/ 0, 2, 0x04, 0x04,
  /* RLE: 048 Pixels @ 214,290*/ 48, 0x00,
  /* RLE: 007 Pixels @ 262,290*/ 7, 0x03,
  /* RLE: 001 Pixels @ 269,290*/ 1, 0x04,
  /* RLE: 059 Pixels @ 270,290*/ 59, 0x00,
  /* ABS: 003 Pixels @ 329,290*/ 0, 3, 0x03, 0x08, 0x0C,
  /* RLE: 004 Pixels @ 332,290*/ 4, 0x03,
  /* ABS: 003 Pixels @ 336,290*/ 0, 3, 0x00, 0x08, 0x03,
  /* RLE: 061 Pixels @ 339,290*/ 61, 0x00,
  /* RLE: 003 Pixels @ 000,291*/ 3, 0x02,
  /* RLE: 001 Pixels @ 003,291*/ 1, 0x00,
  /* RLE: 006 Pixels @ 004,291*/ 6, 0x03,
  /* RLE: 001 Pixels @ 010,291*/ 1, 0x00,
  /* RLE: 004 Pixels @ 011,291*/ 4, 0x02,
  /* ABS: 007 Pixels @ 015,291*/ 0, 7, 0x00, 0x00, 0x08, 0x00, 0x00, 0x02, 0x18,
  /* RLE: 004 Pixels @ 022,291*/ 4, 0x03,
  /* ABS: 036 Pixels @ 026,291*/ 0, 36, 0x00, 0x08, 0x0C, 0x00, 0x0C, 0x00, 0x03, 0x03, 0x11, 0x0C, 0x03, 0x0B, 0x08, 0x0B, 0x06, 0x06, 0x03, 0x0D, 0x08, 0x08, 0x08, 0x12, 0x03, 0x08, 0x11, 0x06, 0x0F, 0x0F, 0x00, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x06,
        0x06,
  /* RLE: 011 Pixels @ 062,291*/ 11, 0x03,
  /* RLE: 004 Pixels @ 073,291*/ 4, 0x04,
  /* RLE: 035 Pixels @ 077,291*/ 35, 0x00,
  /* RLE: 001 Pixels @ 112,291*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 113,291*/ 8, 0x09,
  /* RLE: 001 Pixels @ 121,291*/ 1, 0x0C,
  /* RLE: 081 Pixels @ 122,291*/ 81, 0x00,
  /* RLE: 001 Pixels @ 203,291*/ 1, 0x04,
  /* RLE: 007 Pixels @ 204,291*/ 7, 0x03,
  /* ABS: 002 Pixels @ 211,291*/ 0, 2, 0x04, 0x04,
  /* RLE: 049 Pixels @ 213,291*/ 49, 0x00,
  /* RLE: 001 Pixels @ 262,291*/ 1, 0x04,
  /* RLE: 006 Pixels @ 263,291*/ 6, 0x03,
  /* RLE: 001 Pixels @ 269,291*/ 1, 0x04,
  /* RLE: 059 Pixels @ 270,291*/ 59, 0x00,
  /* ABS: 003 Pixels @ 329,291*/ 0, 3, 0x03, 0x06, 0x0E,
  /* RLE: 004 Pixels @ 332,291*/ 4, 0x03,
  /* ABS: 003 Pixels @ 336,291*/ 0, 3, 0x12, 0x0F, 0x03,
  /* RLE: 061 Pixels @ 339,291*/ 61, 0x00,
  /* RLE: 004 Pixels @ 000,292*/ 4, 0x03,
  /* ABS: 058 Pixels @ 004,292*/ 0, 58, 0x0C, 0x06, 0x04, 0x0B, 0x08, 0x00, 0x03, 0x02, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x03, 0x03, 0x03, 0x00, 0x0C, 0x00, 0x03, 0x03, 0x08, 0x06, 0x0F, 0x11, 0x08, 0x00, 0x03, 0x11, 0x12, 0x03, 0x00, 0x08,
        0x0C, 0x06, 0x0D, 0x03, 0x03, 0x0B, 0x04, 0x0E, 0x08, 0x00, 0x08, 0x12, 0x00, 0x00, 0x0B, 0x00, 0x03, 0x04, 0x08, 0x00, 0x03, 0x12, 0x11,
  /* RLE: 007 Pixels @ 062,292*/ 7, 0x03,
  /* RLE: 004 Pixels @ 069,292*/ 4, 0x04,
  /* RLE: 039 Pixels @ 073,292*/ 39, 0x00,
  /* RLE: 001 Pixels @ 112,292*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 113,292*/ 8, 0x09,
  /* RLE: 001 Pixels @ 121,292*/ 1, 0x0C,
  /* RLE: 081 Pixels @ 122,292*/ 81, 0x00,
  /* RLE: 001 Pixels @ 203,292*/ 1, 0x04,
  /* RLE: 006 Pixels @ 204,292*/ 6, 0x03,
  /* ABS: 002 Pixels @ 210,292*/ 0, 2, 0x04, 0x04,
  /* RLE: 050 Pixels @ 212,292*/ 50, 0x00,
  /* RLE: 001 Pixels @ 262,292*/ 1, 0x04,
  /* RLE: 007 Pixels @ 263,292*/ 7, 0x03,
  /* RLE: 059 Pixels @ 270,292*/ 59, 0x00,
  /* ABS: 010 Pixels @ 329,292*/ 0, 10, 0x03, 0x04, 0x08, 0x0C, 0x00, 0x04, 0x06, 0x08, 0x0C, 0x03,
  /* RLE: 061 Pixels @ 339,292*/ 61, 0x00,
  /* ABS: 062 Pixels @ 000,293*/ 0, 62, 0x08, 0x0C, 0x03, 0x0B, 0x08, 0x0E, 0x03, 0x03, 0x08, 0x0C, 0x03, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x11, 0x0E, 0x03, 0x11, 0x06, 0x0F, 0x0F, 0x08, 0x00, 0x03, 0x0D, 0x0F, 0x03, 0x00, 0x08, 0x0C, 0x03, 0x06,
        0x06, 0x03, 0x03, 0x08, 0x0E, 0x0C, 0x08, 0x03, 0x12, 0x0D, 0x0C, 0x12, 0x08, 0x00, 0x0E, 0x08, 0x0E, 0x12, 0x0F, 0x00, 0x03, 0x0B, 0x06, 0x00, 0x03, 0x00, 0x0B,
  /* RLE: 005 Pixels @ 062,293*/ 5, 0x03,
  /* RLE: 001 Pixels @ 067,293*/ 1, 0x04,
  /* RLE: 044 Pixels @ 068,293*/ 44, 0x00,
  /* RLE: 001 Pixels @ 112,293*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 113,293*/ 8, 0x09,
  /* RLE: 001 Pixels @ 121,293*/ 1, 0x0C,
  /* RLE: 081 Pixels @ 122,293*/ 81, 0x00,
  /* RLE: 001 Pixels @ 203,293*/ 1, 0x04,
  /* RLE: 006 Pixels @ 204,293*/ 6, 0x03,
  /* RLE: 001 Pixels @ 210,293*/ 1, 0x04,
  /* RLE: 052 Pixels @ 211,293*/ 52, 0x00,
  /* RLE: 001 Pixels @ 263,293*/ 1, 0x04,
  /* RLE: 006 Pixels @ 264,293*/ 6, 0x03,
  /* RLE: 001 Pixels @ 270,293*/ 1, 0x04,
  /* RLE: 059 Pixels @ 271,293*/ 59, 0x00,
  /* ABS: 009 Pixels @ 330,293*/ 0, 9, 0x03, 0x0E, 0x08, 0x08, 0x08, 0x0F, 0x0C, 0x03, 0x03,
  /* RLE: 061 Pixels @ 339,293*/ 61, 0x00,
  /* ABS: 054 Pixels @ 000,294*/ 0, 54, 0x08, 0x0C, 0x03, 0x0F, 0x0D, 0x03, 0x00, 0x03, 0x08, 0x0C, 0x03, 0x04, 0x08, 0x08, 0x08, 0x04, 0x03, 0x06, 0x06, 0x03, 0x0D, 0x0F, 0x03, 0x00, 0x08, 0x0C, 0x03, 0x06, 0x0D, 0x03, 0x03, 0x08, 0x0E, 0x03, 0x0E,
        0x08, 0x03, 0x03, 0x08, 0x06, 0x04, 0x08, 0x00, 0x00, 0x0E, 0x06, 0x0E, 0x00, 0x03, 0x03, 0x0C, 0x06, 0x0E, 0x00,
  /* RLE: 005 Pixels @ 054,294*/ 5, 0x03,
  /* ABS: 002 Pixels @ 059,294*/ 0, 2, 0x00, 0x00,
  /* RLE: 006 Pixels @ 061,294*/ 6, 0x03,
  /* RLE: 001 Pixels @ 067,294*/ 1, 0x04,
  /* RLE: 045 Pixels @ 068,294*/ 45, 0x00,
  /* RLE: 001 Pixels @ 113,294*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 114,294*/ 8, 0x09,
  /* RLE: 001 Pixels @ 122,294*/ 1, 0x0C,
  /* RLE: 080 Pixels @ 123,294*/ 80, 0x00,
  /* RLE: 001 Pixels @ 203,294*/ 1, 0x04,
  /* RLE: 006 Pixels @ 204,294*/ 6, 0x03,
  /* RLE: 001 Pixels @ 210,294*/ 1, 0x04,
  /* RLE: 052 Pixels @ 211,294*/ 52, 0x00,
  /* RLE: 001 Pixels @ 263,294*/ 1, 0x04,
  /* RLE: 006 Pixels @ 264,294*/ 6, 0x03,
  /* RLE: 001 Pixels @ 270,294*/ 1, 0x04,
  /* RLE: 060 Pixels @ 271,294*/ 60, 0x00,
  /* ABS: 010 Pixels @ 331,294*/ 0, 10, 0x03, 0x00, 0x0B, 0x00, 0x03, 0x0C, 0x0E, 0x0C, 0x03, 0x03,
  /* RLE: 059 Pixels @ 341,294*/ 59, 0x00,
  /* ABS: 043 Pixels @ 000,295*/ 0, 43, 0x08, 0x06, 0x12, 0x08, 0x04, 0x03, 0x03, 0x03, 0x06, 0x06, 0x03, 0x0F, 0x0E, 0x03, 0x0C, 0x08, 0x03, 0x12, 0x0D, 0x03, 0x06, 0x0D, 0x03, 0x03, 0x08, 0x0E, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x06, 0x06, 0x03, 0x04,
        0x08, 0x12, 0x12, 0x11, 0x11, 0x0B, 0x06, 0x00,
  /* RLE: 007 Pixels @ 043,295*/ 7, 0x03,
  /* ABS: 006 Pixels @ 050,295*/ 0, 6, 0x00, 0x03, 0x03, 0x00, 0x00, 0x04,
  /* RLE: 004 Pixels @ 056,295*/ 4, 0x02,
  /* RLE: 001 Pixels @ 060,295*/ 1, 0x04,
  /* RLE: 006 Pixels @ 061,295*/ 6, 0x03,
  /* RLE: 001 Pixels @ 067,295*/ 1, 0x04,
  /* RLE: 045 Pixels @ 068,295*/ 45, 0x00,
  /* RLE: 001 Pixels @ 113,295*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 114,295*/ 8, 0x09,
  /* RLE: 001 Pixels @ 122,295*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 123,295*/ 79, 0x00,
  /* RLE: 001 Pixels @ 202,295*/ 1, 0x04,
  /* RLE: 007 Pixels @ 203,295*/ 7, 0x03,
  /* RLE: 053 Pixels @ 210,295*/ 53, 0x00,
  /* RLE: 001 Pixels @ 263,295*/ 1, 0x04,
  /* RLE: 007 Pixels @ 264,295*/ 7, 0x03,
  /* RLE: 061 Pixels @ 271,295*/ 61, 0x00,
  /* RLE: 003 Pixels @ 332,295*/ 3, 0x03,
  /* ABS: 006 Pixels @ 335,295*/ 0, 6, 0x0C, 0x11, 0x08, 0x0F, 0x04, 0x03,
  /* RLE: 059 Pixels @ 341,295*/ 59, 0x00,
  /* ABS: 044 Pixels @ 000,296*/ 0, 44, 0x06, 0x0F, 0x08, 0x0F, 0x11, 0x03, 0x03, 0x03, 0x06, 0x0D, 0x03, 0x08, 0x11, 0x06, 0x0F, 0x0F, 0x00, 0x0C, 0x08, 0x03, 0x0C, 0x08, 0x03, 0x03, 0x06, 0x06, 0x03, 0x04, 0x08, 0x00, 0x03, 0x12, 0x11, 0x03, 0x03,
        0x0E, 0x06, 0x0C, 0x00, 0x0B, 0x03, 0x03, 0x03, 0x00,
  /* RLE: 005 Pixels @ 044,296*/ 5, 0x04,
  /* RLE: 012 Pixels @ 049,296*/ 12, 0x02,
  /* RLE: 007 Pixels @ 061,296*/ 7, 0x03,
  /* RLE: 001 Pixels @ 068,296*/ 1, 0x04,
  /* RLE: 044 Pixels @ 069,296*/ 44, 0x00,
  /* RLE: 001 Pixels @ 113,296*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 114,296*/ 8, 0x09,
  /* RLE: 001 Pixels @ 122,296*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 123,296*/ 79, 0x00,
  /* RLE: 001 Pixels @ 202,296*/ 1, 0x04,
  /* RLE: 006 Pixels @ 203,296*/ 6, 0x03,
  /* RLE: 001 Pixels @ 209,296*/ 1, 0x04,
  /* RLE: 054 Pixels @ 210,296*/ 54, 0x00,
  /* RLE: 001 Pixels @ 264,296*/ 1, 0x04,
  /* RLE: 006 Pixels @ 265,296*/ 6, 0x03,
  /* RLE: 001 Pixels @ 271,296*/ 1, 0x04,
  /* RLE: 059 Pixels @ 272,296*/ 59, 0x00,
  /* ABS: 010 Pixels @ 331,296*/ 0, 10, 0x03, 0x03, 0x0E, 0x08, 0x08, 0x06, 0x08, 0x00, 0x03, 0x03,
  /* RLE: 059 Pixels @ 341,296*/ 59, 0x00,
  /* ABS: 033 Pixels @ 000,297*/ 0, 33, 0x12, 0x08, 0x12, 0x00, 0x08, 0x0D, 0x03, 0x03, 0x0C, 0x08, 0x03, 0x08, 0x12, 0x0B, 0x00, 0x0B, 0x00, 0x04, 0x08, 0x00, 0x04, 0x08, 0x00, 0x03, 0x12, 0x11, 0x03, 0x0B, 0x06, 0x00, 0x03, 0x00, 0x0B,
  /* RLE: 006 Pixels @ 033,297*/ 6, 0x03,
  /* ABS: 003 Pixels @ 039,297*/ 0, 3, 0x00, 0x00, 0x04,
  /* RLE: 019 Pixels @ 042,297*/ 19, 0x02,
  /* RLE: 001 Pixels @ 061,297*/ 1, 0x04,
  /* RLE: 006 Pixels @ 062,297*/ 6, 0x03,
  /* RLE: 001 Pixels @ 068,297*/ 1, 0x04,
  /* RLE: 044 Pixels @ 069,297*/ 44, 0x00,
  /* RLE: 001 Pixels @ 113,297*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 114,297*/ 8, 0x09,
  /* RLE: 001 Pixels @ 122,297*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 123,297*/ 79, 0x00,
  /* RLE: 001 Pixels @ 202,297*/ 1, 0x04,
  /* RLE: 006 Pixels @ 203,297*/ 6, 0x03,
  /* RLE: 001 Pixels @ 209,297*/ 1, 0x04,
  /* RLE: 054 Pixels @ 210,297*/ 54, 0x00,
  /* RLE: 001 Pixels @ 264,297*/ 1, 0x04,
  /* RLE: 006 Pixels @ 265,297*/ 6, 0x03,
  /* RLE: 001 Pixels @ 271,297*/ 1, 0x04,
  /* RLE: 060 Pixels @ 272,297*/ 60, 0x00,
  /* ABS: 010 Pixels @ 332,297*/ 0, 10, 0x0C, 0x08, 0x0C, 0x00, 0x03, 0x0C, 0x0E, 0x0C, 0x03, 0x03,
  /* RLE: 058 Pixels @ 342,297*/ 58, 0x00,
  /* ABS: 026 Pixels @ 000,298*/ 0, 26, 0x0C, 0x08, 0x00, 0x03, 0x0C, 0x08, 0x12, 0x03, 0x04, 0x08, 0x00, 0x0E, 0x08, 0x0E, 0x12, 0x0F, 0x00, 0x0B, 0x06, 0x00, 0x0B, 0x06, 0x00, 0x03, 0x00, 0x0B,
  /* RLE: 004 Pixels @ 026,298*/ 4, 0x03,
  /* RLE: 004 Pixels @ 030,298*/ 4, 0x00,
  /* RLE: 001 Pixels @ 034,298*/ 1, 0x04,
  /* RLE: 026 Pixels @ 035,298*/ 26, 0x02,
  /* RLE: 001 Pixels @ 061,298*/ 1, 0x04,
  /* RLE: 006 Pixels @ 062,298*/ 6, 0x03,
  /* RLE: 001 Pixels @ 068,298*/ 1, 0x04,
  /* RLE: 044 Pixels @ 069,298*/ 44, 0x00,
  /* RLE: 001 Pixels @ 113,298*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 114,298*/ 8, 0x09,
  /* RLE: 001 Pixels @ 122,298*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 123,298*/ 79, 0x00,
  /* RLE: 001 Pixels @ 202,298*/ 1, 0x04,
  /* RLE: 006 Pixels @ 203,298*/ 6, 0x03,
  /* RLE: 001 Pixels @ 209,298*/ 1, 0x04,
  /* RLE: 055 Pixels @ 210,298*/ 55, 0x00,
  /* RLE: 007 Pixels @ 265,298*/ 7, 0x03,
  /* RLE: 001 Pixels @ 272,298*/ 1, 0x04,
  /* RLE: 057 Pixels @ 273,298*/ 57, 0x00,
  /* ABS: 012 Pixels @ 330,298*/ 0, 12, 0x0B, 0x03, 0x00, 0x08, 0x00, 0x03, 0x04, 0x11, 0x08, 0x0F, 0x04, 0x03,
  /* RLE: 058 Pixels @ 342,298*/ 58, 0x00,
  /* ABS: 016 Pixels @ 000,299*/ 0, 16, 0x04, 0x08, 0x00, 0x03, 0x03, 0x0E, 0x08, 0x0E, 0x0B, 0x06, 0x00, 0x03, 0x0C, 0x06, 0x0E, 0x00,
  /* RLE: 007 Pixels @ 016,299*/ 7, 0x03,
  /* RLE: 004 Pixels @ 023,299*/ 4, 0x00,
  /* RLE: 001 Pixels @ 027,299*/ 1, 0x04,
  /* RLE: 033 Pixels @ 028,299*/ 33, 0x02,
  /* RLE: 001 Pixels @ 061,299*/ 1, 0x04,
  /* RLE: 006 Pixels @ 062,299*/ 6, 0x03,
  /* RLE: 001 Pixels @ 068,299*/ 1, 0x04,
  /* RLE: 045 Pixels @ 069,299*/ 45, 0x00,
  /* RLE: 001 Pixels @ 114,299*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 115,299*/ 8, 0x09,
  /* RLE: 001 Pixels @ 123,299*/ 1, 0x0C,
  /* RLE: 077 Pixels @ 124,299*/ 77, 0x00,
  /* ABS: 002 Pixels @ 201,299*/ 0, 2, 0x04, 0x04,
  /* RLE: 006 Pixels @ 203,299*/ 6, 0x03,
  /* RLE: 056 Pixels @ 209,299*/ 56, 0x00,
  /* RLE: 001 Pixels @ 265,299*/ 1, 0x04,
  /* RLE: 006 Pixels @ 266,299*/ 6, 0x03,
  /* RLE: 001 Pixels @ 272,299*/ 1, 0x04,
  /* RLE: 059 Pixels @ 273,299*/ 59, 0x00,
  /* ABS: 009 Pixels @ 332,299*/ 0, 9, 0x03, 0x03, 0x0E, 0x08, 0x08, 0x06, 0x08, 0x00, 0x03,
  /* RLE: 059 Pixels @ 341,299*/ 59, 0x00,
  /* ABS: 003 Pixels @ 000,300*/ 0, 3, 0x0B, 0x06, 0x0B,
  /* RLE: 012 Pixels @ 003,300*/ 12, 0x03,
  /* ABS: 002 Pixels @ 015,300*/ 0, 2, 0x00, 0x00,
  /* RLE: 004 Pixels @ 017,300*/ 4, 0x04,
  /* RLE: 040 Pixels @ 021,300*/ 40, 0x02,
  /* RLE: 001 Pixels @ 061,300*/ 1, 0x04,
  /* RLE: 006 Pixels @ 062,300*/ 6, 0x03,
  /* RLE: 001 Pixels @ 068,300*/ 1, 0x04,
  /* RLE: 045 Pixels @ 069,300*/ 45, 0x00,
  /* RLE: 001 Pixels @ 114,300*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 115,300*/ 8, 0x09,
  /* RLE: 001 Pixels @ 123,300*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 124,300*/ 78, 0x00,
  /* RLE: 001 Pixels @ 202,300*/ 1, 0x04,
  /* RLE: 006 Pixels @ 203,300*/ 6, 0x03,
  /* RLE: 001 Pixels @ 209,300*/ 1, 0x04,
  /* RLE: 055 Pixels @ 210,300*/ 55, 0x00,
  /* RLE: 001 Pixels @ 265,300*/ 1, 0x04,
  /* RLE: 007 Pixels @ 266,300*/ 7, 0x03,
  /* RLE: 059 Pixels @ 273,300*/ 59, 0x00,
  /* ABS: 010 Pixels @ 332,300*/ 0, 10, 0x03, 0x0C, 0x08, 0x0C, 0x00, 0x03, 0x00, 0x00, 0x03, 0x04,
  /* RLE: 058 Pixels @ 342,300*/ 58, 0x00,
  /* RLE: 007 Pixels @ 000,301*/ 7, 0x03,
  /* RLE: 007 Pixels @ 007,301*/ 7, 0x04,
  /* RLE: 047 Pixels @ 014,301*/ 47, 0x02,
  /* RLE: 001 Pixels @ 061,301*/ 1, 0x04,
  /* RLE: 006 Pixels @ 062,301*/ 6, 0x03,
  /* RLE: 001 Pixels @ 068,301*/ 1, 0x04,
  /* RLE: 045 Pixels @ 069,301*/ 45, 0x00,
  /* RLE: 001 Pixels @ 114,301*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 115,301*/ 8, 0x09,
  /* RLE: 001 Pixels @ 123,301*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 124,301*/ 78, 0x00,
  /* RLE: 001 Pixels @ 202,301*/ 1, 0x04,
  /* RLE: 006 Pixels @ 203,301*/ 6, 0x03,
  /* RLE: 001 Pixels @ 209,301*/ 1, 0x04,
  /* RLE: 056 Pixels @ 210,301*/ 56, 0x00,
  /* RLE: 001 Pixels @ 266,301*/ 1, 0x04,
  /* RLE: 006 Pixels @ 267,301*/ 6, 0x03,
  /* RLE: 001 Pixels @ 273,301*/ 1, 0x04,
  /* RLE: 058 Pixels @ 274,301*/ 58, 0x00,
  /* ABS: 004 Pixels @ 332,301*/ 0, 4, 0x03, 0x00, 0x08, 0x00,
  /* RLE: 005 Pixels @ 336,301*/ 5, 0x03,
  /* RLE: 059 Pixels @ 341,301*/ 59, 0x00,
  /* RLE: 007 Pixels @ 000,302*/ 7, 0x04,
  /* RLE: 055 Pixels @ 007,302*/ 55, 0x02,
  /* RLE: 007 Pixels @ 062,302*/ 7, 0x03,
  /* RLE: 001 Pixels @ 069,302*/ 1, 0x04,
  /* RLE: 044 Pixels @ 070,302*/ 44, 0x00,
  /* RLE: 001 Pixels @ 114,302*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 115,302*/ 8, 0x09,
  /* RLE: 001 Pixels @ 123,302*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 124,302*/ 78, 0x00,
  /* RLE: 001 Pixels @ 202,302*/ 1, 0x04,
  /* RLE: 006 Pixels @ 203,302*/ 6, 0x03,
  /* RLE: 001 Pixels @ 209,302*/ 1, 0x04,
  /* RLE: 056 Pixels @ 210,302*/ 56, 0x00,
  /* RLE: 001 Pixels @ 266,302*/ 1, 0x04,
  /* RLE: 006 Pixels @ 267,302*/ 6, 0x03,
  /* RLE: 001 Pixels @ 273,302*/ 1, 0x04,
  /* RLE: 059 Pixels @ 274,302*/ 59, 0x00,
  /* RLE: 004 Pixels @ 333,302*/ 4, 0x03,
  /* ABS: 005 Pixels @ 337,302*/ 0, 5, 0x0C, 0x06, 0x06, 0x0B, 0x03,
  /* RLE: 058 Pixels @ 342,302*/ 58, 0x00,
  /* RLE: 062 Pixels @ 000,303*/ 62, 0x02,
  /* RLE: 001 Pixels @ 062,303*/ 1, 0x04,
  /* RLE: 006 Pixels @ 063,303*/ 6, 0x03,
  /* RLE: 001 Pixels @ 069,303*/ 1, 0x04,
  /* RLE: 044 Pixels @ 070,303*/ 44, 0x00,
  /* RLE: 001 Pixels @ 114,303*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 115,303*/ 8, 0x09,
  /* RLE: 001 Pixels @ 123,303*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 124,303*/ 79, 0x00,
  /* RLE: 007 Pixels @ 203,303*/ 7, 0x03,
  /* RLE: 001 Pixels @ 210,303*/ 1, 0x04,
  /* RLE: 055 Pixels @ 211,303*/ 55, 0x00,
  /* RLE: 001 Pixels @ 266,303*/ 1, 0x04,
  /* RLE: 007 Pixels @ 267,303*/ 7, 0x03,
  /* RLE: 061 Pixels @ 274,303*/ 61, 0x00,
  /* ABS: 006 Pixels @ 335,303*/ 0, 6, 0x03, 0x06, 0x08, 0x06, 0x12, 0x08,
  /* RLE: 059 Pixels @ 341,303*/ 59, 0x00,
  /* RLE: 062 Pixels @ 000,304*/ 62, 0x02,
  /* RLE: 001 Pixels @ 062,304*/ 1, 0x04,
  /* RLE: 006 Pixels @ 063,304*/ 6, 0x03,
  /* RLE: 001 Pixels @ 069,304*/ 1, 0x04,
  /* RLE: 045 Pixels @ 070,304*/ 45, 0x00,
  /* RLE: 001 Pixels @ 115,304*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 116,304*/ 8, 0x09,
  /* RLE: 001 Pixels @ 124,304*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 125,304*/ 78, 0x00,
  /* RLE: 001 Pixels @ 203,304*/ 1, 0x04,
  /* RLE: 006 Pixels @ 204,304*/ 6, 0x03,
  /* RLE: 001 Pixels @ 210,304*/ 1, 0x04,
  /* RLE: 056 Pixels @ 211,304*/ 56, 0x00,
  /* RLE: 001 Pixels @ 267,304*/ 1, 0x04,
  /* RLE: 006 Pixels @ 268,304*/ 6, 0x03,
  /* RLE: 001 Pixels @ 274,304*/ 1, 0x04,
  /* RLE: 060 Pixels @ 275,304*/ 60, 0x00,
  /* ABS: 008 Pixels @ 335,304*/ 0, 8, 0x03, 0x08, 0x0C, 0x03, 0x03, 0x0E, 0x0E, 0x03,
  /* RLE: 057 Pixels @ 343,304*/ 57, 0x00,
  /* RLE: 062 Pixels @ 000,305*/ 62, 0x02,
  /* RLE: 001 Pixels @ 062,305*/ 1, 0x04,
  /* RLE: 006 Pixels @ 063,305*/ 6, 0x03,
  /* RLE: 001 Pixels @ 069,305*/ 1, 0x04,
  /* RLE: 045 Pixels @ 070,305*/ 45, 0x00,
  /* RLE: 001 Pixels @ 115,305*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 116,305*/ 8, 0x09,
  /* RLE: 001 Pixels @ 124,305*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 125,305*/ 78, 0x00,
  /* RLE: 001 Pixels @ 203,305*/ 1, 0x04,
  /* RLE: 006 Pixels @ 204,305*/ 6, 0x03,
  /* RLE: 001 Pixels @ 210,305*/ 1, 0x04,
  /* RLE: 056 Pixels @ 211,305*/ 56, 0x00,
  /* RLE: 001 Pixels @ 267,305*/ 1, 0x04,
  /* RLE: 006 Pixels @ 268,305*/ 6, 0x03,
  /* RLE: 001 Pixels @ 274,305*/ 1, 0x04,
  /* RLE: 060 Pixels @ 275,305*/ 60, 0x00,
  /* ABS: 009 Pixels @ 335,305*/ 0, 9, 0x03, 0x08, 0x00, 0x03, 0x03, 0x12, 0x12, 0x03, 0x04,
  /* RLE: 056 Pixels @ 344,305*/ 56, 0x00,
  /* RLE: 062 Pixels @ 000,306*/ 62, 0x02,
  /* RLE: 001 Pixels @ 062,306*/ 1, 0x04,
  /* RLE: 006 Pixels @ 063,306*/ 6, 0x03,
  /* RLE: 001 Pixels @ 069,306*/ 1, 0x04,
  /* RLE: 045 Pixels @ 070,306*/ 45, 0x00,
  /* RLE: 001 Pixels @ 115,306*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 116,306*/ 8, 0x09,
  /* RLE: 001 Pixels @ 124,306*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 125,306*/ 78, 0x00,
  /* RLE: 001 Pixels @ 203,306*/ 1, 0x04,
  /* RLE: 006 Pixels @ 204,306*/ 6, 0x03,
  /* RLE: 001 Pixels @ 210,306*/ 1, 0x04,
  /* RLE: 057 Pixels @ 211,306*/ 57, 0x00,
  /* RLE: 007 Pixels @ 268,306*/ 7, 0x03,
  /* RLE: 001 Pixels @ 275,306*/ 1, 0x04,
  /* RLE: 059 Pixels @ 276,306*/ 59, 0x00,
  /* ABS: 009 Pixels @ 335,306*/ 0, 9, 0x03, 0x06, 0x0D, 0x0C, 0x06, 0x08, 0x04, 0x03, 0x04,
  /* RLE: 056 Pixels @ 344,306*/ 56, 0x00,
  /* RLE: 062 Pixels @ 000,307*/ 62, 0x02,
  /* RLE: 001 Pixels @ 062,307*/ 1, 0x04,
  /* RLE: 006 Pixels @ 063,307*/ 6, 0x03,
  /* RLE: 001 Pixels @ 069,307*/ 1, 0x04,
  /* RLE: 045 Pixels @ 070,307*/ 45, 0x00,
  /* RLE: 001 Pixels @ 115,307*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 116,307*/ 8, 0x09,
  /* RLE: 001 Pixels @ 124,307*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 125,307*/ 79, 0x00,
  /* RLE: 007 Pixels @ 204,307*/ 7, 0x03,
  /* RLE: 001 Pixels @ 211,307*/ 1, 0x04,
  /* RLE: 056 Pixels @ 212,307*/ 56, 0x00,
  /* RLE: 001 Pixels @ 268,307*/ 1, 0x04,
  /* RLE: 008 Pixels @ 269,307*/ 8, 0x03,
  /* RLE: 058 Pixels @ 277,307*/ 58, 0x00,
  /* ABS: 010 Pixels @ 335,307*/ 0, 10, 0x03, 0x03, 0x0E, 0x08, 0x11, 0x0B, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 055 Pixels @ 345,307*/ 55, 0x00,
  /* RLE: 063 Pixels @ 000,308*/ 63, 0x02,
  /* RLE: 007 Pixels @ 063,308*/ 7, 0x03,
  /* RLE: 001 Pixels @ 070,308*/ 1, 0x04,
  /* RLE: 045 Pixels @ 071,308*/ 45, 0x00,
  /* RLE: 001 Pixels @ 116,308*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 117,308*/ 8, 0x09,
  /* RLE: 001 Pixels @ 125,308*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 126,308*/ 78, 0x00,
  /* RLE: 001 Pixels @ 204,308*/ 1, 0x04,
  /* RLE: 006 Pixels @ 205,308*/ 6, 0x03,
  /* RLE: 001 Pixels @ 211,308*/ 1, 0x04,
  /* RLE: 056 Pixels @ 212,308*/ 56, 0x00,
  /* RLE: 001 Pixels @ 268,308*/ 1, 0x04,
  /* RLE: 004 Pixels @ 269,308*/ 4, 0x03,
  /* ABS: 004 Pixels @ 273,308*/ 0, 4, 0x04, 0x0D, 0x12, 0x03,
  /* RLE: 059 Pixels @ 277,308*/ 59, 0x00,
  /* RLE: 003 Pixels @ 336,308*/ 3, 0x03,
  /* ABS: 006 Pixels @ 339,308*/ 0, 6, 0x0E, 0x0E, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 055 Pixels @ 345,308*/ 55, 0x00,
  /* RLE: 063 Pixels @ 000,309*/ 63, 0x02,
  /* RLE: 001 Pixels @ 063,309*/ 1, 0x04,
  /* RLE: 006 Pixels @ 064,309*/ 6, 0x03,
  /* RLE: 001 Pixels @ 070,309*/ 1, 0x04,
  /* RLE: 045 Pixels @ 071,309*/ 45, 0x00,
  /* RLE: 001 Pixels @ 116,309*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 117,309*/ 8, 0x09,
  /* RLE: 001 Pixels @ 125,309*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 126,309*/ 78, 0x00,
  /* RLE: 001 Pixels @ 204,309*/ 1, 0x04,
  /* RLE: 006 Pixels @ 205,309*/ 6, 0x03,
  /* RLE: 001 Pixels @ 211,309*/ 1, 0x04,
  /* RLE: 056 Pixels @ 212,309*/ 56, 0x00,
  /* ABS: 009 Pixels @ 268,309*/ 0, 9, 0x03, 0x03, 0x00, 0x0E, 0x0F, 0x08, 0x11, 0x0C, 0x03,
  /* RLE: 060 Pixels @ 277,309*/ 60, 0x00,
  /* ABS: 004 Pixels @ 337,309*/ 0, 4, 0x04, 0x03, 0x0E, 0x0F,
  /* RLE: 005 Pixels @ 341,309*/ 5, 0x03,
  /* RLE: 054 Pixels @ 346,309*/ 54, 0x00,
  /* RLE: 063 Pixels @ 000,310*/ 63, 0x02,
  /* RLE: 001 Pixels @ 063,310*/ 1, 0x04,
  /* RLE: 006 Pixels @ 064,310*/ 6, 0x03,
  /* RLE: 001 Pixels @ 070,310*/ 1, 0x04,
  /* RLE: 045 Pixels @ 071,310*/ 45, 0x00,
  /* RLE: 001 Pixels @ 116,310*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 117,310*/ 8, 0x09,
  /* RLE: 001 Pixels @ 125,310*/ 1, 0x0C,
  /* RLE: 078 Pixels @ 126,310*/ 78, 0x00,
  /* RLE: 001 Pixels @ 204,310*/ 1, 0x04,
  /* RLE: 007 Pixels @ 205,310*/ 7, 0x03,
  /* RLE: 054 Pixels @ 212,310*/ 54, 0x00,
  /* ABS: 010 Pixels @ 266,310*/ 0, 10, 0x04, 0x00, 0x0B, 0x0D, 0x08, 0x08, 0x0E, 0x00, 0x03, 0x03,
  /* RLE: 062 Pixels @ 276,310*/ 62, 0x00,
  /* ABS: 009 Pixels @ 338,310*/ 0, 9, 0x03, 0x00, 0x08, 0x00, 0x03, 0x04, 0x12, 0x0E, 0x03,
  /* RLE: 053 Pixels @ 347,310*/ 53, 0x00,
  /* RLE: 063 Pixels @ 000,311*/ 63, 0x02,
  /* RLE: 001 Pixels @ 063,311*/ 1, 0x04,
  /* RLE: 006 Pixels @ 064,311*/ 6, 0x03,
  /* RLE: 001 Pixels @ 070,311*/ 1, 0x04,
  /* RLE: 045 Pixels @ 071,311*/ 45, 0x00,
  /* RLE: 001 Pixels @ 116,311*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 117,311*/ 8, 0x09,
  /* RLE: 001 Pixels @ 125,311*/ 1, 0x0C,
  /* RLE: 079 Pixels @ 126,311*/ 79, 0x00,
  /* RLE: 001 Pixels @ 205,311*/ 1, 0x04,
  /* RLE: 006 Pixels @ 206,311*/ 6, 0x03,
  /* RLE: 001 Pixels @ 212,311*/ 1, 0x04,
  /* RLE: 050 Pixels @ 213,311*/ 50, 0x00,
  /* ABS: 015 Pixels @ 263,311*/ 0, 15, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x11, 0x0C, 0x03, 0x03, 0x00, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 060 Pixels @ 278,311*/ 60, 0x00,
  /* ABS: 009 Pixels @ 338,311*/ 0, 9, 0x03, 0x03, 0x00, 0x0E, 0x11, 0x08, 0x0F, 0x0E, 0x03,
  /* RLE: 053 Pixels @ 347,311*/ 53, 0x00,
  /* RLE: 063 Pixels @ 000,312*/ 63, 0x02,
  /* RLE: 001 Pixels @ 063,312*/ 1, 0x04,
  /* RLE: 006 Pixels @ 064,312*/ 6, 0x03,
  /* RLE: 001 Pixels @ 070,312*/ 1, 0x04,
  /* RLE: 045 Pixels @ 071,312*/ 45, 0x00,
  /* RLE: 001 Pixels @ 116,312*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 117,312*/ 8, 0x09,
  /* RLE: 001 Pixels @ 125,312*/ 1, 0x0C,
  /* RLE: 027 Pixels @ 126,312*/ 27, 0x00,
  /* RLE: 005 Pixels @ 153,312*/ 5, 0x04,
  /* RLE: 047 Pixels @ 158,312*/ 47, 0x00,
  /* RLE: 001 Pixels @ 205,312*/ 1, 0x04,
  /* RLE: 006 Pixels @ 206,312*/ 6, 0x03,
  /* RLE: 001 Pixels @ 212,312*/ 1, 0x04,
  /* RLE: 048 Pixels @ 213,312*/ 48, 0x00,
  /* ABS: 002 Pixels @ 261,312*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 263,312*/ 8, 0x03,
  /* ABS: 007 Pixels @ 271,312*/ 0, 7, 0x0B, 0x12, 0x08, 0x0D, 0x03, 0x03, 0x04,
  /* RLE: 059 Pixels @ 278,312*/ 59, 0x00,
  /* ABS: 009 Pixels @ 337,312*/ 0, 9, 0x03, 0x0B, 0x06, 0x08, 0x08, 0x08, 0x0C, 0x03, 0x03,
  /* RLE: 054 Pixels @ 346,312*/ 54, 0x00,
  /* RLE: 063 Pixels @ 000,313*/ 63, 0x02,
  /* RLE: 001 Pixels @ 063,313*/ 1, 0x04,
  /* RLE: 006 Pixels @ 064,313*/ 6, 0x03,
  /* RLE: 001 Pixels @ 070,313*/ 1, 0x04,
  /* RLE: 046 Pixels @ 071,313*/ 46, 0x00,
  /* RLE: 001 Pixels @ 117,313*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 118,313*/ 8, 0x09,
  /* RLE: 001 Pixels @ 126,313*/ 1, 0x0C,
  /* RLE: 023 Pixels @ 127,313*/ 23, 0x00,
  /* ABS: 002 Pixels @ 150,313*/ 0, 2, 0x04, 0x04,
  /* RLE: 005 Pixels @ 152,313*/ 5, 0x03,
  /* ABS: 002 Pixels @ 157,313*/ 0, 2, 0x04, 0x04,
  /* RLE: 046 Pixels @ 159,313*/ 46, 0x00,
  /* RLE: 001 Pixels @ 205,313*/ 1, 0x04,
  /* RLE: 006 Pixels @ 206,313*/ 6, 0x03,
  /* RLE: 001 Pixels @ 212,313*/ 1, 0x04,
  /* RLE: 045 Pixels @ 213,313*/ 45, 0x00,
  /* ABS: 002 Pixels @ 258,313*/ 0, 2, 0x04, 0x04,
  /* RLE: 009 Pixels @ 260,313*/ 9, 0x03,
  /* ABS: 010 Pixels @ 269,313*/ 0, 10, 0x0C, 0x11, 0x08, 0x11, 0x06, 0x12, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 058 Pixels @ 279,313*/ 58, 0x00,
  /* ABS: 007 Pixels @ 337,313*/ 0, 7, 0x03, 0x04, 0x11, 0x0E, 0x00, 0x06, 0x0E,
  /* RLE: 004 Pixels @ 344,313*/ 4, 0x03,
  /* RLE: 052 Pixels @ 348,313*/ 52, 0x00,
  /* RLE: 064 Pixels @ 000,314*/ 64, 0x02,
  /* RLE: 007 Pixels @ 064,314*/ 7, 0x03,
  /* RLE: 001 Pixels @ 071,314*/ 1, 0x04,
  /* RLE: 045 Pixels @ 072,314*/ 45, 0x00,
  /* RLE: 001 Pixels @ 117,314*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 118,314*/ 8, 0x09,
  /* RLE: 001 Pixels @ 126,314*/ 1, 0x0C,
  /* RLE: 021 Pixels @ 127,314*/ 21, 0x00,
  /* ABS: 002 Pixels @ 148,314*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 150,314*/ 8, 0x03,
  /* RLE: 001 Pixels @ 158,314*/ 1, 0x04,
  /* RLE: 046 Pixels @ 159,314*/ 46, 0x00,
  /* RLE: 001 Pixels @ 205,314*/ 1, 0x04,
  /* RLE: 007 Pixels @ 206,314*/ 7, 0x03,
  /* RLE: 043 Pixels @ 213,314*/ 43, 0x00,
  /* ABS: 002 Pixels @ 256,314*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 258,314*/ 11, 0x03,
  /* ABS: 010 Pixels @ 269,314*/ 0, 10, 0x04, 0x12, 0x0B, 0x03, 0x03, 0x0E, 0x12, 0x03, 0x03, 0x04,
  /* RLE: 059 Pixels @ 279,314*/ 59, 0x00,
  /* RLE: 004 Pixels @ 338,314*/ 4, 0x03,
  /* ABS: 007 Pixels @ 342,314*/ 0, 7, 0x0C, 0x0F, 0x03, 0x00, 0x0E, 0x00, 0x03,
  /* RLE: 051 Pixels @ 349,314*/ 51, 0x00,
  /* RLE: 064 Pixels @ 000,315*/ 64, 0x02,
  /* RLE: 001 Pixels @ 064,315*/ 1, 0x04,
  /* RLE: 006 Pixels @ 065,315*/ 6, 0x03,
  /* RLE: 001 Pixels @ 071,315*/ 1, 0x04,
  /* RLE: 045 Pixels @ 072,315*/ 45, 0x00,
  /* RLE: 001 Pixels @ 117,315*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 118,315*/ 8, 0x09,
  /* RLE: 001 Pixels @ 126,315*/ 1, 0x0C,
  /* RLE: 018 Pixels @ 127,315*/ 18, 0x00,
  /* ABS: 002 Pixels @ 145,315*/ 0, 2, 0x04, 0x04,
  /* RLE: 011 Pixels @ 147,315*/ 11, 0x03,
  /* RLE: 001 Pixels @ 158,315*/ 1, 0x04,
  /* RLE: 047 Pixels @ 159,315*/ 47, 0x00,
  /* RLE: 001 Pixels @ 206,315*/ 1, 0x04,
  /* RLE: 006 Pixels @ 207,315*/ 6, 0x03,
  /* RLE: 001 Pixels @ 213,315*/ 1, 0x04,
  /* RLE: 039 Pixels @ 214,315*/ 39, 0x00,
  /* ABS: 002 Pixels @ 253,315*/ 0, 2, 0x04, 0x04,
  /* RLE: 018 Pixels @ 255,315*/ 18, 0x03,
  /* ABS: 006 Pixels @ 273,315*/ 0, 6, 0x0C, 0x0F, 0x06, 0x03, 0x03, 0x04,
  /* RLE: 061 Pixels @ 279,315*/ 61, 0x00,
  /* RLE: 003 Pixels @ 340,315*/ 3, 0x03,
  /* ABS: 005 Pixels @ 343,315*/ 0, 5, 0x08, 0x11, 0x08, 0x08, 0x04,
  /* RLE: 052 Pixels @ 348,315*/ 52, 0x00,
  /* RLE: 064 Pixels @ 000,316*/ 64, 0x02,
  /* RLE: 001 Pixels @ 064,316*/ 1, 0x04,
  /* RLE: 006 Pixels @ 065,316*/ 6, 0x03,
  /* RLE: 001 Pixels @ 071,316*/ 1, 0x04,
  /* RLE: 045 Pixels @ 072,316*/ 45, 0x00,
  /* RLE: 001 Pixels @ 117,316*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 118,316*/ 8, 0x09,
  /* RLE: 001 Pixels @ 126,316*/ 1, 0x0C,
  /* RLE: 016 Pixels @ 127,316*/ 16, 0x00,
  /* ABS: 002 Pixels @ 143,316*/ 0, 2, 0x04, 0x04,
  /* RLE: 013 Pixels @ 145,316*/ 13, 0x03,
  /* RLE: 001 Pixels @ 158,316*/ 1, 0x04,
  /* RLE: 047 Pixels @ 159,316*/ 47, 0x00,
  /* RLE: 001 Pixels @ 206,316*/ 1, 0x04,
  /* RLE: 006 Pixels @ 207,316*/ 6, 0x03,
  /* RLE: 001 Pixels @ 213,316*/ 1, 0x04,
  /* RLE: 037 Pixels @ 214,316*/ 37, 0x00,
  /* ABS: 002 Pixels @ 251,316*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 253,316*/ 16, 0x03,
  /* ABS: 011 Pixels @ 269,316*/ 0, 11, 0x00, 0x00, 0x0E, 0x08, 0x08, 0x0D, 0x0D, 0x00, 0x03, 0x03, 0x04,
  /* RLE: 059 Pixels @ 280,316*/ 59, 0x00,
  /* ABS: 009 Pixels @ 339,316*/ 0, 9, 0x03, 0x00, 0x0E, 0x0F, 0x08, 0x11, 0x0C, 0x00, 0x03,
  /* RLE: 052 Pixels @ 348,316*/ 52, 0x00,
  /* RLE: 064 Pixels @ 000,317*/ 64, 0x02,
  /* RLE: 001 Pixels @ 064,317*/ 1, 0x04,
  /* RLE: 006 Pixels @ 065,317*/ 6, 0x03,
  /* RLE: 001 Pixels @ 071,317*/ 1, 0x04,
  /* RLE: 045 Pixels @ 072,317*/ 45, 0x00,
  /* RLE: 001 Pixels @ 117,317*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 118,317*/ 8, 0x09,
  /* RLE: 001 Pixels @ 126,317*/ 1, 0x0C,
  /* RLE: 013 Pixels @ 127,317*/ 13, 0x00,
  /* ABS: 002 Pixels @ 140,317*/ 0, 2, 0x04, 0x04,
  /* RLE: 015 Pixels @ 142,317*/ 15, 0x03,
  /* ABS: 002 Pixels @ 157,317*/ 0, 2, 0x04, 0x04,
  /* RLE: 047 Pixels @ 159,317*/ 47, 0x00,
  /* RLE: 001 Pixels @ 206,317*/ 1, 0x04,
  /* RLE: 006 Pixels @ 207,317*/ 6, 0x03,
  /* ABS: 002 Pixels @ 213,317*/ 0, 2, 0x04, 0x04,
  /* RLE: 033 Pixels @ 215,317*/ 33, 0x00,
  /* ABS: 002 Pixels @ 248,317*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 250,317*/ 16, 0x03,
  /* ABS: 014 Pixels @ 266,317*/ 0, 14, 0x04, 0x04, 0x00, 0x03, 0x00, 0x0F, 0x0E, 0x00, 0x03, 0x0B, 0x0F, 0x03, 0x03, 0x04,
  /* RLE: 059 Pixels @ 280,317*/ 59, 0x00,
  /* ABS: 005 Pixels @ 339,317*/ 0, 5, 0x03, 0x11, 0x08, 0x12, 0x0B,
  /* RLE: 004 Pixels @ 344,317*/ 4, 0x03,
  /* RLE: 001 Pixels @ 348,317*/ 1, 0x04,
  /* RLE: 051 Pixels @ 349,317*/ 51, 0x00,
  /* RLE: 064 Pixels @ 000,318*/ 64, 0x02,
  /* RLE: 001 Pixels @ 064,318*/ 1, 0x04,
  /* RLE: 006 Pixels @ 065,318*/ 6, 0x03,
  /* RLE: 001 Pixels @ 071,318*/ 1, 0x04,
  /* RLE: 046 Pixels @ 072,318*/ 46, 0x00,
  /* RLE: 001 Pixels @ 118,318*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 119,318*/ 8, 0x09,
  /* RLE: 001 Pixels @ 127,318*/ 1, 0x0C,
  /* RLE: 009 Pixels @ 128,318*/ 9, 0x00,
  /* ABS: 002 Pixels @ 137,318*/ 0, 2, 0x04, 0x04,
  /* RLE: 017 Pixels @ 139,318*/ 17, 0x03,
  /* ABS: 002 Pixels @ 156,318*/ 0, 2, 0x04, 0x04,
  /* RLE: 048 Pixels @ 158,318*/ 48, 0x00,
  /* RLE: 001 Pixels @ 206,318*/ 1, 0x04,
  /* RLE: 007 Pixels @ 207,318*/ 7, 0x03,
  /* ABS: 002 Pixels @ 214,318*/ 0, 2, 0x04, 0x04,
  /* RLE: 030 Pixels @ 216,318*/ 30, 0x00,
  /* ABS: 002 Pixels @ 246,318*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 248,318*/ 16, 0x03,
  /* ABS: 002 Pixels @ 264,318*/ 0, 2, 0x04, 0x04,
  /* RLE: 004 Pixels @ 266,318*/ 4, 0x00,
  /* RLE: 004 Pixels @ 270,318*/ 4, 0x03,
  /* ABS: 007 Pixels @ 274,318*/ 0, 7, 0x00, 0x12, 0x08, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 058 Pixels @ 281,318*/ 58, 0x00,
  /* ABS: 002 Pixels @ 339,318*/ 0, 2, 0x03, 0x00,
  /* RLE: 007 Pixels @ 341,318*/ 7, 0x03,
  /* RLE: 001 Pixels @ 348,318*/ 1, 0x04,
  /* RLE: 051 Pixels @ 349,318*/ 51, 0x00,
  /* RLE: 064 Pixels @ 000,319*/ 64, 0x02,
  /* RLE: 001 Pixels @ 064,319*/ 1, 0x04,
  /* RLE: 006 Pixels @ 065,319*/ 6, 0x03,
  /* RLE: 001 Pixels @ 071,319*/ 1, 0x04,
  /* RLE: 046 Pixels @ 072,319*/ 46, 0x00,
  /* RLE: 001 Pixels @ 118,319*/ 1, 0x0C,
  /* RLE: 008 Pixels @ 119,319*/ 8, 0x09,
  /* RLE: 001 Pixels @ 127,319*/ 1, 0x0C,
  /* RLE: 007 Pixels @ 128,319*/ 7, 0x00,
  /* ABS: 002 Pixels @ 135,319*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 137,319*/ 16, 0x03,
  /* ABS: 002 Pixels @ 153,319*/ 0, 2, 0x04, 0x04,
  /* RLE: 052 Pixels @ 155,319*/ 52, 0x00,
  /* RLE: 001 Pixels @ 207,319*/ 1, 0x04,
  /* RLE: 007 Pixels @ 208,319*/ 7, 0x03,
  /* ABS: 002 Pixels @ 215,319*/ 0, 2, 0x04, 0x04,
  /* RLE: 026 Pixels @ 217,319*/ 26, 0x00,
  /* ABS: 002 Pixels @ 243,319*/ 0, 2, 0x04, 0x04,
  /* RLE: 016 Pixels @ 245,319*/ 16, 0x03,
  /* ABS: 002 Pixels @ 261,319*/ 0, 2, 0x04, 0x04,
  /* RLE: 008 Pixels @ 263,319*/ 8, 0x00,
  /* ABS: 010 Pixels @ 271,319*/ 0, 10, 0x03, 0x0C, 0x11, 0x08, 0x0F, 0x04, 0x03, 0x03, 0x03, 0x04,
  /* RLE: 059 Pixels @ 281,319*/ 59, 0x00,
  /* ABS: 009 Pixels @ 340,319*/ 0, 9, 0x03, 0x03, 0x00, 0x00, 0x03, 0x11, 0x0E, 0x03, 0x03,
  /* RLE: 051 Pixels @ 349,319*/ 51, 0x00,


  0};  /* 23077 for 128000 pixels */

static GUI_CONST_STORAGE GUI_BITMAP _bmMap_400x320 = {
  400, /* XSize */
  320, /* YSize */
  400, /* BytesPerLine */
  GUI_COMPRESS_RLE8, /* BitsPerPixel */
  acMap_400x320,  /* Pointer to picture data (indices) */
  &PalMap_400x320  /* Pointer to palette */
 ,GUI_DRAW_RLE8
};

static int _Alpha_0     = 85;
static int _Alpha_1     = 0;

static WM_CALLBACK * _pcbClient;

static const GUI_WIDGET_CREATE_INFO _aDialogCreate[] = {
  { FRAMEWIN_CreateIndirect, "Transparent dialog", 0,                0,   0, 220, 100, FRAMEWIN_CF_MOVEABLE},
  { TEXT_CreateIndirect,     "Background:",        GUI_ID_TEXT0,     5,  10,  90,  20, TEXT_CF_LEFT },
  { TEXT_CreateIndirect,     "Title:",             GUI_ID_TEXT1,     5,  40,  90,  20, TEXT_CF_LEFT },
  { SLIDER_CreateIndirect,   NULL,                 GUI_ID_SLIDER0, 100,  10, 100,  20 },
  { SLIDER_CreateIndirect,   NULL,                 GUI_ID_SLIDER1, 100,  40, 100,  20 },
};

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/
/*********************************************************************
*
*       _cbWin
*
* Purpose:
*   Callback routine of map window. On receiving a timer message it
*   invalidates itself and restarts the timer.
*/
static void _cbWin(WM_MESSAGE * pMsg) {
  static int xAdd,  yAdd,  xPos,     yPos;
  int        xSize, ySize, xSizeBmp, ySizeBmp;
  WM_HWIN    hWin;

  hWin     = pMsg->hWin;
  xSize    = LCD_GetXSize();
  ySize    = LCD_GetYSize();
  xSizeBmp = _bmMap_400x320.XSize;
  ySizeBmp = _bmMap_400x320.YSize;
  switch (pMsg->MsgId) {
  case APP_INIT:
    if ((xAdd == 0) && (yAdd == 0)) {
      xAdd = 0;
      yAdd = 2;
    }
  case APP_TIMER:
    xSizeBmp = _bmMap_400x320.XSize;
    ySizeBmp = _bmMap_400x320.YSize;
    if (xAdd < 0) {
      if (xPos <= (xSize - xSizeBmp)) {
        xAdd = 0;
        yAdd = -2;
      }
    } else if (yAdd < 0) {
      if (yPos <= (ySize - ySizeBmp)) {
        xAdd = 2;
        yAdd = 0;
      }
    } else if (xAdd > 0) {
      if (xPos >= 0) {
        xAdd = 0;
        yAdd = 2;
      }
    } else if (yAdd > 0) {
      if (yPos >= 0) {
        xAdd = -2;
        yAdd = 0;
      }
    }
    if (xSize < xSizeBmp) {
      xPos += xAdd;
    }
    if (ySize < ySizeBmp) {
      yPos += yAdd;
    }
    WM_InvalidateWindow(hWin);
    break;
  case WM_PAINT:
    GUI_SetBkColor(GUI_WHITE);
    if (xSize > xSizeBmp) {
      xPos = (xSize - xSizeBmp) >> 1;
    } 
    if (ySize > ySizeBmp) {
      yPos = (ySize - ySizeBmp) >> 1;
    } 
    GUI_DrawBitmap(&_bmMap_400x320, xPos, yPos);
    if (xPos) {
      GUI_ClearRect(0, 0, xPos - 1, ySize - 1);
    }
    if ((xPos + xSizeBmp) < xSize) {
      GUI_ClearRect(xPos + xSizeBmp, 0, xSize - 1, ySize - 1);
    }
    if (yPos) {
      GUI_ClearRect(xPos, 0, xPos + xSizeBmp - 1, yPos - 1);
    }
    if ((yPos + ySizeBmp) < ySize) {
      GUI_ClearRect(xPos, yPos + ySizeBmp, xPos + xSizeBmp - 1, ySize - 1);
    }
    break;
  }
}

/*********************************************************************
*
*       _OnValueChanged
*/
static void _OnValueChanged(WM_HWIN hDlg, int Id) {
  WM_HWIN hItem;
  int Value;
  hItem = WM_GetDialogItem(hDlg, Id);
  Value = SLIDER_GetValue(hItem);
  switch (Id) {
  case GUI_ID_SLIDER0:
    _Alpha_0 = Value;
    WM_InvalidateWindow(hDlg);
    break;
  case GUI_ID_SLIDER1:
    _Alpha_1 = Value;
    WM_InvalidateWindow(WM_GetParent(hDlg));
    break;
  }
}

/*********************************************************************
*
*       _cbClient
*
* Purpose:
*   Callback routine of property dialog
*/
static void _cbClient(WM_MESSAGE * pMsg) {
  WM_HWIN hDlg, hItem;
  int NCode, Id;
  hDlg = pMsg->hWin;
  switch (pMsg->MsgId) {
  case WM_INIT_DIALOG:
    hItem = WM_GetDialogItem(hDlg, GUI_ID_SLIDER0);
    SLIDER_SetRange(hItem, 0, 255);
    SLIDER_SetValue(hItem, _Alpha_0);
    hItem = WM_GetDialogItem(hDlg, GUI_ID_SLIDER1);
    SLIDER_SetRange(hItem, 0, 255);
    SLIDER_SetValue(hItem, _Alpha_1);
    break;
  case WM_NOTIFY_PARENT:
    Id    = WM_GetId(pMsg->hWinSrc);      // Id of widget
    NCode = pMsg->Data.v;                 // Notification code
    switch (NCode) {
    case WM_NOTIFICATION_VALUE_CHANGED:   // Value has changed
      _OnValueChanged(hDlg, Id);
      break;
    }
    break;
  case WM_PAINT:
    GUI_SetAlpha(_Alpha_0);   // Set alpha value for drawing operations
    GUI_SetBkColor(0xAAAAAA); // Draw gray background...
    GUI_Clear();              // ...with alpha blending
    GUI_SetAlpha(0);          // Set alpha value to default
    return;
  }
  if (_pcbClient) {
    _pcbClient(pMsg);
  }
}

/*********************************************************************
*
*       _cbFrame
*
* Purpose:
*   Callback routine of frame window
*/
static void _cbFrame(WM_MESSAGE * pMsg) {
  switch (pMsg->MsgId) {
  case WM_PAINT:
    GUI_SetAlpha(_Alpha_1);
    break;
  }
  FRAMEWIN_Callback(pMsg);
  GUI_SetAlpha(0);
}

/*********************************************************************
*
*       _TransparentDialog
*/
static void _TransparentDialog(void) {
  WM_HWIN hFrame, hClient, hSlider0, hSlider1;
  int xSize, TimeNow, TimeNext, TimeStart, TimeUsed, Value;
  const GUI_FONT GUI_UNI_PTR * pFontOld;
  WM_CALLBACK * pCbOld;

  xSize = LCD_GetXSize();
  //
  // Set default properties
  //
  FRAMEWIN_SetDefaultFont(&GUI_FontComic18B_ASCII);
  FRAMEWIN_SetDefaultTextAlign(GUI_TA_CENTER);
  FRAMEWIN_SetDefaultBarColor(0, GUI_MAGENTA);
  FRAMEWIN_SetDefaultBarColor(1, GUI_MAGENTA);
  pFontOld = TEXT_GetDefaultFont();
  TEXT_SetDefaultFont(&GUI_FontComic18B_ASCII);
  TEXT_SetDefaultTextColor(GUI_BLUE);
  //
  // Create window with moving map
  //
  pCbOld = WM_SetCallback(WM_HBKWIN, _cbWin);
  WM_SendMessageNoPara(WM_HBKWIN, APP_INIT);
  //
  // Create dialog
  //
  hFrame = GUI_CreateDialogBox(_aDialogCreate, GUI_COUNTOF(_aDialogCreate), 0, 0, (xSize - 220) / 2, 55);
  WM_SetHasTrans(hFrame);           // Set transparency
  WM_SetCallback(hFrame, _cbFrame); // Overwrite callback
  //
  // Set client attributes
  //
  hClient    = WM_GetClientWindow(hFrame);         // Get handle of client window
  WM_SetHasTrans(hClient);                         // Set transparency
  _pcbClient = WM_SetCallback(hClient, _cbClient); // Overwrite callback
  WM_SendMessageNoPara(hClient, WM_INIT_DIALOG);   // Send WM_INIT_DIALOG
  //
  // Get slider handles
  //
  hSlider0 = WM_GetDialogItem(WM_GetClientWindow(hFrame), GUI_ID_SLIDER0);
  hSlider1 = WM_GetDialogItem(WM_GetClientWindow(hFrame), GUI_ID_SLIDER1);
  //
  // Loop
  //
  TimeStart= GUIDEMO_GetTime();
  TimeNext = TimeStart + PERIOD;
  do {
    GUI_Delay(1);
    TimeNow  = GUIDEMO_GetTime();
    TimeUsed = TimeNow - TimeStart;
    if (TimeNow >= TimeNext) {
      TimeNext = TimeNow + PERIOD;
      WM_SendMessageNoPara(WM_HBKWIN, APP_TIMER);
      TimeUsed = TimeUsed % (DURATION / 2);
      if (TimeUsed < (DURATION / 4)) {
        Value = (TimeUsed * 255 * 4) / DURATION;
      } else {
        Value = 255 - ((TimeUsed - (DURATION / 4)) * 255 * 4) / DURATION;
      }
      SLIDER_SetValue(hSlider0, Value);
      SLIDER_SetValue(hSlider1, 255 - Value);
    }
  } while (((GUIDEMO_GetTime() - TimeStart) < DURATION) && (GUIDEMO_CheckCancel() == 0));
  //
  // Free memory
  //
  WM_DeleteWindow(hFrame);
  //
  // Set default values
  //
  FRAMEWIN_SetDefaultFont(&GUI_Font8_1);
  FRAMEWIN_SetDefaultTextAlign(GUI_TA_LEFT);
  FRAMEWIN_SetDefaultBarColor(0, 0x404040);
  FRAMEWIN_SetDefaultBarColor(1, GUI_BLUE);
  TEXT_SetDefaultFont(pFontOld);
  TEXT_SetDefaultTextColor(GUI_BLACK);
  WM_SetCallback(WM_HBKWIN, pCbOld);
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/
/*********************************************************************
*
*       GUIDEMO_TransparentDialog
*/
void GUIDEMO_TransparentDialog(void) {
  GUIDEMO_ShowIntro("Transparent dialog",
                    "Uses alpha blending\n"
                    "for transparency effect");
  GUIDEMO_HideInfoWin();
  _TransparentDialog();
}

#else

void GUIDEMO_TransparentDialog(void) {}

#endif

/*************************** End of file ****************************/
