#ifndef __CALI_FILES_H
#define __CALI_FILES_H 					   

#include "mybsp.h"
#include "app_cali.h"

#define FILE_ID_STRING_CALI 	"fileType:CALI_WAVE"
#define FILE_ID_STRING_LIB_BOLT "fileType:LIB_BOLT"

uint8_t File_Cali_SavePointData(char *path, char *filename, uint8_t isSecret);
uint8_t File_Cali_ReadPointData_TOF(char *path, float *tof_L, float *tof_S);
uint8_t File_Cali_ReadResData_Coef(char* path, BOLT_COEF_T *coef);
uint8_t File_Cali_SaveResData(char* path, char* filename, CALI_POINT_T *pts[], U8 depth, U8 ptsmax, uint8_t isSecret);
uint8_t File_Cali_ReadCaliPtsTable(char* path, CALI_POINT_T *pts, uint8_t ptsnum, uint8_t ptsidx);

#endif


                                                  