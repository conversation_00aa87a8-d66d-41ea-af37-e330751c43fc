#include "netconn_tcp_server.h"
#include "lwip/opt.h"
#include "lwip_comm.h"
#include "led.h"
#include "lwip/lwip_sys.h"
#include "lwip/api.h"
#include "delay.h"


u8 tcp_server_recvbuf[TCP_SERVER_RX_BUFSIZE];	//TCP客户端接收数据缓冲区
u8 tcp_server_sendbuf[64];//="Apollo STM32F4/F7 NETCONN TCP Server send data\r\n";	
u16 tcp_server_send_len;

u8 tcp_server_flag;								//TCP服务器数据发送标志位

//TCP服务器任务
#define TCPSERVER_PRIO		6
//任务堆栈大小
#define TCPSERVER_STK_SIZE	300
//任务控制块
OS_TCB	TcpServerTaskTCB;
//任务堆栈
CPU_STK TCPSERVER_TASK_STK[TCPSERVER_STK_SIZE];

//tcp server send data
#define 	TCPTX_TASK_PRIO						6
#define 	TCPTX_STK_SIZE						64
OS_TCB 		TcpTxTaskTCB;
CPU_STK		TCPTX_TASK_STK[TCPTX_STK_SIZE];
void 		tcptx_task(void *pdata);  


static u8 tcp_server_conneted = 0;
struct netconn *conn_senddata;
uint8_t tcp_server_senddata(uint8_t *buf, uint16_t len)  {
	err_t err;
	
	char str[30];
//	LED1_Toggle;
	if(tcp_server_conneted) {
//		LED1(1);
		err = netconn_write(conn_senddata, buf, len, NETCONN_COPY); //发送tcp_server_sendbuf中的数据NETCONN_MORE);//
//		LED1(0);
		if(err != ERR_OK)
		{
			sprintf(str, "send failed!%d", err);
//			OLED_DispStringInLine(str, 1, GUI_TA_CENTER);
			return 2;
		}
//		OLED_DispStringInLine("send successed!", 1, GUI_TA_CENTER);
		return 0;
	}
	else {
//		OLED_DispStringInLine("send failed! no connect!", 1, GUI_TA_CENTER);
		return 1;
	}
}
static uint8_t *pbuf; 
static uint16_t buflen;
void tcp_server_start_senddata(uint8_t *buf, uint16_t len) {
	OS_ERR err;
	
	pbuf = buf;
	buflen = len;
//	tcp_server_flag |= LWIP_SEND_DATA;
	OSTaskSemPost(&TcpTxTaskTCB,OS_OPT_POST_NONE,&err);
}

void tcptx_task(void *arg) {
	CPU_SR_ALLOC();
	OS_ERR err;
	while(1) {
		OSTaskSemPend(0,OS_OPT_PEND_BLOCKING,0,&err);		//请求任务内建的信号量
		OSTaskSemSet(&TcpTxTaskTCB, 0, &err); //清除多余的信号量
		OS_CRITICAL_ENTER(); //关中断
		tcp_server_senddata(pbuf, buflen);
		OS_CRITICAL_EXIT();  //开中断
	}
}


//tcp服务器任务
//struct netconn tstconn, tstconn2;
//static struct netconn *pNetconn;
static void tcp_server_thread(void *arg)
{
	CPU_SR_ALLOC();
	
	u32 data_len = 0;
	struct pbuf *q;
	err_t err,recv_err;
	u8 addr[4];
	struct netconn *conn, *newconn;
	static ip_addr_t ipaddr;
	static u16_t 			port;
	
	LWIP_UNUSED_ARG(arg);

	conn=netconn_new(NETCONN_TCP);  //创建一个TCP链接
	netconn_bind(conn,IP_ADDR_ANY,TCP_SERVER_PORT);  //绑定端口 8088号端口
	netconn_listen(conn);  		//进入监听模式
	conn->recv_timeout = 5;  	//禁止阻塞线程 等待50ms
	while (1) 
	{
		err = netconn_accept(conn,&newconn);  //接收连接请求
		if(err==ERR_OK)newconn->recv_timeout = 5; //线程阻塞10ms

		tcp_server_conneted = 0;
//		tstconn = *newconn;
//		tstconn2 = *conn;
		if (err == ERR_OK)    //处理新连接的数据
		{ 
			struct netbuf *recvbuf;
			char str[52];
			
			conn_senddata = newconn;
			tcp_server_conneted = 1;
			//local ip port
			netconn_getaddr(newconn,&ipaddr,&port, 1); //获取远端IP地址和端口号
			addr[3] = (uint8_t)(ipaddr.addr >> 24); 
			addr[2] = (uint8_t)(ipaddr.addr >> 16);
			addr[1] = (uint8_t)(ipaddr.addr >> 8);
			addr[0] = (uint8_t)(ipaddr.addr);
//			printf("主机%d.%d.%d.%d连接上服务器,主机端口号为:%d\r\n",remot_addr[0], remot_addr[1],remot_addr[2],remot_addr[3],port);
			sprintf(str, "local  ip: %d.%d.%d.%d (%4d)", addr[0], addr[1], addr[2], addr[3], port);//remote ip port
//			OLED_DispStringInLine(str, 3, GUI_TA_LEFT);
			
			//remote ip port
			netconn_getaddr(newconn,&ipaddr,&port,0);
			addr[3] = (uint8_t)(ipaddr.addr >> 24); 
			addr[2] = (uint8_t)(ipaddr.addr >> 16);
			addr[1] = (uint8_t)(ipaddr.addr >> 8);
			addr[0] = (uint8_t)(ipaddr.addr);
			sprintf(str, "remote ip: %d.%d.%d.%d (%4d)", addr[0], addr[1], addr[2], addr[3], port);
//			OLED_DispStringInLine(str, 4, GUI_TA_LEFT);
			
			while(1)
			{
//				tstconn = *newconn;
//				tstconn2 = *conn;
//				LED1_Toggle;
				if((tcp_server_flag & LWIP_SEND_DATA) == LWIP_SEND_DATA) //有数据要发送
				{
					tcp_server_senddata(pbuf, buflen);
//					err = netconn_write(newconn, tcp_server_sendbuf, tcp_server_send_len, NETCONN_COPY); //发送tcp_server_sendbuf中的数据
//					if(err != ERR_OK)
//					{
//						printf("发送失败\r\n");
//					}
					tcp_server_flag &= ~LWIP_SEND_DATA;
				}
				
				if((recv_err = netconn_recv(newconn,&recvbuf)) == ERR_OK)  	//接收到数据
				{
					OS_CRITICAL_ENTER(); //关中断
					memset(tcp_server_recvbuf,0,TCP_SERVER_RX_BUFSIZE);  //数据接收缓冲区清零
					for(q=recvbuf->p;q!=NULL;q=q->next)  //遍历完整个pbuf链表
					{
						//判断要拷贝到TCP_SERVER_RX_BUFSIZE中的数据是否大于TCP_SERVER_RX_BUFSIZE的剩余空间，如果大于
						//的话就只拷贝TCP_SERVER_RX_BUFSIZE中剩余长度的数据，否则的话就拷贝所有的数据
						if(q->len > (TCP_SERVER_RX_BUFSIZE-data_len)) memcpy(tcp_server_recvbuf+data_len,q->payload,(TCP_SERVER_RX_BUFSIZE-data_len));//拷贝数据
						else memcpy(tcp_server_recvbuf+data_len,q->payload,q->len);
						data_len += q->len;  	
						if(data_len > TCP_SERVER_RX_BUFSIZE) break; //超出TCP客户端接收数组,跳出	
					}
					OS_CRITICAL_EXIT();  //开中断
//					memcpy(tcp_server_sendbuf, tcp_server_recvbuf, data_len);
//					tcp_server_send_len = data_len;
					Comm_RxOneFrameDone(tcp_server_recvbuf, data_len, COM_NET); //转存收到的数据
//					tcp_server_flag |= LWIP_SEND_DATA;
					data_len=0;  //复制完成后data_len要清零。	
//					printf("%s\r\n",tcp_server_recvbuf);  //通过串口发送接收到的数据
					netbuf_delete(recvbuf);
				}
				else if(recv_err == ERR_CLSD)  //关闭连接
				{
					netconn_close(newconn);
					netconn_delete(newconn);
//					printf("主机:%d.%d.%d.%d断开与服务器的连接\r\n",remot_addr[0], remot_addr[1],remot_addr[2],remot_addr[3]);
					break;
				}
			}
		}
	}
}

//注意只是提交任务
//uint8_t tcp_server_senddata(uint8_t *buf, uint16_t len) {
//	uint16_t i;
//	
//	if(len > (sizeof(tcp_server_sendbuf) / sizeof(tcp_server_sendbuf[0]))) {
//		return 1; // 超长
//	}
//	if((tcp_server_flag & LWIP_SEND_DATA) == LWIP_SEND_DATA) { //有数据要发送
//		return 2; // busy
//	}
//	for(i = 0; i < len; i++) {
//		tcp_server_sendbuf[i] = buf[i];
//	}
//	
//	tcp_server_send_len = len;
//	tcp_server_flag |= LWIP_SEND_DATA; //标记LWIP有数据要发送
//	
//	return 0; //OK
//}

//创建TCP服务器线程
//返回值:0 TCP服务器创建成功
//		其他 TCP服务器创建失败
u8 tcp_server_init(void)
{
	OS_ERR err;
	CPU_SR_ALLOC();
	
	OS_CRITICAL_ENTER();//进入临界区
	//创建TCP客户端任务
	OSTaskCreate((OS_TCB 	* )&TcpServerTaskTCB,		
				 (CPU_CHAR	* )"tcp_Server task", 		
                 (OS_TASK_PTR )tcp_server_thread, 			
                 (void		* )0,					
                 (OS_PRIO	  )TCPSERVER_PRIO,     
                 (CPU_STK   * )&TCPSERVER_TASK_STK[0],	
                 (CPU_STK_SIZE)TCPSERVER_STK_SIZE/10,	
                 (CPU_STK_SIZE)TCPSERVER_STK_SIZE,		
                 (OS_MSG_QTY  )0,					
                 (OS_TICK	  )0,					
                 (void   	* )0,					
                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
                 (OS_ERR 	* )&err);
	
//	OSTaskCreate((OS_TCB*     )&TcpTxTaskTCB,		
//				 (CPU_CHAR*   )"Emit task", 		
//                 (OS_TASK_PTR )tcptx_task, 			
//                 (void*       )0,					
//                 (OS_PRIO	  )TCPTX_TASK_PRIO,     
//                 (CPU_STK*    )&TCPTX_TASK_STK[0],	
//                 (CPU_STK_SIZE)TCPTX_STK_SIZE/10,	
//                 (CPU_STK_SIZE)TCPTX_STK_SIZE,		
//                 (OS_MSG_QTY  )0,					
//                 (OS_TICK	  )0,  					
//                 (void*       )0,					
//                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
//                 (OS_ERR*     )&err);
	OS_CRITICAL_EXIT();	//退出临界区
	return err;				 
}



