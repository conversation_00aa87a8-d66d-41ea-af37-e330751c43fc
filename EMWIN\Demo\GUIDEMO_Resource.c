/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : GUIDEMO_Resource.c
Purpose     : Contains fonts and bitmaps used in the demo.
---------------------------END-OF-HEADER------------------------------
*/

/**
  ******************************************************************************
  * @file    GUIDEMO_Resource.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Contains fonts and bitmaps used in the demo.
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


#include "GUIDEMO.h"

#ifndef GUI_CONST_STORAGE
  #define GUI_CONST_STORAGE const
#endif

/*********************************************************************
*                                                                    *
*       Fonts                                                        *
*                                                                    *
**********************************************************************
*/
/*********************************************************************
*                                                                    *
*       GUI_FontD6x8                                                 *
*                                                                    *
*  Used in GUIDEMO.c (PROGBAR)                                       *
*                                                                    *
**********************************************************************
*/
static GUI_CONST_STORAGE unsigned char acFontD6x8[16][8] = {
  {
    _XXX____,
    X___X___,
    X___X___,
    X___X___,
    X___X___,
    X___X___,
    X___X___,
    _XXX____,
  },{
    __X_____,
    _XX_____,
    __X_____,
    __X_____,
    __X_____,
    __X_____,
    __X_____,
    _XXX____,
  },{
    _XXX____,
    X___X___,
    ____X___,
    ___X____,
    __X_____,
    _X______,
    X_______,
    XXXXX___,
  },{
    _XXX____,
    X___X___,
    ____X___,
    ___X____,
    ___X____,
    ____X___,
    X___X___,
    _XXX____,
  },{
    ___X____,
    __XX____,
    _X_X____,
    X__X____,
    XXXXX___,
    ___X____,
    ___X____,
    ___X____,
  },{
    XXXXX___,
    X_______,
    X_______,
    XXXX____,
    ____X___,
    ____X___,
    X___X___,
    _XXX____,
  },{
    __XX____,
    _X______,
    X_______,
    XXXX____,
    X___X___,
    X___X___,
    X___X___,
    _XXX____,
  },{
    XXXXX___,
    ____X___,
    ____X___,
    ___X____,
    __X_____,
    _X______,
    _X______,
    _X______,
  },{
    _XXX____,
    X___X___,
    X___X___,
    _XXX____,
    X___X___,
    X___X___,
    X___X___,
    _XXX____,
  },{
    _XXX____,
    X___X___,
    X___X___,
    _XXXX___,
    ____X___,
    ____X___,
    ___X____,
    _XX_____,
  },{
    ________,
    ________,
    __X_____,
    __X_____,
    XXXXX___,
    __X_____,
    __X_____,
    ________,
  },{
    ________,
    ________,
    ________,
    ________,
    XXXXX___,
    ________,
    ________,
    ________,
  },{
    ________,
    ________,
    ________,
    ________,
    ________,
    ________,
    ________,
    ________,
  },{
    ________,
    ________,
    ________,
    ________,
    ________,
    ________,
    _XX_____,
    _XX_____,
  },{
    ________,
    ________,
    _XX_____,
    _XX_____,
    ________,
    _XX_____,
    _XX_____,
    ________
  },{
    ________,
    _XX___X_,
    _XX__X__,
    ____X___,
    ___X____,
    __X__XX_,
    _X___XX_,
    ________}

};

static GUI_CONST_STORAGE GUI_CHARINFO GUI_FontD6x8_CharInfo[16] = {
   {  6,  6,  1, acFontD6x8[12] } /* code 0020 ' ' */
  ,{  6,  6,  1, acFontD6x8[15] } /* code 0025 '%' */
  ,{  6,  6,  1, acFontD6x8[10] } /* code 002B '+' */
  ,{  6,  6,  1, acFontD6x8[11] } /* code 002D '-' */
  ,{  6,  6,  1, acFontD6x8[13] } /* code 002E '.' */
  ,{  6,  6,  1, acFontD6x8[0]  } /* code 0030 '0' */
  ,{  6,  6,  1, acFontD6x8[1]  } /* code 0031 '1' */
  ,{  6,  6,  1, acFontD6x8[2]  } /* code 0032 '2' */
  ,{  6,  6,  1, acFontD6x8[3]  } /* code 0033 '3' */
  ,{  6,  6,  1, acFontD6x8[4]  } /* code 0034 '4' */
  ,{  6,  6,  1, acFontD6x8[5]  } /* code 0035 '5' */
  ,{  6,  6,  1, acFontD6x8[6]  } /* code 0036 '6' */
  ,{  6,  6,  1, acFontD6x8[7]  } /* code 0037 '7' */
  ,{  6,  6,  1, acFontD6x8[8]  } /* code 0038 '8' */
  ,{  6,  6,  1, acFontD6x8[9]  } /* code 0039 '9' */
  ,{  6,  6,  1, acFontD6x8[14] } /* code 003A ':' */
};

static GUI_CONST_STORAGE GUI_FONT_PROP GUI_FontD6x8_Prop5 = {
   0x0030 /* first character */
  ,0x003A /* last character  */
  ,&GUI_FontD6x8_CharInfo[  5] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP*)0 /* pointer to next GUI_FONT_PROP */
};

static GUI_CONST_STORAGE GUI_FONT_PROP GUI_FontD6x8_Prop4 = {
   0x002D /* first character */
  ,0x002E /* last character  */
  ,&GUI_FontD6x8_CharInfo[  3] /* address of first character */
  ,&GUI_FontD6x8_Prop5 /* pointer to next GUI_FONT_PROP */
};

static GUI_CONST_STORAGE GUI_FONT_PROP GUI_FontD6x8_Prop3 = {
   0x002B /* first character */
  ,0x002B /* last character  */
  ,&GUI_FontD6x8_CharInfo[  2] /* address of first character */
  ,&GUI_FontD6x8_Prop4 /* pointer to next GUI_FONT_PROP */
};

static GUI_CONST_STORAGE GUI_FONT_PROP GUI_FontD6x8_Prop2 = {
   0x0025 /* first character */
  ,0x0025 /* last character  */
  ,&GUI_FontD6x8_CharInfo[  1] /* address of first character */
  ,&GUI_FontD6x8_Prop3 /* pointer to next GUI_FONT_PROP */
};

static GUI_CONST_STORAGE GUI_FONT_PROP GUI_FontD6x8_Prop1 = {
   0x0020 /* first character */
  ,0x0020 /* last character  */
  ,&GUI_FontD6x8_CharInfo[  0] /* address of first character */
  ,&GUI_FontD6x8_Prop2 /* pointer to next GUI_FONT_PROP */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontD6x8 = {
   GUI_FONTTYPE_PROP /* type of font    */
  ,8 /* height of font  */
  ,8 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontD6x8_Prop1}
  ,8 /* Baseline */
  ,0 /* LHeight */
  ,8 /* CHeight */
};

/*********************************************************************
*                                                                    *
*       GUI_FontRounded16                                            *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO_Automotive.c                                            *
*  - GUIDEMO_Cursor.c                                                *
*  - GUIDEMO_Speedometer.c                                           *
*                                                                    *
**********************************************************************
*/
GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0020[  1] = { /* code 0020, SPACE */
  0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0021[ 22] = { /* code 0021, EXCLAMATION MARK */
  0x56, 0x00,
  0xFF, 0x10,
  0xFF, 0x20,
  0xEF, 0x10,
  0xCE, 0x00,
  0xAC, 0x00,
  0x89, 0x00,
  0x01, 0x00,
  0xCD, 0x10,
  0xCE, 0x10,
  0x01, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0022[ 15] = { /* code 0022, QUOTATION MARK */
  0x36, 0x07, 0x30,
  0x9F, 0x3F, 0x90,
  0x9F, 0x4F, 0x90,
  0x9F, 0x3F, 0x90,
  0x24, 0x05, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0023[ 44] = { /* code 0023, NUMBER SIGN */
  0x00, 0x03, 0x02, 0x10,
  0x00, 0x5E, 0x0D, 0x60,
  0x00, 0x8C, 0x0F, 0x40,
  0x07, 0xDE, 0xAF, 0xA1,
  0x09, 0xFD, 0xDF, 0xB2,
  0x00, 0xE6, 0x7D, 0x00,
  0x3B, 0xFC, 0xDE, 0x90,
  0x19, 0xF8, 0xDC, 0x50,
  0x05, 0xF0, 0xD7, 0x00,
  0x07, 0xC0, 0xF4, 0x00,
  0x00, 0x10, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0024[ 48] = { /* code 0024, DOLLAR SIGN */
  0x00, 0x17, 0x71, 0x00,
  0x08, 0xFF, 0xFF, 0x91,
  0x4F, 0x87, 0x79, 0xF7,
  0x7F, 0x36, 0x60, 0x62,
  0x3F, 0xEC, 0x92, 0x00,
  0x05, 0xCF, 0xFF, 0xB1,
  0x00, 0x06, 0x9B, 0xF7,
  0x9E, 0x06, 0x60, 0xF9,
  0x7F, 0x97, 0x78, 0xF6,
  0x09, 0xFF, 0xFF, 0x80,
  0x00, 0x17, 0x70, 0x00,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0025[ 66] = { /* code 0025, PERCENT SIGN */
  0x02, 0x30, 0x00, 0x04, 0x30, 0x00,
  0x6F, 0xDC, 0x00, 0x1E, 0x30, 0x00,
  0xD8, 0x2F, 0x50, 0x79, 0x00, 0x00,
  0xF7, 0x0F, 0x61, 0xE2, 0x00, 0x00,
  0xBB, 0x7F, 0x28, 0x90, 0x00, 0x00,
  0x19, 0xB5, 0x2E, 0x16, 0xBA, 0x20,
  0x00, 0x00, 0x98, 0x3F, 0x6B, 0xB0,
  0x00, 0x02, 0xE1, 0x6F, 0x07, 0xF0,
  0x00, 0x0A, 0x70, 0x4F, 0x28, 0xD0,
  0x00, 0x3E, 0x00, 0x0B, 0xEF, 0x60,
  0x00, 0x24, 0x00, 0x00, 0x21, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0026[ 55] = { /* code 0026, AMPERSAND */
  0x00, 0x05, 0x74, 0x00, 0x00,
  0x00, 0xCF, 0xEF, 0x80, 0x00,
  0x03, 0xF8, 0x0B, 0xF0, 0x00,
  0x02, 0xFB, 0x2D, 0xD0, 0x00,
  0x00, 0xAF, 0xFE, 0x40, 0x00,
  0x08, 0xFF, 0xFB, 0x07, 0x50,
  0x3F, 0xD2, 0xBF, 0xBF, 0x90,
  0x6F, 0x70, 0x1D, 0xFE, 0x20,
  0x3F, 0xD6, 0x7E, 0xFF, 0x50,
  0x08, 0xFF, 0xFB, 0x4E, 0x90,
  0x00, 0x12, 0x10, 0x01, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0027[  5] = { /* code 0027, APOSTROPHE */
  0x45,
  0xBD,
  0xBD,
  0xBD,
  0x33
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0028[ 26] = { /* code 0028, LEFT PARENTHESIS */
  0x00, 0x37,
  0x00, 0xCB,
  0x04, 0xF7,
  0x09, 0xF2,
  0x0D, 0xD0,
  0x1F, 0xB0,
  0x2F, 0xB0,
  0x1F, 0xB0,
  0x0D, 0xD0,
  0x09, 0xF2,
  0x04, 0xF6,
  0x00, 0xCB,
  0x00, 0x37
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0029[ 26] = { /* code 0029, RIGHT PARENTHESIS */
  0x36, 0x00,
  0x6F, 0x20,
  0x2F, 0x90,
  0x0C, 0xE0,
  0x08, 0xF3,
  0x06, 0xF6,
  0x06, 0xF7,
  0x06, 0xF6,
  0x08, 0xF3,
  0x0C, 0xE0,
  0x2F, 0x90,
  0x6F, 0x30,
  0x47, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_002A[ 18] = { /* code 002A, ASTERISK */
  0x00, 0x33, 0x00,
  0x12, 0x79, 0x21,
  0x4F, 0xEE, 0xF5,
  0x02, 0xFE, 0x30,
  0x0A, 0x98, 0xA0,
  0x01, 0x00, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_002B[ 32] = { /* code 002B, PLUS SIGN */
  0x00, 0x03, 0x20, 0x00,
  0x00, 0x0D, 0xA0, 0x00,
  0x00, 0x0D, 0xB0, 0x00,
  0x37, 0x7E, 0xD7, 0x72,
  0xAF, 0xFF, 0xFF, 0xF7,
  0x02, 0x2D, 0xC2, 0x20,
  0x00, 0x0D, 0xB0, 0x00,
  0x00, 0x0B, 0x80, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_002C[  8] = { /* code 002C, COMMA */
  0x0D, 0xD0,
  0x0E, 0xF3,
  0x01, 0xD1,
  0x0B, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_002D[  9] = { /* code 002D, HYPHEN-MINUS */
  0x17, 0x77, 0x30,
  0x7F, 0xFF, 0xB0,
  0x04, 0x65, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_002E[  6] = { /* code 002E, FULL STOP */
  0x0D, 0xD0,
  0x0D, 0xD0,
  0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_002F[ 33] = { /* code 002F, SOLIDUS */
  0x00, 0x04, 0x50,
  0x00, 0x0D, 0x90,
  0x00, 0x4F, 0x30,
  0x00, 0xAD, 0x00,
  0x01, 0xF7, 0x00,
  0x06, 0xF2, 0x00,
  0x0C, 0xB0, 0x00,
  0x3F, 0x50, 0x00,
  0x8E, 0x00, 0x00,
  0xD9, 0x00, 0x00,
  0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0030[ 44] = { /* code 0030, DIGIT ZERO */
  0x00, 0x03, 0x30, 0x00,
  0x03, 0xDF, 0xFD, 0x30,
  0x0D, 0xF7, 0x7F, 0xD0,
  0x4F, 0xA0, 0x0B, 0xF4,
  0x7F, 0x70, 0x07, 0xF6,
  0x7F, 0x60, 0x07, 0xF7,
  0x6F, 0x70, 0x07, 0xF6,
  0x4F, 0xA0, 0x0B, 0xF3,
  0x0D, 0xF7, 0x8F, 0xC0,
  0x02, 0xDF, 0xFC, 0x20,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0031[ 33] = { /* code 0031, DIGIT ONE */
  0x00, 0x00, 0x30,
  0x00, 0x07, 0xF3,
  0x04, 0x8F, 0xF4,
  0x1F, 0xFF, 0xF4,
  0x01, 0x2C, 0xF4,
  0x00, 0x0B, 0xF4,
  0x00, 0x0B, 0xF4,
  0x00, 0x0B, 0xF4,
  0x00, 0x0B, 0xF4,
  0x00, 0x0A, 0xF2,
  0x00, 0x00, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0032[ 40] = { /* code 0032, DIGIT TWO */
  0x00, 0x13, 0x30, 0x00,
  0x06, 0xEF, 0xFE, 0x50,
  0x4F, 0xD6, 0x7F, 0xF1,
  0x7F, 0x50, 0x0B, 0xF4,
  0x25, 0x00, 0x1E, 0xF1,
  0x00, 0x05, 0xEF, 0x70,
  0x01, 0xBF, 0xC4, 0x00,
  0x1D, 0xF7, 0x00, 0x00,
  0x7F, 0xD9, 0x99, 0x92,
  0x6F, 0xFF, 0xFF, 0xF3
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0033[ 44] = { /* code 0033, DIGIT THREE */
  0x00, 0x13, 0x30, 0x00,
  0x08, 0xFF, 0xFD, 0x20,
  0x4F, 0xC6, 0x8F, 0xB0,
  0x3B, 0x20, 0x0F, 0xC0,
  0x00, 0x07, 0xAF, 0x60,
  0x00, 0x1E, 0xFF, 0x70,
  0x00, 0x00, 0x3F, 0xF1,
  0x7E, 0x20, 0x0E, 0xF1,
  0x7F, 0xC6, 0xAF, 0xC0,
  0x09, 0xFF, 0xFB, 0x20,
  0x00, 0x12, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0034[ 44] = { /* code 0034, DIGIT FOUR */
  0x00, 0x00, 0x13, 0x00,
  0x00, 0x01, 0xCF, 0x30,
  0x00, 0x0A, 0xFF, 0x40,
  0x00, 0x7E, 0xBF, 0x40,
  0x03, 0xF5, 0x9F, 0x40,
  0x1E, 0x90, 0x9F, 0x40,
  0xAF, 0xA9, 0xDF, 0xB4,
  0x9D, 0xDD, 0xEF, 0xE7,
  0x00, 0x00, 0x9F, 0x40,
  0x00, 0x00, 0x8F, 0x30,
  0x00, 0x00, 0x02, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0035[ 44] = { /* code 0035, DIGIT FIVE */
  0x00, 0x22, 0x22, 0x00,
  0x0C, 0xFF, 0xFF, 0x90,
  0x1F, 0xC9, 0x99, 0x40,
  0x3F, 0x50, 0x00, 0x00,
  0x6F, 0xCF, 0xFB, 0x10,
  0x5F, 0xA6, 0xBF, 0xB0,
  0x00, 0x00, 0x0E, 0xF1,
  0x37, 0x00, 0x0E, 0xF0,
  0x8F, 0xB6, 0xAF, 0xB0,
  0x1B, 0xFF, 0xFA, 0x10,
  0x00, 0x12, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0036[ 44] = { /* code 0036, DIGIT SIX */
  0x00, 0x03, 0x41, 0x00,
  0x02, 0xCF, 0xFF, 0x80,
  0x0C, 0xF5, 0x3D, 0xF0,
  0x3F, 0x90, 0x01, 0x30,
  0x6F, 0x9B, 0xDB, 0x40,
  0x7F, 0xF9, 0x8E, 0xE2,
  0x7F, 0x90, 0x08, 0xF6,
  0x5F, 0x90, 0x08, 0xF6,
  0x0D, 0xE7, 0x6E, 0xF2,
  0x03, 0xDF, 0xFE, 0x50,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0037[ 44] = { /* code 0037, DIGIT SEVEN */
  0x02, 0x22, 0x22, 0x10,
  0xCF, 0xFF, 0xFF, 0xF1,
  0x59, 0x99, 0xAF, 0xD0,
  0x00, 0x00, 0xAE, 0x20,
  0x00, 0x05, 0xF6, 0x00,
  0x00, 0x0D, 0xD0, 0x00,
  0x00, 0x6F, 0x70, 0x00,
  0x00, 0xCF, 0x20, 0x00,
  0x02, 0xFD, 0x00, 0x00,
  0x03, 0xF9, 0x00, 0x00,
  0x00, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0038[ 44] = { /* code 0038, DIGIT EIGHT */
  0x00, 0x13, 0x31, 0x00,
  0x06, 0xFF, 0xFE, 0x60,
  0x2F, 0xD3, 0x4D, 0xF1,
  0x3F, 0xA0, 0x0A, 0xF2,
  0x0B, 0xE8, 0x8F, 0xB0,
  0x09, 0xFD, 0xDF, 0x90,
  0x6F, 0xA0, 0x0B, 0xF5,
  0x7F, 0x60, 0x08, 0xF7,
  0x3F, 0xE6, 0x6E, 0xF3,
  0x06, 0xEF, 0xFE, 0x60,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0039[ 44] = { /* code 0039, DIGIT NINE */
  0x00, 0x13, 0x30, 0x00,
  0x06, 0xFF, 0xFD, 0x30,
  0x3F, 0xD5, 0x5E, 0xD0,
  0x7F, 0x70, 0x0A, 0xF4,
  0x7F, 0x80, 0x0B, 0xF7,
  0x2E, 0xF9, 0xAF, 0xF7,
  0x03, 0xBD, 0xAA, 0xF5,
  0x04, 0x10, 0x0A, 0xF2,
  0x0F, 0xD4, 0x7F, 0xB0,
  0x07, 0xFF, 0xFB, 0x10,
  0x00, 0x12, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_003A[ 16] = { /* code 003A, COLON */
  0x09, 0x90,
  0x1F, 0xF0,
  0x03, 0x30,
  0x00, 0x00,
  0x00, 0x00,
  0x0D, 0xD0,
  0x0D, 0xD0,
  0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_003B[ 18] = { /* code 003B, SEMICOLON */
  0x09, 0x90,
  0x1F, 0xF0,
  0x03, 0x30,
  0x00, 0x00,
  0x00, 0x00,
  0x0D, 0xD0,
  0x0E, 0xF3,
  0x01, 0xD1,
  0x0B, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_003C[ 28] = { /* code 003C, LESS-THAN SIGN */
  0x00, 0x00, 0x28, 0xF6,
  0x00, 0x3A, 0xFF, 0xA2,
  0x3B, 0xFE, 0x82, 0x00,
  0x9F, 0xF5, 0x00, 0x00,
  0x06, 0xDF, 0xD7, 0x10,
  0x00, 0x05, 0xCF, 0xE4,
  0x00, 0x00, 0x03, 0x94
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_003D[ 20] = { /* code 003D, EQUALS SIGN */
  0xAF, 0xFF, 0xFF, 0xF7,
  0x49, 0x99, 0x99, 0x93,
  0x00, 0x00, 0x00, 0x00,
  0x8D, 0xDD, 0xDD, 0xD6,
  0x59, 0x99, 0x99, 0x93
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_003E[ 28] = { /* code 003E, GREATER-THAN SIGN */
  0x9E, 0x71, 0x00, 0x00,
  0x3B, 0xFF, 0x82, 0x00,
  0x00, 0x39, 0xFF, 0xA2,
  0x00, 0x00, 0x8F, 0xF7,
  0x02, 0x8E, 0xFB, 0x50,
  0x7F, 0xFA, 0x30, 0x00,
  0x58, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_003F[ 44] = { /* code 003F, QUESTION MARK */
  0x00, 0x57, 0x72, 0x00,
  0x0B, 0xFF, 0xFF, 0x50,
  0x5F, 0x90, 0x4F, 0xC0,
  0x39, 0x10, 0x3F, 0xD0,
  0x00, 0x03, 0xEF, 0x50,
  0x00, 0x2E, 0xE4, 0x00,
  0x00, 0x4F, 0x50, 0x00,
  0x00, 0x01, 0x00, 0x00,
  0x00, 0x5F, 0x60, 0x00,
  0x00, 0x5F, 0x70, 0x00,
  0x00, 0x01, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0040[ 55] = { /* code 0040, COMMERCIAL AT */
  0x00, 0x03, 0x79, 0x73, 0x00,
  0x01, 0xBF, 0xB9, 0xBF, 0x90,
  0x0B, 0xE3, 0x13, 0x03, 0xE7,
  0x3F, 0x46, 0xFF, 0xCE, 0x6E,
  0x8E, 0x1F, 0x94, 0xDC, 0x2F,
  0x9C, 0x5F, 0x20, 0xAA, 0x3D,
  0x7E, 0x4F, 0x75, 0xE9, 0xB7,
  0x2F, 0x7B, 0xFD, 0xFF, 0x80,
  0x06, 0xF7, 0x30, 0x27, 0x80,
  0x00, 0x5D, 0xFF, 0xFC, 0x30,
  0x00, 0x00, 0x24, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0041[ 55] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x05, 0x73, 0x00, 0x00,
  0x00, 0x1F, 0xFD, 0x00, 0x00,
  0x00, 0x7F, 0xFF, 0x30, 0x00,
  0x00, 0xCF, 0x7F, 0x90, 0x00,
  0x03, 0xFB, 0x1F, 0xE0, 0x00,
  0x09, 0xF7, 0x0B, 0xF4, 0x00,
  0x0E, 0xF9, 0x7B, 0xFA, 0x00,
  0x5F, 0xFF, 0xFF, 0xFF, 0x10,
  0xBF, 0x70, 0x00, 0xCF, 0x60,
  0xCF, 0x10, 0x00, 0x6F, 0x70,
  0x11, 0x00, 0x00, 0x02, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0042[ 40] = { /* code 0042, LATIN CAPITAL LETTER B */
  0x36, 0x66, 0x63, 0x00,
  0xFF, 0xFF, 0xFF, 0xC1,
  0xFF, 0x54, 0x5E, 0xF6,
  0xFF, 0x20, 0x0A, 0xF6,
  0xFF, 0x87, 0x9F, 0xC1,
  0xFF, 0xDD, 0xEF, 0xD3,
  0xFF, 0x20, 0x08, 0xFB,
  0xFF, 0x20, 0x06, 0xFB,
  0xFF, 0xA9, 0xAF, 0xF7,
  0xCF, 0xFF, 0xFD, 0x80
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0043[ 55] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x04, 0x89, 0x61, 0x00,
  0x01, 0xBF, 0xFF, 0xFE, 0x50,
  0x09, 0xFD, 0x42, 0x8F, 0xE0,
  0x1F, 0xF3, 0x00, 0x09, 0xA0,
  0x3F, 0xE0, 0x00, 0x00, 0x00,
  0x4F, 0xD0, 0x00, 0x00, 0x00,
  0x3F, 0xE0, 0x00, 0x02, 0x20,
  0x0E, 0xF6, 0x00, 0x1E, 0xF0,
  0x07, 0xFF, 0xA8, 0xDF, 0xB0,
  0x00, 0x7E, 0xFF, 0xFA, 0x10,
  0x00, 0x00, 0x33, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0044[ 50] = { /* code 0044, LATIN CAPITAL LETTER D */
  0x36, 0x66, 0x52, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xB1, 0x00,
  0xFF, 0x76, 0x7D, 0xFA, 0x00,
  0xFF, 0x20, 0x03, 0xFF, 0x10,
  0xFF, 0x20, 0x00, 0xEF, 0x40,
  0xFF, 0x20, 0x00, 0xDF, 0x40,
  0xFF, 0x20, 0x00, 0xFF, 0x30,
  0xFF, 0x20, 0x07, 0xFE, 0x00,
  0xFF, 0xCB, 0xCF, 0xF5, 0x00,
  0xCF, 0xFF, 0xFB, 0x40, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0045[ 40] = { /* code 0045, LATIN CAPITAL LETTER E */
  0x26, 0x66, 0x66, 0x51,
  0xEF, 0xFF, 0xFF, 0xF5,
  0xFF, 0x76, 0x66, 0x50,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0xA9, 0x99, 0x50,
  0xFF, 0xFF, 0xFF, 0xA0,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0xCB, 0xBB, 0xB3,
  0xBF, 0xFF, 0xFF, 0xF5
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0046[ 44] = { /* code 0046, LATIN CAPITAL LETTER F */
  0x26, 0x66, 0x66, 0x40,
  0xEF, 0xFF, 0xFF, 0xF0,
  0xFF, 0x76, 0x66, 0x40,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x87, 0x77, 0x10,
  0xFF, 0xFF, 0xFF, 0x50,
  0xFF, 0x32, 0x22, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xDF, 0x10, 0x00, 0x00,
  0x11, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0047[ 55] = { /* code 0047, LATIN CAPITAL LETTER G */
  0x00, 0x03, 0x79, 0x73, 0x00,
  0x01, 0xBF, 0xFF, 0xFF, 0x80,
  0x09, 0xFD, 0x52, 0x5E, 0xF2,
  0x1F, 0xF3, 0x00, 0x04, 0x80,
  0x4F, 0xE0, 0x00, 0x00, 0x00,
  0x4F, 0xD0, 0x08, 0xFF, 0xF8,
  0x3F, 0xE0, 0x04, 0x9B, 0xF9,
  0x0E, 0xF6, 0x00, 0x09, 0xF9,
  0x07, 0xFF, 0x96, 0xAF, 0xF9,
  0x00, 0x7E, 0xFF, 0xF7, 0xC9,
  0x00, 0x00, 0x33, 0x10, 0x11
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0048[ 55] = { /* code 0048, LATIN CAPITAL LETTER H */
  0x56, 0x00, 0x00, 0x56, 0x00,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0xDD, 0xDD, 0xFF, 0x20,
  0xFF, 0xFF, 0xFF, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xDE, 0x10, 0x00, 0xDE, 0x10,
  0x11, 0x00, 0x00, 0x11, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0049[ 22] = { /* code 0049, LATIN CAPITAL LETTER I */
  0x56, 0x00,
  0xFF, 0x20,
  0xFF, 0x20,
  0xFF, 0x20,
  0xFF, 0x20,
  0xFF, 0x20,
  0xFF, 0x20,
  0xFF, 0x20,
  0xFF, 0x20,
  0xDE, 0x10,
  0x11, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_004A[ 44] = { /* code 004A, LATIN CAPITAL LETTER J */
  0x00, 0x00, 0x27, 0x10,
  0x00, 0x00, 0x9F, 0x70,
  0x00, 0x00, 0x9F, 0x70,
  0x00, 0x00, 0x9F, 0x70,
  0x00, 0x00, 0x9F, 0x70,
  0x00, 0x00, 0x9F, 0x70,
  0x4A, 0x00, 0x9F, 0x70,
  0xBF, 0x30, 0xAF, 0x70,
  0x9F, 0xB7, 0xFF, 0x30,
  0x1C, 0xFF, 0xF8, 0x00,
  0x00, 0x33, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_004B[ 44] = { /* code 004B, LATIN CAPITAL LETTER K */
  0x56, 0x00, 0x02, 0x71,
  0xFF, 0x20, 0x2E, 0xF4,
  0xFF, 0x21, 0xDF, 0x80,
  0xFF, 0x3C, 0xF9, 0x00,
  0xFF, 0xDF, 0xE1, 0x00,
  0xFF, 0xFF, 0xF8, 0x00,
  0xFF, 0x84, 0xFF, 0x30,
  0xFF, 0x20, 0x9F, 0xD0,
  0xFF, 0x20, 0x1D, 0xF8,
  0xDE, 0x10, 0x05, 0xFA,
  0x11, 0x00, 0x00, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_004C[ 40] = { /* code 004C, LATIN CAPITAL LETTER L */
  0x56, 0x00, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0xCB, 0xBB, 0x70,
  0xCF, 0xFF, 0xFF, 0x90
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_004D[ 55] = { /* code 004D, LATIN CAPITAL LETTER M */
  0x67, 0x40, 0x00, 0x07, 0x72,
  0xFF, 0xE0, 0x00, 0x5F, 0xF9,
  0xFF, 0xF4, 0x00, 0xAF, 0xF9,
  0xFE, 0xF8, 0x00, 0xED, 0xF9,
  0xFD, 0xBD, 0x04, 0xF8, 0xF9,
  0xFD, 0x7F, 0x39, 0xE4, 0xF9,
  0xFD, 0x2F, 0x7D, 0xA4, 0xF9,
  0xFD, 0x0C, 0xEF, 0x54, 0xF9,
  0xFD, 0x07, 0xFF, 0x14, 0xF9,
  0xEB, 0x02, 0xFA, 0x03, 0xF8,
  0x11, 0x00, 0x10, 0x00, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_004E[ 55] = { /* code 004E, LATIN CAPITAL LETTER N */
  0x57, 0x10, 0x00, 0x57, 0x00,
  0xFF, 0xA0, 0x00, 0xDF, 0x20,
  0xFF, 0xF4, 0x00, 0xDF, 0x20,
  0xFF, 0xFD, 0x00, 0xDF, 0x20,
  0xFF, 0x7F, 0x70, 0xDF, 0x20,
  0xFF, 0x0D, 0xF2, 0xDF, 0x20,
  0xFF, 0x03, 0xFB, 0xDF, 0x20,
  0xFF, 0x00, 0x9F, 0xFF, 0x20,
  0xFF, 0x00, 0x1D, 0xFF, 0x20,
  0xEE, 0x00, 0x05, 0xFE, 0x10,
  0x11, 0x00, 0x00, 0x11, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_004F[ 55] = { /* code 004F, LATIN CAPITAL LETTER O */
  0x00, 0x04, 0x89, 0x72, 0x00,
  0x01, 0xCF, 0xFF, 0xFF, 0x80,
  0x0B, 0xFC, 0x42, 0x6E, 0xF5,
  0x3F, 0xF1, 0x00, 0x07, 0xFB,
  0x6F, 0xB0, 0x00, 0x02, 0xFF,
  0x6F, 0xB0, 0x00, 0x02, 0xFF,
  0x5F, 0xD0, 0x00, 0x04, 0xFD,
  0x1F, 0xF4, 0x00, 0x0A, 0xF9,
  0x07, 0xFF, 0x97, 0xCF, 0xE2,
  0x00, 0x7E, 0xFF, 0xFB, 0x20,
  0x00, 0x00, 0x33, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0050[ 44] = { /* code 0050, LATIN CAPITAL LETTER P */
  0x36, 0x66, 0x63, 0x00,
  0xFF, 0xFF, 0xFF, 0xC1,
  0xFF, 0x54, 0x5D, 0xF8,
  0xFF, 0x20, 0x06, 0xFB,
  0xFF, 0x32, 0x2B, 0xF9,
  0xFF, 0xFF, 0xFF, 0xE2,
  0xFF, 0xA9, 0x87, 0x10,
  0xFF, 0x20, 0x00, 0x00,
  0xFF, 0x20, 0x00, 0x00,
  0xDE, 0x10, 0x00, 0x00,
  0x11, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0051[ 55] = { /* code 0051, LATIN CAPITAL LETTER Q */
  0x00, 0x04, 0x89, 0x72, 0x00,
  0x01, 0xCF, 0xFF, 0xFF, 0x80,
  0x0B, 0xFC, 0x42, 0x6E, 0xF5,
  0x3F, 0xF1, 0x00, 0x07, 0xFB,
  0x6F, 0xB0, 0x00, 0x02, 0xFF,
  0x6F, 0xB0, 0x00, 0x02, 0xFF,
  0x5F, 0xD0, 0x03, 0x34, 0xFE,
  0x1F, 0xF4, 0x09, 0xFC, 0xF9,
  0x07, 0xFF, 0x98, 0xFF, 0xE2,
  0x00, 0x7E, 0xFF, 0xFD, 0xF6,
  0x00, 0x00, 0x33, 0x10, 0x76
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0052[ 44] = { /* code 0052, LATIN CAPITAL LETTER R */
  0x36, 0x66, 0x65, 0x10,
  0xFF, 0xFF, 0xFF, 0xF5,
  0xFF, 0x54, 0x4A, 0xFD,
  0xFF, 0x20, 0x02, 0xFF,
  0xFF, 0x54, 0x49, 0xFA,
  0xFF, 0xFF, 0xFF, 0xD1,
  0xFF, 0x76, 0x6D, 0xF8,
  0xFF, 0x20, 0x06, 0xFA,
  0xFF, 0x20, 0x05, 0xFC,
  0xDE, 0x10, 0x02, 0xFD,
  0x11, 0x00, 0x00, 0x11
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0053[ 55] = { /* code 0053, LATIN CAPITAL LETTER S */
  0x00, 0x48, 0x97, 0x10, 0x00,
  0x0A, 0xFF, 0xFF, 0xF6, 0x00,
  0x4F, 0xD3, 0x26, 0xFE, 0x00,
  0x6F, 0xC0, 0x00, 0x44, 0x00,
  0x2F, 0xFE, 0xB8, 0x30, 0x00,
  0x03, 0xBF, 0xFF, 0xFA, 0x00,
  0x00, 0x00, 0x48, 0xFF, 0x30,
  0x4F, 0x50, 0x00, 0xCF, 0x50,
  0x3F, 0xF8, 0x69, 0xFE, 0x10,
  0x06, 0xEF, 0xFF, 0xD4, 0x00,
  0x00, 0x02, 0x42, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0054[ 55] = { /* code 0054, LATIN CAPITAL LETTER T */
  0x26, 0x66, 0x66, 0x65, 0x10,
  0xCF, 0xFF, 0xFF, 0xFF, 0x70,
  0x37, 0x7D, 0xFA, 0x76, 0x10,
  0x00, 0x0B, 0xF6, 0x00, 0x00,
  0x00, 0x0B, 0xF6, 0x00, 0x00,
  0x00, 0x0B, 0xF6, 0x00, 0x00,
  0x00, 0x0B, 0xF6, 0x00, 0x00,
  0x00, 0x0B, 0xF6, 0x00, 0x00,
  0x00, 0x0B, 0xF6, 0x00, 0x00,
  0x00, 0x09, 0xF4, 0x00, 0x00,
  0x00, 0x00, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0055[ 55] = { /* code 0055, LATIN CAPITAL LETTER U */
  0x56, 0x00, 0x00, 0x56, 0x00,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xFF, 0x20, 0x00, 0xFF, 0x20,
  0xEF, 0x40, 0x02, 0xFF, 0x10,
  0x8F, 0xE8, 0x8D, 0xFA, 0x00,
  0x09, 0xFF, 0xFF, 0xA1, 0x00,
  0x00, 0x13, 0x31, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0056[ 55] = { /* code 0056, LATIN CAPITAL LETTER V */
  0x57, 0x00, 0x00, 0x37, 0x10,
  0xDF, 0x50, 0x00, 0xCF, 0x40,
  0x9F, 0x90, 0x02, 0xFE, 0x10,
  0x3F, 0xE0, 0x07, 0xFA, 0x00,
  0x0D, 0xF3, 0x0B, 0xF4, 0x00,
  0x08, 0xF8, 0x1F, 0xD0, 0x00,
  0x02, 0xFC, 0x6F, 0x80, 0x00,
  0x00, 0xCF, 0xCF, 0x30, 0x00,
  0x00, 0x7F, 0xFC, 0x00, 0x00,
  0x00, 0x2E, 0xF6, 0x00, 0x00,
  0x00, 0x01, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0057[ 66] = { /* code 0057, LATIN CAPITAL LETTER W */
  0x46, 0x00, 0x06, 0x70, 0x00, 0x64,
  0xDF, 0x20, 0x2F, 0xF3, 0x01, 0xFD,
  0xAF, 0x60, 0x6F, 0xF7, 0x05, 0xFA,
  0x6F, 0x90, 0x9E, 0xEA, 0x08, 0xF7,
  0x2F, 0xC0, 0xDB, 0xAE, 0x0B, 0xF3,
  0x0E, 0xF2, 0xF7, 0x7F, 0x2E, 0xE0,
  0x09, 0xF7, 0xF4, 0x3F, 0x7F, 0xA0,
  0x06, 0xFE, 0xF0, 0x0E, 0xEF, 0x60,
  0x02, 0xFF, 0xB0, 0x0B, 0xFF, 0x20,
  0x00, 0xCF, 0x70, 0x06, 0xFD, 0x00,
  0x00, 0x12, 0x00, 0x00, 0x21, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0058[ 44] = { /* code 0058, LATIN CAPITAL LETTER X */
  0x27, 0x10, 0x02, 0x71,
  0x8F, 0xA0, 0x0B, 0xF6,
  0x2F, 0xF3, 0x6F, 0xD1,
  0x07, 0xFC, 0xEF, 0x30,
  0x00, 0xCF, 0xF8, 0x00,
  0x00, 0x8F, 0xF5, 0x00,
  0x04, 0xFF, 0xFD, 0x10,
  0x1D, 0xF6, 0xBF, 0x90,
  0x9F, 0xC0, 0x2F, 0xF4,
  0xBF, 0x30, 0x08, 0xF7,
  0x11, 0x00, 0x00, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0059[ 44] = { /* code 0059, LATIN CAPITAL LETTER Y */
  0x47, 0x00, 0x00, 0x74,
  0xDF, 0x60, 0x06, 0xFC,
  0x6F, 0xD0, 0x0D, 0xF5,
  0x0C, 0xF7, 0x7F, 0xB0,
  0x03, 0xFE, 0xEF, 0x30,
  0x00, 0x9F, 0xF8, 0x00,
  0x00, 0x2F, 0xF1, 0x00,
  0x00, 0x2F, 0xF0, 0x00,
  0x00, 0x2F, 0xF0, 0x00,
  0x00, 0x1E, 0xE0, 0x00,
  0x00, 0x01, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_005A[ 50] = { /* code 005A, LATIN CAPITAL LETTER Z */
  0x04, 0x66, 0x66, 0x65, 0x10,
  0x3F, 0xFF, 0xFF, 0xFF, 0x60,
  0x05, 0x77, 0x7D, 0xFE, 0x20,
  0x00, 0x00, 0x5F, 0xF3, 0x00,
  0x00, 0x03, 0xFF, 0x60, 0x00,
  0x00, 0x2E, 0xF8, 0x00, 0x00,
  0x01, 0xDF, 0xA0, 0x00, 0x00,
  0x0B, 0xFC, 0x10, 0x00, 0x00,
  0x7F, 0xFC, 0xBB, 0xBB, 0x40,
  0x7F, 0xFF, 0xFF, 0xFF, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_005B[ 26] = { /* code 005B, LEFT SQUARE BRACKET */
  0x36, 0x62,
  0xDF, 0xF7,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDD, 0x00,
  0xDF, 0xF7,
  0x36, 0x62
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_005C[ 33] = { /* code 005C, REVERSE SOLIDUS */
  0x73, 0x00, 0x00,
  0xCB, 0x00, 0x00,
  0x6F, 0x20, 0x00,
  0x1F, 0x70, 0x00,
  0x0A, 0xD0, 0x00,
  0x04, 0xF3, 0x00,
  0x00, 0xD9, 0x00,
  0x00, 0x8E, 0x00,
  0x00, 0x2F, 0x60,
  0x00, 0x0B, 0xA0,
  0x00, 0x01, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_005D[ 26] = { /* code 005D, RIGHT SQUARE BRACKET */
  0x46, 0x51,
  0xEF, 0xF6,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0x04, 0xF7,
  0xEF, 0xF6,
  0x46, 0x51
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_005E[ 28] = { /* code 005E, CIRCUMFLEX ACCENT */
  0x00, 0x00, 0x00, 0x00,
  0x00, 0x1D, 0xB0, 0x00,
  0x00, 0x7F, 0xF5, 0x00,
  0x01, 0xF9, 0xCD, 0x00,
  0x09, 0xF1, 0x4F, 0x60,
  0x1F, 0x70, 0x0A, 0xD0,
  0x02, 0x00, 0x00, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_005F[  4] = { /* code 005F, LOW LINE */
  0x39, 0x99, 0x99, 0x98
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0060[  6] = { /* code 0060, GRAVE ACCENT */
  0xC7, 0x00,
  0x8F, 0xA0,
  0x03, 0x60
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0061[ 32] = { /* code 0061, LATIN SMALL LETTER A */
  0x03, 0xBE, 0xEC, 0x40,
  0x0E, 0xE8, 0x8F, 0xE0,
  0x06, 0x20, 0x2E, 0xF2,
  0x07, 0xCF, 0xFF, 0xF2,
  0x5F, 0xB3, 0x1D, 0xF2,
  0x7F, 0xA2, 0x6F, 0xF2,
  0x1C, 0xFF, 0xAA, 0xF3,
  0x00, 0x21, 0x00, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0062[ 44] = { /* code 0062, LATIN SMALL LETTER B */
  0x07, 0x30, 0x00, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x4F, 0xBB, 0xFC, 0x30,
  0x4F, 0xFA, 0x9F, 0xE1,
  0x4F, 0xB0, 0x09, 0xF5,
  0x4F, 0x80, 0x06, 0xF7,
  0x4F, 0xA0, 0x08, 0xF6,
  0x4F, 0xF7, 0x7E, 0xF2,
  0x3F, 0xAE, 0xFE, 0x50,
  0x02, 0x00, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0063[ 32] = { /* code 0063, LATIN SMALL LETTER C */
  0x03, 0xBE, 0xE9, 0x10,
  0x1E, 0xF9, 0xAF, 0xA0,
  0x7F, 0x80, 0x07, 0x50,
  0x9F, 0x40, 0x00, 0x00,
  0x8F, 0x60, 0x05, 0x40,
  0x3F, 0xE6, 0x7F, 0xA0,
  0x06, 0xEF, 0xFC, 0x20,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0064[ 44] = { /* code 0064, LATIN SMALL LETTER D */
  0x00, 0x00, 0x03, 0x70,
  0x00, 0x00, 0x0B, 0xF3,
  0x00, 0x00, 0x0B, 0xF4,
  0x04, 0xCF, 0xAC, 0xF4,
  0x1E, 0xF9, 0xAF, 0xF4,
  0x6F, 0x90, 0x0C, 0xF4,
  0x7F, 0x60, 0x09, 0xF4,
  0x7F, 0x70, 0x0B, 0xF4,
  0x2F, 0xE7, 0x7F, 0xF4,
  0x06, 0xFF, 0xEA, 0xF2,
  0x00, 0x12, 0x00, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0065[ 32] = { /* code 0065, LATIN SMALL LETTER E */
  0x02, 0xAE, 0xEB, 0x20,
  0x0D, 0xE7, 0x7E, 0xE1,
  0x6F, 0x82, 0x28, 0xF6,
  0x7F, 0xFF, 0xFF, 0xF6,
  0x7F, 0x70, 0x00, 0x30,
  0x2E, 0xE7, 0x6C, 0xF1,
  0x04, 0xDF, 0xFE, 0x70,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0066[ 33] = { /* code 0066, LATIN SMALL LETTER F */
  0x00, 0x69, 0x30,
  0x07, 0xFF, 0x90,
  0x09, 0xF4, 0x00,
  0x9E, 0xFC, 0x70,
  0x4C, 0xF9, 0x30,
  0x09, 0xF4, 0x00,
  0x09, 0xF4, 0x00,
  0x09, 0xF4, 0x00,
  0x09, 0xF4, 0x00,
  0x08, 0xF3, 0x00,
  0x00, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0067[ 40] = { /* code 0067, LATIN SMALL LETTER G */
  0x03, 0xCF, 0xB7, 0xE1,
  0x1E, 0xF9, 0xAF, 0xF4,
  0x6F, 0x80, 0x0C, 0xF4,
  0x7F, 0x60, 0x09, 0xF4,
  0x7F, 0x80, 0x0C, 0xF4,
  0x1E, 0xFA, 0xBF, 0xF4,
  0x03, 0xAB, 0x8A, 0xF3,
  0x08, 0x30, 0x0C, 0xF1,
  0x0E, 0xFA, 0xCF, 0x90,
  0x02, 0x9B, 0xB7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0068[ 44] = { /* code 0068, LATIN SMALL LETTER H */
  0x07, 0x30, 0x00, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x4F, 0xAA, 0xED, 0x50,
  0x4F, 0xFB, 0xAF, 0xF1,
  0x4F, 0xC0, 0x0C, 0xF3,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0x90, 0x0B, 0xF4,
  0x3F, 0x90, 0x0A, 0xF2,
  0x02, 0x00, 0x00, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0069[ 22] = { /* code 0069, LATIN SMALL LETTER I */
  0x07, 0x40,
  0x2F, 0xB0,
  0x04, 0x20,
  0x1E, 0x80,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x1F, 0xA0,
  0x01, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_006A[ 26] = { /* code 006A, LATIN SMALL LETTER J */
  0x00, 0x74,
  0x02, 0xFB,
  0x00, 0x42,
  0x01, 0xE8,
  0x02, 0xFB,
  0x02, 0xFB,
  0x02, 0xFB,
  0x02, 0xFB,
  0x02, 0xFB,
  0x02, 0xFB,
  0x02, 0xFB,
  0x2C, 0xFA,
  0x19, 0x92
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_006B[ 44] = { /* code 006B, LATIN SMALL LETTER K */
  0x07, 0x30, 0x00, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x4F, 0x90, 0x5E, 0x30,
  0x4F, 0x94, 0xFE, 0x20,
  0x4F, 0xCE, 0xE3, 0x00,
  0x4F, 0xFF, 0xE2, 0x00,
  0x4F, 0xD8, 0xFC, 0x00,
  0x4F, 0x90, 0xBF, 0x80,
  0x3F, 0x90, 0x2E, 0xB0,
  0x01, 0x00, 0x01, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_006C[ 22] = { /* code 006C, LATIN SMALL LETTER L */
  0x07, 0x40,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x2F, 0xB0,
  0x1F, 0xA0,
  0x01, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_006D[ 48] = { /* code 006D, LATIN SMALL LETTER M */
  0x2E, 0x69, 0xED, 0x38, 0xEE, 0x70,
  0x4F, 0xEB, 0xBF, 0xED, 0xAF, 0xF3,
  0x4F, 0xC0, 0x0F, 0xF2, 0x0A, 0xF4,
  0x4F, 0x90, 0x0F, 0xF0, 0x09, 0xF4,
  0x4F, 0x90, 0x0F, 0xF0, 0x09, 0xF4,
  0x4F, 0x90, 0x0F, 0xF0, 0x09, 0xF4,
  0x3F, 0x90, 0x0D, 0xE0, 0x08, 0xF3,
  0x02, 0x00, 0x01, 0x10, 0x00, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_006E[ 32] = { /* code 006E, LATIN SMALL LETTER N */
  0x2E, 0x6A, 0xED, 0x50,
  0x4F, 0xFB, 0xAF, 0xF1,
  0x4F, 0xC0, 0x0C, 0xF3,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0x90, 0x0B, 0xF4,
  0x3F, 0x90, 0x0A, 0xF2,
  0x02, 0x00, 0x00, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_006F[ 32] = { /* code 006F, LATIN SMALL LETTER O */
  0x02, 0xBE, 0xEA, 0x20,
  0x1D, 0xF8, 0x8F, 0xD0,
  0x6F, 0x80, 0x09, 0xF5,
  0x7F, 0x60, 0x06, 0xF7,
  0x7F, 0x70, 0x08, 0xF6,
  0x2F, 0xE5, 0x5E, 0xF2,
  0x05, 0xEF, 0xFE, 0x40,
  0x00, 0x02, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0070[ 40] = { /* code 0070, LATIN SMALL LETTER P */
  0x2E, 0x7B, 0xFC, 0x30,
  0x4F, 0xFA, 0x9F, 0xE1,
  0x4F, 0xB0, 0x0A, 0xF5,
  0x4F, 0x80, 0x06, 0xF7,
  0x4F, 0xA0, 0x08, 0xF6,
  0x4F, 0xF7, 0x7E, 0xF2,
  0x4F, 0xBD, 0xFF, 0x50,
  0x4F, 0x90, 0x21, 0x00,
  0x4F, 0x90, 0x00, 0x00,
  0x19, 0x40, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0071[ 40] = { /* code 0071, LATIN SMALL LETTER Q */
  0x03, 0xCF, 0xB7, 0xE1,
  0x1E, 0xF9, 0xAF, 0xF4,
  0x6F, 0x90, 0x0C, 0xF4,
  0x7F, 0x60, 0x09, 0xF4,
  0x7F, 0x70, 0x0B, 0xF4,
  0x3F, 0xE6, 0x7F, 0xF4,
  0x06, 0xFF, 0xDD, 0xF4,
  0x00, 0x12, 0x0B, 0xF4,
  0x00, 0x00, 0x0B, 0xF3,
  0x00, 0x00, 0x05, 0x90
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0072[ 24] = { /* code 0072, LATIN SMALL LETTER R */
  0x1E, 0x7B, 0xE1,
  0x2F, 0xEE, 0xC1,
  0x2F, 0xE1, 0x00,
  0x2F, 0xB0, 0x00,
  0x2F, 0xB0, 0x00,
  0x2F, 0xB0, 0x00,
  0x1F, 0xA0, 0x00,
  0x01, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0073[ 32] = { /* code 0073, LATIN SMALL LETTER S */
  0x08, 0xDF, 0xC7, 0x00,
  0x6F, 0xB7, 0xCF, 0x20,
  0x7F, 0xB4, 0x13, 0x00,
  0x2C, 0xFF, 0xFA, 0x10,
  0x13, 0x36, 0xCF, 0x70,
  0x7F, 0x72, 0x9F, 0x60,
  0x1B, 0xFF, 0xFA, 0x00,
  0x00, 0x12, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0074[ 30] = { /* code 0074, LATIN SMALL LETTER T */
  0x08, 0xC1, 0x00,
  0x0B, 0xF2, 0x00,
  0x9E, 0xFC, 0x60,
  0x4D, 0xF8, 0x30,
  0x0B, 0xF2, 0x00,
  0x0B, 0xF2, 0x00,
  0x0B, 0xF2, 0x00,
  0x0B, 0xF7, 0x20,
  0x07, 0xFF, 0x70,
  0x00, 0x12, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0075[ 32] = { /* code 0075, LATIN SMALL LETTER U */
  0x2E, 0x70, 0x08, 0xE1,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0x90, 0x0B, 0xF4,
  0x4F, 0xA0, 0x0C, 0xF4,
  0x2F, 0xF7, 0x9F, 0xF4,
  0x08, 0xFF, 0xC8, 0xF2,
  0x00, 0x12, 0x00, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0076[ 32] = { /* code 0076, LATIN SMALL LETTER V */
  0xBB, 0x00, 0x4E, 0x30,
  0xCF, 0x20, 0xAF, 0x40,
  0x7F, 0x70, 0xEE, 0x00,
  0x1F, 0xB4, 0xF9, 0x00,
  0x0B, 0xF9, 0xF3, 0x00,
  0x05, 0xFF, 0xD0, 0x00,
  0x01, 0xEF, 0x70, 0x00,
  0x00, 0x12, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0077[ 40] = { /* code 0077, LATIN SMALL LETTER W */
  0x9C, 0x00, 0xCD, 0x10, 0xBB,
  0xBF, 0x23, 0xFF, 0x51, 0xFC,
  0x7F, 0x66, 0xFE, 0x84, 0xF8,
  0x2F, 0xA9, 0xCA, 0xC7, 0xF3,
  0x0C, 0xDC, 0x87, 0xFB, 0xE0,
  0x08, 0xFF, 0x43, 0xFF, 0x90,
  0x03, 0xFE, 0x10, 0xDF, 0x40,
  0x00, 0x11, 0x00, 0x12, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0078[ 24] = { /* code 0078, LATIN SMALL LETTER X */
  0x6E, 0x20, 0xAC,
  0x6F, 0xB5, 0xFB,
  0x0B, 0xFE, 0xE2,
  0x03, 0xFF, 0x80,
  0x0B, 0xFF, 0xE2,
  0x6F, 0xA7, 0xFB,
  0xBE, 0x20, 0xCE,
  0x01, 0x00, 0x11
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_0079[ 40] = { /* code 0079, LATIN SMALL LETTER Y */
  0xAC, 0x00, 0x5E, 0x30,
  0xBF, 0x40, 0xBF, 0x30,
  0x6F, 0x80, 0xED, 0x00,
  0x1F, 0xC4, 0xF7, 0x00,
  0x0A, 0xF9, 0xF2, 0x00,
  0x05, 0xFF, 0xC0, 0x00,
  0x00, 0xEF, 0x70, 0x00,
  0x00, 0xBF, 0x10, 0x00,
  0x4F, 0xFA, 0x00, 0x00,
  0x19, 0x81, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_007A[ 28] = { /* code 007A, LATIN SMALL LETTER Z */
  0x2B, 0xBB, 0xBB, 0x70,
  0x2A, 0xBB, 0xFF, 0x90,
  0x00, 0x08, 0xFA, 0x00,
  0x00, 0x7F, 0xB1, 0x00,
  0x06, 0xFD, 0x10, 0x00,
  0x4F, 0xF9, 0x77, 0x50,
  0x7F, 0xFF, 0xFF, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_007B[ 39] = { /* code 007B, LEFT CURLY BRACKET */
  0x00, 0x46, 0x20,
  0x08, 0xFE, 0x50,
  0x0B, 0xF0, 0x00,
  0x0B, 0xF0, 0x00,
  0x0B, 0xF0, 0x00,
  0x1C, 0xF0, 0x00,
  0xEF, 0x70, 0x00,
  0x1C, 0xF0, 0x00,
  0x0B, 0xF0, 0x00,
  0x0B, 0xF0, 0x00,
  0x0B, 0xF0, 0x00,
  0x08, 0xFC, 0x50,
  0x00, 0x56, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_007C[ 22] = { /* code 007C, VERTICAL LINE */
  0x18, 0x00,
  0x6F, 0x40,
  0x6F, 0x40,
  0x6F, 0x40,
  0x6F, 0x40,
  0x6F, 0x40,
  0x6F, 0x40,
  0x6F, 0x40,
  0x6F, 0x40,
  0x5F, 0x30,
  0x03, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_007D[ 39] = { /* code 007D, RIGHT CURLY BRACKET */
  0x46, 0x30, 0x00,
  0xBE, 0xF2, 0x00,
  0x08, 0xF4, 0x00,
  0x07, 0xF4, 0x00,
  0x07, 0xF4, 0x00,
  0x06, 0xF6, 0x00,
  0x00, 0xCF, 0x70,
  0x06, 0xF7, 0x00,
  0x07, 0xF4, 0x00,
  0x07, 0xF4, 0x00,
  0x07, 0xF4, 0x00,
  0xAE, 0xF2, 0x00,
  0x56, 0x30, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded16_007E[ 12] = { /* code 007E, TILDE */
  0x0A, 0xDA, 0x41, 0xA2,
  0x6F, 0xAE, 0xFF, 0xE2,
  0x23, 0x00, 0x67, 0x30
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontRounded16_CharInfo[95] = {
   {   1,   1,   0,  13,   4, acGUI_FontRounded16_0020 } /* code 0020, SPACE */
  ,{   3,  11,   1,   3,   4, acGUI_FontRounded16_0021 } /* code 0021, EXCLAMATION MARK */
  ,{   5,   5,   1,   3,   7, acGUI_FontRounded16_0022 } /* code 0022, QUOTATION MARK */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0023 } /* code 0023, NUMBER SIGN */
  ,{   8,  12,   0,   3,   8, acGUI_FontRounded16_0024 } /* code 0024, DOLLAR SIGN */
  ,{  11,  11,   1,   3,  13, acGUI_FontRounded16_0025 } /* code 0025, PERCENT SIGN */
  ,{   9,  11,   0,   3,   9, acGUI_FontRounded16_0026 } /* code 0026, AMPERSAND */
  ,{   2,   5,   1,   3,   4, acGUI_FontRounded16_0027 } /* code 0027, APOSTROPHE */
  ,{   4,  13,   0,   3,   4, acGUI_FontRounded16_0028 } /* code 0028, LEFT PARENTHESIS */
  ,{   4,  13,   0,   3,   4, acGUI_FontRounded16_0029 } /* code 0029, RIGHT PARENTHESIS */
  ,{   6,   6,   0,   3,   6, acGUI_FontRounded16_002A } /* code 002A, ASTERISK */
  ,{   8,   8,   0,   5,   8, acGUI_FontRounded16_002B } /* code 002B, PLUS SIGN */
  ,{   4,   4,   0,  11,   4, acGUI_FontRounded16_002C } /* code 002C, COMMA */
  ,{   5,   3,   0,   8,   5, acGUI_FontRounded16_002D } /* code 002D, HYPHEN-MINUS */
  ,{   4,   3,   0,  11,   4, acGUI_FontRounded16_002E } /* code 002E, FULL STOP */
  ,{   5,  11,   0,   3,   5, acGUI_FontRounded16_002F } /* code 002F, SOLIDUS */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0030 } /* code 0030, DIGIT ZERO */
  ,{   6,  11,  -1,   3,   5, acGUI_FontRounded16_0031 } /* code 0031, DIGIT ONE */
  ,{   8,  10,   0,   3,   8, acGUI_FontRounded16_0032 } /* code 0032, DIGIT TWO */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0033 } /* code 0033, DIGIT THREE */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0034 } /* code 0034, DIGIT FOUR */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0035 } /* code 0035, DIGIT FIVE */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0036 } /* code 0036, DIGIT SIX */
  ,{   8,  11,   0,   3,   7, acGUI_FontRounded16_0037 } /* code 0037, DIGIT SEVEN */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0038 } /* code 0038, DIGIT EIGHT */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0039 } /* code 0039, DIGIT NINE */
  ,{   4,   8,   0,   6,   4, acGUI_FontRounded16_003A } /* code 003A, COLON */
  ,{   4,   9,   0,   6,   4, acGUI_FontRounded16_003B } /* code 003B, SEMICOLON */
  ,{   8,   7,   0,   6,   8, acGUI_FontRounded16_003C } /* code 003C, LESS-THAN SIGN */
  ,{   8,   5,   0,   7,   8, acGUI_FontRounded16_003D } /* code 003D, EQUALS SIGN */
  ,{   8,   7,   0,   6,   8, acGUI_FontRounded16_003E } /* code 003E, GREATER-THAN SIGN */
  ,{   7,  11,   0,   3,   7, acGUI_FontRounded16_003F } /* code 003F, QUESTION MARK */
  ,{  10,  11,   0,   3,  10, acGUI_FontRounded16_0040 } /* code 0040, COMMERCIAL AT */
  ,{   9,  11,   0,   3,   9, acGUI_FontRounded16_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{   8,  10,   1,   3,   9, acGUI_FontRounded16_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{   9,  11,   0,   3,   9, acGUI_FontRounded16_0043 } /* code 0043, LATIN CAPITAL LETTER C */
  ,{   9,  10,   1,   3,  10, acGUI_FontRounded16_0044 } /* code 0044, LATIN CAPITAL LETTER D */
  ,{   8,  10,   1,   3,   9, acGUI_FontRounded16_0045 } /* code 0045, LATIN CAPITAL LETTER E */
  ,{   8,  11,   1,   3,   8, acGUI_FontRounded16_0046 } /* code 0046, LATIN CAPITAL LETTER F */
  ,{  10,  11,   0,   3,  11, acGUI_FontRounded16_0047 } /* code 0047, LATIN CAPITAL LETTER G */
  ,{   9,  11,   1,   3,  10, acGUI_FontRounded16_0048 } /* code 0048, LATIN CAPITAL LETTER H */
  ,{   3,  11,   1,   3,   4, acGUI_FontRounded16_0049 } /* code 0049, LATIN CAPITAL LETTER I */
  ,{   7,  11,   0,   3,   7, acGUI_FontRounded16_004A } /* code 004A, LATIN CAPITAL LETTER J */
  ,{   8,  11,   1,   3,   9, acGUI_FontRounded16_004B } /* code 004B, LATIN CAPITAL LETTER K */
  ,{   7,  10,   1,   3,   8, acGUI_FontRounded16_004C } /* code 004C, LATIN CAPITAL LETTER L */
  ,{  10,  11,   1,   3,  12, acGUI_FontRounded16_004D } /* code 004D, LATIN CAPITAL LETTER M */
  ,{   9,  11,   1,   3,  10, acGUI_FontRounded16_004E } /* code 004E, LATIN CAPITAL LETTER N */
  ,{  10,  11,   0,   3,  11, acGUI_FontRounded16_004F } /* code 004F, LATIN CAPITAL LETTER O */
  ,{   8,  11,   1,   3,   9, acGUI_FontRounded16_0050 } /* code 0050, LATIN CAPITAL LETTER P */
  ,{  10,  11,   0,   3,  11, acGUI_FontRounded16_0051 } /* code 0051, LATIN CAPITAL LETTER Q */
  ,{   8,  11,   1,   3,  10, acGUI_FontRounded16_0052 } /* code 0052, LATIN CAPITAL LETTER R */
  ,{   9,  11,   0,   3,   9, acGUI_FontRounded16_0053 } /* code 0053, LATIN CAPITAL LETTER S */
  ,{   9,  11,   0,   3,   9, acGUI_FontRounded16_0054 } /* code 0054, LATIN CAPITAL LETTER T */
  ,{   9,  11,   1,   3,  10, acGUI_FontRounded16_0055 } /* code 0055, LATIN CAPITAL LETTER U */
  ,{   9,  11,   0,   3,   8, acGUI_FontRounded16_0056 } /* code 0056, LATIN CAPITAL LETTER V */
  ,{  12,  11,   0,   3,  12, acGUI_FontRounded16_0057 } /* code 0057, LATIN CAPITAL LETTER W */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0058 } /* code 0058, LATIN CAPITAL LETTER X */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0059 } /* code 0059, LATIN CAPITAL LETTER Y */
  ,{   9,  10,   0,   3,   9, acGUI_FontRounded16_005A } /* code 005A, LATIN CAPITAL LETTER Z */
  ,{   4,  13,   1,   3,   5, acGUI_FontRounded16_005B } /* code 005B, LEFT SQUARE BRACKET */
  ,{   5,  11,   0,   3,   5, acGUI_FontRounded16_005C } /* code 005C, REVERSE SOLIDUS */
  ,{   4,  13,   0,   3,   5, acGUI_FontRounded16_005D } /* code 005D, RIGHT SQUARE BRACKET */
  ,{   7,   7,   0,   3,   8, acGUI_FontRounded16_005E } /* code 005E, CIRCUMFLEX ACCENT */
  ,{   8,   1,  -1,  14,   7, acGUI_FontRounded16_005F } /* code 005F, LOW LINE */
  ,{   3,   3,   0,   3,   4, acGUI_FontRounded16_0060 } /* code 0060, GRAVE ACCENT */
  ,{   8,   8,   0,   6,   8, acGUI_FontRounded16_0061 } /* code 0061, LATIN SMALL LETTER A */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0062 } /* code 0062, LATIN SMALL LETTER B */
  ,{   7,   8,   0,   6,   7, acGUI_FontRounded16_0063 } /* code 0063, LATIN SMALL LETTER C */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0064 } /* code 0064, LATIN SMALL LETTER D */
  ,{   8,   8,   0,   6,   8, acGUI_FontRounded16_0065 } /* code 0065, LATIN SMALL LETTER E */
  ,{   5,  11,   0,   3,   5, acGUI_FontRounded16_0066 } /* code 0066, LATIN SMALL LETTER F */
  ,{   8,  10,   0,   6,   8, acGUI_FontRounded16_0067 } /* code 0067, LATIN SMALL LETTER G */
  ,{   8,  11,   0,   3,   8, acGUI_FontRounded16_0068 } /* code 0068, LATIN SMALL LETTER H */
  ,{   3,  11,   0,   3,   4, acGUI_FontRounded16_0069 } /* code 0069, LATIN SMALL LETTER I */
  ,{   4,  13,  -1,   3,   4, acGUI_FontRounded16_006A } /* code 006A, LATIN SMALL LETTER J */
  ,{   7,  11,   0,   3,   7, acGUI_FontRounded16_006B } /* code 006B, LATIN SMALL LETTER K */
  ,{   3,  11,   0,   3,   4, acGUI_FontRounded16_006C } /* code 006C, LATIN SMALL LETTER L */
  ,{  12,   8,   0,   6,  12, acGUI_FontRounded16_006D } /* code 006D, LATIN SMALL LETTER M */
  ,{   8,   8,   0,   6,   8, acGUI_FontRounded16_006E } /* code 006E, LATIN SMALL LETTER N */
  ,{   8,   8,   0,   6,   8, acGUI_FontRounded16_006F } /* code 006F, LATIN SMALL LETTER O */
  ,{   8,  10,   0,   6,   8, acGUI_FontRounded16_0070 } /* code 0070, LATIN SMALL LETTER P */
  ,{   8,  10,   0,   6,   8, acGUI_FontRounded16_0071 } /* code 0071, LATIN SMALL LETTER Q */
  ,{   6,   8,   0,   6,   5, acGUI_FontRounded16_0072 } /* code 0072, LATIN SMALL LETTER R */
  ,{   7,   8,   0,   6,   7, acGUI_FontRounded16_0073 } /* code 0073, LATIN SMALL LETTER S */
  ,{   5,  10,   0,   4,   5, acGUI_FontRounded16_0074 } /* code 0074, LATIN SMALL LETTER T */
  ,{   8,   8,   0,   6,   8, acGUI_FontRounded16_0075 } /* code 0075, LATIN SMALL LETTER U */
  ,{   7,   8,   0,   6,   7, acGUI_FontRounded16_0076 } /* code 0076, LATIN SMALL LETTER V */
  ,{  10,   8,   0,   6,  10, acGUI_FontRounded16_0077 } /* code 0077, LATIN SMALL LETTER W */
  ,{   6,   8,   0,   6,   6, acGUI_FontRounded16_0078 } /* code 0078, LATIN SMALL LETTER X */
  ,{   7,  10,   0,   6,   7, acGUI_FontRounded16_0079 } /* code 0079, LATIN SMALL LETTER Y */
  ,{   7,   7,   0,   6,   7, acGUI_FontRounded16_007A } /* code 007A, LATIN SMALL LETTER Z */
  ,{   5,  13,   0,   3,   5, acGUI_FontRounded16_007B } /* code 007B, LEFT CURLY BRACKET */
  ,{   3,  11,   0,   3,   3, acGUI_FontRounded16_007C } /* code 007C, VERTICAL LINE */
  ,{   5,  13,   0,   3,   5, acGUI_FontRounded16_007D } /* code 007D, RIGHT CURLY BRACKET */
  ,{   8,   3,   0,   8,   8, acGUI_FontRounded16_007E } /* code 007E, TILDE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontRounded16_Prop1 = {
   0x0020 /* first character */
  ,0x007E /* last character  */
  ,&GUI_FontRounded16_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontRounded16 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,16 /* height of font  */
  ,16 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontRounded16_Prop1}
  ,16 /* Baseline */
  ,8 /* Height of lowercase characters */
  ,11 /* Height of capital characters */
};

/*********************************************************************
*                                                                    *
*       GUI_FontRounded22                                            *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO.c                                                       *
*  - GUIDEMO_AntiAliasedText.c                                       *
*  - GUIDEMO_Automotive.c                                            *
*  - GUIDEMO_Bargraph.c                                              *
*  - GUIDEMO_ColorBar.c                                              *
*  - GUIDEMO_IconView.c                                              *
*  - GUIDEMO_ImageFlow.c                                             *
*  - GUIDEMO_Intro.c                                                 *
*                                                                    *
**********************************************************************
*/
GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0020[  1] = { /* code 0020, SPACE */
  0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0021[ 30] = { /* code 0021, EXCLAMATION MARK */
  0x02, 0x10,
  0x6F, 0xE2,
  0x9F, 0xF4,
  0x9F, 0xF4,
  0x9F, 0xF4,
  0x7F, 0xF2,
  0x5F, 0xF0,
  0x3F, 0xD0,
  0x1F, 0xB0,
  0x0D, 0x80,
  0x00, 0x00,
  0x2B, 0xA0,
  0x9F, 0xF4,
  0x5F, 0xE1,
  0x01, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0022[ 28] = { /* code 0022, QUOTATION MARK */
  0x01, 0x10, 0x02, 0x00,
  0x0E, 0xF2, 0x7F, 0x90,
  0x2F, 0xF4, 0x9F, 0xB0,
  0x2F, 0xF4, 0x9F, 0xB0,
  0x2F, 0xF4, 0x9F, 0xB0,
  0x1E, 0xF2, 0x8F, 0xA0,
  0x02, 0x20, 0x03, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0023[ 84] = { /* code 0023, NUMBER SIGN */
  0x00, 0x02, 0xB4, 0x08, 0x90, 0x00,
  0x00, 0x06, 0xF7, 0x0F, 0xD0, 0x00,
  0x00, 0x08, 0xF5, 0x2F, 0xB0, 0x00,
  0x00, 0x2B, 0xF4, 0x5F, 0xA1, 0x00,
  0x07, 0xFF, 0xFF, 0xFF, 0xFE, 0x10,
  0x03, 0x9F, 0xE9, 0xDF, 0xB8, 0x00,
  0x00, 0x1F, 0xB0, 0xAF, 0x30, 0x00,
  0x02, 0x6F, 0xB4, 0xDF, 0x40, 0x00,
  0x0F, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x06, 0xBF, 0x98, 0xFD, 0x72, 0x00,
  0x00, 0xAF, 0x33, 0xF9, 0x00, 0x00,
  0x00, 0xCF, 0x15, 0xF7, 0x00, 0x00,
  0x00, 0xDD, 0x07, 0xF4, 0x00, 0x00,
  0x00, 0x22, 0x00, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0024[ 96] = { /* code 0024, DOLLAR SIGN */
  0x00, 0x00, 0x0B, 0x10, 0x00, 0x00,
  0x00, 0x28, 0xBF, 0xC8, 0x30, 0x00,
  0x05, 0xFF, 0xFF, 0xFF, 0xF9, 0x00,
  0x1E, 0xF9, 0x2F, 0x39, 0xFF, 0x50,
  0x3F, 0xF0, 0x0F, 0x20, 0x9E, 0x30,
  0x3F, 0xF6, 0x0F, 0x20, 0x00, 0x00,
  0x0D, 0xFF, 0xDF, 0x73, 0x00, 0x00,
  0x02, 0xCF, 0xFF, 0xFF, 0xC3, 0x00,
  0x00, 0x04, 0x8F, 0xEF, 0xFE, 0x10,
  0x03, 0x00, 0x0F, 0x25, 0xFF, 0x70,
  0x7F, 0xA0, 0x0F, 0x20, 0xBF, 0x80,
  0x7F, 0xF3, 0x0F, 0x21, 0xEF, 0x60,
  0x1D, 0xFE, 0xAF, 0xBE, 0xFD, 0x10,
  0x01, 0xAF, 0xFF, 0xFF, 0xA2, 0x00,
  0x00, 0x01, 0x3F, 0x41, 0x00, 0x00,
  0x00, 0x00, 0x0D, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0025[120] = { /* code 0025, PERCENT SIGN */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x03, 0xAB, 0xA2, 0x00, 0x00, 0x7F, 0x00, 0x00,
  0x2E, 0xEA, 0xFE, 0x10, 0x01, 0xE8, 0x00, 0x00,
  0x7F, 0x70, 0x9F, 0x50, 0x08, 0xE1, 0x00, 0x00,
  0x9F, 0x60, 0x7F, 0x70, 0x2F, 0x70, 0x00, 0x00,
  0x7F, 0x60, 0x8F, 0x60, 0x9E, 0x10, 0x00, 0x00,
  0x3F, 0xD6, 0xEF, 0x22, 0xF7, 0x00, 0x00, 0x00,
  0x07, 0xEF, 0xE6, 0x0A, 0xE0, 0x17, 0x98, 0x20,
  0x00, 0x01, 0x00, 0x3F, 0x60, 0xCF, 0xCF, 0xE1,
  0x00, 0x00, 0x00, 0xBD, 0x04, 0xFA, 0x09, 0xF6,
  0x00, 0x00, 0x04, 0xF5, 0x07, 0xF7, 0x06, 0xF9,
  0x00, 0x00, 0x0C, 0xC0, 0x06, 0xF8, 0x06, 0xF8,
  0x00, 0x00, 0x5F, 0x40, 0x03, 0xFC, 0x3B, 0xF4,
  0x00, 0x00, 0xCB, 0x00, 0x00, 0x8F, 0xFF, 0x90,
  0x00, 0x00, 0x93, 0x00, 0x00, 0x01, 0x42, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0026[105] = { /* code 0026, AMPERSAND */
  0x00, 0x00, 0x01, 0x10, 0x00, 0x00, 0x00,
  0x00, 0x04, 0xDF, 0xFD, 0x50, 0x00, 0x00,
  0x00, 0x3F, 0xFD, 0xCF, 0xF4, 0x00, 0x00,
  0x00, 0x9F, 0xD0, 0x0B, 0xFA, 0x00, 0x00,
  0x00, 0x9F, 0xD0, 0x0B, 0xFA, 0x00, 0x00,
  0x00, 0x4F, 0xF9, 0x8F, 0xF5, 0x00, 0x00,
  0x00, 0x0A, 0xFF, 0xFF, 0x80, 0x00, 0x00,
  0x00, 0x9F, 0xFF, 0xFB, 0x00, 0x54, 0x00,
  0x09, 0xFF, 0xBA, 0xFF, 0x74, 0xFF, 0x00,
  0x1F, 0xFB, 0x00, 0xBF, 0xFE, 0xFC, 0x00,
  0x3F, 0xF7, 0x00, 0x1D, 0xFF, 0xE3, 0x00,
  0x1F, 0xFB, 0x00, 0x2C, 0xFF, 0xE3, 0x00,
  0x0A, 0xFF, 0xED, 0xFF, 0xEE, 0xFD, 0x00,
  0x01, 0xAF, 0xFF, 0xFB, 0x24, 0xFD, 0x00,
  0x00, 0x01, 0x43, 0x10, 0x00, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0027[ 14] = { /* code 0027, APOSTROPHE */
  0x01, 0x10,
  0x3F, 0xD0,
  0x6F, 0xF0,
  0x6F, 0xF0,
  0x6F, 0xF0,
  0x3F, 0xE0,
  0x03, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0028[ 54] = { /* code 0028, LEFT PARENTHESIS */
  0x00, 0x03, 0x00,
  0x00, 0x9F, 0x40,
  0x02, 0xFF, 0x20,
  0x08, 0xFB, 0x00,
  0x0E, 0xF7, 0x00,
  0x4F, 0xF3, 0x00,
  0x7F, 0xE0, 0x00,
  0xAF, 0xC0, 0x00,
  0xBF, 0xB0, 0x00,
  0xBF, 0xB0, 0x00,
  0xBF, 0xB0, 0x00,
  0x8F, 0xE0, 0x00,
  0x5F, 0xF2, 0x00,
  0x1F, 0xF5, 0x00,
  0x0A, 0xFA, 0x00,
  0x04, 0xFE, 0x10,
  0x00, 0xCF, 0x50,
  0x00, 0x29, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0029[ 54] = { /* code 0029, RIGHT PARENTHESIS */
  0x03, 0x00, 0x00,
  0x5F, 0x90, 0x00,
  0x2F, 0xF2, 0x00,
  0x0B, 0xF8, 0x00,
  0x07, 0xFE, 0x00,
  0x03, 0xFF, 0x30,
  0x00, 0xFF, 0x70,
  0x00, 0xCF, 0xA0,
  0x00, 0xBF, 0xB0,
  0x00, 0xBF, 0xB0,
  0x00, 0xCF, 0xB0,
  0x00, 0xEF, 0x80,
  0x02, 0xFF, 0x50,
  0x05, 0xFF, 0x10,
  0x0A, 0xFA, 0x00,
  0x1E, 0xF4, 0x00,
  0x5F, 0xC0, 0x00,
  0x19, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_002A[ 32] = { /* code 002A, ASTERISK */
  0x00, 0x00, 0x10, 0x00,
  0x00, 0x08, 0xE0, 0x00,
  0x05, 0x29, 0xF0, 0x51,
  0x2F, 0xFE, 0xFE, 0xF7,
  0x02, 0x7F, 0xFB, 0x40,
  0x00, 0xAF, 0xCE, 0x20,
  0x03, 0xF7, 0x2F, 0x90,
  0x00, 0x30, 0x03, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_002B[ 66] = { /* code 002B, PLUS SIGN */
  0x00, 0x00, 0x02, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x8F, 0x50, 0x00, 0x00,
  0x00, 0x00, 0xBF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0xBF, 0x70, 0x00, 0x00,
  0x02, 0x22, 0xCF, 0x82, 0x22, 0x00,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x6F, 0xFF, 0xFF, 0xFF, 0xFE, 0x40,
  0x00, 0x00, 0xBF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0xBF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0xBF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0x40, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_002C[ 12] = { /* code 002C, COMMA */
  0x3C, 0x90,
  0xBF, 0xF5,
  0x6F, 0xF7,
  0x01, 0xE5,
  0x4C, 0xC0,
  0x67, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_002D[ 16] = { /* code 002D, HYPHEN-MINUS */
  0x01, 0x44, 0x42, 0x00,
  0x2F, 0xFF, 0xFF, 0x70,
  0x3F, 0xFF, 0xFF, 0x80,
  0x02, 0x44, 0x43, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_002E[  8] = { /* code 002E, FULL STOP */
  0x3C, 0x90,
  0xBF, 0xF3,
  0x6F, 0xD1,
  0x01, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_002F[ 60] = { /* code 002F, SOLIDUS */
  0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x6F, 0x70,
  0x00, 0x00, 0xCF, 0x40,
  0x00, 0x03, 0xFD, 0x00,
  0x00, 0x08, 0xF8, 0x00,
  0x00, 0x0E, 0xF2, 0x00,
  0x00, 0x5F, 0xB0, 0x00,
  0x00, 0xBF, 0x60, 0x00,
  0x01, 0xFE, 0x10, 0x00,
  0x07, 0xF9, 0x00, 0x00,
  0x0D, 0xF3, 0x00, 0x00,
  0x3F, 0xD0, 0x00, 0x00,
  0x9F, 0x70, 0x00, 0x00,
  0xCF, 0x20, 0x00, 0x00,
  0x23, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0030[ 84] = { /* code 0030, DIGIT ZERO */
  0x00, 0x05, 0xBB, 0xB6, 0x00, 0x00,
  0x00, 0xAF, 0xFF, 0xFF, 0xA0, 0x00,
  0x06, 0xFF, 0xA6, 0xAF, 0xF7, 0x00,
  0x0D, 0xFC, 0x00, 0x0C, 0xFD, 0x00,
  0x2F, 0xF8, 0x00, 0x08, 0xFF, 0x20,
  0x4F, 0xF6, 0x00, 0x06, 0xFF, 0x40,
  0x4F, 0xF6, 0x00, 0x06, 0xFF, 0x40,
  0x4F, 0xF6, 0x00, 0x06, 0xFF, 0x40,
  0x2F, 0xF7, 0x00, 0x06, 0xFF, 0x30,
  0x0E, 0xFA, 0x00, 0x0A, 0xFE, 0x00,
  0x09, 0xFF, 0x40, 0x4F, 0xF9, 0x00,
  0x02, 0xEF, 0xFE, 0xFF, 0xE2, 0x00,
  0x00, 0x3C, 0xFF, 0xFC, 0x30, 0x00,
  0x00, 0x00, 0x24, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0031[ 56] = { /* code 0031, DIGIT ONE */
  0x00, 0x00, 0x2B, 0x70,
  0x00, 0x00, 0xAF, 0xC0,
  0x01, 0x49, 0xFF, 0xD0,
  0x1E, 0xFF, 0xFF, 0xD0,
  0x1B, 0xDD, 0xFF, 0xD0,
  0x00, 0x00, 0xDF, 0xD0,
  0x00, 0x00, 0xDF, 0xD0,
  0x00, 0x00, 0xDF, 0xD0,
  0x00, 0x00, 0xDF, 0xD0,
  0x00, 0x00, 0xDF, 0xD0,
  0x00, 0x00, 0xDF, 0xD0,
  0x00, 0x00, 0xDF, 0xC0,
  0x00, 0x00, 0xBF, 0xA0,
  0x00, 0x00, 0x13, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0032[ 78] = { /* code 0032, DIGIT TWO */
  0x00, 0x17, 0xBB, 0xB6, 0x00, 0x00,
  0x03, 0xEF, 0xFF, 0xFF, 0xD1, 0x00,
  0x0D, 0xFE, 0x86, 0xAF, 0xFB, 0x00,
  0x5F, 0xF6, 0x00, 0x0D, 0xFF, 0x00,
  0x4F, 0xE1, 0x00, 0x0B, 0xFF, 0x00,
  0x03, 0x20, 0x00, 0x3F, 0xFB, 0x00,
  0x00, 0x00, 0x07, 0xEF, 0xF3, 0x00,
  0x00, 0x03, 0xCF, 0xFD, 0x30, 0x00,
  0x00, 0x6F, 0xFE, 0x70, 0x00, 0x00,
  0x06, 0xFF, 0xA2, 0x00, 0x00, 0x00,
  0x2F, 0xFC, 0x44, 0x44, 0x42, 0x00,
  0x6F, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0x3E, 0xFF, 0xFF, 0xFF, 0xFD, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0033[ 70] = { /* code 0033, DIGIT THREE */
  0x00, 0x28, 0xBB, 0xA5, 0x00,
  0x05, 0xFF, 0xFF, 0xFF, 0xA0,
  0x1F, 0xFE, 0x76, 0xCF, 0xF4,
  0x2F, 0xF4, 0x00, 0x2F, 0xF7,
  0x04, 0x40, 0x00, 0x2F, 0xF5,
  0x00, 0x00, 0x5A, 0xEF, 0xB0,
  0x00, 0x00, 0xFF, 0xFF, 0x90,
  0x00, 0x00, 0x38, 0xCF, 0xF8,
  0x01, 0x00, 0x00, 0x1E, 0xFC,
  0x3F, 0xC0, 0x00, 0x0E, 0xFD,
  0x5F, 0xF8, 0x00, 0x7F, 0xF9,
  0x1D, 0xFF, 0xFE, 0xFF, 0xE2,
  0x02, 0xBF, 0xFF, 0xFB, 0x30,
  0x00, 0x01, 0x33, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0034[ 84] = { /* code 0034, DIGIT FOUR */
  0x00, 0x00, 0x00, 0x9B, 0x40, 0x00,
  0x00, 0x00, 0x07, 0xFF, 0x90, 0x00,
  0x00, 0x00, 0x4F, 0xFF, 0x90, 0x00,
  0x00, 0x02, 0xEE, 0xEF, 0x90, 0x00,
  0x00, 0x0C, 0xF4, 0xDF, 0x90, 0x00,
  0x00, 0x9F, 0x80, 0xDF, 0x90, 0x00,
  0x06, 0xFB, 0x00, 0xDF, 0x90, 0x00,
  0x3F, 0xE2, 0x00, 0xDF, 0x90, 0x00,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFE, 0x50,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x02, 0x22, 0x22, 0xDF, 0xA2, 0x00,
  0x00, 0x00, 0x00, 0xDF, 0x90, 0x00,
  0x00, 0x00, 0x00, 0xBF, 0x80, 0x00,
  0x00, 0x00, 0x00, 0x13, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0035[ 70] = { /* code 0035, DIGIT FIVE */
  0x01, 0x89, 0x99, 0x99, 0x70,
  0x07, 0xFF, 0xFF, 0xFF, 0xF3,
  0x0A, 0xFD, 0xBB, 0xBB, 0x90,
  0x0C, 0xF5, 0x00, 0x00, 0x00,
  0x0F, 0xF3, 0x46, 0x51, 0x00,
  0x3F, 0xFD, 0xFF, 0xFE, 0x60,
  0x3F, 0xFE, 0xAB, 0xFF, 0xF3,
  0x06, 0x60, 0x00, 0x4F, 0xFA,
  0x00, 0x00, 0x00, 0x0D, 0xFB,
  0x17, 0x40, 0x00, 0x0E, 0xFB,
  0x5F, 0xF4, 0x00, 0x8F, 0xF7,
  0x3F, 0xFF, 0xDE, 0xFF, 0xD1,
  0x04, 0xCF, 0xFF, 0xFA, 0x10,
  0x00, 0x02, 0x43, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0036[ 84] = { /* code 0036, DIGIT SIX */
  0x00, 0x04, 0x9B, 0xB9, 0x30, 0x00,
  0x00, 0x8F, 0xFF, 0xFF, 0xF4, 0x00,
  0x04, 0xFF, 0x93, 0x4E, 0xFB, 0x00,
  0x0B, 0xFC, 0x00, 0x02, 0x93, 0x00,
  0x1F, 0xF7, 0x02, 0x31, 0x00, 0x00,
  0x3F, 0xF9, 0xDF, 0xFF, 0x91, 0x00,
  0x4F, 0xFF, 0xEB, 0xDF, 0xFA, 0x00,
  0x4F, 0xFD, 0x10, 0x0B, 0xFF, 0x20,
  0x3F, 0xF8, 0x00, 0x06, 0xFF, 0x40,
  0x1F, 0xF8, 0x00, 0x06, 0xFF, 0x40,
  0x0B, 0xFE, 0x20, 0x1C, 0xFF, 0x10,
  0x03, 0xEF, 0xFD, 0xEF, 0xF7, 0x00,
  0x00, 0x3C, 0xFF, 0xFE, 0x70, 0x00,
  0x00, 0x00, 0x24, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0037[ 70] = { /* code 0037, DIGIT SEVEN */
  0x49, 0x99, 0x99, 0x99, 0x94,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFD,
  0x5B, 0xBB, 0xBB, 0xBF, 0xF9,
  0x00, 0x00, 0x00, 0x9F, 0xC0,
  0x00, 0x00, 0x05, 0xFE, 0x20,
  0x00, 0x00, 0x1E, 0xF6, 0x00,
  0x00, 0x00, 0x9F, 0xD0, 0x00,
  0x00, 0x02, 0xFF, 0x70, 0x00,
  0x00, 0x08, 0xFF, 0x10, 0x00,
  0x00, 0x0E, 0xFB, 0x00, 0x00,
  0x00, 0x4F, 0xF7, 0x00, 0x00,
  0x00, 0x7F, 0xF3, 0x00, 0x00,
  0x00, 0x6F, 0xD0, 0x00, 0x00,
  0x00, 0x03, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0038[ 84] = { /* code 0038, DIGIT EIGHT */
  0x00, 0x17, 0xBB, 0xB8, 0x10, 0x00,
  0x02, 0xEF, 0xFF, 0xFF, 0xE3, 0x00,
  0x0B, 0xFE, 0x62, 0x6E, 0xFB, 0x00,
  0x0D, 0xFA, 0x00, 0x0A, 0xFF, 0x00,
  0x0C, 0xFB, 0x00, 0x0B, 0xFC, 0x00,
  0x04, 0xFF, 0xB8, 0xBF, 0xF4, 0x00,
  0x02, 0xBF, 0xFF, 0xFF, 0xB2, 0x00,
  0x0C, 0xFE, 0x64, 0x6E, 0xFD, 0x00,
  0x3F, 0xF7, 0x00, 0x07, 0xFF, 0x30,
  0x4F, 0xF6, 0x00, 0x06, 0xFF, 0x40,
  0x2F, 0xFC, 0x10, 0x1B, 0xFF, 0x20,
  0x09, 0xFF, 0xED, 0xEF, 0xF9, 0x00,
  0x00, 0x7E, 0xFF, 0xFE, 0x80, 0x00,
  0x00, 0x00, 0x24, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0039[ 84] = { /* code 0039, DIGIT NINE */
  0x00, 0x17, 0xBB, 0xB6, 0x00, 0x00,
  0x03, 0xEF, 0xFF, 0xFF, 0xB0, 0x00,
  0x0C, 0xFE, 0x74, 0x8F, 0xF8, 0x00,
  0x3F, 0xF8, 0x00, 0x0A, 0xFE, 0x00,
  0x4F, 0xF6, 0x00, 0x07, 0xFF, 0x30,
  0x3F, 0xF7, 0x00, 0x0A, 0xFF, 0x40,
  0x0D, 0xFF, 0x74, 0x9F, 0xFF, 0x40,
  0x03, 0xEF, 0xFF, 0xFD, 0xFF, 0x40,
  0x00, 0x17, 0x99, 0x56, 0xFF, 0x20,
  0x00, 0x30, 0x00, 0x09, 0xFD, 0x00,
  0x09, 0xFA, 0x00, 0x3F, 0xF8, 0x00,
  0x08, 0xFF, 0xDB, 0xFF, 0xD1, 0x00,
  0x01, 0x9F, 0xFF, 0xFA, 0x20, 0x00,
  0x00, 0x01, 0x33, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_003A[ 22] = { /* code 003A, COLON */
  0x05, 0x30,
  0x9F, 0xF2,
  0x9F, 0xF2,
  0x17, 0x40,
  0x00, 0x00,
  0x00, 0x00,
  0x00, 0x00,
  0x3C, 0x90,
  0xBF, 0xF3,
  0x6F, 0xD1,
  0x01, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_003B[ 26] = { /* code 003B, SEMICOLON */
  0x05, 0x30,
  0x9F, 0xF2,
  0x9F, 0xF2,
  0x17, 0x40,
  0x00, 0x00,
  0x00, 0x00,
  0x00, 0x00,
  0x3C, 0x90,
  0xBF, 0xF5,
  0x6F, 0xF7,
  0x01, 0xE5,
  0x4C, 0xC0,
  0x67, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_003C[ 60] = { /* code 003C, LESS-THAN SIGN */
  0x00, 0x00, 0x00, 0x01, 0x8B, 0x20,
  0x00, 0x00, 0x02, 0x9F, 0xFF, 0x50,
  0x00, 0x03, 0xAF, 0xFF, 0xC6, 0x00,
  0x05, 0xCF, 0xFF, 0xB4, 0x00, 0x00,
  0x7F, 0xFF, 0x92, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0xB5, 0x00, 0x00, 0x00,
  0x02, 0x9F, 0xFF, 0xD7, 0x10, 0x00,
  0x00, 0x01, 0x7E, 0xFF, 0xF8, 0x10,
  0x00, 0x00, 0x00, 0x7D, 0xFF, 0x50,
  0x00, 0x00, 0x00, 0x00, 0x57, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_003D[ 48] = { /* code 003D, EQUALS SIGN */
  0x04, 0x44, 0x44, 0x44, 0x43, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x5D, 0xDD, 0xDD, 0xDD, 0xDC, 0x30,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0x50,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0x50,
  0x02, 0x22, 0x22, 0x22, 0x21, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_003E[ 60] = { /* code 003E, GREATER-THAN SIGN */
  0x4B, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x8F, 0xFE, 0x81, 0x00, 0x00, 0x00,
  0x17, 0xEF, 0xFF, 0x92, 0x00, 0x00,
  0x00, 0x05, 0xCF, 0xFF, 0xA3, 0x00,
  0x00, 0x00, 0x03, 0xAF, 0xFF, 0x40,
  0x00, 0x00, 0x06, 0xDF, 0xFE, 0x30,
  0x00, 0x28, 0xEF, 0xFE, 0x71, 0x00,
  0x1A, 0xFF, 0xFD, 0x60, 0x00, 0x00,
  0x9F, 0xFC, 0x50, 0x00, 0x00, 0x00,
  0x27, 0x30, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_003F[ 75] = { /* code 003F, QUESTION MARK */
  0x00, 0x00, 0x22, 0x00, 0x00,
  0x00, 0x7E, 0xFF, 0xFA, 0x20,
  0x09, 0xFF, 0xEE, 0xFF, 0xE1,
  0x2F, 0xFA, 0x00, 0x7F, 0xF7,
  0x2F, 0xE2, 0x00, 0x3F, 0xF7,
  0x02, 0x20, 0x00, 0xAF, 0xF5,
  0x00, 0x00, 0x1B, 0xFF, 0xA0,
  0x00, 0x00, 0xCF, 0xF7, 0x00,
  0x00, 0x05, 0xFF, 0x50, 0x00,
  0x00, 0x02, 0xEC, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x02, 0xBA, 0x10, 0x00,
  0x00, 0x08, 0xFF, 0x50, 0x00,
  0x00, 0x04, 0xFE, 0x20, 0x00,
  0x00, 0x00, 0x11, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0040[105] = { /* code 0040, COMMERCIAL AT */
  0x00, 0x00, 0x00, 0x34, 0x31, 0x00, 0x00,
  0x00, 0x02, 0x9E, 0xFF, 0xFF, 0xA2, 0x00,
  0x00, 0x3E, 0xFB, 0x76, 0x7A, 0xFE, 0x50,
  0x02, 0xEF, 0x50, 0x00, 0x00, 0x3E, 0xE2,
  0x0B, 0xF6, 0x07, 0xEE, 0x96, 0xB5, 0xF8,
  0x2F, 0xD0, 0x7F, 0xFF, 0xFF, 0xF0, 0xEC,
  0x5F, 0x91, 0xEF, 0x40, 0x8F, 0xC0, 0xBD,
  0x7F, 0x74, 0xFA, 0x00, 0x4F, 0x90, 0xCB,
  0x6F, 0x85, 0xFB, 0x00, 0x6F, 0x71, 0xF7,
  0x3F, 0xC2, 0xFF, 0x98, 0xEF, 0xAC, 0xD1,
  0x0C, 0xF4, 0x9F, 0xFF, 0xEF, 0xFB, 0x20,
  0x03, 0xFE, 0x45, 0x73, 0x37, 0x57, 0x00,
  0x00, 0x5E, 0xFB, 0x66, 0x69, 0xEE, 0x10,
  0x00, 0x02, 0x9F, 0xFF, 0xFF, 0xA2, 0x00,
  0x00, 0x00, 0x01, 0x34, 0x31, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0041[ 90] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x00, 0x02, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x9F, 0xFB, 0x00, 0x00,
  0x00, 0x01, 0xFF, 0xFF, 0x30, 0x00,
  0x00, 0x06, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0x0C, 0xFD, 0xCF, 0xE0, 0x00,
  0x00, 0x3F, 0xF8, 0x7F, 0xF4, 0x00,
  0x00, 0x8F, 0xF3, 0x2F, 0xFA, 0x00,
  0x00, 0xEF, 0xD0, 0x0D, 0xFF, 0x10,
  0x04, 0xFF, 0x80, 0x08, 0xFF, 0x60,
  0x0A, 0xFF, 0xCB, 0xBC, 0xFF, 0xB0,
  0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2,
  0x7F, 0xF9, 0x44, 0x44, 0x9F, 0xF7,
  0xCF, 0xF3, 0x00, 0x00, 0x3F, 0xFC,
  0xAF, 0xC0, 0x00, 0x00, 0x0C, 0xFA,
  0x03, 0x10, 0x00, 0x00, 0x01, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0042[ 78] = { /* code 0042, LATIN CAPITAL LETTER B */
  0x3D, 0xFF, 0xFF, 0xED, 0xA3, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x30,
  0x9F, 0xF8, 0x44, 0x4C, 0xFF, 0xA0,
  0x9F, 0xF6, 0x00, 0x04, 0xFF, 0xB0,
  0x9F, 0xF6, 0x00, 0x06, 0xFF, 0x70,
  0x9F, 0xFB, 0x99, 0xBF, 0xFB, 0x10,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFB, 0x20,
  0x9F, 0xF8, 0x44, 0x4A, 0xFF, 0xD0,
  0x9F, 0xF6, 0x00, 0x00, 0xCF, 0xF2,
  0x9F, 0xF6, 0x00, 0x00, 0xCF, 0xF3,
  0x9F, 0xF8, 0x44, 0x5A, 0xFF, 0xE0,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x4E, 0xFF, 0xFF, 0xFE, 0xB4, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0043[ 90] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x00, 0x13, 0x43, 0x00, 0x00,
  0x00, 0x2A, 0xFF, 0xFF, 0xE7, 0x00,
  0x03, 0xEF, 0xFF, 0xFF, 0xFF, 0xB0,
  0x1E, 0xFF, 0xB3, 0x25, 0xEF, 0xF6,
  0x7F, 0xFB, 0x00, 0x00, 0x4F, 0xF7,
  0xCF, 0xF3, 0x00, 0x00, 0x03, 0x61,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xF3, 0x00, 0x00, 0x05, 0x71,
  0x9F, 0xFA, 0x00, 0x00, 0x4F, 0xF7,
  0x3F, 0xFF, 0x93, 0x25, 0xEF, 0xF5,
  0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0,
  0x00, 0x4C, 0xFF, 0xFF, 0xE7, 0x00,
  0x00, 0x00, 0x24, 0x43, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0044[ 78] = { /* code 0044, LATIN CAPITAL LETTER D */
  0x3D, 0xFF, 0xFF, 0xEC, 0x71, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFE, 0x30,
  0x9F, 0xF9, 0x66, 0x7D, 0xFF, 0xD0,
  0x9F, 0xF6, 0x00, 0x01, 0xCF, 0xF6,
  0x9F, 0xF6, 0x00, 0x00, 0x5F, 0xFA,
  0x9F, 0xF6, 0x00, 0x00, 0x2F, 0xFD,
  0x9F, 0xF6, 0x00, 0x00, 0x2F, 0xFD,
  0x9F, 0xF6, 0x00, 0x00, 0x2F, 0xFC,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xFA,
  0x9F, 0xF6, 0x00, 0x01, 0xDF, 0xF5,
  0x9F, 0xF9, 0x66, 0x7D, 0xFF, 0xC0,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFD, 0x20,
  0x4E, 0xFF, 0xFF, 0xFC, 0x71, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0045[ 78] = { /* code 0045, LATIN CAPITAL LETTER E */
  0x3D, 0xFF, 0xFF, 0xFF, 0xFE, 0x50,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x9F, 0xF9, 0x66, 0x66, 0x65, 0x10,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xFD, 0xBB, 0xBB, 0xB6, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFB, 0x00,
  0x9F, 0xFA, 0x77, 0x77, 0x73, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF9, 0x66, 0x66, 0x65, 0x10,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0,
  0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0x70
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0046[ 84] = { /* code 0046, LATIN CAPITAL LETTER F */
  0x3D, 0xFF, 0xFF, 0xFF, 0xFB, 0x10,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0x9F, 0xF9, 0x66, 0x66, 0x64, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xFB, 0x99, 0x99, 0x70, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xF3, 0x00,
  0x9F, 0xFD, 0xBB, 0xBB, 0x90, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x5F, 0xF3, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0047[105] = { /* code 0047, LATIN CAPITAL LETTER G */
  0x00, 0x00, 0x03, 0x43, 0x10, 0x00, 0x00,
  0x00, 0x2A, 0xFF, 0xFF, 0xFA, 0x20, 0x00,
  0x03, 0xEF, 0xFF, 0xFF, 0xFF, 0xE2, 0x00,
  0x1E, 0xFF, 0xB4, 0x23, 0xAF, 0xFA, 0x00,
  0x8F, 0xFB, 0x00, 0x00, 0x0B, 0xF8, 0x00,
  0xCF, 0xF3, 0x00, 0x00, 0x00, 0x20, 0x00,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xD0, 0x00, 0x5D, 0xDD, 0xDD, 0x30,
  0xFF, 0xE0, 0x00, 0x8F, 0xFF, 0xFF, 0x60,
  0xDF, 0xF2, 0x00, 0x15, 0x69, 0xFF, 0x60,
  0x9F, 0xF9, 0x00, 0x00, 0x0B, 0xFF, 0x60,
  0x2F, 0xFF, 0x82, 0x02, 0x9F, 0xFF, 0x60,
  0x06, 0xFF, 0xFF, 0xFF, 0xFE, 0xEF, 0x60,
  0x00, 0x4C, 0xFF, 0xFF, 0xD3, 0x9F, 0x50,
  0x00, 0x00, 0x24, 0x43, 0x00, 0x03, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0048[ 90] = { /* code 0048, LATIN CAPITAL LETTER H */
  0x02, 0x10, 0x00, 0x00, 0x01, 0x20,
  0x6F, 0xE2, 0x00, 0x00, 0x2E, 0xF6,
  0x9F, 0xF5, 0x00, 0x00, 0x5F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9,
  0x9F, 0xFA, 0x77, 0x77, 0xAF, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF5, 0x00, 0x00, 0x5F, 0xF9,
  0x6F, 0xE2, 0x00, 0x00, 0x2E, 0xF7,
  0x03, 0x20, 0x00, 0x00, 0x02, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0049[ 30] = { /* code 0049, LATIN CAPITAL LETTER I */
  0x02, 0x10,
  0x6F, 0xE2,
  0x9F, 0xF5,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF6,
  0x9F, 0xF5,
  0x6F, 0xE2,
  0x03, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_004A[ 75] = { /* code 004A, LATIN CAPITAL LETTER J */
  0x00, 0x00, 0x00, 0x02, 0x00,
  0x00, 0x00, 0x00, 0xBF, 0xB0,
  0x00, 0x00, 0x00, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0xFF, 0xF0,
  0x01, 0x00, 0x00, 0xFF, 0xF0,
  0x5F, 0xC0, 0x00, 0xFF, 0xF0,
  0x9F, 0xF1, 0x00, 0xFF, 0xE0,
  0x9F, 0xF6, 0x07, 0xFF, 0xB0,
  0x3F, 0xFF, 0xFF, 0xFF, 0x50,
  0x05, 0xEF, 0xFF, 0xF7, 0x00,
  0x00, 0x03, 0x43, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_004B[ 90] = { /* code 004B, LATIN CAPITAL LETTER K */
  0x02, 0x10, 0x00, 0x00, 0x11, 0x00,
  0x6F, 0xE2, 0x00, 0x02, 0xEF, 0x50,
  0x9F, 0xF5, 0x00, 0x1D, 0xFF, 0x60,
  0x9F, 0xF6, 0x01, 0xDF, 0xF8, 0x00,
  0x9F, 0xF6, 0x1B, 0xFF, 0x90, 0x00,
  0x9F, 0xF6, 0xAF, 0xFA, 0x00, 0x00,
  0x9F, 0xFE, 0xFF, 0xF4, 0x00, 0x00,
  0x9F, 0xFF, 0xFF, 0xFD, 0x00, 0x00,
  0x9F, 0xFF, 0xAD, 0xFF, 0x80, 0x00,
  0x9F, 0xFA, 0x03, 0xFF, 0xF3, 0x00,
  0x9F, 0xF6, 0x00, 0x9F, 0xFD, 0x00,
  0x9F, 0xF6, 0x00, 0x1D, 0xFF, 0x70,
  0x9F, 0xF5, 0x00, 0x04, 0xFF, 0xF1,
  0x6F, 0xE2, 0x00, 0x00, 0x9F, 0xE1,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_004C[ 70] = { /* code 004C, LATIN CAPITAL LETTER L */
  0x02, 0x10, 0x00, 0x00, 0x00,
  0x6F, 0xE2, 0x00, 0x00, 0x00,
  0x9F, 0xF5, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00,
  0x9F, 0xFA, 0x77, 0x77, 0x72,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFB,
  0x4E, 0xFF, 0xFF, 0xFF, 0xF7
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_004D[105] = { /* code 004D, LATIN CAPITAL LETTER M */
  0x02, 0x31, 0x00, 0x00, 0x00, 0x13, 0x20,
  0x7F, 0xFE, 0x30, 0x00, 0x02, 0xEF, 0xF7,
  0xBF, 0xFF, 0x80, 0x00, 0x08, 0xFF, 0xF9,
  0xBF, 0xFF, 0xD0, 0x00, 0x0C, 0xFF, 0xF9,
  0xBF, 0xFF, 0xF2, 0x00, 0x2F, 0xFF, 0xF9,
  0xBF, 0xFB, 0xF7, 0x00, 0x7F, 0xBF, 0xF9,
  0xBF, 0xF7, 0xFC, 0x00, 0xBF, 0x7F, 0xF9,
  0xBF, 0xF2, 0xFF, 0x21, 0xFF, 0x2F, 0xF9,
  0xBF, 0xF0, 0xCF, 0x65, 0xFC, 0x0F, 0xF9,
  0xBF, 0xF0, 0x8F, 0xBA, 0xF7, 0x0F, 0xF9,
  0xBF, 0xF0, 0x3F, 0xFE, 0xF3, 0x0F, 0xF9,
  0xBF, 0xF0, 0x0D, 0xFF, 0xD0, 0x0F, 0xF9,
  0xBF, 0xF0, 0x08, 0xFF, 0x80, 0x0F, 0xF9,
  0x8F, 0xB0, 0x03, 0xFF, 0x30, 0x0B, 0xF7,
  0x03, 0x10, 0x00, 0x22, 0x00, 0x01, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_004E[ 90] = { /* code 004E, LATIN CAPITAL LETTER N */
  0x01, 0x20, 0x00, 0x00, 0x01, 0x20,
  0x6F, 0xF8, 0x00, 0x00, 0x1E, 0xF8,
  0xAF, 0xFF, 0x30, 0x00, 0x2F, 0xFB,
  0xBF, 0xFF, 0xC0, 0x00, 0x2F, 0xFB,
  0xBF, 0xFF, 0xF7, 0x00, 0x2F, 0xFB,
  0xBF, 0xFC, 0xFE, 0x20, 0x2F, 0xFB,
  0xBF, 0xF4, 0xEF, 0xB0, 0x2F, 0xFB,
  0xBF, 0xF2, 0x7F, 0xF5, 0x2F, 0xFB,
  0xBF, 0xF2, 0x0C, 0xFD, 0x3F, 0xFB,
  0xBF, 0xF2, 0x03, 0xFF, 0xAF, 0xFB,
  0xBF, 0xF2, 0x00, 0x8F, 0xFF, 0xFB,
  0xBF, 0xF2, 0x00, 0x0D, 0xFF, 0xFB,
  0xBF, 0xF2, 0x00, 0x04, 0xFF, 0xFA,
  0x8F, 0xE1, 0x00, 0x00, 0x9F, 0xF6,
  0x03, 0x10, 0x00, 0x00, 0x03, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_004F[105] = { /* code 004F, LATIN CAPITAL LETTER O */
  0x00, 0x00, 0x01, 0x34, 0x30, 0x00, 0x00,
  0x00, 0x03, 0xBF, 0xFF, 0xFE, 0x91, 0x00,
  0x00, 0x6F, 0xFF, 0xFF, 0xFF, 0xFE, 0x20,
  0x03, 0xFF, 0xF9, 0x32, 0x4B, 0xFF, 0xD0,
  0x0B, 0xFF, 0x80, 0x00, 0x00, 0xCF, 0xF6,
  0x0F, 0xFF, 0x10, 0x00, 0x00, 0x5F, 0xFA,
  0x2F, 0xFC, 0x00, 0x00, 0x00, 0x2F, 0xFD,
  0x4F, 0xFB, 0x00, 0x00, 0x00, 0x2F, 0xFD,
  0x2F, 0xFC, 0x00, 0x00, 0x00, 0x2F, 0xFC,
  0x0F, 0xFF, 0x10, 0x00, 0x00, 0x5F, 0xF9,
  0x0A, 0xFF, 0x70, 0x00, 0x00, 0xCF, 0xF4,
  0x03, 0xFF, 0xF8, 0x22, 0x3B, 0xFF, 0xC0,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFD, 0x20,
  0x00, 0x05, 0xDF, 0xFF, 0xFF, 0x91, 0x00,
  0x00, 0x00, 0x02, 0x44, 0x31, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0050[ 84] = { /* code 0050, LATIN CAPITAL LETTER P */
  0x3D, 0xFF, 0xFF, 0xFD, 0xA3, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0x9F, 0xF9, 0x66, 0x6B, 0xFF, 0xC0,
  0x9F, 0xF6, 0x00, 0x00, 0xEF, 0xF1,
  0x9F, 0xF6, 0x00, 0x00, 0xEF, 0xF1,
  0x9F, 0xF6, 0x00, 0x18, 0xFF, 0xD0,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x9F, 0xFF, 0xFF, 0xFF, 0xE6, 0x00,
  0x9F, 0xF8, 0x44, 0x43, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xF5, 0x00, 0x00, 0x00, 0x00,
  0x6F, 0xE2, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0051[105] = { /* code 0051, LATIN CAPITAL LETTER Q */
  0x00, 0x00, 0x01, 0x34, 0x30, 0x00, 0x00,
  0x00, 0x03, 0xBF, 0xFF, 0xFE, 0x91, 0x00,
  0x00, 0x6F, 0xFF, 0xFF, 0xFF, 0xFE, 0x20,
  0x03, 0xFF, 0xF9, 0x32, 0x4B, 0xFF, 0xD0,
  0x0B, 0xFF, 0x80, 0x00, 0x00, 0xCF, 0xF6,
  0x0F, 0xFF, 0x10, 0x00, 0x00, 0x5F, 0xFA,
  0x2F, 0xFC, 0x00, 0x00, 0x00, 0x2F, 0xFD,
  0x4F, 0xFB, 0x00, 0x00, 0x00, 0x2F, 0xFD,
  0x2F, 0xFC, 0x00, 0x00, 0x00, 0x2F, 0xFC,
  0x0F, 0xFF, 0x10, 0x04, 0xB2, 0x5F, 0xF9,
  0x0A, 0xFF, 0x70, 0x09, 0xFE, 0xCF, 0xF4,
  0x03, 0xFF, 0xF8, 0x23, 0xCF, 0xFF, 0xC0,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x00, 0x05, 0xDF, 0xFF, 0xFF, 0xBF, 0xF4,
  0x00, 0x00, 0x02, 0x44, 0x30, 0x08, 0xF4
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0052[ 84] = { /* code 0052, LATIN CAPITAL LETTER R */
  0x3D, 0xFF, 0xFF, 0xFE, 0xC7, 0x10,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0,
  0x9F, 0xF9, 0x66, 0x67, 0xEF, 0xF4,
  0x9F, 0xF6, 0x00, 0x00, 0x9F, 0xF7,
  0x9F, 0xF6, 0x00, 0x00, 0x8F, 0xF5,
  0x9F, 0xF8, 0x44, 0x46, 0xEF, 0xD0,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFC, 0x20,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x9F, 0xF6, 0x00, 0x06, 0xFF, 0xE0,
  0x9F, 0xF6, 0x00, 0x00, 0xDF, 0xF0,
  0x9F, 0xF6, 0x00, 0x00, 0xBF, 0xF1,
  0x9F, 0xF5, 0x00, 0x00, 0xBF, 0xF5,
  0x6F, 0xE2, 0x00, 0x00, 0x5F, 0xF4,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0053[ 90] = { /* code 0053, LATIN CAPITAL LETTER S */
  0x00, 0x00, 0x24, 0x41, 0x00, 0x00,
  0x00, 0x5D, 0xFF, 0xFF, 0xC4, 0x00,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x0E, 0xFE, 0x40, 0x16, 0xEF, 0xF0,
  0x2F, 0xFA, 0x00, 0x00, 0x4D, 0x90,
  0x1F, 0xFF, 0x72, 0x00, 0x00, 0x00,
  0x0B, 0xFF, 0xFF, 0xDA, 0x61, 0x00,
  0x01, 0xAF, 0xFF, 0xFF, 0xFE, 0x50,
  0x00, 0x01, 0x69, 0xDF, 0xFF, 0xF2,
  0x01, 0x10, 0x00, 0x02, 0xCF, 0xF7,
  0x2E, 0xF4, 0x00, 0x00, 0x6F, 0xF7,
  0x2F, 0xFE, 0x40, 0x02, 0xDF, 0xF5,
  0x09, 0xFF, 0xFE, 0xDF, 0xFF, 0xC0,
  0x00, 0x6D, 0xFF, 0xFF, 0xF9, 0x10,
  0x00, 0x00, 0x34, 0x43, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0054[ 84] = { /* code 0054, LATIN CAPITAL LETTER T */
  0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB,
  0x27, 0x77, 0xBF, 0xFB, 0x77, 0x72,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x4F, 0xF4, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0055[ 90] = { /* code 0055, LATIN CAPITAL LETTER U */
  0x02, 0x10, 0x00, 0x00, 0x01, 0x20,
  0x6F, 0xE2, 0x00, 0x00, 0x2E, 0xF6,
  0x9F, 0xF5, 0x00, 0x00, 0x5F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x9F, 0xF6, 0x00, 0x00, 0x6F, 0xF9,
  0x7F, 0xF7, 0x00, 0x00, 0x7F, 0xF8,
  0x3F, 0xFE, 0x62, 0x26, 0xEF, 0xF3,
  0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0,
  0x00, 0x7E, 0xFF, 0xFF, 0xE7, 0x00,
  0x00, 0x00, 0x34, 0x43, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0056[ 90] = { /* code 0056, LATIN CAPITAL LETTER V */
  0x02, 0x00, 0x00, 0x00, 0x01, 0x10,
  0xAF, 0xC0, 0x00, 0x00, 0x2E, 0xF5,
  0xCF, 0xF4, 0x00, 0x00, 0x7F, 0xF7,
  0x7F, 0xF9, 0x00, 0x00, 0xCF, 0xF2,
  0x2F, 0xFD, 0x00, 0x02, 0xFF, 0xB0,
  0x0B, 0xFF, 0x30, 0x07, 0xFF, 0x60,
  0x06, 0xFF, 0x70, 0x0B, 0xFF, 0x10,
  0x01, 0xFF, 0xB0, 0x1F, 0xFA, 0x00,
  0x00, 0xAF, 0xF1, 0x6F, 0xF5, 0x00,
  0x00, 0x5F, 0xF6, 0xBF, 0xE0, 0x00,
  0x00, 0x0E, 0xFB, 0xFF, 0x80, 0x00,
  0x00, 0x09, 0xFF, 0xFF, 0x30, 0x00,
  0x00, 0x04, 0xFF, 0xFD, 0x00, 0x00,
  0x00, 0x00, 0xCF, 0xF6, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0057[135] = { /* code 0057, LATIN CAPITAL LETTER W */
  0x02, 0x00, 0x00, 0x01, 0x20, 0x00, 0x00, 0x11, 0x00,
  0xAF, 0xC0, 0x00, 0x2E, 0xFB, 0x00, 0x01, 0xEF, 0x50,
  0xCF, 0xF1, 0x00, 0x6F, 0xFF, 0x10, 0x05, 0xFF, 0x70,
  0x8F, 0xF4, 0x00, 0x9F, 0xFF, 0x40, 0x08, 0xFF, 0x30,
  0x4F, 0xF7, 0x00, 0xCF, 0xFF, 0x80, 0x0B, 0xFE, 0x00,
  0x1F, 0xFA, 0x01, 0xFF, 0xAF, 0xB0, 0x0E, 0xFB, 0x00,
  0x0C, 0xFD, 0x05, 0xFE, 0x4F, 0xF0, 0x3F, 0xF7, 0x00,
  0x08, 0xFF, 0x18, 0xFA, 0x0F, 0xF3, 0x6F, 0xF3, 0x00,
  0x04, 0xFF, 0x4B, 0xF7, 0x0B, 0xF7, 0x9F, 0xE0, 0x00,
  0x00, 0xFF, 0x8F, 0xF3, 0x08, 0xFA, 0xCF, 0xA0, 0x00,
  0x00, 0xBF, 0xDF, 0xE0, 0x04, 0xFE, 0xFF, 0x70, 0x00,
  0x00, 0x7F, 0xFF, 0xA0, 0x01, 0xFF, 0xFF, 0x20, 0x00,
  0x00, 0x4F, 0xFF, 0x70, 0x00, 0xCF, 0xFE, 0x00, 0x00,
  0x00, 0x0C, 0xFE, 0x20, 0x00, 0x6F, 0xF8, 0x00, 0x00,
  0x00, 0x01, 0x41, 0x00, 0x00, 0x03, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0058[ 90] = { /* code 0058, LATIN CAPITAL LETTER X */
  0x01, 0x10, 0x00, 0x00, 0x11, 0x00,
  0x3F, 0xF3, 0x00, 0x04, 0xFF, 0x20,
  0x5F, 0xFC, 0x00, 0x0D, 0xFF, 0x30,
  0x0D, 0xFF, 0x60, 0x8F, 0xFA, 0x00,
  0x03, 0xFF, 0xE3, 0xFF, 0xD1, 0x00,
  0x00, 0x8F, 0xFF, 0xFF, 0x40, 0x00,
  0x00, 0x0D, 0xFF, 0xF9, 0x00, 0x00,
  0x00, 0x07, 0xFF, 0xF3, 0x00, 0x00,
  0x00, 0x2F, 0xFF, 0xFC, 0x00, 0x00,
  0x00, 0xCF, 0xFD, 0xFF, 0x70, 0x00,
  0x07, 0xFF, 0xB2, 0xFF, 0xF3, 0x00,
  0x3F, 0xFF, 0x20, 0x8F, 0xFC, 0x00,
  0xAF, 0xF7, 0x00, 0x0D, 0xFF, 0x60,
  0xAF, 0xC0, 0x00, 0x05, 0xFF, 0x40,
  0x03, 0x10, 0x00, 0x00, 0x32, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0059[ 90] = { /* code 0059, LATIN CAPITAL LETTER Y */
  0x02, 0x10, 0x00, 0x00, 0x02, 0x00,
  0x9F, 0xC0, 0x00, 0x00, 0xBF, 0x90,
  0xBF, 0xF7, 0x00, 0x05, 0xFF, 0xC0,
  0x4F, 0xFE, 0x10, 0x0D, 0xFF, 0x40,
  0x0A, 0xFF, 0x80, 0x6F, 0xFA, 0x00,
  0x01, 0xEF, 0xF2, 0xEF, 0xE2, 0x00,
  0x00, 0x7F, 0xFE, 0xFF, 0x70, 0x00,
  0x00, 0x0C, 0xFF, 0xFD, 0x00, 0x00,
  0x00, 0x03, 0xFF, 0xF4, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xCF, 0xC0, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_005A[ 78] = { /* code 005A, LATIN CAPITAL LETTER Z */
  0x09, 0xFF, 0xFF, 0xFF, 0xFF, 0xE6,
  0x0E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA,
  0x03, 0x77, 0x77, 0x7B, 0xFF, 0xF4,
  0x00, 0x00, 0x00, 0x3E, 0xFF, 0x60,
  0x00, 0x00, 0x01, 0xDF, 0xF8, 0x00,
  0x00, 0x00, 0x0C, 0xFF, 0xB0, 0x00,
  0x00, 0x00, 0xAF, 0xFC, 0x10, 0x00,
  0x00, 0x07, 0xFF, 0xE2, 0x00, 0x00,
  0x00, 0x5F, 0xFF, 0x30, 0x00, 0x00,
  0x03, 0xFF, 0xF5, 0x00, 0x00, 0x00,
  0x2E, 0xFF, 0xD7, 0x77, 0x77, 0x72,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB,
  0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_005B[ 51] = { /* code 005B, LEFT SQUARE BRACKET */
  0x3D, 0xFF, 0xE2,
  0x7F, 0xFF, 0xE2,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF0, 0x00,
  0x7F, 0xF9, 0x91,
  0x4F, 0xFF, 0xF3,
  0x03, 0x66, 0x40
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_005C[ 60] = { /* code 005C, REVERSE SOLIDUS */
  0x12, 0x00, 0x00, 0x00,
  0xCF, 0x10, 0x00, 0x00,
  0x9F, 0x70, 0x00, 0x00,
  0x4F, 0xC0, 0x00, 0x00,
  0x0D, 0xF3, 0x00, 0x00,
  0x07, 0xF9, 0x00, 0x00,
  0x02, 0xFE, 0x00, 0x00,
  0x00, 0xBF, 0x50, 0x00,
  0x00, 0x5F, 0xB0, 0x00,
  0x00, 0x0E, 0xF2, 0x00,
  0x00, 0x09, 0xF7, 0x00,
  0x00, 0x03, 0xFD, 0x00,
  0x00, 0x00, 0xCF, 0x40,
  0x00, 0x00, 0x6F, 0x70,
  0x00, 0x00, 0x04, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_005D[ 51] = { /* code 005D, RIGHT SQUARE BRACKET */
  0xBF, 0xFE, 0x60,
  0xCF, 0xFF, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x00, 0x9F, 0xB0,
  0x79, 0xDF, 0xB0,
  0xEF, 0xFF, 0x90,
  0x25, 0x64, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_005E[ 40] = { /* code 005E, CIRCUMFLEX ACCENT */
  0x00, 0x03, 0x72, 0x00, 0x00,
  0x00, 0x0E, 0xFB, 0x00, 0x00,
  0x00, 0x7F, 0xFF, 0x50, 0x00,
  0x01, 0xEF, 0xAF, 0xD0, 0x00,
  0x09, 0xFB, 0x0D, 0xF6, 0x00,
  0x2F, 0xF3, 0x06, 0xFE, 0x10,
  0xAF, 0x90, 0x00, 0xCF, 0x70,
  0x69, 0x10, 0x00, 0x39, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_005F[ 12] = { /* code 005F, LOW LINE */
  0x49, 0x99, 0x99, 0x99, 0x99, 0x40,
  0x14, 0x44, 0x44, 0x44, 0x44, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0060[  8] = { /* code 0060, GRAVE ACCENT */
  0x78, 0x00,
  0xEF, 0xC2,
  0x3C, 0xFE,
  0x00, 0x78
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0061[ 55] = { /* code 0061, LATIN SMALL LETTER A */
  0x00, 0x05, 0x99, 0x96, 0x10,
  0x02, 0xDF, 0xFF, 0xFF, 0xE3,
  0x09, 0xFE, 0x74, 0x8F, 0xFB,
  0x04, 0xB3, 0x00, 0x0E, 0xFB,
  0x00, 0x15, 0x79, 0xCF, 0xFB,
  0x06, 0xFF, 0xFF, 0xDE, 0xFB,
  0x2F, 0xFC, 0x41, 0x0D, 0xFB,
  0x4F, 0xF6, 0x00, 0x3F, 0xFB,
  0x2F, 0xFE, 0x89, 0xEE, 0xFD,
  0x05, 0xEF, 0xFE, 0x67, 0xFE,
  0x00, 0x13, 0x30, 0x00, 0x31
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0062[ 75] = { /* code 0062, LATIN SMALL LETTER B */
  0x02, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0x80, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xFF, 0x92, 0x89, 0x71, 0x00,
  0xFF, 0xCE, 0xFF, 0xFE, 0x20,
  0xFF, 0xFB, 0x69, 0xFF, 0xB0,
  0xFF, 0xE0, 0x00, 0xAF, 0xF2,
  0xFF, 0x90, 0x00, 0x5F, 0xF4,
  0xFF, 0x80, 0x00, 0x4F, 0xF6,
  0xFF, 0xB0, 0x00, 0x7F, 0xF3,
  0xFF, 0xF3, 0x02, 0xDF, 0xE0,
  0xFF, 0xEF, 0xDF, 0xFF, 0x70,
  0xDF, 0x6C, 0xFF, 0xF7, 0x00,
  0x13, 0x00, 0x33, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0063[ 55] = { /* code 0063, LATIN SMALL LETTER C */
  0x00, 0x05, 0x99, 0x83, 0x00,
  0x01, 0xCF, 0xFF, 0xFF, 0xA0,
  0x0B, 0xFF, 0xA6, 0xBF, 0xF3,
  0x3F, 0xF9, 0x00, 0x0B, 0xD2,
  0x6F, 0xF4, 0x00, 0x00, 0x00,
  0x7F, 0xF2, 0x00, 0x00, 0x00,
  0x5F, 0xF4, 0x00, 0x04, 0x70,
  0x2F, 0xFC, 0x10, 0x3F, 0xF4,
  0x09, 0xFF, 0xED, 0xFF, 0xE1,
  0x00, 0x8E, 0xFF, 0xFC, 0x30,
  0x00, 0x00, 0x34, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0064[ 75] = { /* code 0064, LATIN SMALL LETTER D */
  0x00, 0x00, 0x00, 0x00, 0x20,
  0x00, 0x00, 0x00, 0x08, 0xFC,
  0x00, 0x00, 0x00, 0x09, 0xFF,
  0x00, 0x00, 0x00, 0x09, 0xFF,
  0x00, 0x17, 0x98, 0x29, 0xFF,
  0x02, 0xEF, 0xFF, 0xEC, 0xFF,
  0x0B, 0xFF, 0x96, 0xBF, 0xFF,
  0x2F, 0xFA, 0x00, 0x0E, 0xFF,
  0x5F, 0xF5, 0x00, 0x09, 0xFF,
  0x6F, 0xF4, 0x00, 0x08, 0xFF,
  0x4F, 0xF7, 0x00, 0x0B, 0xFF,
  0x1E, 0xFD, 0x20, 0x3F, 0xFF,
  0x07, 0xFF, 0xFD, 0xFE, 0xFF,
  0x00, 0x7F, 0xFF, 0xC6, 0xFD,
  0x00, 0x01, 0x32, 0x00, 0x31
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0065[ 66] = { /* code 0065, LATIN SMALL LETTER E */
  0x00, 0x03, 0x89, 0x95, 0x00, 0x00,
  0x00, 0xAF, 0xFF, 0xFF, 0xC1, 0x00,
  0x09, 0xFF, 0x74, 0x6E, 0xFB, 0x00,
  0x1F, 0xF8, 0x00, 0x05, 0xFF, 0x30,
  0x4F, 0xFC, 0xBB, 0xBC, 0xFF, 0x60,
  0x6F, 0xFC, 0xBB, 0xBB, 0xBB, 0x30,
  0x4F, 0xF6, 0x00, 0x00, 0x10, 0x00,
  0x0E, 0xFD, 0x20, 0x06, 0xFA, 0x00,
  0x05, 0xFF, 0xFD, 0xEF, 0xFA, 0x00,
  0x00, 0x5D, 0xFF, 0xFE, 0x91, 0x00,
  0x00, 0x00, 0x24, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0066[ 60] = { /* code 0066, LATIN SMALL LETTER F */
  0x00, 0x01, 0x42, 0x00,
  0x00, 0x6F, 0xFF, 0x50,
  0x01, 0xFF, 0xFD, 0x40,
  0x02, 0xFF, 0x80, 0x00,
  0x37, 0xFF, 0xA6, 0x10,
  0xBF, 0xFF, 0xFF, 0x70,
  0x26, 0xFF, 0xA4, 0x10,
  0x02, 0xFF, 0x70, 0x00,
  0x02, 0xFF, 0x70, 0x00,
  0x02, 0xFF, 0x70, 0x00,
  0x02, 0xFF, 0x70, 0x00,
  0x02, 0xFF, 0x70, 0x00,
  0x02, 0xFF, 0x70, 0x00,
  0x01, 0xEF, 0x60, 0x00,
  0x00, 0x13, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0067[ 70] = { /* code 0067, LATIN SMALL LETTER G */
  0x00, 0x16, 0x98, 0x31, 0x96,
  0x02, 0xDF, 0xFF, 0xFA, 0xFE,
  0x0B, 0xFF, 0xA6, 0xBF, 0xFF,
  0x2F, 0xF9, 0x00, 0x0C, 0xFF,
  0x5F, 0xF5, 0x00, 0x09, 0xFF,
  0x6F, 0xF4, 0x00, 0x09, 0xFF,
  0x3F, 0xF8, 0x00, 0x0C, 0xFF,
  0x0E, 0xFF, 0x64, 0x8F, 0xFF,
  0x04, 0xFF, 0xFF, 0xFE, 0xFF,
  0x00, 0x38, 0xBA, 0x49, 0xFF,
  0x01, 0x61, 0x00, 0x0B, 0xFC,
  0x09, 0xFD, 0x32, 0x6F, 0xF7,
  0x05, 0xFF, 0xFF, 0xFF, 0xB1,
  0x00, 0x39, 0xCD, 0xB7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0068[ 75] = { /* code 0068, LATIN SMALL LETTER H */
  0x02, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0x80, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xFF, 0x91, 0x79, 0x83, 0x00,
  0xFF, 0xBE, 0xFF, 0xFF, 0x40,
  0xFF, 0xFD, 0x8B, 0xFF, 0xB0,
  0xFF, 0xE1, 0x00, 0xDF, 0xE0,
  0xFF, 0xA0, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xCF, 0x80, 0x00, 0x8F, 0xC0,
  0x13, 0x00, 0x00, 0x03, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0069[ 30] = { /* code 0069, LATIN SMALL LETTER I */
  0x04, 0x00,
  0xAF, 0xB0,
  0xCF, 0xC0,
  0x27, 0x20,
  0x49, 0x40,
  0xCF, 0xC0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xAF, 0xA0,
  0x04, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_006A[ 54] = { /* code 006A, LATIN SMALL LETTER J */
  0x00, 0x04, 0x00,
  0x00, 0xAF, 0xB0,
  0x00, 0xCF, 0xC0,
  0x00, 0x27, 0x20,
  0x00, 0x49, 0x40,
  0x00, 0xCF, 0xC0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x00, 0xDF, 0xD0,
  0x05, 0xEF, 0xB0,
  0x4F, 0xFF, 0x80,
  0x1A, 0xB7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_006B[ 75] = { /* code 006B, LATIN SMALL LETTER K */
  0x02, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0x80, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xFF, 0x90, 0x02, 0x93, 0x00,
  0xFF, 0x90, 0x1D, 0xFC, 0x00,
  0xFF, 0x91, 0xDF, 0xF5, 0x00,
  0xFF, 0xAB, 0xFF, 0x60, 0x00,
  0xFF, 0xFF, 0xFB, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0x40, 0x00,
  0xFF, 0xE6, 0xFF, 0xE2, 0x00,
  0xFF, 0x90, 0x6F, 0xFB, 0x00,
  0xFF, 0x90, 0x0B, 0xFF, 0x60,
  0xCF, 0x80, 0x01, 0xEF, 0x50,
  0x13, 0x00, 0x00, 0x13, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_006C[ 30] = { /* code 006C, LATIN SMALL LETTER L */
  0x02, 0x00,
  0xAF, 0xA0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xDF, 0xD0,
  0xAF, 0xA0,
  0x03, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_006D[ 88] = { /* code 006D, LATIN SMALL LETTER M */
  0x59, 0x11, 0x79, 0x82, 0x03, 0x89, 0x72, 0x00,
  0xEF, 0x9D, 0xFF, 0xFE, 0x6F, 0xFF, 0xFE, 0x20,
  0xFF, 0xFC, 0x8C, 0xFF, 0xFC, 0x8C, 0xFF, 0x70,
  0xFF, 0xE1, 0x02, 0xFF, 0xD1, 0x02, 0xFF, 0x90,
  0xFF, 0xA0, 0x00, 0xFF, 0xA0, 0x00, 0xFF, 0x90,
  0xFF, 0x90, 0x00, 0xFF, 0x90, 0x00, 0xFF, 0x90,
  0xFF, 0x90, 0x00, 0xFF, 0x90, 0x00, 0xFF, 0x90,
  0xFF, 0x90, 0x00, 0xFF, 0x90, 0x00, 0xFF, 0x90,
  0xFF, 0x90, 0x00, 0xFF, 0x90, 0x00, 0xFF, 0x90,
  0xCF, 0x80, 0x00, 0xDF, 0x80, 0x00, 0xDF, 0x70,
  0x13, 0x00, 0x00, 0x13, 0x00, 0x00, 0x13, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_006E[ 55] = { /* code 006E, LATIN SMALL LETTER N */
  0x59, 0x11, 0x79, 0x83, 0x00,
  0xEF, 0x8E, 0xFF, 0xFF, 0x40,
  0xFF, 0xFD, 0x8B, 0xFF, 0xB0,
  0xFF, 0xE1, 0x00, 0xDF, 0xE0,
  0xFF, 0xA0, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xCF, 0x80, 0x00, 0x8F, 0xC0,
  0x13, 0x00, 0x00, 0x03, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_006F[ 66] = { /* code 006F, LATIN SMALL LETTER O */
  0x00, 0x04, 0x89, 0x84, 0x00, 0x00,
  0x01, 0xBF, 0xFF, 0xFF, 0xB1, 0x00,
  0x0A, 0xFF, 0x95, 0x8F, 0xFA, 0x00,
  0x1F, 0xFA, 0x00, 0x0A, 0xFF, 0x20,
  0x5F, 0xF5, 0x00, 0x05, 0xFF, 0x50,
  0x6F, 0xF4, 0x00, 0x04, 0xFF, 0x60,
  0x4F, 0xF7, 0x00, 0x06, 0xFF, 0x40,
  0x0E, 0xFD, 0x10, 0x1D, 0xFE, 0x10,
  0x07, 0xFF, 0xEB, 0xEF, 0xF7, 0x00,
  0x00, 0x6E, 0xFF, 0xFE, 0x60, 0x00,
  0x00, 0x00, 0x34, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0070[ 70] = { /* code 0070, LATIN SMALL LETTER P */
  0x69, 0x13, 0x99, 0x61, 0x00,
  0xEF, 0xAF, 0xFF, 0xFD, 0x20,
  0xFF, 0xFB, 0x6A, 0xFF, 0xB0,
  0xFF, 0xD0, 0x00, 0xAF, 0xF2,
  0xFF, 0x90, 0x00, 0x5F, 0xF4,
  0xFF, 0x80, 0x00, 0x4F, 0xF6,
  0xFF, 0xB0, 0x00, 0x7F, 0xF3,
  0xFF, 0xF3, 0x01, 0xDF, 0xE1,
  0xFF, 0xFF, 0xDE, 0xFF, 0x80,
  0xFF, 0xAB, 0xFF, 0xF9, 0x00,
  0xFF, 0x90, 0x24, 0x10, 0x00,
  0xFF, 0x90, 0x00, 0x00, 0x00,
  0xEF, 0x90, 0x00, 0x00, 0x00,
  0x6B, 0x30, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0071[ 70] = { /* code 0071, LATIN SMALL LETTER Q */
  0x00, 0x16, 0x99, 0x31, 0x96,
  0x02, 0xDF, 0xFF, 0xFA, 0xFE,
  0x0B, 0xFF, 0xA6, 0xBF, 0xFF,
  0x2F, 0xFA, 0x00, 0x0D, 0xFF,
  0x5F, 0xF5, 0x00, 0x09, 0xFF,
  0x6F, 0xF4, 0x00, 0x08, 0xFF,
  0x4F, 0xF7, 0x00, 0x0B, 0xFF,
  0x1F, 0xFD, 0x10, 0x3F, 0xFF,
  0x08, 0xFF, 0xED, 0xFF, 0xFF,
  0x00, 0x9F, 0xFF, 0xBA, 0xFF,
  0x00, 0x01, 0x42, 0x09, 0xFF,
  0x00, 0x00, 0x00, 0x09, 0xFF,
  0x00, 0x00, 0x00, 0x09, 0xFE,
  0x00, 0x00, 0x00, 0x03, 0xB6
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0072[ 44] = { /* code 0072, LATIN SMALL LETTER R */
  0x49, 0x22, 0x97, 0x00,
  0xCF, 0x9E, 0xFF, 0x30,
  0xDF, 0xFF, 0xFB, 0x10,
  0xDF, 0xF5, 0x00, 0x00,
  0xDF, 0xE0, 0x00, 0x00,
  0xDF, 0xD0, 0x00, 0x00,
  0xDF, 0xD0, 0x00, 0x00,
  0xDF, 0xD0, 0x00, 0x00,
  0xDF, 0xD0, 0x00, 0x00,
  0xAF, 0xA0, 0x00, 0x00,
  0x04, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0073[ 55] = { /* code 0073, LATIN SMALL LETTER S */
  0x00, 0x48, 0x99, 0x61, 0x00,
  0x0A, 0xFF, 0xFF, 0xFE, 0x30,
  0x4F, 0xFA, 0x46, 0xEF, 0x80,
  0x6F, 0xF7, 0x00, 0x27, 0x10,
  0x2F, 0xFF, 0xFB, 0x82, 0x00,
  0x04, 0xCF, 0xFF, 0xFF, 0x50,
  0x03, 0x01, 0x59, 0xEF, 0xE0,
  0x4F, 0xC1, 0x00, 0xAF, 0xF0,
  0x3F, 0xFE, 0xAB, 0xFF, 0xA0,
  0x04, 0xCF, 0xFF, 0xF9, 0x10,
  0x00, 0x01, 0x33, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0074[ 56] = { /* code 0074, LATIN SMALL LETTER T */
  0x00, 0x33, 0x00, 0x00,
  0x02, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x60, 0x00,
  0x38, 0xFF, 0x95, 0x10,
  0xBF, 0xFF, 0xFF, 0x50,
  0x27, 0xFF, 0x94, 0x00,
  0x04, 0xFF, 0x60, 0x00,
  0x04, 0xFF, 0x60, 0x00,
  0x04, 0xFF, 0x60, 0x00,
  0x04, 0xFF, 0x60, 0x00,
  0x04, 0xFF, 0x60, 0x00,
  0x03, 0xFF, 0xEC, 0x30,
  0x00, 0xBF, 0xFF, 0x30,
  0x00, 0x02, 0x41, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0075[ 55] = { /* code 0075, LATIN SMALL LETTER U */
  0x59, 0x30, 0x00, 0x39, 0x50,
  0xEF, 0x90, 0x00, 0x9F, 0xE0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0x90, 0x00, 0x9F, 0xF0,
  0xFF, 0xA0, 0x00, 0xBF, 0xF0,
  0xEF, 0xE2, 0x06, 0xFF, 0xF0,
  0x9F, 0xFF, 0xEF, 0xEF, 0xF0,
  0x1C, 0xFF, 0xF9, 0x5F, 0xD0,
  0x00, 0x24, 0x10, 0x03, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0076[ 55] = { /* code 0076, LATIN SMALL LETTER V */
  0x49, 0x20, 0x00, 0x29, 0x40,
  0xDF, 0xB0, 0x00, 0xBF, 0xD0,
  0xAF, 0xF1, 0x01, 0xFF, 0xA0,
  0x5F, 0xF5, 0x05, 0xFF, 0x50,
  0x0E, 0xF9, 0x09, 0xFE, 0x00,
  0x09, 0xFE, 0x0E, 0xF9, 0x00,
  0x03, 0xFF, 0x7F, 0xF3, 0x00,
  0x00, 0xDF, 0xEF, 0xD0, 0x00,
  0x00, 0x8F, 0xFF, 0x80, 0x00,
  0x00, 0x2F, 0xFF, 0x20, 0x00,
  0x00, 0x02, 0x42, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0077[ 77] = { /* code 0077, LATIN SMALL LETTER W */
  0x39, 0x40, 0x01, 0x89, 0x10, 0x04, 0x93,
  0xBF, 0xB0, 0x07, 0xFF, 0x80, 0x0B, 0xFB,
  0x9F, 0xF1, 0x0B, 0xFF, 0xC0, 0x0F, 0xF9,
  0x4F, 0xF4, 0x0E, 0xFF, 0xF1, 0x3F, 0xF4,
  0x0E, 0xF7, 0x2F, 0xCC, 0xF3, 0x7F, 0xE0,
  0x0A, 0xFB, 0x5F, 0x88, 0xF7, 0xAF, 0xA0,
  0x06, 0xFE, 0x9F, 0x44, 0xFA, 0xDF, 0x60,
  0x01, 0xFF, 0xEF, 0x11, 0xFE, 0xFF, 0x10,
  0x00, 0xBF, 0xFC, 0x00, 0xCF, 0xFB, 0x00,
  0x00, 0x6F, 0xF7, 0x00, 0x7F, 0xF6, 0x00,
  0x00, 0x03, 0x30, 0x00, 0x03, 0x30, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0078[ 55] = { /* code 0078, LATIN SMALL LETTER X */
  0x18, 0x70, 0x00, 0x78, 0x00,
  0x7F, 0xF4, 0x05, 0xFF, 0x30,
  0x2F, 0xFD, 0x1E, 0xFD, 0x00,
  0x07, 0xFF, 0xDF, 0xF3, 0x00,
  0x00, 0xCF, 0xFF, 0x80, 0x00,
  0x00, 0x8F, 0xFF, 0x50, 0x00,
  0x04, 0xFF, 0xFF, 0xD1, 0x00,
  0x1D, 0xFE, 0x6F, 0xF9, 0x00,
  0x8F, 0xF5, 0x0B, 0xFF, 0x30,
  0x8F, 0xB0, 0x02, 0xEF, 0x30,
  0x03, 0x00, 0x00, 0x22, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_0079[ 70] = { /* code 0079, LATIN SMALL LETTER Y */
  0x39, 0x40, 0x00, 0x39, 0x30,
  0xBF, 0xD0, 0x00, 0xCF, 0xB0,
  0x8F, 0xF2, 0x01, 0xFF, 0x80,
  0x3F, 0xF7, 0x05, 0xFF, 0x30,
  0x0D, 0xFB, 0x09, 0xFD, 0x00,
  0x08, 0xFE, 0x0D, 0xF7, 0x00,
  0x03, 0xFF, 0x6F, 0xF2, 0x00,
  0x00, 0xCF, 0xEF, 0xB0, 0x00,
  0x00, 0x7F, 0xFF, 0x70, 0x00,
  0x00, 0x2F, 0xFF, 0x10, 0x00,
  0x00, 0x0C, 0xFB, 0x00, 0x00,
  0x06, 0x8F, 0xF5, 0x00, 0x00,
  0x1F, 0xFF, 0xC0, 0x00, 0x00,
  0x07, 0xB8, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_007A[ 50] = { /* code 007A, LATIN SMALL LETTER Z */
  0x03, 0x66, 0x66, 0x66, 0x50,
  0x0F, 0xFF, 0xFF, 0xFF, 0xF5,
  0x07, 0x99, 0x9B, 0xFF, 0xE1,
  0x00, 0x00, 0x1D, 0xFE, 0x30,
  0x00, 0x01, 0xDF, 0xF4, 0x00,
  0x00, 0x1B, 0xFF, 0x50, 0x00,
  0x00, 0xBF, 0xF7, 0x00, 0x00,
  0x0A, 0xFF, 0x80, 0x00, 0x00,
  0x5F, 0xFF, 0xFF, 0xFF, 0xF5,
  0x4F, 0xFF, 0xFF, 0xFF, 0xF4
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_007B[ 68] = { /* code 007B, LEFT CURLY BRACKET */
  0x00, 0x6D, 0xFE, 0x30,
  0x01, 0xFF, 0xC9, 0x20,
  0x03, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x30, 0x00,
  0x8D, 0xFB, 0x00, 0x00,
  0xDF, 0xE7, 0x00, 0x00,
  0x06, 0xFF, 0x10, 0x00,
  0x04, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x40, 0x00,
  0x04, 0xFF, 0x40, 0x00,
  0x03, 0xFF, 0x40, 0x00,
  0x02, 0xFF, 0x95, 0x00,
  0x00, 0xBF, 0xFF, 0x40,
  0x00, 0x03, 0x55, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_007C[ 30] = { /* code 007C, VERTICAL LINE */
  0x02, 0x20,
  0x1E, 0xE1,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x2F, 0xF2,
  0x1F, 0xF1,
  0x02, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_007D[ 68] = { /* code 007D, RIGHT CURLY BRACKET */
  0xCF, 0xEA, 0x00, 0x00,
  0x8B, 0xFF, 0x60, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x80, 0x00,
  0x00, 0x7F, 0xEA, 0x10,
  0x00, 0x3C, 0xFF, 0x30,
  0x00, 0xCF, 0xA0, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x00, 0xDF, 0x70, 0x00,
  0x36, 0xFF, 0x70, 0x00,
  0xEF, 0xFE, 0x20, 0x00,
  0x36, 0x41, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded22_007E[ 24] = { /* code 007E, TILDE */
  0x02, 0xAB, 0x83, 0x00, 0x38, 0x00,
  0x0D, 0xFF, 0xFF, 0xBA, 0xFF, 0x00,
  0x4F, 0xC6, 0xAF, 0xFF, 0xF9, 0x00,
  0x06, 0x10, 0x01, 0x79, 0x50, 0x00
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontRounded22_CharInfo[95] = {
   {   1,   1,   0,  18,   6, acGUI_FontRounded22_0020 } /* code 0020, SPACE */
  ,{   4,  15,   1,   4,   6, acGUI_FontRounded22_0021 } /* code 0021, EXCLAMATION MARK */
  ,{   7,   7,   1,   4,  10, acGUI_FontRounded22_0022 } /* code 0022, QUOTATION MARK */
  ,{  11,  14,   0,   5,  11, acGUI_FontRounded22_0023 } /* code 0023, NUMBER SIGN */
  ,{  11,  16,   0,   4,  11, acGUI_FontRounded22_0024 } /* code 0024, DOLLAR SIGN */
  ,{  16,  15,   1,   4,  18, acGUI_FontRounded22_0025 } /* code 0025, PERCENT SIGN */
  ,{  13,  15,   0,   4,  13, acGUI_FontRounded22_0026 } /* code 0026, AMPERSAND */
  ,{   3,   7,   1,   4,   6, acGUI_FontRounded22_0027 } /* code 0027, APOSTROPHE */
  ,{   5,  18,   1,   4,   6, acGUI_FontRounded22_0028 } /* code 0028, LEFT PARENTHESIS */
  ,{   5,  18,   0,   4,   6, acGUI_FontRounded22_0029 } /* code 0029, RIGHT PARENTHESIS */
  ,{   8,   8,   0,   4,   8, acGUI_FontRounded22_002A } /* code 002A, ASTERISK */
  ,{  11,  11,   0,   7,  11, acGUI_FontRounded22_002B } /* code 002B, PLUS SIGN */
  ,{   4,   6,   1,  15,   6, acGUI_FontRounded22_002C } /* code 002C, COMMA */
  ,{   7,   4,   0,  11,   7, acGUI_FontRounded22_002D } /* code 002D, HYPHEN-MINUS */
  ,{   4,   4,   1,  15,   6, acGUI_FontRounded22_002E } /* code 002E, FULL STOP */
  ,{   7,  15,   0,   4,   7, acGUI_FontRounded22_002F } /* code 002F, SOLIDUS */
  ,{  11,  14,   0,   5,  11, acGUI_FontRounded22_0030 } /* code 0030, DIGIT ZERO */
  ,{   7,  14,  -1,   5,   7, acGUI_FontRounded22_0031 } /* code 0031, DIGIT ONE */
  ,{  11,  13,   0,   5,  11, acGUI_FontRounded22_0032 } /* code 0032, DIGIT TWO */
  ,{  10,  14,   0,   5,  11, acGUI_FontRounded22_0033 } /* code 0033, DIGIT THREE */
  ,{  11,  14,   0,   5,  11, acGUI_FontRounded22_0034 } /* code 0034, DIGIT FOUR */
  ,{  10,  14,   0,   5,  11, acGUI_FontRounded22_0035 } /* code 0035, DIGIT FIVE */
  ,{  11,  14,   0,   5,  11, acGUI_FontRounded22_0036 } /* code 0036, DIGIT SIX */
  ,{  10,  14,   0,   5,  10, acGUI_FontRounded22_0037 } /* code 0037, DIGIT SEVEN */
  ,{  11,  14,   0,   5,  11, acGUI_FontRounded22_0038 } /* code 0038, DIGIT EIGHT */
  ,{  11,  14,   0,   5,  11, acGUI_FontRounded22_0039 } /* code 0039, DIGIT NINE */
  ,{   4,  11,   1,   8,   6, acGUI_FontRounded22_003A } /* code 003A, COLON */
  ,{   4,  13,   1,   8,   6, acGUI_FontRounded22_003B } /* code 003B, SEMICOLON */
  ,{  11,  10,   0,   8,  11, acGUI_FontRounded22_003C } /* code 003C, LESS-THAN SIGN */
  ,{  11,   8,   0,   9,  11, acGUI_FontRounded22_003D } /* code 003D, EQUALS SIGN */
  ,{  11,  10,   0,   8,  11, acGUI_FontRounded22_003E } /* code 003E, GREATER-THAN SIGN */
  ,{  10,  15,   0,   4,  10, acGUI_FontRounded22_003F } /* code 003F, QUESTION MARK */
  ,{  14,  15,   0,   4,  14, acGUI_FontRounded22_0040 } /* code 0040, COMMERCIAL AT */
  ,{  12,  15,   0,   4,  12, acGUI_FontRounded22_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{  12,  13,   1,   5,  13, acGUI_FontRounded22_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{  12,  15,   1,   4,  13, acGUI_FontRounded22_0043 } /* code 0043, LATIN CAPITAL LETTER C */
  ,{  12,  13,   1,   5,  14, acGUI_FontRounded22_0044 } /* code 0044, LATIN CAPITAL LETTER D */
  ,{  11,  13,   1,   5,  12, acGUI_FontRounded22_0045 } /* code 0045, LATIN CAPITAL LETTER E */
  ,{  11,  14,   1,   5,  11, acGUI_FontRounded22_0046 } /* code 0046, LATIN CAPITAL LETTER F */
  ,{  13,  15,   1,   4,  15, acGUI_FontRounded22_0047 } /* code 0047, LATIN CAPITAL LETTER G */
  ,{  12,  15,   1,   4,  14, acGUI_FontRounded22_0048 } /* code 0048, LATIN CAPITAL LETTER H */
  ,{   4,  15,   1,   4,   6, acGUI_FontRounded22_0049 } /* code 0049, LATIN CAPITAL LETTER I */
  ,{   9,  15,   0,   4,  10, acGUI_FontRounded22_004A } /* code 004A, LATIN CAPITAL LETTER J */
  ,{  12,  15,   1,   4,  12, acGUI_FontRounded22_004B } /* code 004B, LATIN CAPITAL LETTER K */
  ,{  10,  14,   1,   4,  11, acGUI_FontRounded22_004C } /* code 004C, LATIN CAPITAL LETTER L */
  ,{  14,  15,   1,   4,  16, acGUI_FontRounded22_004D } /* code 004D, LATIN CAPITAL LETTER M */
  ,{  12,  15,   1,   4,  14, acGUI_FontRounded22_004E } /* code 004E, LATIN CAPITAL LETTER N */
  ,{  14,  15,   0,   4,  15, acGUI_FontRounded22_004F } /* code 004F, LATIN CAPITAL LETTER O */
  ,{  12,  14,   1,   5,  13, acGUI_FontRounded22_0050 } /* code 0050, LATIN CAPITAL LETTER P */
  ,{  14,  15,   0,   4,  15, acGUI_FontRounded22_0051 } /* code 0051, LATIN CAPITAL LETTER Q */
  ,{  12,  14,   1,   5,  13, acGUI_FontRounded22_0052 } /* code 0052, LATIN CAPITAL LETTER R */
  ,{  12,  15,   0,   4,  12, acGUI_FontRounded22_0053 } /* code 0053, LATIN CAPITAL LETTER S */
  ,{  12,  14,   0,   5,  12, acGUI_FontRounded22_0054 } /* code 0054, LATIN CAPITAL LETTER T */
  ,{  12,  15,   1,   4,  14, acGUI_FontRounded22_0055 } /* code 0055, LATIN CAPITAL LETTER U */
  ,{  12,  15,   0,   4,  12, acGUI_FontRounded22_0056 } /* code 0056, LATIN CAPITAL LETTER V */
  ,{  17,  15,   0,   4,  17, acGUI_FontRounded22_0057 } /* code 0057, LATIN CAPITAL LETTER W */
  ,{  11,  15,   0,   4,  11, acGUI_FontRounded22_0058 } /* code 0058, LATIN CAPITAL LETTER X */
  ,{  11,  15,   0,   4,  11, acGUI_FontRounded22_0059 } /* code 0059, LATIN CAPITAL LETTER Y */
  ,{  12,  13,   0,   5,  12, acGUI_FontRounded22_005A } /* code 005A, LATIN CAPITAL LETTER Z */
  ,{   6,  17,   1,   5,   6, acGUI_FontRounded22_005B } /* code 005B, LEFT SQUARE BRACKET */
  ,{   7,  15,   0,   4,   7, acGUI_FontRounded22_005C } /* code 005C, REVERSE SOLIDUS */
  ,{   5,  17,   0,   5,   6, acGUI_FontRounded22_005D } /* code 005D, RIGHT SQUARE BRACKET */
  ,{   9,   8,   1,   5,  11, acGUI_FontRounded22_005E } /* code 005E, CIRCUMFLEX ACCENT */
  ,{  11,   2,  -1,  19,   9, acGUI_FontRounded22_005F } /* code 005F, LOW LINE */
  ,{   4,   4,   0,   4,   5, acGUI_FontRounded22_0060 } /* code 0060, GRAVE ACCENT */
  ,{  10,  11,   0,   8,  11, acGUI_FontRounded22_0061 } /* code 0061, LATIN SMALL LETTER A */
  ,{  10,  15,   1,   4,  11, acGUI_FontRounded22_0062 } /* code 0062, LATIN SMALL LETTER B */
  ,{  10,  11,   0,   8,  10, acGUI_FontRounded22_0063 } /* code 0063, LATIN SMALL LETTER C */
  ,{  10,  15,   0,   4,  11, acGUI_FontRounded22_0064 } /* code 0064, LATIN SMALL LETTER D */
  ,{  11,  11,   0,   8,  11, acGUI_FontRounded22_0065 } /* code 0065, LATIN SMALL LETTER E */
  ,{   7,  15,   0,   4,   7, acGUI_FontRounded22_0066 } /* code 0066, LATIN SMALL LETTER F */
  ,{  10,  14,   0,   8,  11, acGUI_FontRounded22_0067 } /* code 0067, LATIN SMALL LETTER G */
  ,{   9,  15,   1,   4,  11, acGUI_FontRounded22_0068 } /* code 0068, LATIN SMALL LETTER H */
  ,{   3,  15,   1,   4,   5, acGUI_FontRounded22_0069 } /* code 0069, LATIN SMALL LETTER I */
  ,{   5,  18,  -1,   4,   5, acGUI_FontRounded22_006A } /* code 006A, LATIN SMALL LETTER J */
  ,{   9,  15,   1,   4,  10, acGUI_FontRounded22_006B } /* code 006B, LATIN SMALL LETTER K */
  ,{   3,  15,   1,   4,   5, acGUI_FontRounded22_006C } /* code 006C, LATIN SMALL LETTER L */
  ,{  15,  11,   1,   8,  17, acGUI_FontRounded22_006D } /* code 006D, LATIN SMALL LETTER M */
  ,{   9,  11,   1,   8,  11, acGUI_FontRounded22_006E } /* code 006E, LATIN SMALL LETTER N */
  ,{  11,  11,   0,   8,  11, acGUI_FontRounded22_006F } /* code 006F, LATIN SMALL LETTER O */
  ,{  10,  14,   1,   8,  11, acGUI_FontRounded22_0070 } /* code 0070, LATIN SMALL LETTER P */
  ,{  10,  14,   0,   8,  11, acGUI_FontRounded22_0071 } /* code 0071, LATIN SMALL LETTER Q */
  ,{   7,  11,   1,   8,   7, acGUI_FontRounded22_0072 } /* code 0072, LATIN SMALL LETTER R */
  ,{   9,  11,   0,   8,  10, acGUI_FontRounded22_0073 } /* code 0073, LATIN SMALL LETTER S */
  ,{   7,  14,   0,   5,   7, acGUI_FontRounded22_0074 } /* code 0074, LATIN SMALL LETTER T */
  ,{   9,  11,   1,   8,  11, acGUI_FontRounded22_0075 } /* code 0075, LATIN SMALL LETTER U */
  ,{   9,  11,   0,   8,   9, acGUI_FontRounded22_0076 } /* code 0076, LATIN SMALL LETTER V */
  ,{  14,  11,   0,   8,  14, acGUI_FontRounded22_0077 } /* code 0077, LATIN SMALL LETTER W */
  ,{   9,  11,   0,   8,   9, acGUI_FontRounded22_0078 } /* code 0078, LATIN SMALL LETTER X */
  ,{   9,  14,   0,   8,   9, acGUI_FontRounded22_0079 } /* code 0079, LATIN SMALL LETTER Y */
  ,{  10,  10,   0,   8,  10, acGUI_FontRounded22_007A } /* code 007A, LATIN SMALL LETTER Z */
  ,{   7,  17,   0,   5,   6, acGUI_FontRounded22_007B } /* code 007B, LEFT CURLY BRACKET */
  ,{   4,  15,   0,   4,   4, acGUI_FontRounded22_007C } /* code 007C, VERTICAL LINE */
  ,{   7,  17,   0,   5,   6, acGUI_FontRounded22_007D } /* code 007D, RIGHT CURLY BRACKET */
  ,{  11,   4,   0,  11,  11, acGUI_FontRounded22_007E } /* code 007E, TILDE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontRounded22_Prop1 = {
   0x0020 /* first character */
  ,0x007E /* last character  */
  ,&GUI_FontRounded22_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontRounded22 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,22 /* height of font  */
  ,22 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontRounded22_Prop1}
  ,22 /* Baseline */
  ,11 /* Height of lowercase characters */
  ,15 /* Height of capital characters */
};

/*********************************************************************
*                                                                    *
*       GUI_FontSouvenir18                                           *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO.c                                                       *
*  - GUIDEMO_ColorBar.c                                              *
*  - GUIDEMO_Intro.c                                                 *
*                                                                    *
**********************************************************************
*/
GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0020[  1] = { /* code 0020, SPACE */
  0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0021[ 11] = { /* code 0021, EXCLAMATION MARK */
  0xCB,
  0xED,
  0xDC,
  0xBB,
  0xA9,
  0x97,
  0x76,
  0x54,
  0x00,
  0xCC,
  0xCC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0022[  8] = { /* code 0022, QUOTATION MARK */
  0xE0, 0xE0,
  0xC0, 0xC0,
  0x80, 0x80,
  0x50, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0023[ 55] = { /* code 0023, NUMBER SIGN */
  0x00, 0x00, 0x3B, 0x02, 0xC0,
  0x00, 0x00, 0x87, 0x07, 0x80,
  0x00, 0x00, 0xC2, 0x0B, 0x30,
  0x00, 0x02, 0xC0, 0x1D, 0x00,
  0x0E, 0xFF, 0xFF, 0xFF, 0xFD,
  0x00, 0x0C, 0x30, 0xA4, 0x00,
  0x00, 0x2C, 0x01, 0xD0, 0x00,
  0xDF, 0xFF, 0xFF, 0xFF, 0xE0,
  0x00, 0xC2, 0x0C, 0x30, 0x00,
  0x03, 0xB0, 0x3C, 0x00, 0x00,
  0x0A, 0x50, 0x96, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0024[ 60] = { /* code 0024, DOLLAR SIGN */
  0x00, 0x01, 0x00, 0x00,
  0x00, 0x0F, 0x00, 0x00,
  0x03, 0xDF, 0xE7, 0x00,
  0x0C, 0x5F, 0x1C, 0x30,
  0x0F, 0x0F, 0x05, 0x30,
  0x0E, 0x3F, 0x00, 0x00,
  0x07, 0xEF, 0x30, 0x00,
  0x00, 0x6F, 0xFA, 0x10,
  0x00, 0x0F, 0x4C, 0xA0,
  0x00, 0x0F, 0x02, 0xF0,
  0xD0, 0x0F, 0x01, 0xE0,
  0x99, 0x1F, 0x2A, 0x70,
  0x08, 0xEF, 0xC6, 0x00,
  0x00, 0x0F, 0x00, 0x00,
  0x00, 0x09, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0025[ 78] = { /* code 0025, PERCENT SIGN */
  0x00, 0x00, 0x00, 0x00, 0x10, 0x00,
  0x2C, 0xFC, 0x61, 0x06, 0xC0, 0x00,
  0xC6, 0x06, 0xEE, 0xED, 0x40, 0x00,
  0xF0, 0x00, 0xF0, 0x19, 0x00, 0x00,
  0xC6, 0x06, 0xD0, 0x82, 0x00, 0x00,
  0x3C, 0xFC, 0x33, 0x70, 0x00, 0x00,
  0x00, 0x00, 0x0A, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x74, 0x2C, 0xFC, 0x20,
  0x00, 0x02, 0x90, 0xC6, 0x06, 0xC0,
  0x00, 0x0A, 0x20, 0xF0, 0x00, 0xF0,
  0x00, 0x57, 0x00, 0xC6, 0x06, 0xC0,
  0x00, 0xA0, 0x00, 0x2C, 0xFC, 0x20,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0026[ 66] = { /* code 0026, AMPERSAND */
  0x01, 0xBF, 0xD3, 0x00, 0x00, 0x00,
  0x0B, 0x70, 0x6D, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x0F, 0x00, 0x00, 0x00,
  0x0D, 0x40, 0x79, 0x00, 0x00, 0x00,
  0x08, 0xCA, 0xA0, 0x00, 0x00, 0x00,
  0x05, 0xFA, 0x00, 0x8F, 0xFC, 0x20,
  0x5B, 0x7F, 0x40, 0x09, 0x90, 0x00,
  0xD2, 0x08, 0xE2, 0x2B, 0x00, 0x00,
  0xF1, 0x00, 0x9E, 0xC1, 0x00, 0x00,
  0xB9, 0x11, 0x7D, 0xF9, 0x32, 0x30,
  0x1B, 0xFE, 0x80, 0x4A, 0xDC, 0x80
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0027[  4] = { /* code 0027, APOSTROPHE */
  0xE0,
  0xC0,
  0x80,
  0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0028[ 26] = { /* code 0028, LEFT PARENTHESIS */
  0x02, 0xC0,
  0x0A, 0x40,
  0x3C, 0x00,
  0x87, 0x00,
  0xC3, 0x00,
  0xE1, 0x00,
  0xF0, 0x00,
  0xE1, 0x00,
  0xC3, 0x00,
  0x87, 0x00,
  0x3C, 0x00,
  0x0A, 0x40,
  0x02, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0029[ 26] = { /* code 0029, RIGHT PARENTHESIS */
  0xC2, 0x00,
  0x5A, 0x00,
  0x0C, 0x30,
  0x07, 0x80,
  0x03, 0xC0,
  0x01, 0xE0,
  0x00, 0xF0,
  0x01, 0xE0,
  0x03, 0xC0,
  0x07, 0x80,
  0x0C, 0x30,
  0x5A, 0x00,
  0xB2, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_002A[ 18] = { /* code 002A, ASTERISK */
  0x00, 0xE0, 0x00,
  0xB3, 0x93, 0xB0,
  0x68, 0x77, 0x60,
  0x68, 0x78, 0x60,
  0xB3, 0x93, 0xB0,
  0x00, 0xE0, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_002B[ 45] = { /* code 002B, PLUS SIGN */
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_002C[  4] = { /* code 002C, COMMA */
  0xCB,
  0xDF,
  0x07,
  0x52
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_002D[  2] = { /* code 002D, HYPHEN-MINUS */
  0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_002E[  2] = { /* code 002E, FULL STOP */
  0xCC,
  0xCC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_002F[ 24] = { /* code 002F, SOLIDUS */
  0x00, 0x09,
  0x00, 0x09,
  0x00, 0x45,
  0x00, 0x81,
  0x00, 0x90,
  0x03, 0x70,
  0x07, 0x20,
  0x09, 0x00,
  0x18, 0x00,
  0x54, 0x00,
  0x90, 0x00,
  0x90, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0030[ 44] = { /* code 0030, DIGIT ZERO */
  0x04, 0xCF, 0xC4, 0x00,
  0x2D, 0x30, 0x3D, 0x20,
  0x95, 0x00, 0x05, 0x90,
  0xC2, 0x00, 0x02, 0xC0,
  0xF0, 0x00, 0x00, 0xF0,
  0xF0, 0x00, 0x00, 0xF0,
  0xF0, 0x00, 0x00, 0xF0,
  0xC2, 0x00, 0x02, 0xC0,
  0x95, 0x00, 0x05, 0x90,
  0x2D, 0x30, 0x3D, 0x20,
  0x04, 0xCF, 0xC4, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0031[ 33] = { /* code 0031, DIGIT ONE */
  0x14, 0xC0, 0x00,
  0xEE, 0xF0, 0x00,
  0x01, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x03, 0xF4, 0x00,
  0xAF, 0xFF, 0xD0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0032[ 44] = { /* code 0032, DIGIT TWO */
  0x00, 0x6D, 0xFE, 0x91,
  0x09, 0xA2, 0x02, 0x9A,
  0x0F, 0x10, 0x00, 0x0F,
  0x0C, 0x60, 0x00, 0x2E,
  0x00, 0x00, 0x00, 0xB7,
  0x00, 0x00, 0x2C, 0x80,
  0x00, 0x04, 0xE6, 0x00,
  0x00, 0x5D, 0x30, 0x00,
  0x03, 0xD2, 0x00, 0x00,
  0x0D, 0x30, 0x00, 0x2D,
  0x5F, 0xFF, 0xFF, 0xFB
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0033[ 44] = { /* code 0033, DIGIT THREE */
  0x02, 0xAF, 0xFA, 0x10,
  0x0C, 0x60, 0x19, 0xA0,
  0x0E, 0x10, 0x01, 0xF0,
  0x00, 0x00, 0x01, 0xE0,
  0x00, 0x00, 0x1A, 0x60,
  0x00, 0x6D, 0xF7, 0x00,
  0x00, 0x00, 0x2A, 0x80,
  0x00, 0x00, 0x01, 0xE0,
  0xD0, 0x00, 0x01, 0xE0,
  0xD7, 0x10, 0x3B, 0x80,
  0x2B, 0xFF, 0xC7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0034[ 44] = { /* code 0034, DIGIT FOUR */
  0x00, 0x00, 0x8E, 0x00,
  0x00, 0x08, 0xBF, 0x00,
  0x00, 0x6C, 0x1F, 0x00,
  0x02, 0xD2, 0x0F, 0x00,
  0x0B, 0x50, 0x0F, 0x00,
  0x4C, 0x00, 0x0F, 0x00,
  0xA5, 0x00, 0x0F, 0x00,
  0xEF, 0xFF, 0xFF, 0xFE,
  0x00, 0x00, 0x0F, 0x00,
  0x00, 0x00, 0x0F, 0x10,
  0x00, 0x00, 0xCF, 0xE0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0035[ 44] = { /* code 0035, DIGIT FIVE */
  0x0F, 0xFF, 0xD9, 0x00,
  0x0E, 0x00, 0x00, 0x00,
  0x0D, 0x00, 0x00, 0x00,
  0x0B, 0x00, 0x00, 0x00,
  0x0C, 0xBF, 0xE8, 0x00,
  0x0B, 0x50, 0x1A, 0x90,
  0x00, 0x00, 0x02, 0xE0,
  0x00, 0x00, 0x00, 0xF0,
  0xD0, 0x00, 0x04, 0xC0,
  0xC6, 0x00, 0x4E, 0x50,
  0x2B, 0xFF, 0xC5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0036[ 44] = { /* code 0036, DIGIT SIX */
  0x00, 0x06, 0xBF, 0x50,
  0x01, 0xCB, 0x41, 0x00,
  0x0C, 0x70, 0x00, 0x00,
  0x6A, 0x00, 0x00, 0x00,
  0xB6, 0xBF, 0xE9, 0x00,
  0xE9, 0x20, 0x2B, 0x80,
  0xF0, 0x00, 0x02, 0xE0,
  0xE1, 0x00, 0x00, 0xF0,
  0xB5, 0x00, 0x03, 0xD0,
  0x4E, 0x40, 0x2B, 0x60,
  0x05, 0xDF, 0xD6, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0037[ 44] = { /* code 0037, DIGIT SEVEN */
  0xEF, 0xFF, 0xFF, 0xE0,
  0xE1, 0x00, 0x07, 0x80,
  0x30, 0x00, 0x0D, 0x20,
  0x00, 0x00, 0x5B, 0x00,
  0x00, 0x00, 0xC4, 0x00,
  0x00, 0x04, 0xD0, 0x00,
  0x00, 0x0B, 0x80, 0x00,
  0x00, 0x2F, 0x30, 0x00,
  0x00, 0x9C, 0x00, 0x00,
  0x00, 0xE8, 0x00, 0x00,
  0x03, 0xF3, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0038[ 44] = { /* code 0038, DIGIT EIGHT */
  0x19, 0xDF, 0xEB, 0x20,
  0xB8, 0x20, 0x18, 0xD0,
  0xF1, 0x00, 0x01, 0xE0,
  0x9C, 0x51, 0x3A, 0x30,
  0x07, 0xFF, 0xF5, 0x00,
  0x1B, 0x43, 0xAF, 0x50,
  0xA6, 0x00, 0x07, 0xD0,
  0xF0, 0x00, 0x00, 0xF0,
  0xE2, 0x00, 0x02, 0xD0,
  0x9B, 0x20, 0x3C, 0x60,
  0x08, 0xEF, 0xC6, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0039[ 44] = { /* code 0039, DIGIT NINE */
  0x06, 0xDF, 0xD5, 0x00,
  0x6B, 0x20, 0x4E, 0x40,
  0xD3, 0x00, 0x05, 0xB0,
  0xF0, 0x00, 0x01, 0xE0,
  0xE2, 0x00, 0x00, 0xF0,
  0x8B, 0x20, 0x29, 0xE0,
  0x09, 0xEF, 0xB6, 0xB0,
  0x00, 0x00, 0x09, 0x60,
  0x00, 0x00, 0x7B, 0x00,
  0x01, 0x4B, 0xB1, 0x00,
  0x5F, 0xB6, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_003A[  7] = { /* code 003A, COLON */
  0xCC,
  0xCC,
  0x00,
  0x00,
  0x00,
  0xCC,
  0xCC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_003B[  9] = { /* code 003B, SEMICOLON */
  0xCC,
  0xCC,
  0x00,
  0x00,
  0x00,
  0xCB,
  0xDF,
  0x05,
  0x42
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_003C[ 40] = { /* code 003C, LESS-THAN SIGN */
  0x00, 0x00, 0x00, 0x16, 0xC0,
  0x00, 0x00, 0x39, 0xD8, 0x20,
  0x01, 0x6C, 0xB5, 0x00, 0x00,
  0x9D, 0x82, 0x00, 0x00, 0x00,
  0x9D, 0x82, 0x00, 0x00, 0x00,
  0x01, 0x6C, 0xB5, 0x00, 0x00,
  0x00, 0x00, 0x39, 0xD9, 0x30,
  0x00, 0x00, 0x00, 0x16, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_003D[ 20] = { /* code 003D, EQUALS SIGN */
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_003E[ 40] = { /* code 003E, GREATER-THAN SIGN */
  0xC6, 0x10, 0x00, 0x00, 0x00,
  0x28, 0xD9, 0x30, 0x00, 0x00,
  0x00, 0x05, 0xBC, 0x61, 0x00,
  0x00, 0x00, 0x02, 0x8D, 0x90,
  0x00, 0x00, 0x02, 0x8D, 0x90,
  0x00, 0x05, 0xBC, 0x61, 0x00,
  0x39, 0xD9, 0x30, 0x00, 0x00,
  0xC6, 0x10, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_003F[ 33] = { /* code 003F, QUESTION MARK */
  0x2A, 0xFF, 0xA1,
  0xCE, 0x21, 0x9B,
  0xDD, 0x00, 0x1F,
  0x00, 0x00, 0x1E,
  0x00, 0x00, 0x78,
  0x00, 0x4A, 0x90,
  0x00, 0xF2, 0x00,
  0x00, 0xE0, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0xCC, 0x00,
  0x00, 0xCC, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0040[ 91] = { /* code 0040, COMMERCIAL AT */
  0x00, 0x01, 0x8C, 0xFF, 0xEA, 0x40, 0x00,
  0x00, 0x5D, 0x83, 0x00, 0x27, 0xE8, 0x00,
  0x05, 0xC2, 0x00, 0x00, 0x00, 0x2D, 0x50,
  0x2D, 0x20, 0x2B, 0xF8, 0x55, 0x05, 0xB0,
  0x88, 0x01, 0xE7, 0x07, 0xC4, 0x01, 0xF0,
  0xD3, 0x09, 0xB0, 0x03, 0xE0, 0x00, 0xF0,
  0xF0, 0x0E, 0x40, 0x06, 0xB0, 0x03, 0xC0,
  0xF0, 0x0F, 0x00, 0x0D, 0x60, 0x0B, 0x70,
  0xC3, 0x0C, 0x41, 0x9B, 0x41, 0xAC, 0x00,
  0x7A, 0x03, 0xEE, 0x33, 0xDE, 0x91, 0x00,
  0x0C, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xCB, 0x51, 0x00, 0x26, 0x50, 0x00,
  0x00, 0x06, 0xBE, 0xFE, 0xA4, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0041[ 55] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x00, 0x87, 0x00, 0x00,
  0x00, 0x02, 0xEF, 0x10, 0x00,
  0x00, 0x09, 0x4C, 0x80, 0x00,
  0x00, 0x1B, 0x05, 0xF1, 0x00,
  0x00, 0x75, 0x00, 0xD7, 0x00,
  0x00, 0xC0, 0x00, 0x7D, 0x00,
  0x04, 0xFF, 0xFF, 0xFF, 0x30,
  0x0A, 0x40, 0x00, 0x0B, 0x80,
  0x1E, 0x00, 0x00, 0x07, 0xD0,
  0x5C, 0x00, 0x00, 0x03, 0xF3,
  0xDF, 0x50, 0x00, 0x0B, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0042[ 55] = { /* code 0042, LATIN CAPITAL LETTER B */
  0xBF, 0xFF, 0xFF, 0xC3, 0x00,
  0x0F, 0x20, 0x02, 0x8D, 0x00,
  0x0F, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x79, 0x00,
  0x0F, 0x14, 0x7D, 0x90, 0x00,
  0x0F, 0xDA, 0x66, 0xBB, 0x10,
  0x0F, 0x00, 0x00, 0x07, 0xA0,
  0x0F, 0x00, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0x02, 0xE0,
  0x0F, 0x30, 0x01, 0x5D, 0x80,
  0xCF, 0xFF, 0xFF, 0xD8, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0043[ 55] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x3A, 0xEF, 0xEB, 0x40,
  0x07, 0xC4, 0x00, 0x4C, 0xE0,
  0x3D, 0x10, 0x00, 0x01, 0xC0,
  0xA6, 0x00, 0x00, 0x00, 0x00,
  0xE1, 0x00, 0x00, 0x00, 0x00,
  0xF0, 0x00, 0x00, 0x00, 0x00,
  0xE1, 0x00, 0x00, 0x00, 0x00,
  0xB5, 0x00, 0x00, 0x00, 0x00,
  0x5C, 0x10, 0x00, 0x00, 0x85,
  0x09, 0xC4, 0x00, 0x3A, 0xC1,
  0x00, 0x6C, 0xFF, 0xC7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0044[ 55] = { /* code 0044, LATIN CAPITAL LETTER D */
  0xCF, 0xFF, 0xFD, 0x93, 0x00,
  0x1F, 0x30, 0x03, 0x7E, 0x60,
  0x0F, 0x00, 0x00, 0x02, 0xD3,
  0x0F, 0x00, 0x00, 0x00, 0x6A,
  0x0F, 0x00, 0x00, 0x00, 0x1E,
  0x0F, 0x00, 0x00, 0x00, 0x0F,
  0x0F, 0x00, 0x00, 0x00, 0x1E,
  0x0F, 0x00, 0x00, 0x00, 0x6A,
  0x0F, 0x00, 0x00, 0x02, 0xE3,
  0x1F, 0x40, 0x03, 0x8E, 0x60,
  0xCF, 0xFF, 0xFD, 0x93, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0045[ 55] = { /* code 0045, LATIN CAPITAL LETTER E */
  0xBF, 0xFF, 0xFF, 0xFF, 0x40,
  0x0F, 0x20, 0x00, 0x39, 0x50,
  0x0F, 0x00, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x01, 0xB0, 0x00,
  0x0F, 0xFF, 0xFF, 0xF0, 0x00,
  0x0F, 0x00, 0x01, 0xB0, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x00,
  0x0F, 0x20, 0x00, 0x16, 0xD0,
  0xBF, 0xFF, 0xFF, 0xFF, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0046[ 44] = { /* code 0046, LATIN CAPITAL LETTER F */
  0xBF, 0xFF, 0xFF, 0xFC,
  0x0F, 0x20, 0x02, 0x5D,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x01, 0xA0,
  0x0F, 0xFF, 0xFF, 0xF0,
  0x0F, 0x00, 0x02, 0xA0,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0xCF, 0xB0, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0047[ 66] = { /* code 0047, LATIN CAPITAL LETTER G */
  0x00, 0x28, 0xDF, 0xFC, 0x81, 0x00,
  0x03, 0xE7, 0x20, 0x14, 0xC8, 0x00,
  0x2E, 0x30, 0x00, 0x00, 0x19, 0x00,
  0x98, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xD2, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xF0, 0x00, 0x00, 0x7F, 0xFE, 0x00,
  0xE1, 0x00, 0x00, 0x00, 0x3F, 0x00,
  0xB6, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x4E, 0x20, 0x00, 0x00, 0x1F, 0x10,
  0x07, 0xE7, 0x30, 0x03, 0xBC, 0x10,
  0x00, 0x4B, 0xEF, 0xEB, 0x60, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0048[ 66] = { /* code 0048, LATIN CAPITAL LETTER H */
  0xCF, 0xC0, 0x00, 0x00, 0xCF, 0xC0,
  0x1F, 0x10, 0x00, 0x00, 0x0F, 0x10,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x1F, 0x00, 0x00, 0x00, 0x1F, 0x00,
  0xCF, 0xB0, 0x00, 0x00, 0xCF, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0049[ 22] = { /* code 0049, LATIN CAPITAL LETTER I */
  0xBF, 0xC0,
  0x0F, 0x10,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x1F, 0x00,
  0xCF, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_004A[ 44] = { /* code 004A, LATIN CAPITAL LETTER J */
  0x00, 0x00, 0xEF, 0xC0,
  0x00, 0x00, 0x2F, 0x00,
  0x00, 0x00, 0x0F, 0x00,
  0x00, 0x00, 0x0F, 0x00,
  0x00, 0x00, 0x0F, 0x00,
  0x00, 0x00, 0x0F, 0x00,
  0x00, 0x00, 0x0F, 0x00,
  0x6E, 0x90, 0x0F, 0x00,
  0xF3, 0x00, 0x0F, 0x00,
  0xD6, 0x01, 0x8B, 0x00,
  0x3B, 0xFF, 0xB1, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_004B[ 55] = { /* code 004B, LATIN CAPITAL LETTER K */
  0xBF, 0xC0, 0x05, 0xFF, 0x40,
  0x0F, 0x10, 0x01, 0xE4, 0x00,
  0x0F, 0x00, 0x0B, 0x70, 0x00,
  0x0F, 0x00, 0xA9, 0x00, 0x00,
  0x0F, 0x09, 0xE1, 0x00, 0x00,
  0x0F, 0xAA, 0xDB, 0x00, 0x00,
  0x0F, 0x80, 0x2E, 0x60, 0x00,
  0x0F, 0x00, 0x06, 0xE1, 0x00,
  0x0F, 0x00, 0x00, 0xC9, 0x00,
  0x1F, 0x00, 0x00, 0x5F, 0x20,
  0xCF, 0xB0, 0x00, 0xAF, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_004C[ 44] = { /* code 004C, LATIN CAPITAL LETTER L */
  0xBF, 0xC0, 0x00, 0x00,
  0x0F, 0x10, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x20, 0x01, 0x5D,
  0xBF, 0xFF, 0xFF, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_004D[ 66] = { /* code 004D, LATIN CAPITAL LETTER M */
  0xAF, 0xE1, 0x00, 0x00, 0x09, 0xFC,
  0x0C, 0xF7, 0x00, 0x00, 0x1F, 0xE0,
  0x0C, 0xAD, 0x00, 0x00, 0x7C, 0xD0,
  0x0D, 0x4F, 0x50, 0x00, 0xD6, 0xF0,
  0x0D, 0x2A, 0xB0, 0x06, 0xC2, 0xF0,
  0x0D, 0x23, 0xF3, 0x0D, 0x50, 0xF0,
  0x0E, 0x00, 0xBA, 0x6C, 0x00, 0xF0,
  0x0F, 0x00, 0x4F, 0xE5, 0x00, 0xF0,
  0x0F, 0x00, 0x0B, 0xC0, 0x00, 0xF0,
  0x1F, 0x00, 0x02, 0x20, 0x00, 0xF1,
  0xCF, 0xC0, 0x00, 0x00, 0x0B, 0xFB
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_004E[ 60] = { /* code 004E, LATIN CAPITAL LETTER N */
  0xEF, 0xB0, 0x00, 0x0B, 0xFB,
  0x1F, 0xF8, 0x00, 0x00, 0xF0,
  0x0F, 0x5F, 0x40, 0x00, 0xF0,
  0x0F, 0x08, 0xE2, 0x00, 0xF0,
  0x0F, 0x00, 0xCB, 0x00, 0xF0,
  0x0F, 0x00, 0x2E, 0x60, 0xF0,
  0x0F, 0x00, 0x06, 0xE2, 0xF0,
  0x0F, 0x00, 0x00, 0xBA, 0xF0,
  0x0F, 0x00, 0x00, 0x2F, 0xF0,
  0x1F, 0x10, 0x00, 0x07, 0xF0,
  0xCF, 0xD0, 0x00, 0x00, 0xC0,
  0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_004F[ 55] = { /* code 004F, LATIN CAPITAL LETTER O */
  0x00, 0x5B, 0xFF, 0xB5, 0x00,
  0x08, 0xC3, 0x00, 0x3C, 0x80,
  0x4C, 0x10, 0x00, 0x01, 0xC4,
  0xB5, 0x00, 0x00, 0x00, 0x5B,
  0xE1, 0x00, 0x00, 0x00, 0x1E,
  0xF0, 0x00, 0x00, 0x00, 0x0F,
  0xE1, 0x00, 0x00, 0x00, 0x1E,
  0xB5, 0x00, 0x00, 0x00, 0x5B,
  0x4C, 0x10, 0x00, 0x01, 0xC4,
  0x08, 0xC3, 0x00, 0x3C, 0x80,
  0x00, 0x5B, 0xFF, 0xB5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0050[ 55] = { /* code 0050, LATIN CAPITAL LETTER P */
  0xCF, 0xFF, 0xFE, 0xB6, 0x00,
  0x1F, 0x50, 0x02, 0x5D, 0x80,
  0x0F, 0x00, 0x00, 0x02, 0xE0,
  0x0F, 0x00, 0x00, 0x01, 0xE0,
  0x0F, 0x00, 0x00, 0x09, 0x90,
  0x0F, 0x01, 0x37, 0xCB, 0x10,
  0x0F, 0xFE, 0xC9, 0x30, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x00,
  0x0F, 0x10, 0x00, 0x00, 0x00,
  0xBF, 0xE0, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0051[ 65] = { /* code 0051, LATIN CAPITAL LETTER Q */
  0x00, 0x5B, 0xFF, 0xB5, 0x00,
  0x08, 0xC3, 0x00, 0x3C, 0x80,
  0x4C, 0x10, 0x00, 0x01, 0xC4,
  0xB5, 0x00, 0x00, 0x00, 0x5B,
  0xE1, 0x00, 0x00, 0x00, 0x1E,
  0xF0, 0x00, 0x00, 0x00, 0x0F,
  0xE1, 0x00, 0x00, 0x00, 0x1E,
  0xB5, 0x00, 0x00, 0x00, 0x5B,
  0x4C, 0x10, 0x38, 0x01, 0xC4,
  0x08, 0xC3, 0x1C, 0x4C, 0x80,
  0x00, 0x5B, 0xFF, 0xE5, 0x00,
  0x00, 0x00, 0x01, 0xC6, 0x00,
  0x00, 0x00, 0x00, 0x19, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0052[ 55] = { /* code 0052, LATIN CAPITAL LETTER R */
  0xBF, 0xFF, 0xFF, 0xDA, 0x20,
  0x0F, 0x20, 0x01, 0x3A, 0xD0,
  0x0F, 0x00, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0x07, 0xB0,
  0x0F, 0x02, 0x47, 0xCB, 0x10,
  0x0F, 0xEC, 0xAE, 0xE1, 0x00,
  0x0F, 0x00, 0x03, 0xE9, 0x00,
  0x0F, 0x00, 0x00, 0x5F, 0x20,
  0x0F, 0x00, 0x00, 0x0B, 0x90,
  0x1F, 0x20, 0x00, 0x04, 0xE1,
  0xCF, 0xE0, 0x00, 0x2D, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0053[ 55] = { /* code 0053, LATIN CAPITAL LETTER S */
  0x00, 0x5B, 0xFF, 0xDA, 0x30,
  0x07, 0xA2, 0x03, 0xAF, 0xE0,
  0x0E, 0x10, 0x00, 0x05, 0xE0,
  0x0F, 0x20, 0x00, 0x00, 0x00,
  0x0A, 0xD7, 0x30, 0x00, 0x00,
  0x01, 0x8D, 0xFF, 0xC7, 0x00,
  0x00, 0x00, 0x24, 0x8E, 0xA0,
  0x00, 0x00, 0x00, 0x02, 0xF0,
  0xE5, 0x00, 0x00, 0x02, 0xD0,
  0x6F, 0x82, 0x01, 0x4C, 0x60,
  0x04, 0xBE, 0xFE, 0xB4, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0054[ 55] = { /* code 0054, LATIN CAPITAL LETTER T */
  0xCF, 0xFF, 0xFF, 0xFF, 0xC0,
  0xF7, 0x11, 0xF1, 0x17, 0xF0,
  0x20, 0x00, 0xF0, 0x00, 0x20,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x0D, 0xFB, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0055[ 66] = { /* code 0055, LATIN CAPITAL LETTER U */
  0xCF, 0xD0, 0x00, 0x00, 0xCF, 0xA0,
  0x0D, 0x40, 0x00, 0x00, 0x3C, 0x00,
  0x0D, 0x20, 0x00, 0x00, 0x2D, 0x00,
  0x0E, 0x10, 0x00, 0x00, 0x0E, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x00, 0x00, 0x0F, 0x00,
  0x0E, 0x20, 0x00, 0x00, 0x2D, 0x00,
  0x0A, 0x70, 0x00, 0x00, 0x7A, 0x00,
  0x03, 0xF7, 0x10, 0x27, 0xE2, 0x00,
  0x00, 0x3B, 0xEF, 0xEA, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0056[ 55] = { /* code 0056, LATIN CAPITAL LETTER V */
  0xDF, 0xC0, 0x00, 0x07, 0xFD,
  0x6F, 0x30, 0x00, 0x00, 0xD5,
  0x2F, 0x30, 0x00, 0x01, 0xF1,
  0x0C, 0x70, 0x00, 0x05, 0xB0,
  0x08, 0xC0, 0x00, 0x0A, 0x60,
  0x02, 0xF2, 0x00, 0x1E, 0x10,
  0x00, 0xB8, 0x00, 0x6A, 0x00,
  0x00, 0x5F, 0x10, 0xD3, 0x00,
  0x00, 0x0D, 0x96, 0xB0, 0x00,
  0x00, 0x05, 0xFE, 0x30, 0x00,
  0x00, 0x00, 0xA8, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0057[ 77] = { /* code 0057, LATIN CAPITAL LETTER W */
  0xCF, 0xB0, 0x02, 0xEF, 0x70, 0x07, 0xFC,
  0x5F, 0x10, 0x00, 0x7C, 0x00, 0x00, 0xB4,
  0x1F, 0x30, 0x00, 0xAC, 0x00, 0x00, 0xC1,
  0x0D, 0x60, 0x00, 0xDF, 0x10, 0x01, 0xC0,
  0x0A, 0x90, 0x03, 0xCE, 0x50, 0x04, 0x90,
  0x06, 0xE0, 0x07, 0x7A, 0xB0, 0x08, 0x50,
  0x02, 0xF3, 0x0D, 0x24, 0xF1, 0x0D, 0x10,
  0x00, 0xD8, 0x4C, 0x00, 0xD8, 0x3B, 0x00,
  0x00, 0x8D, 0xB6, 0x00, 0x7E, 0xA7, 0x00,
  0x00, 0x3F, 0xE1, 0x00, 0x1F, 0xF1, 0x00,
  0x00, 0x0B, 0x60, 0x00, 0x07, 0x90, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0058[ 55] = { /* code 0058, LATIN CAPITAL LETTER X */
  0xBF, 0xD1, 0x00, 0x6F, 0xF3,
  0x1D, 0xA0, 0x00, 0x0E, 0x60,
  0x04, 0xF4, 0x00, 0x79, 0x00,
  0x00, 0x9E, 0x14, 0xC1, 0x00,
  0x00, 0x0C, 0xCC, 0x20, 0x00,
  0x00, 0x03, 0xFB, 0x00, 0x00,
  0x00, 0x0B, 0x6D, 0x90, 0x00,
  0x00, 0x79, 0x02, 0xE6, 0x00,
  0x02, 0xD1, 0x00, 0x3E, 0x20,
  0x0C, 0x70, 0x00, 0x0B, 0xC0,
  0xBF, 0xB0, 0x00, 0x1F, 0xFB
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0059[ 55] = { /* code 0059, LATIN CAPITAL LETTER Y */
  0xDF, 0xE3, 0x00, 0x05, 0xFC,
  0x1D, 0xD0, 0x00, 0x00, 0xC2,
  0x04, 0xF6, 0x00, 0x02, 0x80,
  0x00, 0x8E, 0x20, 0x09, 0x10,
  0x00, 0x0B, 0xB0, 0x64, 0x00,
  0x00, 0x01, 0xD9, 0xA0, 0x00,
  0x00, 0x00, 0x3F, 0x10, 0x00,
  0x00, 0x00, 0x0F, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0x00, 0x00,
  0x00, 0x00, 0xCF, 0xB0, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_005A[ 44] = { /* code 005A, LATIN CAPITAL LETTER Z */
  0x3F, 0xFF, 0xFF, 0xFB,
  0x6B, 0x30, 0x00, 0xD7,
  0x10, 0x00, 0x08, 0xD1,
  0x00, 0x00, 0x4F, 0x40,
  0x00, 0x02, 0xE8, 0x00,
  0x00, 0x0C, 0xB0, 0x00,
  0x00, 0x9D, 0x10, 0x00,
  0x06, 0xF3, 0x00, 0x00,
  0x2E, 0x70, 0x00, 0x02,
  0xAD, 0x00, 0x02, 0x7E,
  0xEF, 0xFF, 0xFF, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_005B[ 26] = { /* code 005B, LEFT SQUARE BRACKET */
  0xFF, 0xF0,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xF0, 0x00,
  0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_005C[ 24] = { /* code 005C, REVERSE SOLIDUS */
  0x90, 0x00,
  0x90, 0x00,
  0x63, 0x00,
  0x18, 0x00,
  0x09, 0x00,
  0x07, 0x20,
  0x03, 0x60,
  0x00, 0xA0,
  0x00, 0x91,
  0x00, 0x55,
  0x00, 0x19,
  0x00, 0x09
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_005D[ 26] = { /* code 005D, RIGHT SQUARE BRACKET */
  0xFF, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_005E[ 20] = { /* code 005E, CIRCUMFLEX ACCENT */
  0x00, 0x0A, 0xFA, 0x00, 0x00,
  0x00, 0x8B, 0x1B, 0x80, 0x00,
  0x08, 0x80, 0x00, 0x88, 0x00,
  0x85, 0x00, 0x00, 0x05, 0x80
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_005F[  4] = { /* code 005F, LOW LINE */
  0xFF, 0xFF, 0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0060[  6] = { /* code 0060, GRAVE ACCENT */
  0xD2, 0x00,
  0x5C, 0x10,
  0x03, 0x70
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0061[ 28] = { /* code 0061, LATIN SMALL LETTER A */
  0xBE, 0xFE, 0x80, 0x00,
  0x00, 0x03, 0xD7, 0x00,
  0x00, 0x00, 0x4C, 0x00,
  0x18, 0xCF, 0xFF, 0x00,
  0xC8, 0x31, 0x0F, 0x00,
  0xF3, 0x01, 0x7F, 0x00,
  0x6E, 0xFC, 0x4B, 0x90
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0062[ 44] = { /* code 0062, LATIN SMALL LETTER B */
  0x8E, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x4C, 0xFE, 0x80,
  0x0F, 0x92, 0x03, 0xB8,
  0x0F, 0x00, 0x00, 0x2E,
  0x0F, 0x00, 0x00, 0x0F,
  0x0F, 0x10, 0x00, 0x3C,
  0x0B, 0xA1, 0x03, 0xD5,
  0x02, 0xBF, 0xFC, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0063[ 28] = { /* code 0063, LATIN SMALL LETTER C */
  0x05, 0xCF, 0xD6, 0x00,
  0x5C, 0x20, 0x8D, 0x00,
  0xC3, 0x00, 0x00, 0x00,
  0xF0, 0x00, 0x00, 0x00,
  0xE2, 0x00, 0x00, 0x00,
  0x8B, 0x20, 0x3C, 0x20,
  0x08, 0xEF, 0xC4, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0064[ 44] = { /* code 0064, LATIN SMALL LETTER D */
  0x00, 0x00, 0x09, 0xE0,
  0x00, 0x00, 0x01, 0xF0,
  0x00, 0x00, 0x00, 0xF0,
  0x00, 0x00, 0x00, 0xF0,
  0x05, 0xCF, 0xE8, 0xF0,
  0x5D, 0x40, 0x16, 0xF0,
  0xC3, 0x00, 0x00, 0xF0,
  0xF0, 0x00, 0x00, 0xF0,
  0xE2, 0x00, 0x00, 0xF0,
  0x7C, 0x30, 0x15, 0xF0,
  0x07, 0xDF, 0xD7, 0xBB
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0065[ 28] = { /* code 0065, LATIN SMALL LETTER E */
  0x04, 0xCF, 0xD5, 0x00,
  0x4E, 0x30, 0x4E, 0x00,
  0xC4, 0x00, 0x2E, 0x00,
  0xF1, 0x37, 0xD6, 0x00,
  0xEE, 0xC8, 0x30, 0x00,
  0x88, 0x10, 0x4B, 0x00,
  0x08, 0xEF, 0xB3, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0066[ 33] = { /* code 0066, LATIN SMALL LETTER F */
  0x00, 0x3E, 0xC0,
  0x00, 0xB6, 0xD0,
  0x00, 0xE1, 0x00,
  0x00, 0xF0, 0x00,
  0x2F, 0xFF, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x0D, 0xFB, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0067[ 40] = { /* code 0067, LATIN SMALL LETTER G */
  0x09, 0xFF, 0xFF, 0x20,
  0x8A, 0x11, 0xA6, 0x00,
  0xE2, 0x00, 0x2D, 0x00,
  0xF0, 0x00, 0x0F, 0x00,
  0xD2, 0x00, 0x2D, 0x00,
  0x8A, 0x11, 0xA7, 0x00,
  0x09, 0xEE, 0xE6, 0x00,
  0x00, 0x00, 0x2E, 0x00,
  0xC2, 0x01, 0x6D, 0x00,
  0x8E, 0xFE, 0xB2, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0068[ 44] = { /* code 0068, LATIN SMALL LETTER H */
  0x5E, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x5D, 0xFC, 0x30,
  0x0F, 0x81, 0x08, 0xB0,
  0x0F, 0x00, 0x01, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0xAF, 0xC0, 0x0A, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0069[ 20] = { /* code 0069, LATIN SMALL LETTER I */
  0x0C, 0x00,
  0x0C, 0x00,
  0x00, 0x00,
  0x8E, 0x00,
  0x1F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0xBF, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_006A[ 26] = { /* code 006A, LATIN SMALL LETTER J */
  0x00, 0xC0,
  0x00, 0xC0,
  0x00, 0x00,
  0x09, 0xE0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xF0,
  0x00, 0xE0,
  0x04, 0xB0,
  0xDE, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_006B[ 44] = { /* code 006B, LATIN SMALL LETTER K */
  0x8E, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x00, 0x00,
  0x0F, 0x00, 0x5F, 0xB0,
  0x0F, 0x00, 0x7B, 0x00,
  0x0F, 0x09, 0xB0, 0x00,
  0x0F, 0xCA, 0xE2, 0x00,
  0x0F, 0x20, 0x6B, 0x00,
  0x0F, 0x00, 0x0B, 0x40,
  0x9F, 0xC0, 0x0C, 0xD0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_006C[ 22] = { /* code 006C, LATIN SMALL LETTER L */
  0x9E, 0x00,
  0x1F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0xBF, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_006D[ 42] = { /* code 006D, LATIN SMALL LETTER M */
  0x9B, 0x9E, 0xE6, 0x8E, 0xE5, 0x00,
  0x0F, 0x60, 0x4F, 0x60, 0x4E, 0x00,
  0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00,
  0x0F, 0x00, 0x0F, 0x00, 0x0F, 0x00,
  0xBF, 0xC0, 0xBF, 0xC0, 0xBF, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_006E[ 28] = { /* code 006E, LATIN SMALL LETTER N */
  0xAA, 0x7D, 0xFC, 0x30,
  0x1F, 0x71, 0x07, 0xC0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0xBF, 0xC0, 0x0B, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_006F[ 28] = { /* code 006F, LATIN SMALL LETTER O */
  0x06, 0xDF, 0xD7, 0x00,
  0x6D, 0x30, 0x3D, 0x70,
  0xD3, 0x00, 0x03, 0xD0,
  0xF0, 0x00, 0x00, 0xF0,
  0xD3, 0x00, 0x03, 0xD0,
  0x7C, 0x30, 0x3C, 0x70,
  0x07, 0xDF, 0xD7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0070[ 40] = { /* code 0070, LATIN SMALL LETTER P */
  0xBB, 0x6D, 0xFD, 0x70,
  0x1F, 0x71, 0x03, 0xC7,
  0x0F, 0x00, 0x00, 0x2D,
  0x0F, 0x00, 0x00, 0x0F,
  0x0F, 0x00, 0x00, 0x3D,
  0x0F, 0x22, 0x03, 0xD6,
  0x0F, 0x6E, 0xFC, 0x60,
  0x0F, 0x00, 0x00, 0x00,
  0x1F, 0x10, 0x00, 0x00,
  0xDF, 0xC0, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0071[ 40] = { /* code 0071, LATIN SMALL LETTER Q */
  0x05, 0xCF, 0xE7, 0xB0,
  0x5D, 0x30, 0x2A, 0xF0,
  0xC3, 0x00, 0x01, 0xF0,
  0xF0, 0x00, 0x00, 0xF0,
  0xE2, 0x00, 0x00, 0xF0,
  0x8B, 0x30, 0x29, 0xF0,
  0x08, 0xEF, 0xC5, 0xF0,
  0x00, 0x00, 0x00, 0xF0,
  0x00, 0x00, 0x00, 0xF0,
  0x00, 0x00, 0x0D, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0072[ 21] = { /* code 0072, LATIN SMALL LETTER R */
  0xB8, 0x5D, 0xD0,
  0x2E, 0xA0, 0x00,
  0x0F, 0x20, 0x00,
  0x0F, 0x00, 0x00,
  0x0F, 0x00, 0x00,
  0x0F, 0x00, 0x00,
  0xCF, 0xB0, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0073[ 21] = { /* code 0073, LATIN SMALL LETTER S */
  0x3C, 0xFE, 0x80,
  0xE4, 0x04, 0xD0,
  0xE6, 0x10, 0x00,
  0x4C, 0xFD, 0x50,
  0x00, 0x16, 0xE0,
  0xD4, 0x04, 0xD0,
  0x4C, 0xFC, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0074[ 20] = { /* code 0074, LATIN SMALL LETTER T */
  0x09, 0x00,
  0x0B, 0x00,
  0x0E, 0x00,
  0xDF, 0xFE,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x00,
  0x0F, 0x10,
  0x0E, 0xEB
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0075[ 28] = { /* code 0075, LATIN SMALL LETTER U */
  0xBE, 0x00, 0x0B, 0xE0,
  0x1F, 0x00, 0x01, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x00, 0x00, 0xF0,
  0x0F, 0x10, 0x00, 0xF0,
  0x0B, 0x80, 0x18, 0xF2,
  0x03, 0xCF, 0xD5, 0xBB
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0076[ 28] = { /* code 0076, LATIN SMALL LETTER V */
  0xCF, 0x30, 0x0F, 0xD0,
  0x4C, 0x00, 0x08, 0x60,
  0x1E, 0x10, 0x0A, 0x20,
  0x0A, 0x60, 0x1B, 0x00,
  0x03, 0xC0, 0x75, 0x00,
  0x00, 0xB8, 0xC0, 0x00,
  0x00, 0x2E, 0x30, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0077[ 42] = { /* code 0077, LATIN SMALL LETTER W */
  0xCF, 0x40, 0x4F, 0x80, 0x0B, 0xD0,
  0x6B, 0x00, 0x0D, 0x40, 0x04, 0x80,
  0x3D, 0x00, 0x1F, 0x70, 0x07, 0x40,
  0x0D, 0x30, 0x6C, 0xD0, 0x0C, 0x10,
  0x08, 0x90, 0xC2, 0xE5, 0x59, 0x00,
  0x02, 0xFA, 0x70, 0x7D, 0xC2, 0x00,
  0x00, 0x7B, 0x00, 0x0C, 0x70, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0078[ 28] = { /* code 0078, LATIN SMALL LETTER X */
  0xBF, 0x40, 0x7F, 0x30,
  0x0C, 0x60, 0x86, 0x00,
  0x02, 0xE7, 0xA0, 0x00,
  0x00, 0x7F, 0x30, 0x00,
  0x02, 0xB6, 0xE2, 0x00,
  0x0B, 0x20, 0x8C, 0x10,
  0xBF, 0x30, 0x6F, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_0079[ 40] = { /* code 0079, LATIN SMALL LETTER Y */
  0xCF, 0x40, 0x0C, 0xD0,
  0x6C, 0x00, 0x06, 0x80,
  0x4B, 0x00, 0x06, 0x60,
  0x2E, 0x00, 0x09, 0x30,
  0x0D, 0x40, 0x0C, 0x00,
  0x07, 0xC1, 0x68, 0x00,
  0x00, 0xBF, 0xE1, 0x00,
  0x00, 0x05, 0x70, 0x00,
  0x00, 0x5A, 0x00, 0x00,
  0x9E, 0x80, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_007A[ 21] = { /* code 007A, LATIN SMALL LETTER Z */
  0x7F, 0xFF, 0xD0,
  0x82, 0x08, 0x80,
  0x00, 0x4C, 0x00,
  0x01, 0xD3, 0x00,
  0x0B, 0x60, 0x00,
  0x79, 0x02, 0xB0,
  0xEF, 0xFF, 0xC0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_007B[ 39] = { /* code 007B, LEFT CURLY BRACKET */
  0x00, 0x4C, 0xF0,
  0x00, 0xD6, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xE0, 0x00,
  0x17, 0xA0, 0x00,
  0xFB, 0x10, 0x00,
  0x17, 0xA0, 0x00,
  0x00, 0xE0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xD6, 0x00,
  0x00, 0x4C, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_007C[ 15] = { /* code 007C, VERTICAL LINE */
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0,
  0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_007D[ 39] = { /* code 007D, RIGHT CURLY BRACKET */
  0xFC, 0x40, 0x00,
  0x06, 0xD0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xE0, 0x00,
  0x00, 0xA7, 0x10,
  0x00, 0x1B, 0xF0,
  0x00, 0xA7, 0x10,
  0x00, 0xE0, 0x00,
  0x00, 0xF0, 0x00,
  0x00, 0xF0, 0x00,
  0x06, 0xD0, 0x00,
  0xFC, 0x40, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontSouvenir18_007E[ 15] = { /* code 007E, TILDE */
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x4C, 0xFD, 0x83, 0x03, 0xC0,
  0xA3, 0x02, 0x7D, 0xFC, 0x40
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontSouvenir18_CharInfo[95] = {
   {   1,   1,   0,  14,   4, acGUI_FontSouvenir18_0020 } /* code 0020, SPACE */
  ,{   2,  11,   1,   3,   4, acGUI_FontSouvenir18_0021 } /* code 0021, EXCLAMATION MARK */
  ,{   3,   4,   1,   3,   5, acGUI_FontSouvenir18_0022 } /* code 0022, QUOTATION MARK */
  ,{  10,  11,   1,   3,  11, acGUI_FontSouvenir18_0023 } /* code 0023, NUMBER SIGN */
  ,{   7,  15,   0,   1,   8, acGUI_FontSouvenir18_0024 } /* code 0024, DOLLAR SIGN */
  ,{  11,  13,   1,   2,  13, acGUI_FontSouvenir18_0025 } /* code 0025, PERCENT SIGN */
  ,{  11,  11,   1,   3,  11, acGUI_FontSouvenir18_0026 } /* code 0026, AMPERSAND */
  ,{   1,   4,   1,   3,   3, acGUI_FontSouvenir18_0027 } /* code 0027, APOSTROPHE */
  ,{   3,  13,   1,   3,   5, acGUI_FontSouvenir18_0028 } /* code 0028, LEFT PARENTHESIS */
  ,{   3,  13,   1,   3,   5, acGUI_FontSouvenir18_0029 } /* code 0029, RIGHT PARENTHESIS */
  ,{   5,   6,   1,   3,   7, acGUI_FontSouvenir18_002A } /* code 002A, ASTERISK */
  ,{   9,   9,   2,   5,  12, acGUI_FontSouvenir18_002B } /* code 002B, PLUS SIGN */
  ,{   2,   4,   1,  12,   4, acGUI_FontSouvenir18_002C } /* code 002C, COMMA */
  ,{   4,   1,   1,  10,   5, acGUI_FontSouvenir18_002D } /* code 002D, HYPHEN-MINUS */
  ,{   2,   2,   1,  12,   4, acGUI_FontSouvenir18_002E } /* code 002E, FULL STOP */
  ,{   4,  12,   0,   3,   4, acGUI_FontSouvenir18_002F } /* code 002F, SOLIDUS */
  ,{   7,  11,   0,   3,   8, acGUI_FontSouvenir18_0030 } /* code 0030, DIGIT ZERO */
  ,{   5,  11,   2,   3,   8, acGUI_FontSouvenir18_0031 } /* code 0031, DIGIT ONE */
  ,{   8,  11,  -1,   3,   8, acGUI_FontSouvenir18_0032 } /* code 0032, DIGIT TWO */
  ,{   7,  11,   0,   3,   8, acGUI_FontSouvenir18_0033 } /* code 0033, DIGIT THREE */
  ,{   8,  11,   0,   3,   8, acGUI_FontSouvenir18_0034 } /* code 0034, DIGIT FOUR */
  ,{   7,  11,   0,   3,   8, acGUI_FontSouvenir18_0035 } /* code 0035, DIGIT FIVE */
  ,{   7,  11,   0,   3,   8, acGUI_FontSouvenir18_0036 } /* code 0036, DIGIT SIX */
  ,{   7,  11,   1,   3,   8, acGUI_FontSouvenir18_0037 } /* code 0037, DIGIT SEVEN */
  ,{   7,  11,   0,   3,   8, acGUI_FontSouvenir18_0038 } /* code 0038, DIGIT EIGHT */
  ,{   7,  11,   0,   3,   8, acGUI_FontSouvenir18_0039 } /* code 0039, DIGIT NINE */
  ,{   2,   7,   1,   7,   4, acGUI_FontSouvenir18_003A } /* code 003A, COLON */
  ,{   2,   9,   1,   7,   4, acGUI_FontSouvenir18_003B } /* code 003B, SEMICOLON */
  ,{   9,   8,   2,   5,  12, acGUI_FontSouvenir18_003C } /* code 003C, LESS-THAN SIGN */
  ,{   9,   4,   2,   7,  12, acGUI_FontSouvenir18_003D } /* code 003D, EQUALS SIGN */
  ,{   9,   8,   2,   5,  12, acGUI_FontSouvenir18_003E } /* code 003E, GREATER-THAN SIGN */
  ,{   6,  11,   1,   3,   7, acGUI_FontSouvenir18_003F } /* code 003F, QUESTION MARK */
  ,{  13,  13,   1,   3,  15, acGUI_FontSouvenir18_0040 } /* code 0040, COMMERCIAL AT */
  ,{  10,  11,   0,   3,  10, acGUI_FontSouvenir18_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{   9,  11,   0,   3,  10, acGUI_FontSouvenir18_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{  10,  11,   1,   3,  10, acGUI_FontSouvenir18_0043 } /* code 0043, LATIN CAPITAL LETTER C */
  ,{  10,  11,   0,   3,  11, acGUI_FontSouvenir18_0044 } /* code 0044, LATIN CAPITAL LETTER D */
  ,{   9,  11,   0,   3,   9, acGUI_FontSouvenir18_0045 } /* code 0045, LATIN CAPITAL LETTER E */
  ,{   8,  11,   0,   3,   8, acGUI_FontSouvenir18_0046 } /* code 0046, LATIN CAPITAL LETTER F */
  ,{  11,  11,   1,   3,  12, acGUI_FontSouvenir18_0047 } /* code 0047, LATIN CAPITAL LETTER G */
  ,{  11,  11,   0,   3,  12, acGUI_FontSouvenir18_0048 } /* code 0048, LATIN CAPITAL LETTER H */
  ,{   3,  11,   0,   3,   4, acGUI_FontSouvenir18_0049 } /* code 0049, LATIN CAPITAL LETTER I */
  ,{   7,  11,   0,   3,   7, acGUI_FontSouvenir18_004A } /* code 004A, LATIN CAPITAL LETTER J */
  ,{   9,  11,   0,   3,   9, acGUI_FontSouvenir18_004B } /* code 004B, LATIN CAPITAL LETTER K */
  ,{   8,  11,   0,   3,   9, acGUI_FontSouvenir18_004C } /* code 004C, LATIN CAPITAL LETTER L */
  ,{  12,  11,   0,   3,  12, acGUI_FontSouvenir18_004D } /* code 004D, LATIN CAPITAL LETTER M */
  ,{  10,  12,   0,   3,  11, acGUI_FontSouvenir18_004E } /* code 004E, LATIN CAPITAL LETTER N */
  ,{  10,  11,   1,   3,  11, acGUI_FontSouvenir18_004F } /* code 004F, LATIN CAPITAL LETTER O */
  ,{   9,  11,   0,   3,   9, acGUI_FontSouvenir18_0050 } /* code 0050, LATIN CAPITAL LETTER P */
  ,{  10,  13,   1,   3,  11, acGUI_FontSouvenir18_0051 } /* code 0051, LATIN CAPITAL LETTER Q */
  ,{  10,  11,   0,   3,  10, acGUI_FontSouvenir18_0052 } /* code 0052, LATIN CAPITAL LETTER R */
  ,{   9,  11,   0,   3,   9, acGUI_FontSouvenir18_0053 } /* code 0053, LATIN CAPITAL LETTER S */
  ,{   9,  11,   0,   3,   9, acGUI_FontSouvenir18_0054 } /* code 0054, LATIN CAPITAL LETTER T */
  ,{  11,  11,   0,   3,  11, acGUI_FontSouvenir18_0055 } /* code 0055, LATIN CAPITAL LETTER U */
  ,{  10,  11,   0,   3,  10, acGUI_FontSouvenir18_0056 } /* code 0056, LATIN CAPITAL LETTER V */
  ,{  14,  11,   0,   3,  14, acGUI_FontSouvenir18_0057 } /* code 0057, LATIN CAPITAL LETTER W */
  ,{  10,  11,   0,   3,  10, acGUI_FontSouvenir18_0058 } /* code 0058, LATIN CAPITAL LETTER X */
  ,{  10,  11,   0,   3,  10, acGUI_FontSouvenir18_0059 } /* code 0059, LATIN CAPITAL LETTER Y */
  ,{   8,  11,   0,   3,   8, acGUI_FontSouvenir18_005A } /* code 005A, LATIN CAPITAL LETTER Z */
  ,{   3,  13,   1,   3,   5, acGUI_FontSouvenir18_005B } /* code 005B, LEFT SQUARE BRACKET */
  ,{   4,  12,   0,   3,   4, acGUI_FontSouvenir18_005C } /* code 005C, REVERSE SOLIDUS */
  ,{   3,  13,   0,   3,   5, acGUI_FontSouvenir18_005D } /* code 005D, RIGHT SQUARE BRACKET */
  ,{   9,   4,   3,   3,  15, acGUI_FontSouvenir18_005E } /* code 005E, CIRCUMFLEX ACCENT */
  ,{   8,   1,   0,  17,   7, acGUI_FontSouvenir18_005F } /* code 005F, LOW LINE */
  ,{   3,   3,   2,   3,   7, acGUI_FontSouvenir18_0060 } /* code 0060, GRAVE ACCENT */
  ,{   7,   7,   0,   7,   8, acGUI_FontSouvenir18_0061 } /* code 0061, LATIN SMALL LETTER A */
  ,{   8,  11,   0,   3,   8, acGUI_FontSouvenir18_0062 } /* code 0062, LATIN SMALL LETTER B */
  ,{   7,   7,   0,   7,   7, acGUI_FontSouvenir18_0063 } /* code 0063, LATIN SMALL LETTER C */
  ,{   8,  11,   0,   3,   8, acGUI_FontSouvenir18_0064 } /* code 0064, LATIN SMALL LETTER D */
  ,{   7,   7,   0,   7,   7, acGUI_FontSouvenir18_0065 } /* code 0065, LATIN SMALL LETTER E */
  ,{   5,  11,  -1,   3,   4, acGUI_FontSouvenir18_0066 } /* code 0066, LATIN SMALL LETTER F */
  ,{   7,  10,   0,   7,   7, acGUI_FontSouvenir18_0067 } /* code 0067, LATIN SMALL LETTER G */
  ,{   8,  11,   0,   3,   8, acGUI_FontSouvenir18_0068 } /* code 0068, LATIN SMALL LETTER H */
  ,{   3,  10,   0,   4,   4, acGUI_FontSouvenir18_0069 } /* code 0069, LATIN SMALL LETTER I */
  ,{   3,  13,  -1,   4,   4, acGUI_FontSouvenir18_006A } /* code 006A, LATIN SMALL LETTER J */
  ,{   7,  11,   0,   3,   7, acGUI_FontSouvenir18_006B } /* code 006B, LATIN SMALL LETTER K */
  ,{   3,  11,   0,   3,   3, acGUI_FontSouvenir18_006C } /* code 006C, LATIN SMALL LETTER L */
  ,{  11,   7,   0,   7,  12, acGUI_FontSouvenir18_006D } /* code 006D, LATIN SMALL LETTER M */
  ,{   8,   7,   0,   7,   8, acGUI_FontSouvenir18_006E } /* code 006E, LATIN SMALL LETTER N */
  ,{   7,   7,   0,   7,   8, acGUI_FontSouvenir18_006F } /* code 006F, LATIN SMALL LETTER O */
  ,{   8,  10,   0,   7,   8, acGUI_FontSouvenir18_0070 } /* code 0070, LATIN SMALL LETTER P */
  ,{   8,  10,   0,   7,   8, acGUI_FontSouvenir18_0071 } /* code 0071, LATIN SMALL LETTER Q */
  ,{   5,   7,   0,   7,   5, acGUI_FontSouvenir18_0072 } /* code 0072, LATIN SMALL LETTER R */
  ,{   5,   7,   0,   7,   6, acGUI_FontSouvenir18_0073 } /* code 0073, LATIN SMALL LETTER S */
  ,{   4,  10,   0,   4,   4, acGUI_FontSouvenir18_0074 } /* code 0074, LATIN SMALL LETTER T */
  ,{   8,   7,   0,   7,   8, acGUI_FontSouvenir18_0075 } /* code 0075, LATIN SMALL LETTER U */
  ,{   7,   7,   0,   7,   7, acGUI_FontSouvenir18_0076 } /* code 0076, LATIN SMALL LETTER V */
  ,{  11,   7,   0,   7,  11, acGUI_FontSouvenir18_0077 } /* code 0077, LATIN SMALL LETTER W */
  ,{   7,   7,   0,   7,   7, acGUI_FontSouvenir18_0078 } /* code 0078, LATIN SMALL LETTER X */
  ,{   7,  10,   0,   7,   7, acGUI_FontSouvenir18_0079 } /* code 0079, LATIN SMALL LETTER Y */
  ,{   5,   7,   0,   7,   6, acGUI_FontSouvenir18_007A } /* code 007A, LATIN SMALL LETTER Z */
  ,{   5,  13,   1,   3,   7, acGUI_FontSouvenir18_007B } /* code 007B, LEFT CURLY BRACKET */
  ,{   1,  15,   3,   3,   7, acGUI_FontSouvenir18_007C } /* code 007C, VERTICAL LINE */
  ,{   5,  13,   1,   3,   7, acGUI_FontSouvenir18_007D } /* code 007D, RIGHT CURLY BRACKET */
  ,{   9,   3,   1,   7,  12, acGUI_FontSouvenir18_007E } /* code 007E, TILDE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontSouvenir18_Prop1 = {
   0x0020 /* first character */
  ,0x007E /* last character  */
  ,&GUI_FontSouvenir18_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontSouvenir18 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,18 /* height of font  */
  ,18 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontSouvenir18_Prop1}
  ,18 /* Baseline */
  ,7 /* Height of lowercase characters */
  ,11 /* Height of capital characters */
};

/*********************************************************************
*                                                                    *
*       GUI_FontAA4_32                                               *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO_AntiAliasedText.c                                       *
*                                                                    *
**********************************************************************
*/
static GUI_CONST_STORAGE unsigned char acGUI_FontAA4_32_0041[180] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x00, 0x00, 0x3F, 0xFF, 0x20, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x8F, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xEF, 0xFF, 0xD0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x04, 0xFF, 0xFF, 0xF3, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x09, 0xFF, 0xEF, 0xF8, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x0E, 0xFF, 0x6F, 0xFD, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x5F, 0xFD, 0x0D, 0xFF, 0x40, 0x00, 0x00,
  0x00, 0x00, 0xAF, 0xF7, 0x07, 0xFF, 0x90, 0x00, 0x00,
  0x00, 0x01, 0xEF, 0xF2, 0x02, 0xFF, 0xE0, 0x00, 0x00,
  0x00, 0x05, 0xFF, 0xC0, 0x00, 0xCF, 0xF5, 0x00, 0x00,
  0x00, 0x0A, 0xFF, 0x70, 0x00, 0x7F, 0xFA, 0x00, 0x00,
  0x00, 0x1F, 0xFF, 0x20, 0x00, 0x1F, 0xFF, 0x10, 0x00,
  0x00, 0x6F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50, 0x00,
  0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF1, 0x00,
  0x07, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0xAF, 0xF6, 0x00,
  0x0B, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x5F, 0xFB, 0x00,
  0x2F, 0xFF, 0x10, 0x00, 0x00, 0x00, 0x0E, 0xFF, 0x20,
  0x7F, 0xFA, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0x70,
  0xCF, 0xF5, 0x00, 0x00, 0x00, 0x00, 0x04, 0xFF, 0xC0
};

static GUI_CONST_STORAGE unsigned char acGUI_FontAA4_32_0042[140] = { /* code 0042, LATIN CAPITAL LETTER B */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0xB6, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5,
  0xFF, 0xF0, 0x00, 0x00, 0x13, 0xCF, 0xFC,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x2F, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x3F, 0xFC,
  0xFF, 0xF0, 0x00, 0x00, 0x14, 0xDF, 0xF7,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2,
  0xFF, 0xF0, 0x00, 0x00, 0x26, 0xEF, 0xF9,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x5F, 0xFD,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x0F, 0xFF,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x4F, 0xFD,
  0xFF, 0xF0, 0x00, 0x00, 0x14, 0xEF, 0xFA,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF3,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0xB4, 0x00
};

static GUI_CONST_STORAGE unsigned char acGUI_FontAA4_32_0043[140] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x00, 0x5B, 0xEF, 0xEB, 0x60, 0x00,
  0x00, 0x1B, 0xFF, 0xFF, 0xFF, 0xFC, 0x10,
  0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0,
  0x07, 0xFF, 0xF7, 0x10, 0x3A, 0xFF, 0xF7,
  0x0E, 0xFF, 0x50, 0x00, 0x00, 0xAF, 0xFD,
  0x5F, 0xFB, 0x00, 0x00, 0x00, 0x2D, 0x83,
  0x9F, 0xF6, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0xF3, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xEF, 0xF1, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xEF, 0xF1, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xF3, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xAF, 0xF6, 0x00, 0x00, 0x00, 0x2D, 0x83,
  0x6F, 0xFB, 0x00, 0x00, 0x00, 0x7F, 0xFD,
  0x1F, 0xFF, 0x40, 0x00, 0x01, 0xEF, 0xF8,
  0x08, 0xFF, 0xE7, 0x10, 0x4D, 0xFF, 0xF2,
  0x00, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x00, 0x1C, 0xFF, 0xFF, 0xFF, 0xF9, 0x00,
  0x00, 0x00, 0x6B, 0xEF, 0xEA, 0x40, 0x00
};

static GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontAA4_32_CharInfo[3] = {
   {  17,  20,   0,   6,  17, acGUI_FontAA4_32_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{  14,  20,   2,   6,  17, acGUI_FontAA4_32_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{  14,  20,   1,   6,  17, acGUI_FontAA4_32_0043 } /* code 0043, LATIN CAPITAL LETTER C */
};

static GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontAA4_32_Prop1 = {
   0x0041 /* first character */
  ,0x0043 /* last character  */
  ,&GUI_FontAA4_32_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontAA4_32 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,33 /* height of font  */
  ,33 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontAA4_32_Prop1}
  ,33 /* Baseline */
  ,15 /* Height of lowercase characters */
  ,20 /* Height of capital characters */
};

/*********************************************************************
*                                                                    *
*       GUI_FontAA2_32                                               *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO_AntiAliasedText.c                                       *
*                                                                    *
**********************************************************************
*/
static GUI_CONST_STORAGE unsigned char acGUI_FontAA2_32_0041[100] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x03, 0xF0, 0x00, 0x00,
  0x00, 0x0B, 0xF4, 0x00, 0x00,
  0x00, 0x0F, 0xFC, 0x00, 0x00,
  0x00, 0x1F, 0xFC, 0x00, 0x00,
  0x00, 0x2F, 0xFE, 0x00, 0x00,
  0x00, 0x3F, 0x7F, 0x00, 0x00,
  0x00, 0x7F, 0x3F, 0x40, 0x00,
  0x00, 0xBD, 0x1F, 0x80, 0x00,
  0x00, 0xFC, 0x0F, 0xC0, 0x00,
  0x01, 0xFC, 0x0F, 0xD0, 0x00,
  0x02, 0xF4, 0x07, 0xE0, 0x00,
  0x03, 0xF0, 0x03, 0xF0, 0x00,
  0x07, 0xFF, 0xFF, 0xF4, 0x00,
  0x0B, 0xFF, 0xFF, 0xF8, 0x00,
  0x0F, 0xFF, 0xFF, 0xFC, 0x00,
  0x1F, 0x80, 0x00, 0xBD, 0x00,
  0x3F, 0x40, 0x00, 0x7F, 0x00,
  0x3F, 0x00, 0x00, 0x3F, 0x00,
  0x7E, 0x00, 0x00, 0x2F, 0x40,
  0xFD, 0x00, 0x00, 0x1F, 0xC0
};

static GUI_CONST_STORAGE unsigned char acGUI_FontAA2_32_0042[ 80] = { /* code 0042, LATIN CAPITAL LETTER B */
  0xFF, 0xFF, 0xF9, 0x00,
  0xFF, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0xFF, 0xD0,
  0xFC, 0x00, 0x0F, 0xF0,
  0xFC, 0x00, 0x03, 0xF0,
  0xFC, 0x00, 0x03, 0xF0,
  0xFC, 0x00, 0x03, 0xF0,
  0xFC, 0x00, 0x1F, 0xD0,
  0xFF, 0xFF, 0xFF, 0xC0,
  0xFF, 0xFF, 0xFF, 0x40,
  0xFF, 0xFF, 0xFF, 0xC0,
  0xFC, 0x00, 0x1F, 0xE0,
  0xFC, 0x00, 0x07, 0xF0,
  0xFC, 0x00, 0x03, 0xF0,
  0xFC, 0x00, 0x03, 0xF0,
  0xFC, 0x00, 0x07, 0xF0,
  0xFC, 0x00, 0x1F, 0xE0,
  0xFF, 0xFF, 0xFF, 0xC0,
  0xFF, 0xFF, 0xFF, 0x40,
  0xFF, 0xFF, 0xF9, 0x00
};

static GUI_CONST_STORAGE unsigned char acGUI_FontAA2_32_0043[ 80] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x6F, 0xE4, 0x00,
  0x02, 0xFF, 0xFF, 0x00,
  0x0F, 0xFF, 0xFF, 0xC0,
  0x1F, 0xD0, 0x2F, 0xD0,
  0x3F, 0x40, 0x0B, 0xF0,
  0x7E, 0x00, 0x03, 0x80,
  0xBD, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x00, 0x00,
  0xFC, 0x00, 0x00, 0x00,
  0xBD, 0x00, 0x03, 0x80,
  0x7E, 0x00, 0x07, 0xF0,
  0x3F, 0x40, 0x0F, 0xE0,
  0x2F, 0xD0, 0x7F, 0xC0,
  0x0F, 0xFF, 0xFF, 0x80,
  0x03, 0xFF, 0xFE, 0x00,
  0x00, 0x7F, 0xE0, 0x00
};

static GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontAA2_32_CharInfo[3] = {
   {  17,  20,   0,   6,  17, acGUI_FontAA2_32_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{  14,  20,   2,   6,  17, acGUI_FontAA2_32_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{  14,  20,   1,   6,  17, acGUI_FontAA2_32_0043 } /* code 0043, LATIN CAPITAL LETTER C */
};

static GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontAA2_32_Prop1 = {
   0x0041 /* first character */
  ,0x0043 /* last character  */
  ,&GUI_FontAA2_32_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontAA2_32 = {
   GUI_FONTTYPE_PROP_AA2_EXT /* type of font    */
  ,33 /* height of font  */
  ,33 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontAA2_32_Prop1}
  ,33 /* Baseline */
  ,15 /* Height of lowercase characters */
  ,20 /* Height of capital characters */
};

/*********************************************************************
*                                                                    *
*       Bitmaps                                                      *
*                                                                    *
**********************************************************************
*/
/*********************************************************************
*                                                                    *
*       bmSeggerLogo                                                 *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO_Intro.c                                                 *
*                                                                    *
**********************************************************************
*/
static GUI_CONST_STORAGE GUI_COLOR ColorsSeggerLogo[] = {
     0x00FF00,0xFEFEFE,0x201F23,0xA02020
    ,0xE6E6E6,0xBB6060,0xE2BCBC,0xDEDEDE
    ,0x000000,0xFAFAFA,0x212024,0xD3D3D4
    ,0xD5D4D5,0xF1DEDE,0xE0E0E1,0xFCFCFC
    ,0xF1F1F1,0xFCF8F8,0xFEFCFC,0x424145
    ,0xC3C3C4,0xA12222,0xC1C1C2,0x808082
    ,0x444346,0x606062,0xA22626,0xEDEDED
    ,0xA73030,0x000000,0x252528,0x363539
    ,0xB1B0B2,0xE4E4E4,0xF8EEEE,0xB24A4A
    ,0xB34C4C,0x88888A,0xCF8E8E,0xEAEAEA
    ,0xF6F6F6,0x18171A,0x18181B,0x2A292D
    ,0x39383C,0x6C6C6E,0xA22424,0xAA3636
    ,0xCDCDCE,0xF2E0E0,0xF7ECEC,0xFDFAFA
    ,0x28272A,0x2E2D31,0x454448,0xA62E2E
    ,0xB95B5B,0xC47676,0x8E8E90,0xD09090
    ,0xC7C7C8,0xCACACB,0xD9D9DA,0xE5C2C2
    ,0xE6C4C4,0xF3E2E2,0x0D0C0E,0x1E1E21
    ,0x1F1E22,0x222222,0x3C3B3E,0x404043
    ,0x48474A,0x49484C,0xA52B2B,0xAC3B3B
    ,0xAC3D3D,0xBB5E5E,0xC67A7A,0x848385
    ,0xA5A5A6,0xA7A6A8,0xE3BEBE,0xC5C4C5
    ,0xDCDCDC,0xE4C0C0,0xEBD1D1,0xECD3D3
    ,0xF0DCDC,0xF4E4E4,0xF6E9E9,0xF4F4F4
    ,0xFBF6F6,0x242327,0x2C2B2E,0x2D2C2F
    ,0x323135,0x353436,0x555558,0x666568
    ,0x6A6A6C,0x727174,0x7C7B7E,0x7D7C7F
    ,0xA83232,0xA83434,0xB14848,0xB44E4E
    ,0xBC6161,0xBE6767,0x8A8A8C,0x8D8C8F
    ,0x909092,0x949496,0xADACAE,0xAEAEAE
    ,0xB5B5B6,0xB7B7B8,0xBABABB,0xCD8A8A
    ,0xDEB2B2,0xE2BBBB,0xD2D1D2,0xD7D7D8
    ,0xEACECE,0xF3E4E4,0x020202,0x191919
    ,0x343336,0x38373A,0x4F4E51,0x525255
    ,0x59585B,0x59585C,0x5E5D60,0x605F62
    ,0x646467,0x7A7A7C,0xA32828,0xA42929
    ,0xAB3939,0xAB3A3A,0xAF4444,0xB45050
    ,0xB75656,0xB85858,0xBA5D5D,0xBF6868
    ,0xC16E6E,0xC26F6F,0xC27070,0xC47373
    ,0xC57878,0xC77C7C,0xC87E7E,0x7E7E80
    ,0x848486,0x878688,0x8C8B8D,0x908F91
    ,0x9A9A9C,0x9D9C9E,0x9F9EA0,0xA1A1A2
    ,0xA4A3A5,0xA9A9AA,0xB4B3B5,0xBAB9BB
    ,0xC98080,0xCA8383,0xCE8C8C,0xD7A2A2
    ,0xD9A5A5,0xD9A6A6,0xDAA9A9,0xDBABAB
    ,0xDCACAC,0xDDAFAF,0xE0B6B6,0xE1B9B9
    ,0xE4BFBF,0xCFCFD0,0xE7C6C6,0xE8C8C8
    ,0xEDD5D5,0xEED6D6,0xEFD9D9,0xEFDADA
    ,0xF4E6E6,0xF5E8E8,0xF7EBEB,0xF9F1F1
    ,0xFAF3F3,0xFBF5F5,0xFCF7F7,0x0B0B0D
    ,0x0C0B0D,0x171717,0x252427,0x302F33
    ,0x313033,0x4C4B4E,0x4D4C4F,0x545356
    ,0x58575A,0x5C5C5F,0x636265,0x646366
    ,0x6C6B6E,0x706F71,0x78787A,0xA52D2D
    ,0xA62D2D,0xB14747,0xB55252,0xB65353
    ,0xB65454,0xB95C5C,0xBE6565,0xC47575
    ,0x838284,0x888789,0x929294,0x949395
    ,0x989799,0x98989A,0x9C9C9D,0x9E9D9F
    ,0xA3A2A4,0xABABAC,0xAFAEB0,0xB3B3B4
    ,0xB8B7B9,0xB9B8BA,0xBBBBBC,0xBCBBBC
    ,0xBDBDBE,0xCB8686,0xCC8686,0xCD8989
    ,0xD29595,0xD29696,0xD49B9B,0xD59C9C
    ,0xDEB0B0,0xC8C7C8,0xC9C8C9,0xCBCBCC
    ,0xD4D3D4,0xDBDBDC,0xEACDCD,0xE3E3E4
    ,0xEBEBEC,0xEFEFF0,0xF3F3F4,0xFBFBFC
};

static GUI_CONST_STORAGE GUI_LOGPALETTE PalSeggerLogo = {
  256,	/* number of entries */
  1, 	/* Has transparency */
  &ColorsSeggerLogo[0]
};

static GUI_CONST_STORAGE unsigned char acSeggerLogo[] = {
  0x00, 0x00, 0x08, 0x42, 0x2A, 0x44, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x44, 0x2A, 0x42, /**/0x42, 0x00, 0x00,
  0x00, 0x7E, 0x2A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x34, 0x7F, 0x00,
  0x08, 0x29, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x29, 0x08,
  0xC3, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0xC4,
  0x29, 0x02, 0x02, 0x02, 0x46, 0x3C, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x3C, 0x46, 0x02, 0x02, 0x02, 0x29,
  0x43, 0x02, 0x02, 0x02, 0xA7, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x76, 0x02, 0x02, 0x02, 0x43,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0xB1, 0x4C, 0x23, 0x57, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x5A, 0x38, 0x2F, 0x26, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0xBF, 0x1A, 0x03, 0x03, 0x2F, 0x32, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x98, 0x03, 0x03, 0x03, 0xAB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x32, 0x15, 0x03, 0x03, 0x03, 0x92, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x94, 0x03, 0x03, 0x03, 0x1A, 0x7C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x9A, 0x03, 0x03, 0x03, 0x03, 0x3B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xB9, 0x8A, 0x03, 0x03, 0x03, 0x8C, 0x32, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x11, 0x24, 0x03, 0x03, 0x03, 0x15, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xAF, 0x03, 0x03, 0x03, 0x03, 0x38, 0x12, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x31, 0x37, 0x03, 0x03, 0x03, 0x2F, 0x5A, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x4E, 0x03, 0x03, 0x03, 0x03, 0x77, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0xB3, 0x03, 0x03, 0x03, 0x03, 0x91, 0x12, 0x01, 0x01, 0x01, 0x01, 0x01, 0x11, 0x6B, 0x03, 0x03, 0x03, 0x03, 0x79, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xA9, 0x03, 0x03, 0x03, 0x03, 0xAA, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x7D, 0x1C, 0x03, 0x03, 0x03, 0x37, 0x31, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x33, 0xD6, 0x03, 0x03, 0x03, 0x15, 0x55, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x55, 0x15, 0x03, 0x03, 0x03, 0x23, 0xC2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x59, 0x1C, 0x03, 0x03, 0x03, 0x68, 0xBC, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x3B, 0x03, 0x03, 0x03, 0x03, 0x97, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x52, 0x15, 0x03, 0x03, 0x03, 0xD8, 0x33, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x6C, 0x03, 0x03, 0x03, 0x03, 0xAC, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xEF, 0x03, 0x03, 0x03, 0x03, 0xEE, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x22, 0x4C, 0x03, 0x03, 0x03, 0x1A, 0x56, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x12, 0x90, 0x03, 0x03, 0x03, 0x03, 0x06, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x57, 0x8A, 0x03, 0x03, 0x03, 0x4B, 0x22, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0xC0, 0x11, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xBD, 0x68, 0x03, 0x03, 0x03, 0x1C, 0x7D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xAE, 0x03, 0x03, 0x03, 0x03, 0x4D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x99, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x3F, 0x15, 0x03, 0x03, 0x03, 0x8F, 0x33, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x98, 0x03, 0x03, 0x03, 0x03, 0x26, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x78, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x26, 0x03, 0x03, 0x03, 0x03, 0xA8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x11, 0x24, 0x03, 0x03, 0x03, 0x15, 0xB4, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x4A, 0x0D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x38, 0x03, 0x03, 0x03, 0x03, 0xB2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x41, 0x1C, 0x03, 0x03, 0x03, 0x1C, 0x41, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x6A, 0xC2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xBE, 0x2F, 0x03, 0x03, 0x03, 0xD4, 0x31, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x52, 0x15, 0x03, 0x03, 0x03, 0x24, 0x11, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x39, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xB7, 0x15, 0x03, 0x03, 0x03, 0x24, 0x11, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x26, 0x03, 0x03, 0x03, 0x03, 0x95, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x3F, 0x11, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0xB0, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xF0, 0x03, 0x03, 0x03, 0x03, 0x99, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x4D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x23, 0x11, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x8B, 0xBA, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x05, 0x03, 0x03, 0x03, 0x03, 0x78, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x22, 0x4B, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0xB6, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x8E, 0xC1, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x22, 0x8D, 0x03, 0x03, 0x03, 0x4A, 0x0D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x56, 0x1A, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x1A, 0x59, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x96, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFA, 0x2E, 0x03, 0x03, 0x03, 0x6A, 0xC2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x93, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x6D, 0x3F, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0xAD, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xF2, 0x03, 0x03, 0x03, 0x03, 0x39, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x8A, 0xB8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xDA, 0x03, 0x03, 0x03, 0x03, 0xBB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x8A, 0xB8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x6D, 0x03, 0x03, 0x03, 0x03, 0xBB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0xAD, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xF3, 0x03, 0x03, 0x03, 0x03, 0xDB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x96, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x7C, 0x1A, 0x03, 0x03, 0x03, 0xD5, 0x5C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x93, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x6D, 0x3F, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x8E, 0xC1, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x22, 0x8D, 0x03, 0x03, 0x03, 0x4A, 0x58, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x56, 0x1A, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x1A, 0x59, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0x8B, 0xBA, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x6C, 0x03, 0x03, 0x03, 0x03, 0xF4, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x22, 0x4B, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0xB6, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x03, 0xB0, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xF1, 0x03, 0x03, 0x03, 0x03, 0x4E, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x4D, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x23, 0x11, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x03, 0x39, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xB7, 0x15, 0x03, 0x03, 0x03, 0x23, 0x11, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x26, 0x03, 0x03, 0x03, 0x03, 0x95, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x3F, 0x11, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x03, 0x6A, 0xC2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xBE, 0x2F, 0x03, 0x03, 0x03, 0xD3, 0x31, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x52, 0x15, 0x03, 0x03, 0x03, 0x24, 0x11, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x4A, 0x0D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xD9, 0x03, 0x03, 0x03, 0x03, 0xB2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x41, 0x1C, 0x03, 0x03, 0x03, 0x1C, 0x41, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x03, 0x78, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x3B, 0x03, 0x03, 0x03, 0x03, 0xA8, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x11, 0x24, 0x03, 0x03, 0x03, 0x15, 0xB4, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x0D, 0x99, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x15, 0x03, 0x03, 0x03, 0x6B, 0x33, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x98, 0x03, 0x03, 0x03, 0x03, 0x26, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0xC0, 0x11, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xBD, 0x69, 0x03, 0x03, 0x03, 0x37, 0x41, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xAE, 0x03, 0x03, 0x03, 0x03, 0x4D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x28, 0xEC,
        0xE0, 0x25, 0x25, 0xA1, 0xF6, 0x0F, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x0E, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x3E, 0x0F, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0xE6, 0x9F, 0x9D, 0x70, 0x72, 0x0E, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x75, 0x71, 0x9D, 0x6F, 0x51, 0x0C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x3E, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B, 0x07, 0x01, 0x01, 0x01, 0x01, 0xFC, 0xF8, 0x0B, 0x0B, 0x0B, 0x0B, 0x0B,
        0x0C, 0x21, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x12, 0x90, 0x03, 0x03, 0x03, 0x03, 0x79, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x57, 0x8A, 0x03, 0x03, 0x03, 0x4B, 0x22, 0x01, 0x01, 0x01, 0x01, 0x01, 0x71, 0x5E, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x2C, 0x20, 0x01, 0x01, 0x01, 0x01, 0xDD, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x84, 0x01, 0x01, 0x01, 0x01, 0xFB, 0x63, 0x0A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x83, 0x3D, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x10, 0x66, 0x1E, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x47, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x87, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x17, 0x01, 0x01, 0x16, 0x5D, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0xC8, 0x4F, 0x28, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x77, 0x03, 0x03, 0x03, 0x03, 0xED, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x22, 0x4C, 0x03, 0x03, 0x03, 0x1A, 0x56, 0x01, 0x01, 0x01, 0x01, 0x01, 0x70, 0x02, 0x02, 0x02,
        0x18, 0xCE, 0x85, 0x5E, 0x02, 0x02, 0x02, 0x72, 0x01, 0x01, 0x01, 0x48, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x30, 0x35, 0x02, 0x02, 0x02, 0x35, 0x36, 0x61, 0x02, 0x02, 0x02, 0x0A, 0xE7, 0x01, 0x01,
        0x01, 0x01, 0x04, 0x47, 0x02, 0x02, 0x02, 0x34, 0x18, 0x46, 0x02, 0x02, 0x02, 0x02, 0x6E, 0x01, 0x01, 0x01, 0x01, 0x0A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x25, 0x01, 0x01, 0x9C, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x88, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x52, 0x15, 0x03, 0x03, 0x03, 0xD7, 0x33, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x6C, 0x03, 0x03, 0x03, 0x03, 0xAC, 0x01, 0x01, 0x01, 0x01, 0x01, 0x0F, 0x5F, 0x02, 0x02, 0xA4,
        0x01, 0x01, 0x01, 0x5B, 0x63, 0x02, 0x02, 0x84, 0x01, 0x01, 0x01, 0x18, 0x02, 0x02, 0x71, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x04, 0x01, 0x01, 0x01, 0x1B, 0x80, 0x02, 0x02, 0x1F, 0xEB, 0x01, 0x01, 0x01, 0x3C, 0x2C, 0x02, 0x02, 0x36, 0x01, 0x01,
        0x01, 0x01, 0x82, 0x02, 0x02, 0x34, 0xA4, 0x09, 0x01, 0x01, 0xF9, 0x82, 0x02, 0x02, 0x5D, 0x28, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x74, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0xFC, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x85, 0x04, 0x04, 0x04, 0x21,
        0x7A, 0x63, 0x02, 0x02, 0x02, 0xFB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x59, 0x1C, 0x03, 0x03, 0x03, 0x68, 0xBC, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x3B, 0x03, 0x03, 0x03, 0x03, 0x97, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x1B, 0x02, 0x02, 0x02, 0xF9,
        0x01, 0x01, 0x01, 0x01, 0x09, 0x3A, 0x19, 0x14, 0x01, 0x01, 0x01, 0x18, 0x02, 0x02, 0x51, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x6E, 0x02, 0x02, 0x1E, 0x3E, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFB, 0xC9, 0x0A, 0x25, 0x01, 0x01,
        0x01, 0xA6, 0x02, 0x02, 0x02, 0xA6, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFE, 0xCF, 0x0A, 0x19, 0x01, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x30, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x1B, 0x02, 0x02, 0x02, 0x3C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x33, 0xD6, 0x03, 0x03, 0x03, 0x15, 0x55, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x55, 0x15, 0x03, 0x03, 0x03, 0x23, 0xC2, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x0F, 0x5E, 0x02, 0x02, 0x2C,
        0xA0, 0x3E, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x18, 0x02, 0x02, 0xA4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x0F, 0x01, 0x01, 0x01, 0x01, 0x47, 0x02, 0x02, 0x65, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFF, 0x01, 0x01, 0x01,
        0x01, 0x64, 0x02, 0x02, 0x48, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFF, 0x01, 0x01, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x3D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x01, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x0E, 0x02, 0x02, 0x02, 0x54, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xA9, 0x03, 0x03, 0x03, 0x03, 0xAA, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x7D, 0x1C, 0x03, 0x03, 0x03, 0x37, 0x31, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x70, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x5E, 0xCC, 0x6E, 0x53, 0x0F, 0x01, 0x01, 0x01, 0x01, 0x18, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x36, 0xFD, 0x01, 0x09, 0x0A, 0x02, 0x02, 0xA5, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x10, 0x10, 0x10, 0xFF, 0x01, 0x01,
        0x01, 0x18, 0x02, 0x02, 0x67, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFE, 0x10, 0x10, 0x10, 0x09, 0x01, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x0A, 0x86, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x49, 0x20, 0x20, 0x20, 0x20,
        0xE3, 0x18, 0x02, 0x02, 0xCD, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0xB3, 0x03, 0x03, 0x03, 0x03, 0x91, 0x12, 0x01, 0x01, 0x01, 0x01, 0x01, 0x11, 0x6B, 0x03, 0x03, 0x03, 0x03, 0x79, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x73, 0x49, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x2C, 0x76, 0x01, 0x01, 0x01, 0x18, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x1E, 0x21, 0x01, 0x1B, 0x02, 0x02, 0x02, 0x75, 0x01, 0x01, 0x01, 0x10, 0x2C, 0x02, 0x02, 0x02, 0x02, 0xC6, 0xE4, 0x01,
        0x01, 0x81, 0x02, 0x02, 0x9E, 0x01, 0x01, 0x01, 0x01, 0x62, 0x02, 0x02, 0x02, 0x02, 0x0A, 0x66, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x1F, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x0A, 0x89, 0x5B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x31, 0x37, 0x03, 0x03, 0x03, 0x69, 0x5A, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x4E, 0x03, 0x03, 0x03, 0x03, 0x77, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x0E,
        0xE5, 0x9B, 0xCA, 0x0A, 0x02, 0x02, 0x02, 0x5D, 0x7A, 0x01, 0x01, 0x18, 0x02, 0x02, 0xDC, 0x16, 0x16, 0x16, 0x16, 0x16, 0x14, 0x04, 0x01, 0x01, 0x09, 0x0A, 0x02, 0x02, 0x50, 0x01, 0x01, 0x01, 0xFC, 0x2B, 0x02, 0x02, 0x02, 0x02, 0x02, 0x2D, 0x01,
        0x01, 0x18, 0x02, 0x02, 0x89, 0x01, 0x01, 0x01, 0x01, 0x13, 0x02, 0x02, 0x02, 0x02, 0x02, 0x18, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0xE3, 0x16, 0x16, 0x16, 0x16, 0x16, 0x53, 0xFD, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x0A, 0x1E, 0x1E, 0x1E, 0x0A,
        0x02, 0x02, 0x02, 0x35, 0xB5, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x11, 0x24, 0x03, 0x03, 0x03, 0x15, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xAF, 0x03, 0x03, 0x03, 0x03, 0x38, 0x12, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x28, 0x7B, 0x0F, 0x01,
        0x01, 0x01, 0x01, 0x27, 0x9F, 0x5D, 0x02, 0x02, 0x9C, 0x01, 0x01, 0x18, 0x02, 0x02, 0x51, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x13, 0x02, 0x02, 0xD0, 0x01, 0x01, 0x01, 0x01, 0x27, 0x3D, 0x3D, 0x17, 0x02, 0x02, 0x2D, 0x01,
        0x01, 0x2D, 0x02, 0x02, 0x13, 0x01, 0x01, 0x01, 0x01, 0xFE, 0xF7, 0x3D, 0xA3, 0x02, 0x02, 0x18, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x30, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01,
        0x7B, 0x1F, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x9A, 0x03, 0x03, 0x03, 0x03, 0x3B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xB9, 0x8A, 0x03, 0x03, 0x03, 0x8C, 0x32, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x36, 0x02, 0x83, 0x28,
        0x01, 0x01, 0x01, 0x01, 0x01, 0xD1, 0x02, 0x02, 0xD2, 0x01, 0x01, 0x18, 0x02, 0x02, 0x51, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x3A, 0x02, 0x02, 0x5D, 0x30, 0x01, 0x01, 0x01, 0x01, 0x01, 0x09, 0x48, 0x02, 0x02, 0x2D, 0x01,
        0x01, 0xE8, 0x02, 0x02, 0x02, 0x50, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xD0, 0x02, 0x02, 0x18, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0x30, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x17, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x67, 0x02, 0x02, 0x47, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x32, 0x15, 0x03, 0x03, 0x03, 0x92, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x94, 0x03, 0x03, 0x03, 0x1A, 0x7C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFF, 0x2B, 0x02, 0x02, 0x87,
        0xFC, 0x01, 0x01, 0x01, 0x54, 0x81, 0x02, 0x02, 0xE4, 0x01, 0x01, 0x18, 0x02, 0x02, 0xDC, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x0E, 0x01, 0x01, 0x10, 0x81, 0x02, 0x02, 0x5F, 0x51, 0x28, 0x01, 0x01, 0x0C, 0xCC, 0x02, 0x02, 0x02, 0x2D, 0x01,
        0x01, 0x01, 0xCB, 0x02, 0x02, 0x5D, 0x3A, 0x1B, 0x01, 0x01, 0xFB, 0x65, 0x02, 0x02, 0x02, 0x18, 0x01, 0x01, 0x09, 0x02, 0x02, 0x02, 0xA2, 0x14, 0x14, 0x14, 0x14, 0x14, 0x14, 0x53, 0xFC, 0x01, 0x01, 0x17, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01,
        0x01, 0xA0, 0x02, 0x02, 0x60, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0xBF, 0x1A, 0x03, 0x03, 0x2F, 0x32, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x98, 0x03, 0x03, 0x03, 0xAB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xE1, 0x02, 0x02, 0x02,
        0x5D, 0x49, 0x62, 0x18, 0x0A, 0x02, 0x02, 0x36, 0x28, 0x01, 0x01, 0x48, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x0A, 0xB5, 0x01, 0x01, 0x7A, 0xC7, 0x02, 0x02, 0x02, 0x5D, 0x81, 0x35, 0x02, 0x02, 0x5E, 0x2B, 0x02, 0x2D, 0x01,
        0x01, 0x01, 0x27, 0x18, 0x02, 0x02, 0x02, 0x0A, 0x1F, 0x80, 0x02, 0x02, 0x0A, 0x60, 0x02, 0x18, 0x01, 0x01, 0x01, 0x0A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x5E, 0xFD, 0x01, 0x17, 0x02, 0x02, 0x19, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x74, 0x02, 0x02, 0x02, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0xB1, 0x4C, 0x23, 0x57, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x5A, 0x38, 0x2F, 0x26, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xA6, 0x47, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x0A, 0xD0, 0x1B, 0x01, 0x01, 0x01, 0x25, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x34, 0x7B, 0x01, 0x01, 0x01, 0xFB, 0x88, 0x0A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x18, 0x0B, 0x86, 0x02, 0x65, 0x01,
        0x01, 0x01, 0x01, 0x10, 0x66, 0x1E, 0x02, 0x02, 0x02, 0x02, 0x02, 0x80, 0xE9, 0x25, 0x02, 0x49, 0x01, 0x01, 0x01, 0x19, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x61, 0xFE, 0x01, 0xE2, 0x02, 0x02, 0x67, 0x01, 0x01, 0x01, 0x01,
        0x01, 0xFC, 0x34, 0x02, 0x5D, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x30,
        0xA2, 0x25, 0x4F, 0x3A, 0x72, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFB, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0xFD, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x21, 0xA5, 0x9E, 0x4F, 0xDF, 0x14, 0x0F, 0x01, 0x07, 0x9F, 0x54, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0xFD, 0xE7, 0x9F, 0x4F, 0x3A, 0x75, 0x28, 0x01, 0xFD, 0xDE, 0xF5, 0x01, 0x01, 0x01, 0x01, 0x54, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x7B, 0x28, 0x01, 0x01, 0x01, 0x16, 0xEA, 0x09, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x07, 0x20, 0x7B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x02, 0x02, 0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02, 0x02, 0x02,
  0x43, 0x02, 0x02, 0x02, 0xA7, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x76, 0x02, 0x02, 0x02, 0x43,
  0x81, 0x02, 0x02, 0x02, 0x46, 0x3C, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x3C, 0x46, 0x02, 0x02, 0x02, 0x81,
  0x5F, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x5F,
  0x45, 0x29, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x29, 0x45,
  0x00, 0x7E, 0x2A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x34, 0x7F, 0x00,
  0x00, 0x00, 0x08, 0x42, 0x2A, 0x44, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x44, 0x2A, 0x42, /**/0x42, 0x00, 0x00
};

GUI_CONST_STORAGE GUI_BITMAP bmSeggerLogo = {
  140, /* XSize */
  70, /* YSize */
  140, /* BytesPerLine */
  8, /* BitsPerPixel */
  acSeggerLogo,  /* Pointer to picture data (indices) */
  &PalSeggerLogo  /* Pointer to palette */
};

/*********************************************************************
*                                                                    *
*       bmSeggerLogo70x35                                            *
*                                                                    *
*  Used in                                                           *
*  - GUIDEMO.c                                                       *
*  - GUIDEMO_Automotive.c                                            *
*  - GUIDEMO_BarGraph.c                                              *
*  - GUIDEMO_Speed.c                                                 *
*                                                                    *
**********************************************************************
*/
static GUI_CONST_STORAGE GUI_COLOR ColorsSeggerLogo70x35[] = {
     0x00FF00,0xFFFFFF,0x201F23,0xA02020
    ,0xF3F3F3,0xC16E6E,0xDDAFAF,0xEFEFEF
    ,0xE0E0E0,0xF8EEEE,0xFDFDFD,0x7A797B
    ,0xEAEAEA,0xA22424,0xFAFAFA,0x212024
    ,0xFDFAFA,0x262528,0x504F52,0x808082
    ,0x88888A,0xA0A0A2,0xF6EAEA,0xF8F8F8
    ,0xFEFEFE,0xFFFEFE,0x1E1D21,0x2A292D
    ,0xA42A2A,0xAFAFB0,0xD59D9D,0xDEB1B1
    ,0xFDFBFB,0xFCFCFC,0xFEFCFC,0x222124
    ,0x28272B,0x6A6A6D,0xAF4343,0xC57777
    ,0xC87D7D,0x828183,0x9A999B,0xEFDADA
    ,0xF0DBDB,0xF6F6F6,0x111013,0x111113
    ,0x1E1D20,0x29282C,0x2B2A2E,0x2C2B2E
    ,0x313034,0x3F3E42,0x403F43,0x414042
    ,0x474649,0x48474A,0x57565A,0x69686B
    ,0x747476,0x7C7B7D,0xA02121,0xA12323
    ,0xA22525,0xA52B2B,0xA52C2C,0xA62E2E
    ,0xA83434,0xA93535,0xAA3636,0xAA3838
    ,0xAB3B3B,0xAD3E3E,0xAE4141,0xAF4444
    ,0xB34C4C,0xB34D4D,0xB65353,0xB65555
    ,0xB85959,0xBA5D5D,0xBC6262,0xBD6363
    ,0xBF6A6A,0xC06B6B,0xC27070,0xC77C7C
    ,0x7F7F81,0x828284,0x8A8A8C,0x8D8D8F
    ,0x969597,0xA2A1A3,0xBABABB,0xCA8282
    ,0xCA8383,0xCF8D8D,0xD19393,0xD39797
    ,0xD59B9B,0xD8A3A3,0xDEB2B2,0xDFB3B3
    ,0xDFB4B4,0xC4C4C5,0xC5C5C6,0xC8C8C9
    ,0xD5D5D6,0xD7D7D8,0xE7C7C7,0xE8C9C9
    ,0xE8CACA,0xE9CACA,0xEACECE,0xEACFCF
    ,0xECD3D3,0xF1DEDE,0xE1E1E2,0xE2E1E2
    ,0xE8E8E8,0xEBEBEC,0xECECED,0xF5E8E8
    ,0xF8EFEF,0xF4F4F4,0xF8F0F0,0xF9F0F0
    ,0xF9F1F1,0xFAF3F3,0xFAF4F4,0xFCF7F7
    ,0xFCF9F9,0xFEFDFD,0x010101,0x121212
    ,0x232226,0x242327,0x252428,0x2E2D31
    ,0x2F2E31,0x302F33,0x313033,0x323134
    ,0x363539,0x373639,0x38373B,0x3B3A3E
    ,0x3C3B3F,0x3D3C3F,0x3F3E41,0x414043
    ,0x434246,0x464548,0x47464A,0x4C4B4E
    ,0x4F4E52,0x515054,0x525154,0x525155
    ,0x535256,0x545356,0x555457,0x565558
    ,0x575659,0x58575A,0x5A595C,0x5D5C5F
    ,0x5E5D60,0x605F62,0x616163,0x646366
    ,0x6D6D6F,0x6F6F71,0x727274,0x737375
    ,0x767578,0x79787B,0x7A797C,0x7B7A7D
    ,0x7D7C7F,0xA32626,0xA32727,0xB14949
    ,0xB24949,0xBE6767,0xBE6868,0xC57878
    ,0x7E7E80,0x878789,0x8C8C8C,0x919092
    ,0x929193,0x939393,0x969697,0x979698
    ,0x989799,0x99989A,0x9B9A9C,0x9B9B9D
    ,0x9C9C9D,0x9C9C9E,0x9E9D9F,0x9E9EA0
    ,0x9F9EA0,0x9F9FA1,0xA2A2A3,0xA3A2A4
    ,0xA4A3A5,0xA5A4A6,0xA9A9AA,0xACACAD
    ,0xB0B0B1,0xB2B2B3,0xB3B3B4,0xB4B4B5
    ,0xB9B9BA,0xBCBBBD,0xBDBDBE,0xC98080
    ,0xC98181,0xDCAEAE,0xDDAEAE,0xE1B8B8
    ,0xE1B9B9,0xC0C0C1,0xC4C3C5,0xC6C6C7
    ,0xC7C6C7,0xC9C9CA,0xCAC9CA,0xCACACB
    ,0xCBCBCC,0xCCCCCD,0xCDCDCE,0xD1D1D1
    ,0xD1D1D2,0xD4D3D4,0xD5D4D5,0xDADADB
    ,0xDBDBDB,0xDDDCDD,0xDEDEDF,0xDFDFE0
    ,0xE3E3E4,0xE4E4E4,0xE4E4E5,0xE6E6E7
    ,0xECECEC,0xEDEDED,0xF2E1E1,0xF2E2E2
    ,0xEFEFF0,0xF1F1F1,0xF3F2F3,0xF8F7F8
};

static GUI_CONST_STORAGE GUI_LOGPALETTE PalSeggerLogo70x35 = {
  256,	/* number of entries */
  1, 	/* Has transparency */
  &ColorsSeggerLogo70x35[0]
};

static GUI_CONST_STORAGE unsigned char acSeggerLogo70x35[] = {
  0x86, 0x2F, 0x1A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x1A, 0x86, 0x86,
  0x2E, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x2E,
  0x30, 0x02, 0x1D, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x1D, 0x02, 0x30,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x20, 0x4C, 0x51, 0x10, 0x01, 0x01, 0x01, 0x27, 0x4A, 0x7B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x10, 0x47, 0x03, 0x60, 0x01, 0x01, 0x01, 0x53, 0x03, 0x4E, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x70, 0x0D, 0x3E, 0xE0, 0x01, 0x01, 0x16, 0x46, 0x03, 0xDC, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x63, 0x03, 0x43, 0xFB, 0x01, 0x01, 0x71, 0x0D, 0x03, 0x66, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x19, 0x52, 0x03, 0x4D, 0x84, 0x01, 0x01, 0x1E, 0x03, 0x1C, 0x2B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x7C, 0x48, 0x03, 0x28, 0x01, 0x01, 0x01, 0x55, 0x03, 0x26, 0x81, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x22, 0x85, 0x01, 0x01, 0x72, 0x40, 0x03, 0x67, 0x01, 0x01, 0x82, 0x4B, 0x03, 0x54, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x62, 0x01, 0x01, 0x01, 0x1E, 0x03, 0x42, 0x75, 0x01, 0x01, 0x2C, 0x41, 0x03, 0x64, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x3F, 0x6E, 0x01, 0x01, 0x19, 0xB9, 0x03, 0xB8, 0x83, 0x01, 0x01, 0x68, 0x03, 0x0D, 0x6F, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x45, 0x16, 0x01, 0x01, 0x80, 0x49, 0x03, 0xBB, 0x01, 0x01, 0x01, 0x5F, 0x03, 0x44, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x56, 0x73, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x03, 0x50, 0x22, 0x01, 0x01, 0x74, 0xB5, 0x03, 0xDE, 0x01, 0x01, 0x20, 0x4F, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x57, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x03, 0x03, 0x61, 0x01, 0x01, 0x01, 0x65, 0x03, 0x1C, 0x2C, 0x01, 0x01, 0x7F, 0x1F, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x1F, 0x7E, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x03, 0x03, 0x0D, 0x16, 0x01, 0x01, 0x01, 0x26, 0x03, 0x28, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x03, 0x03, 0x61, 0x01, 0x01, 0x01, 0x65, 0x03, 0x1C, 0x2B, 0x01, 0x01, 0x7F, 0x1F, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
        0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x1F, 0x7E, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x03, 0x50, 0x22, 0x01, 0x01, 0x74, 0xB6, 0x03, 0xDD, 0x01, 0x01, 0x20, 0x4F, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x57, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x03, 0x45, 0x16, 0x01, 0x01, 0x80, 0x49, 0x03, 0x27, 0x01, 0x01, 0x01, 0x5F, 0x03, 0x44, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x56, 0x73, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x3F, 0x6E, 0x01, 0x01, 0x19, 0xBA, 0x03, 0xB7, 0x83, 0x01, 0x01, 0x68, 0x03, 0x0D, 0x6F, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x09, 0x62, 0x01, 0x01, 0x01, 0x1E, 0x03, 0x42, 0x75, 0x01, 0x01, 0x2C, 0x41, 0x03, 0x64, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x22, 0x85, 0x01, 0x01, 0x72, 0x40, 0x03, 0x67, 0x01, 0x01, 0x82, 0x4B, 0x03, 0x54, 0x19, 0x01, 0xF6, 0x58, 0xA5, 0xA6, 0xBD, 0x79, 0x01, 0x76, 0xB4, 0x0B, 0x0B, 0x0B, 0xB3, 0xEE, 0x01, 0x17, 0xC7, 0xA9, 0xA3, 0xAF, 0xE4, 0x01, 0x01,
        0x01, 0xF0, 0x3D, 0xA4, 0xA7, 0x5B, 0xF8, 0x01, 0x01, 0x5C, 0x0B, 0x0B, 0x0B, 0x0B, 0xCD, 0x01, 0xD6, 0xB2, 0x0B, 0x0B, 0xBC, 0xD3, 0x0A, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x7C, 0x48, 0x03, 0x28, 0x01, 0x01, 0x01, 0x55, 0x03, 0x26, 0x81, 0x01, 0x18, 0x35, 0x97, 0xD2, 0xCB, 0x8F, 0x9E, 0x01, 0xCE, 0x02, 0xAC, 0x13, 0x13, 0x29, 0x6D, 0x0E, 0xA1, 0x11, 0x29, 0xCA, 0x12, 0x0F, 0xDA, 0x01,
        0xE9, 0x24, 0x98, 0xC5, 0x5B, 0x33, 0x93, 0x0A, 0x0A, 0x0F, 0x99, 0x13, 0x13, 0x13, 0xD1, 0x01, 0x9D, 0x8B, 0x59, 0x59, 0xA8, 0x02, 0x2A, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x19, 0x52, 0x03, 0x4D, 0x84, 0x01, 0x01, 0x1E, 0x03, 0x1C, 0x2B, 0x01, 0x01, 0x0E, 0x88, 0xA2, 0xF1, 0x18, 0x77, 0x6B, 0x01, 0x15, 0x02, 0xEB, 0x0A, 0x0A, 0x18, 0x01, 0xD5, 0x02, 0xC9, 0x01, 0x01, 0x17, 0x2A, 0x77, 0x01,
        0x3A, 0x1B, 0x7A, 0x01, 0x01, 0x6C, 0xCC, 0x01, 0x21, 0x02, 0xB0, 0x0A, 0x0A, 0x0A, 0x01, 0x01, 0x12, 0x36, 0x01, 0x01, 0x04, 0x02, 0xB1, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x63, 0x03, 0x43, 0xFA, 0x01, 0x01, 0x71, 0x0D, 0x03, 0x66, 0x01, 0x01, 0x01, 0x01, 0xC4, 0x1B, 0x02, 0x8E, 0xAB, 0xD9, 0x01, 0x15, 0x02, 0x02, 0x02, 0x02, 0x32, 0x7D, 0x14, 0x02, 0x6D, 0x01, 0xE6, 0x14, 0x14, 0xD4, 0x01,
        0x8C, 0x9F, 0x01, 0x01, 0x2A, 0x14, 0x5A, 0xF2, 0x21, 0x02, 0x02, 0x02, 0x02, 0x02, 0xD0, 0x01, 0x12, 0x1B, 0x3B, 0x3B, 0x39, 0x91, 0xED, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x70, 0x0D, 0x3E, 0xDF, 0x01, 0x01, 0x16, 0x46, 0x03, 0xDB, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0xFF, 0xE7, 0xC2, 0x95, 0x0F, 0x6C, 0x15, 0x02, 0x5E, 0x08, 0x08, 0x0C, 0x01, 0xC3, 0x02, 0xE2, 0x01, 0xE1, 0x3C, 0x92, 0x38, 0x01,
        0x94, 0x35, 0x01, 0x01, 0x14, 0x25, 0x02, 0x5D, 0x21, 0x02, 0x25, 0x08, 0x08, 0x76, 0x0E, 0x01, 0x12, 0x8D, 0xC0, 0xBF, 0xA0, 0x89, 0xE8, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x10, 0x47, 0x03, 0x60, 0x01, 0x01, 0x01, 0x53, 0x03, 0x4E, 0x10, 0x01, 0x01, 0x01, 0x01, 0x18, 0x33, 0xAE, 0x0E, 0x01, 0x15, 0x02, 0xE3, 0x15, 0x02, 0x5E, 0x76, 0x76, 0x76, 0xFF, 0xF3, 0x11, 0x9C, 0xF7, 0x01, 0x6B, 0x1B, 0x38, 0x01,
        0x5A, 0x02, 0x5C, 0x0E, 0x17, 0x58, 0x02, 0x5D, 0x21, 0x02, 0x25, 0x76, 0x76, 0x76, 0x79, 0x01, 0x12, 0x36, 0x01, 0x01, 0x6A, 0x02, 0xC8, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x20, 0x4C, 0x51, 0x10, 0x01, 0x01, 0x01, 0x27, 0x4A, 0x7B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xC6, 0x24, 0x32, 0x90, 0x0F, 0xAD, 0x0A, 0xD7, 0x02, 0x02, 0x02, 0x02, 0x02, 0x3D, 0x01, 0xD8, 0x34, 0x0F, 0x31, 0x31, 0xAA, 0x39, 0x01,
        0x0E, 0x3C, 0x0F, 0x11, 0x8A, 0x9B, 0x96, 0xCF, 0x18, 0x34, 0x02, 0x02, 0x02, 0x02, 0x24, 0x17, 0x3A, 0x9A, 0x01, 0x01, 0x78, 0x23, 0x29, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0xFE, 0xE5, 0x69, 0xF5, 0x01, 0x01, 0x01, 0xF9, 0x0C, 0x0C, 0x0C, 0x0C, 0x0E, 0x01, 0x01, 0x17, 0xEA, 0x6A, 0xFC, 0xFF, 0xEF, 0x01,
        0x01, 0x01, 0x78, 0x69, 0xEC, 0x0A, 0x08, 0xFD, 0x01, 0x2D, 0x0C, 0x0C, 0x0C, 0x0C, 0x04, 0x01, 0x07, 0x7A, 0x01, 0x01, 0x01, 0xF4, 0x2D, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x02, 0x02, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x07, 0x02, 0x02,
  0x11, 0x02, 0x1D, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x1D, 0x02, 0x11,
  0x23, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x23,
  0x87, 0x2F, 0x1A, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x1A, 0x86, 0x86
};

GUI_CONST_STORAGE GUI_BITMAP bmSeggerLogo70x35 = {
  70, /* XSize */
  35, /* YSize */
  70, /* BytesPerLine */
  8, /* BitsPerPixel */
  acSeggerLogo70x35,  /* Pointer to picture data (indices) */
  &PalSeggerLogo70x35  /* Pointer to palette */
};

static GUI_CONST_STORAGE unsigned long acSTLogo[] = {
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF030303, 
        0xF63A3A3A, 0xD4656565, 0xAE919191, 0x94B3B3B3, 0x85B4B4B4, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB2B2B2, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B2, 0x7FB3B3B2, 
        0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B3, 
        0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB2B2B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B2, 
        0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB2B2B2, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 
        0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x7FB3B3B3, 0x88B3B3B3, 0xD7777777, 0xFF030303, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF0C0C0C, 0xE1565656, 0x91ACACAC, 0x46F2F0EE, 
        0x10EAE3D7, 0x00D9CCB4, 0x00CDBB9A, 0x00C5AF88, 0x00C0A87C, 0x00BEA579, 0x00BEA679, 0x00BEA679, 0x00BFA679, 0x00BFA67A, 0x00BFA77A, 0x00C0A77A, 0x00C0A77A, 0x00C1A87B, 0x00C1A87A, 0x00C1A87A, 0x00C2A87A, 0x00C2A97B, 0x00C2A97B, 0x00C2A97B, 
        0x00C3A97C, 0x00C3AA7C, 0x00C3AA7D, 0x00C4AA7D, 0x00C4AA7D, 0x00C4AB7D, 0x00C5AB7E, 0x00C5AC7E, 0x00C5AC7E, 0x00C5AC7E, 0x00C5AD7F, 0x00C6AD7F, 0x00C6AD80, 0x00C6AE7F, 0x00C6AE80, 0x00C7AE81, 0x00C7AE81, 0x00C7AF81, 0x00C8AF81, 0x00C8B082, 
        0x00C8B082, 0x00C9B082, 0x00C9B083, 0x00C9B083, 0x00C9B083, 0x00C9B183, 0x00C9B183, 0x00CAB184, 0x00CAB184, 0x00CAB284, 0x00CAB284, 0x00CBB284, 0x00CBB284, 0x00CBB284, 0x00CBB284, 0x00CCB385, 0x00CBB384, 0x00CCB385, 0x00CCB384, 0x00CCB385, 
        0x00CCB385, 0x00CDB485, 0x00CDB485, 0x00CDB485, 0x00CDB485, 0x00CDB485, 0x00CEB485, 0x00CEB485, 0x00CEB485, 0x00CEB485, 0x00CEB585, 0x00CEB585, 0x00CFB586, 0x00CFB586, 0x00CFB586, 0x00CFB586, 0x00CFB586, 0x00CFB587, 0x00CFB687, 0x00D0B686, 
        0x00CFB687, 0x00CFB687, 0x00CFB687, 0x00D0B687, 0x00CFB687, 0x00D0B789, 0x00D8C39C, 0x17FCFBF9, 0xEE6E6E6E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE0E0E0E, 0xCD737373, 0x57E5E4E3, 0x0AE4DCCF, 0x00BFAC89, 0x00A68955, 
        0x00A07E43, 0x00A17C3F, 0x00A27B3B, 0x00A27B39, 0x00A47B37, 0x00A57C39, 0x00A77D38, 0x00AA7E39, 0x00AB8039, 0x00AD823A, 0x00AE823A, 0x00B1843A, 0x00B3853A, 0x00B5863B, 0x00B6883A, 0x00B8893B, 0x00B9893B, 0x00BB8B3C, 0x00BB8C3D, 0x00BB8D40, 
        0x00BC8E42, 0x00BD8F44, 0x00BD9046, 0x00BE9148, 0x00BE9249, 0x00BF944B, 0x00BF954D, 0x00C09650, 0x00C09752, 0x00C19853, 0x00C29954, 0x00C29A56, 0x00C39B58, 0x00C39D5A, 0x00C49E5C, 0x00C49F5E, 0x00C5A05F, 0x00C5A160, 0x00C5A262, 0x00C6A364, 
        0x00C6A566, 0x00C7A568, 0x00C7A669, 0x00C7A66A, 0x00C8A76A, 0x00C7A76A, 0x00C7A66A, 0x00C7A669, 0x00C7A567, 0x00C7A466, 0x00C6A364, 0x00C6A262, 0x00C5A160, 0x00C4A05F, 0x00C59F5E, 0x00C49E5C, 0x00C39D5A, 0x00C39C58, 0x00C29A56, 0x00C29954, 
        0x00C19853, 0x00C19751, 0x00C09650, 0x00C0954D, 0x00BF934A, 0x00BE9249, 0x00BE9148, 0x00BD9046, 0x00BD8F44, 0x00BC8E42, 0x00BC8D3F, 0x00BC8C3D, 0x00BB8B3D, 0x00B98A3B, 0x00B8893C, 0x00B6873B, 0x00B4863B, 0x00B2853B, 0x00B0843A, 0x00AE833A, 
        0x00AD823A, 0x00AB803A, 0x00A97F39, 0x00A77D38, 0x00A57C38, 0x00B18F52, 0x00D1BD98, 0x3BFEFDFD, 0xF8343434, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEF252525, 0x68DBDBDB, 0x07E1D9CC, 0x00A79068, 0x0094753E, 0x0099743A, 0x009B7536, 
        0x009D7635, 0x009F7736, 0x00A17836, 0x00A37A37, 0x00A47B36, 0x00A67C38, 0x00A87D37, 0x00AA7E38, 0x00AC8038, 0x00AD8139, 0x00AF8239, 0x00B18439, 0x00B38539, 0x00B5863B, 0x00B7873A, 0x00B8893B, 0x00BA8A3A, 0x00BB8B3B, 0x00BB8C3D, 0x00BC8D40, 
        0x00BD8E42, 0x00BD8F44, 0x00BD9047, 0x00BE9147, 0x00BF9349, 0x00BF944B, 0x00C0954E, 0x00C09650, 0x00C19752, 0x00C29853, 0x00C29A55, 0x00C39A57, 0x00C39C59, 0x00C49D5B, 0x00C49E5D, 0x00C49F5F, 0x00C5A05F, 0x00C6A261, 0x00C7A363, 0x00C7A465, 
        0x00C7A567, 0x00C7A669, 0x00C8A76A, 0x00C8A86B, 0x00C9A96D, 0x00C9A96D, 0x00C8A76B, 0x00C8A66A, 0x00C7A669, 0x00C7A567, 0x00C7A464, 0x00C6A363, 0x00C5A261, 0x00C5A05E, 0x00C49F5E, 0x00C49E5C, 0x00C39D5A, 0x00C39C58, 0x00C39A56, 0x00C29954, 
        0x00C19853, 0x00C19751, 0x00C09650, 0x00C0944D, 0x00BF934A, 0x00BF9248, 0x00BE9147, 0x00BD9046, 0x00BD8F43, 0x00BC8E41, 0x00BC8D3F, 0x00BC8C3C, 0x00BB8B3C, 0x00B98A3A, 0x00B8883B, 0x00B6873A, 0x00B4853A, 0x00B28439, 0x00B08439, 0x00AF8239, 
        0x00AD8139, 0x00AB8039, 0x00A97E38, 0x00A77D37, 0x00A57F3E, 0x00AF925F, 0x07F4F0E9, 0xC59B9B9B, 0xFF010101, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010101, 0xD7616161, 0x2BEAE9E7, 0x00B4A387, 0x00886A37, 0x00937036, 0x00987237, 0x009A7336, 0x009B7536, 
        0x009D7636, 0x009F7736, 0x00A17836, 0x00A37A37, 0x00A47B36, 0x00A67C38, 0x00A87D37, 0x00AA7E39, 0x00AB8038, 0x00AD8139, 0x00AF8239, 0x00B18439, 0x00B38539, 0x00B5863A, 0x00B7883A, 0x00B8893B, 0x00BA8A3A, 0x00BB8B3C, 0x00BB8C3C, 0x00BC8D3F, 
        0x00BC8E42, 0x00BD9044, 0x00BD9046, 0x00BE9147, 0x00BF9349, 0x00BF944B, 0x00C0954E, 0x00C09650, 0x00C19752, 0x00C29853, 0x00C29955, 0x00C39A57, 0x00C39C58, 0x00C49D5B, 0x00C49F5D, 0x00C49F5E, 0x00C5A05F, 0x00C6A261, 0x00C6A363, 0x00C7A465, 
        0x00C7A567, 0x00C7A66A, 0x00C8A76A, 0x00C8A86C, 0x00C9A96D, 0x00C9A96D, 0x00C8A86B, 0x00C8A66A, 0x00C7A669, 0x00C6A566, 0x00C6A464, 0x00C6A362, 0x00C6A161, 0x00C5A05E, 0x00C49F5E, 0x00C49E5C, 0x00C39D59, 0x00C39C58, 0x00C29A56, 0x00C29954, 
        0x00C19753, 0x00C19751, 0x00C0964F, 0x00C0944D, 0x00BF934A, 0x00BF9248, 0x00BE9147, 0x00BD9046, 0x00BD8F43, 0x00BC8D41, 0x00BC8D3F, 0x00BC8C3C, 0x00BB8B3C, 0x00B98A3B, 0x00B8893B, 0x00B6873A, 0x00B4853A, 0x00B28539, 0x00B08439, 0x00AE8239, 
        0x00AD8139, 0x00AB8039, 0x00A97E38, 0x00A67D38, 0x009B7C43, 0x00D2C4AC, 0x69E9E9E9, 0xFE191919, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD47E7E7E, 0x1BF5F3F1, 0x0097825F, 0x0082622F, 0x00946F36, 0x00977136, 0x00987237, 0x009A7337, 0x009C7536, 
        0x009D7635, 0x009F7736, 0x00A17836, 0x00A27A37, 0x00A47B36, 0x00A67C37, 0x00A87D37, 0x00A97E38, 0x00AB8038, 0x00AD8139, 0x00AE8239, 0x00B1843A, 0x00B3853A, 0x00B5863A, 0x00B7873A, 0x00B8893B, 0x00BA8A3A, 0x00BB8B3C, 0x00BB8C3D, 0x00BC8D40, 
        0x00BD8E42, 0x00BD8F44, 0x00BE9146, 0x00BE9147, 0x00BF9349, 0x00BF934B, 0x00C0954E, 0x00C09650, 0x00C19752, 0x00C29853, 0x00C39A55, 0x00C29A56, 0x00C39C59, 0x00C49E5B, 0x00C49E5D, 0x00C59F5E, 0x00C5A05F, 0x00C6A261, 0x00C6A363, 0x00C7A464, 
        0x00C7A567, 0x00C7A669, 0x00C7A66A, 0x00C8A76A, 0x00C8A76B, 0x00C8A76B, 0x00C8A76A, 0x00C7A66A, 0x00C7A568, 0x00C7A566, 0x00C6A364, 0x00C6A262, 0x00C5A160, 0x00C5A05E, 0x00C49F5E, 0x00C49E5B, 0x00C39D5A, 0x00C39B58, 0x00C29A56, 0x00C29954, 
        0x00C29853, 0x00C19751, 0x00C0964F, 0x00C0944D, 0x00BF934A, 0x00BE9248, 0x00BE9147, 0x00BD9045, 0x00BD8F43, 0x00BC8D41, 0x00BC8D3E, 0x00BB8C3C, 0x00BB8B3C, 0x00B9893B, 0x00B8883B, 0x00B6873A, 0x00B4853A, 0x00B2843A, 0x00B08339, 0x00AE8239, 
        0x00AD8138, 0x00AB7F39, 0x00A97E37, 0x00997639, 0x00A18A60, 0x1BFBFAF9, 0xE9515151, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED494949, 0x2BF7F6F5, 0x009E8A68, 0x007F5F2C, 0x00936E35, 0x00957036, 0x00967136, 0x00987237, 0x00997337, 0x009B7436, 
        0x009D7636, 0x009F7736, 0x00A17836, 0x00A27A37, 0x00A47B36, 0x00A27937, 0x009A7333, 0x00916C30, 0x0089672D, 0x0084632C, 0x0081622D, 0x0081622E, 0x0081612F, 0x0081622E, 0x0082622E, 0x0082632F, 0x0082622F, 0x0082632F, 0x0082632F, 0x0082632F, 
        0x00826330, 0x00826430, 0x00826431, 0x00826431, 0x00836431, 0x00836432, 0x00836432, 0x00836533, 0x00836534, 0x00836534, 0x00836633, 0x00846634, 0x00846735, 0x00846735, 0x00846735, 0x00846736, 0x00846836, 0x00856837, 0x00856837, 0x00856838, 
        0x00856938, 0x00866939, 0x00866939, 0x00866A39, 0x00866A39, 0x00866A39, 0x00866A3A, 0x00866A39, 0x00876A39, 0x00876A39, 0x00876A39, 0x00876A39, 0x00876A38, 0x00876A39, 0x00876A39, 0x00886A38, 0x00886A38, 0x00886A38, 0x00886A38, 0x00886A38, 
        0x00886A37, 0x00886A38, 0x00896A37, 0x00896A37, 0x00896A37, 0x00896A37, 0x00896A37, 0x00896A36, 0x00896A35, 0x00896A36, 0x00896A36, 0x008A6B36, 0x008A6B35, 0x00896A35, 0x00896A36, 0x00896B36, 0x00896A36, 0x00896A36, 0x00896A36, 0x00896A37, 
        0x00896A36, 0x00886A36, 0x00886A37, 0x00876C3D, 0x01DFD8CC, 0x9FB1B1B1, 0xFF010101, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE0C0C0C, 0x72D9D9D9, 0x00C7B9A4, 0x007F602B, 0x00906C35, 0x00936E35, 0x00956F36, 0x00967036, 0x00987236, 0x00997237, 0x009B7436, 
        0x009D7635, 0x009F7736, 0x009F7736, 0x00946F32, 0x0086652C, 0x008A6E40, 0x00A79373, 0x00C3B6A0, 0x00D9D0C3, 0x00E8E2DB, 0x00F0ECE7, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F1ED, 
        0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F1ED, 
        0x00F3F0ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F0ED, 
        0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F0ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1ED, 0x00F3F1EE, 0x00F3F1EE, 0x00F3F1EE, 0x00F3F1EE, 0x00F3F1EE, 0x00F4F1EE, 
        0x00F3F1EE, 0x00F3F1EE, 0x00F3F1EE, 0x00F7F5F2, 0x40FAFAFA, 0xFA202020, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCB7A7A7A, 0x0AF0ECE6, 0x008D6F3E, 0x008B6932, 0x00916C35, 0x00936E35, 0x00956F36, 0x00967036, 0x00977236, 0x00997237, 0x009B7436, 
        0x009D7535, 0x00946F32, 0x00866630, 0x00AA9572, 0x00DDD5C8, 0x00FBFAF9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x09FFFFFF, 0xCD777777, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9262626, 0x3FF9F9F9, 0x00B49E7A, 0x00896830, 0x008E6B34, 0x00906C35, 0x00926D35, 0x00946F36, 0x00967036, 0x00977136, 0x00997237, 0x00997236, 
        0x008D6A30, 0x00A1875C, 0x00E7E0D6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x72DADADA, 0xFF090909, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF070707, 0x9CBFBFBF, 0x01E1D7C6, 0x00916E32, 0x008C6A34, 0x008E6A35, 0x00906C35, 0x00926D35, 0x00946F35, 0x00957036, 0x00977136, 0x00977136, 0x00916D30, 
        0x00BBA683, 0x00FCFAF9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FFFFFF, 0x1FFAFAFA, 0xEE373737, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE7727272, 0x19FAF8F6, 0x00AA8B56, 0x008F6C32, 0x008C6934, 0x008D6A35, 0x008F6C35, 0x00916D36, 0x00936E35, 0x00957036, 0x00977136, 0x00977131, 0x00BEA781, 
        0x00FDFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x02FFFFFF, 0xA78E8E8E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE1A1A1A, 0x66ECECEC, 0x00D2BFA0, 0x009A7333, 0x008A6834, 0x008B6934, 0x008D6A35, 0x008F6B34, 0x00916C35, 0x00936E35, 0x00946F36, 0x00997334, 0x00AE8C55, 0x00F9F7F4, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x47E7E7E7, 0xFC0E0E0E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010101, 0xC5ABABAB, 0x04F3EDE4, 0x00AA8340, 0x008E6B33, 0x00896833, 0x008A6934, 0x008C6A34, 0x008E6B35, 0x00906C35, 0x00926D35, 0x00946F35, 0x00A37A34, 0x00DECFB7, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x0BFFFFFF, 0xD48F8F8F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9404040, 0x33FEFEFD, 0x00C8AA77, 0x009C7535, 0x00876634, 0x00896733, 0x008A6934, 0x008C6935, 0x008D6A35, 0x008F6C35, 0x00916D35, 0x00997235, 0x00B38A46, 0x00F9F5F1, 0x00FEFEFE, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FAF8F6, 0x00E3D9C6, 0x00DACBB1, 0x00DACBB0, 0x00DBCCB1, 0x00DBCCB1, 0x00DBCCB1, 0x00DCCDB1, 0x00DCCDB2, 0x00DCCDB2, 0x00DCCEB2, 
        0x00DDCEB3, 0x00DDCFB3, 0x00DECFB3, 0x00DECFB4, 0x00DECFB4, 0x00DFD0B4, 0x00DFD0B5, 0x00DFD1B5, 0x00E0D1B5, 0x00E0D1B6, 0x00E0D2B6, 0x00E1D2B6, 0x00E2D3B6, 0x00E1D3B7, 0x00E2D3B7, 0x00E2D3B7, 0x00E3D3B7, 0x00E3D4B8, 0x00E3D4B8, 0x00E4D4B8, 
        0x00E3D4B8, 0x00E4D4B8, 0x00E4D5B8, 0x00E4D5B9, 0x00E4D5B8, 0x00E4D5B8, 0x00E4D5B9, 0x00E5D5B9, 0x00E5D5B9, 0x00E5D5B9, 0x00E7DAC1, 0x00FCFBF8, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FCFBFA, 0x00DDD3C2, 0x00CEC1A7, 0x00CFC0A7, 0x00D1C2A8, 0x00D3C4AA, 0x00D5C6AC, 0x00D7C7AD, 0x00D8C9AE, 0x00D9CAB0, 0x00DBCCB1, 0x00DDCEB3, 0x00DFD0B5, 0x00E1D2B6, 0x00E3D4B7, 0x00E4D5B8, 0x00EBE0CB, 0x00FEFEFD, 
        0x7CEBEBEB, 0xFF121212, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010101, 0x91C7C7C7, 0x00E9DBC5, 0x00B1843A, 0x00886733, 0x00876634, 0x00886733, 0x00896834, 0x008B6934, 0x008C6A34, 0x008F6B35, 0x00906C35, 0x00A17837, 0x00C4A064, 0x00FFFEFE, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F6F3EE, 0x00AE9668, 0x00A07E44, 0x00A07B3F, 0x00A17C3E, 0x00A37D3F, 0x00A47E3F, 0x00A58040, 0x00A6803F, 0x00A88140, 0x00A98241, 0x00AB8341, 
        0x00AD8442, 0x00AE8642, 0x00AF8743, 0x00B08743, 0x00B28943, 0x00B38944, 0x00B58B44, 0x00B68C44, 0x00B88D45, 0x00B88E45, 0x00BA8E45, 0x00BA8F46, 0x00BB9047, 0x00BB9149, 0x00BC914A, 0x00BC924B, 0x00BD934C, 0x00BD944E, 0x00BD954F, 0x00BE9550, 
        0x00BE9651, 0x00BF9751, 0x00C09852, 0x00C09753, 0x00C09853, 0x00C19954, 0x00C19955, 0x00C19956, 0x00C19956, 0x00BF9B5B, 0x00C4A874, 0x00F9F5F0, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FEFEFD, 0x00B4A281, 0x009F7E41, 0x00B28944, 0x00B38A43, 0x00B48A43, 0x00B48A42, 0x00B58A42, 0x00B68B42, 0x00B68B42, 0x00B68B43, 0x00B68C44, 0x00B68C44, 0x00B68C45, 0x00B68C46, 0x00B68C47, 0x00BD9A5B, 0x00D2BB8D, 0x27FDFCFB, 
        0xEF5E5E5E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE3535353, 0x11FCFAF6, 0x00C69E5D, 0x00987237, 0x00846433, 0x00866533, 0x00876633, 0x00896833, 0x008A6834, 0x008C6934, 0x008D6B34, 0x008F6C35, 0x00A47B3A, 0x00CCA970, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00E2DBD0, 0x008B6E3B, 0x00977137, 0x009A7336, 0x009C7536, 0x009E7736, 0x00A07836, 0x00A17936, 0x00A37A37, 0x00A47B36, 0x00A67C37, 0x00A87D37, 
        0x00AA7E38, 0x00AB8038, 0x00AD8139, 0x00AE8239, 0x00B08339, 0x00B28439, 0x00B3853A, 0x00B5863A, 0x00B7873A, 0x00B8883B, 0x00B9893B, 0x00BA8A3B, 0x00BB8B3C, 0x00BB8C3C, 0x00BC8C3E, 0x00BC8D40, 0x00BC8E42, 0x00BD8E43, 0x00BD8F44, 0x00BD9046, 
        0x00BD9147, 0x00BE9147, 0x00BE9248, 0x00BE9248, 0x00BE9249, 0x00BF9349, 0x00BF934A, 0x00BF934A, 0x00BD924A, 0x00A8884F, 0x00DACDB6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00D8CFC1, 0x008D6F3A, 0x00B88B42, 0x00BC8D41, 0x00BC8D3F, 0x00BC8C3D, 0x00BB8B3C, 0x00BB8B3B, 0x00BA8A3B, 0x00B9893B, 0x00B7883A, 0x00B6873A, 0x00B4863A, 0x00B2843A, 0x00B1843A, 0x00B0863F, 0x00B99B63, 0x03EFE7DA, 0xAFB9B9B9, 
        0xFF040404, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFE121212, 0x5BE5E5E5, 0x00DFC9A7, 0x00B08847, 0x00826333, 0x00836333, 0x00856433, 0x00866633, 0x00886733, 0x00896833, 0x008B6934, 0x008C6A35, 0x008F6B35, 0x00A17A3D, 0x00CAA76D, 0x00FEFDFD, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFCFC, 0x00B7A88F, 0x007C5F2F, 0x008C6931, 0x009B7436, 0x009D7636, 0x009F7736, 0x00A07836, 0x00A17937, 0x00A37A37, 0x00A47B37, 0x00A67C38, 
        0x00A87D37, 0x00AA7F38, 0x00AB8038, 0x00AD8139, 0x00AE8239, 0x00B08339, 0x00B1843A, 0x00B38539, 0x00B4863A, 0x00B6873A, 0x00B7883A, 0x00B8893B, 0x00BA8A3B, 0x00BB8A3C, 0x00BB8B3C, 0x00BB8C3C, 0x00BB8C3E, 0x00BC8D40, 0x00BC8E41, 0x00BD8E42, 
        0x00BD8F43, 0x00BD8F45, 0x00BD9046, 0x00BD9047, 0x00BE9047, 0x00BD9147, 0x00BE9147, 0x00BE9148, 0x00A37F41, 0x00A79169, 0x00FBFAF9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 
        0x00F7F5F3, 0x008F774E, 0x00A57D3A, 0x00BC8D3F, 0x00BB8C3D, 0x00BB8C3C, 0x00BB8B3C, 0x00BA8A3B, 0x00B9893B, 0x00B8893B, 0x00B7873A, 0x00B5873A, 0x00B4853A, 0x00B28439, 0x00B0843A, 0x00AF8239, 0x00AB894D, 0x00CFBD9C, 0x52F4F4F4, 0xFC222222, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xB98B8B8B, 0x04F5EFE6, 0x00C4A062, 0x008B6B39, 0x00826232, 0x00836333, 0x00846433, 0x00866533, 0x00876634, 0x00896733, 0x008A6834, 0x008C6934, 0x008D6A35, 0x0096723A, 0x00C4A060, 0x00F6F1E9, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E0D9CF, 0x00907950, 0x0083622C, 0x00977234, 0x009D7636, 0x009F7736, 0x00A17836, 0x00A27937, 0x00A37A37, 0x00A57B37, 
        0x00A67C37, 0x00A87D37, 0x00AA7F38, 0x00AB8038, 0x00AD8139, 0x00AE8239, 0x00AF8339, 0x00B18439, 0x00B28439, 0x00B4853A, 0x00B5873A, 0x00B7873A, 0x00B8883B, 0x00B9893B, 0x00BA8A3B, 0x00BB8B3B, 0x00BB8B3C, 0x00BB8C3C, 0x00BC8C3E, 0x00BC8D3F, 
        0x00BC8D40, 0x00BC8E42, 0x00BD8E42, 0x00BD8F43, 0x00BD8F44, 0x00BD8F44, 0x00BD9045, 0x00B08640, 0x007E6333, 0x00E0DACF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00B6A890, 0x0086652D, 0x00BA8B3D, 0x00BB8C3C, 0x00BB8B3B, 0x00BA8A3B, 0x00B98A3B, 0x00B9893B, 0x00B7883A, 0x00B6873A, 0x00B4863A, 0x00B3853A, 0x00B2853A, 0x00B08339, 0x00AE8239, 0x00A8813F, 0x00AC9160, 0x11F6F3EE, 0xD8717171, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xF2323232, 0x2EFBFAFA, 0x00D7C095, 0x00A5844F, 0x007F6032, 0x00816132, 0x00826333, 0x00836333, 0x00856433, 0x00866533, 0x00886733, 0x00896833, 0x008B6934, 0x008C6A35, 0x008F6B35, 0x00B69458, 0x00E2D0B1, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FAF9F8, 0x00C2B29A, 0x00896A36, 0x00926D32, 0x009D7635, 0x009F7736, 0x00A17836, 0x00A27937, 0x00A37A37, 
        0x00A57B37, 0x00A67C38, 0x00A87D37, 0x00A97F38, 0x00AB8039, 0x00AC8138, 0x00AD8239, 0x00AF8239, 0x00B08339, 0x00B2843A, 0x00B3853A, 0x00B4853A, 0x00B6873A, 0x00B7873A, 0x00B8883B, 0x00B9893B, 0x00BA893B, 0x00BB8A3B, 0x00BB8B3C, 0x00BB8C3C, 
        0x00BC8C3D, 0x00BC8D3E, 0x00BC8D3F, 0x00BC8D40, 0x00BC8D41, 0x00BC8D41, 0x00BB8D41, 0x008A682F, 0x00AD9C7F, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E9E4DB, 
        0x00856735, 0x00AE8237, 0x00BB8B3B, 0x00BA8A3B, 0x00B9893A, 0x00B8893B, 0x00B8883A, 0x00B6873A, 0x00B5863A, 0x00B4853A, 0x00B28439, 0x00B18439, 0x00B08339, 0x00AE8239, 0x00AB803A, 0x00987A42, 0x01D7CBB8, 0x84D5D5D5, 0xFF070707, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF050505, 0x85C1C1C1, 0x01EBDFC9, 0x00BD9C61, 0x00816234, 0x007F5F32, 0x00806132, 0x00816233, 0x00836333, 0x00846433, 0x00856433, 0x00876634, 0x00886733, 0x008A6834, 0x008B6934, 0x008C6A34, 0x0099773F, 0x00C8A86F, 0x00F4EEE2, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00ECE7DF, 0x00AA9167, 0x00916C2F, 0x009B7434, 0x009F7736, 0x00A07836, 0x00A27937, 
        0x00A37A37, 0x00A47B37, 0x00A67C38, 0x00A77D37, 0x00A97E38, 0x00AB7F39, 0x00AC8038, 0x00AD8139, 0x00AE8239, 0x00AF8339, 0x00B18439, 0x00B28439, 0x00B3853A, 0x00B5863A, 0x00B6873A, 0x00B7873A, 0x00B8883A, 0x00B9893B, 0x00BA8A3B, 0x00BA8A3B, 
        0x00BB8B3B, 0x00BB8B3C, 0x00BB8B3C, 0x00BB8B3C, 0x00BB8C3D, 0x00BC8C3D, 0x00A87E37, 0x008F7142, 0x00F3F0EB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFDFC, 0x00AE956B, 
        0x009F7733, 0x00BA8A3A, 0x00B9893B, 0x00B8893B, 0x00B7883A, 0x00B6873A, 0x00B5863A, 0x00B4853A, 0x00B3853A, 0x00B2843A, 0x00B08339, 0x00AF8239, 0x00AE8139, 0x00AC8039, 0x00987539, 0x00A9936D, 0x2EFAF9F8, 0xF13E3E3E, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xD8656565, 0x11FAF7F2, 0x00CAA86F, 0x0092713E, 0x007D5E30, 0x007E5F32, 0x007F6032, 0x00806133, 0x00826232, 0x00836333, 0x00846433, 0x00866533, 0x00876634, 0x00896833, 0x008A6934, 0x008B6934, 0x008D6A35, 0x00A48045, 0x00CCAB73, 
        0x00F7F2E9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFDFC, 0x00D6C8B0, 0x00A07C41, 0x009B7433, 0x009F7736, 0x00A07836, 
        0x00A27936, 0x00A37A37, 0x00A47B37, 0x00A57C37, 0x00A77D37, 0x00A97E37, 0x00AA7F39, 0x00AB8038, 0x00AD8039, 0x00AD813A, 0x00AE8239, 0x00AF8339, 0x00B1843A, 0x00B2843A, 0x00B3853A, 0x00B4853A, 0x00B5863A, 0x00B6873A, 0x00B7883A, 0x00B8883B, 
        0x00B9893B, 0x00B9893B, 0x00BA8A3A, 0x00BA8A3B, 0x00BA8A3B, 0x00B8893A, 0x00926E2F, 0x00D2C5B0, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DFD1BB, 0x009F7734, 
        0x00B5873A, 0x00B7883A, 0x00B7873A, 0x00B5873A, 0x00B5863A, 0x00B4853A, 0x00B2843A, 0x00B18439, 0x00B08339, 0x00AF8239, 0x00AE8139, 0x00AD8139, 0x00AC8038, 0x00A27937, 0x00866B3A, 0x05E6E0D6, 0xB7A2A2A2, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFB252525, 0x50F0F0F0, 0x00DBC39C, 0x00AA8346, 0x007A5C31, 0x007B5D30, 0x007D5E31, 0x007E5F32, 0x007F6032, 0x00816133, 0x00826233, 0x00836333, 0x00856433, 0x00866533, 0x00886733, 0x00896833, 0x008A6934, 0x008C6A34, 0x008D6A35, 0x00A27C40, 
        0x00C49F61, 0x00EBDEC9, 0x00FFFFFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F6F2EC, 0x00C3A97D, 0x00A47B36, 0x00A17836, 
        0x00A07836, 0x00A17936, 0x00A27A37, 0x00A37A36, 0x00A57B37, 0x00A67C38, 0x00A77D37, 0x00A97E38, 0x00AA7F39, 0x00AB7F38, 0x00AC8039, 0x00AD8139, 0x00AE8239, 0x00AF8239, 0x00B08339, 0x00B1843A, 0x00B28539, 0x00B3853A, 0x00B4863A, 0x00B5863A, 
        0x00B6873A, 0x00B6873A, 0x00B7883A, 0x00B8883A, 0x00B8883B, 0x00A77D35, 0x00B29564, 0x00FDFCFB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FAF7F2, 0x00B7904F, 0x00B18338, 
        0x00B5863B, 0x00B4863A, 0x00B4853A, 0x00B38539, 0x00B28439, 0x00B18439, 0x00B08339, 0x00AF8239, 0x00AD8239, 0x00AD8139, 0x00AC8039, 0x00AB7F39, 0x00A87D34, 0x007E5E27, 0x00B2A286, 0x5BDBDBDB, 0xFD141414, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF030303, 
        0xADADADAD, 0x04EFE4CE, 0x00B4851F, 0x00795922, 0x00735525, 0x0077582A, 0x007B5D2E, 0x007D5E31, 0x007F5F32, 0x00806032, 0x00816233, 0x00826233, 0x00846333, 0x00856433, 0x00876633, 0x00886733, 0x00896834, 0x008B6934, 0x008C6A35, 0x008D6A34, 
        0x00977238, 0x00B38844, 0x00D4B686, 0x00F8F3EC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFE, 0x00E7DAC6, 0x00B99354, 
        0x00AA7F37, 0x00A17836, 0x00A17836, 0x00A27937, 0x00A37A37, 0x00A47B36, 0x00A57B37, 0x00A67C37, 0x00A77D37, 0x00A97E38, 0x00AA7F39, 0x00AB8038, 0x00AC8039, 0x00AD8139, 0x00AE8239, 0x00AF8239, 0x00B08339, 0x00B18339, 0x00B1843A, 0x00B28539, 
        0x00B3853A, 0x00B38539, 0x00B4853A, 0x00B4863A, 0x00B28439, 0x00A9803A, 0x00EDE5D9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00DAC198, 0x00B8893C, 0x00B38539, 
        0x00B2843A, 0x00B2843A, 0x00B1843A, 0x00B08339, 0x00AF8239, 0x00AE8239, 0x00AD8139, 0x00AC8037, 0x00AA7E32, 0x00A77B2C, 0x00A47725, 0x00A0741E, 0x008A6417, 0x00796032, 0x14F4F1EE, 0xDF515151, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED525252, 
        0x27FCFAF8, 0x00BF994E, 0x008A6215, 0x006A4C1A, 0x006C4D1A, 0x006D4F19, 0x006F5019, 0x0072531B, 0x00765620, 0x00795925, 0x007C5D2A, 0x0080602F, 0x00836232, 0x00846433, 0x00856533, 0x00876634, 0x00886733, 0x008A6834, 0x008B6934, 0x008C6935, 
        0x008E6A35, 0x00906B35, 0x00A27939, 0x00BC914C, 0x00E3D0B4, 0x00FDFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FBF8F4, 
        0x00D8BE95, 0x00B98C43, 0x00AA7F3A, 0x00A07836, 0x00A17936, 0x00A27A37, 0x00A37A36, 0x00A47B36, 0x00A57B37, 0x00A67C37, 0x00A77D38, 0x00A87E38, 0x00AA7F38, 0x00AB7F38, 0x00AC8038, 0x00AC8139, 0x00AD8139, 0x00AE8139, 0x00AF8239, 0x00AF8339, 
        0x00B08339, 0x00B18339, 0x00B1843A, 0x00B2843A, 0x00B38538, 0x00D4BB92, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F4ECE0, 0x00C29854, 0x00B3863C, 0x00B08339, 
        0x00B08339, 0x00AF8239, 0x00AD8137, 0x00AC7F32, 0x00A97C2C, 0x00A77925, 0x00A4761F, 0x00A1741A, 0x00A07219, 0x009F7219, 0x009E711A, 0x009A6E19, 0x007B580F, 0x01CEC2AD, 0x8EB8B8B8, 0xFF020202, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE0D0D0D, 0x79D8D8D8, 
        0x00D5C4A3, 0x00916816, 0x006B4D1B, 0x00694C1A, 0x006B4D1A, 0x006C4E19, 0x006E4F19, 0x006F5018, 0x00715117, 0x00725217, 0x00735315, 0x00755415, 0x00775516, 0x007A581A, 0x007E5C1F, 0x00805E24, 0x00836129, 0x0087642E, 0x00896732, 0x008B6934, 
        0x008C6A35, 0x008D6A35, 0x008F6B35, 0x00946F35, 0x00A47A36, 0x00BFA06C, 0x00F1EAE0, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FEFEFE, 0x00F1E8DA, 0x00CEAC75, 0x00B78E4B, 0x00A27A39, 0x00A07836, 0x00A17936, 0x00A27A37, 0x00A37A37, 0x00A47B37, 0x00A57B37, 0x00A67C37, 0x00A77D38, 0x00A87D37, 0x00A97E38, 0x00AA7F39, 0x00AB8039, 0x00AC8038, 0x00AC8039, 0x00AD8139, 
        0x00AD8139, 0x00AE823A, 0x00AE8239, 0x00B4883E, 0x00C49C59, 0x00FAF7F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFDFD, 0x00D4B989, 0x00B78E43, 0x00AA7D2B, 0x00A87B25, 
        0x00A77920, 0x00A5771B, 0x00A37518, 0x00A27417, 0x00A17318, 0x00A07219, 0x009F7219, 0x009E711A, 0x009D711B, 0x009C6F1B, 0x009B6E1B, 0x00916811, 0x00A68A4E, 0x32F4F4F3, 0xF71F1F1F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD08D8D8D, 0x0CF2EDE6, 
        0x00927024, 0x00705118, 0x00674A1C, 0x00694B1B, 0x006A4C1A, 0x006C4D1A, 0x006D4E19, 0x006E4F19, 0x006F5118, 0x00715117, 0x00725216, 0x00745315, 0x00765415, 0x00775513, 0x00795612, 0x007A5712, 0x007B5812, 0x007C5913, 0x007E5A13, 0x00805C16, 
        0x00825E1B, 0x0085611F, 0x00876323, 0x008A6627, 0x008D682A, 0x00936D2D, 0x009C7736, 0x00CDBA9B, 0x00FBFAF8, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FDFBFA, 0x00DFCCA9, 0x00C19F61, 0x00A57E3E, 0x009F7736, 0x00A07836, 0x00A17936, 0x00A27A37, 0x00A37A37, 0x00A47B36, 0x00A57B37, 0x00A67C37, 0x00A67C38, 0x00A77D37, 0x00A87E37, 0x00A97E38, 0x00AA7F39, 0x00AA7F39, 
        0x00AB8039, 0x00AC8039, 0x00AE833C, 0x00C29A58, 0x00EADCC6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E8DABF, 0x00BD984A, 0x00A47619, 0x00A37417, 0x00A27418, 
        0x00A27418, 0x00A17318, 0x00A07319, 0x009F7219, 0x009E7219, 0x009E711A, 0x009D701B, 0x009C6F1B, 0x009A6E1B, 0x009A6E1B, 0x009A6E19, 0x009C721D, 0x03EDE6DA, 0xC2838383, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA303030, 0x45F4F4F4, 0x00AB976E, 
        0x00715113, 0x0065491D, 0x00664A1C, 0x00674A1C, 0x00694B1B, 0x006A4C1A, 0x006C4D1A, 0x006D4E19, 0x006E5019, 0x00705118, 0x00715117, 0x00735316, 0x00745315, 0x00765415, 0x00775513, 0x00795612, 0x007A5712, 0x007B5812, 0x007C5913, 0x007D5A13, 
        0x007F5B14, 0x00805C14, 0x00815D14, 0x00825E15, 0x00845F15, 0x00856016, 0x00866116, 0x00845F0F, 0x00987A3D, 0x00E1D9CA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFD, 0x00E3D2B4, 0x00BF9B58, 0x009F7833, 0x009C732F, 0x009D742F, 0x009E752E, 0x009E752F, 0x009F762F, 0x00A0762E, 0x00A0762D, 0x00A1772D, 0x00A1762D, 0x00A1762C, 0x00A2772A, 0x00A27629, 0x00A27727, 
        0x00A37725, 0x00A37624, 0x00B68F43, 0x00D4BB88, 0x00FEFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FAF7F0, 0x00C39D53, 0x00A97D26, 0x00A07219, 0x00A07219, 0x009F7219, 
        0x009E7219, 0x009E711A, 0x009D701B, 0x009D701B, 0x009C6F1B, 0x009B6F1B, 0x009A6E1B, 0x00996E1A, 0x00986D1A, 0x00986D19, 0x00A97811, 0x00D1B987, 0x62DCDCDC, 0xFE060606, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF0A0A0A, 0xA2D4D4D4, 0x02D9D2C5, 0x006C4F1B, 
        0x0066491B, 0x0065481D, 0x0065491D, 0x00664A1C, 0x00674A1C, 0x00694B1B, 0x006A4C1A, 0x006B4D1A, 0x006C4E19, 0x006E5019, 0x006F5118, 0x00715117, 0x00725216, 0x00745315, 0x00765414, 0x00775514, 0x00785612, 0x00795612, 0x007B5812, 0x007C5913, 
        0x007D5912, 0x007E5A13, 0x007F5B14, 0x00815C13, 0x00825E15, 0x00835E16, 0x00856016, 0x00866116, 0x00835E15, 0x00785816, 0x00AD9B7A, 0x00F6F4F1, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFC, 0x00D4B780, 0x00AE8229, 0x00926916, 0x00926916, 0x00936A17, 0x00946A18, 0x00956B19, 0x00966B19, 0x00976C19, 0x00986D1A, 0x00996D1A, 0x00996E1B, 0x009A6E1B, 0x009B6F1B, 0x009B6F1B, 
        0x009C6F1B, 0x00A47722, 0x00BF9747, 0x00F4EEE2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00D5B982, 0x00B08327, 0x009D701A, 0x009D701B, 0x009C6F1B, 0x009C6F1B, 
        0x009B6F1B, 0x009B6E1B, 0x009A6E1B, 0x009A6E1B, 0x00986D1A, 0x00986D1A, 0x00976C19, 0x00966C19, 0x00956B19, 0x00A4771E, 0x00C09539, 0x14FBF8F4, 0xE7707070, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEB727272, 0x1CF9F8F6, 0x008A7145, 0x00674A1A, 
        0x0065481D, 0x0065481D, 0x0065481D, 0x0065491D, 0x00674A1C, 0x00684B1C, 0x00694C1B, 0x006A4C1A, 0x006B4D1A, 0x006D4E19, 0x006E5019, 0x006F5118, 0x00715117, 0x00725217, 0x00745315, 0x00765415, 0x00775513, 0x00785612, 0x00795612, 0x007B5712, 
        0x007C5813, 0x007C5913, 0x007E5A13, 0x007F5B14, 0x00805C14, 0x00825D14, 0x00835E16, 0x00845F15, 0x00856015, 0x00866016, 0x00775515, 0x007C6235, 0x00D2C9BA, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3EA, 0x00BA8C28, 0x009A6E14, 0x00906715, 0x00916816, 0x00926917, 0x00936917, 0x00946A17, 0x00956A18, 0x00956B19, 0x00966B19, 0x00976C19, 0x00976C19, 0x00986D1A, 0x00996D1A, 
        0x009A6E1A, 0x00B1811C, 0x00DAC190, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EDE2CD, 0x00B38315, 0x009E711B, 0x009A6E1B, 0x009A6E1B, 0x009A6E1B, 0x00996D1B, 
        0x00996D1A, 0x00986D1A, 0x00976C19, 0x00976C19, 0x00966B19, 0x00956B19, 0x00956A18, 0x00946A17, 0x00986E1C, 0x00B98F39, 0x00E9DCC1, 0x97D3D3D3, 0xFF020202, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF070707, 0x6CD1D1D1, 0x00C3B498, 0x00725315, 0x0065491C, 
        0x0065481D, 0x0065491D, 0x0065481D, 0x0065481D, 0x0065491D, 0x00674A1C, 0x00684B1C, 0x00694C1B, 0x006A4C1A, 0x006C4D1A, 0x006D4E19, 0x006E5019, 0x006F5018, 0x00715117, 0x00725216, 0x00735315, 0x00755415, 0x00765514, 0x00785612, 0x00795612, 
        0x007B5712, 0x007B5813, 0x007C5913, 0x007E5A12, 0x007F5B14, 0x00805C14, 0x00815D13, 0x00825E15, 0x00845F15, 0x00856015, 0x00866116, 0x00825E16, 0x00755416, 0x00A28E67, 0x00F1EEE8, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C9B07F, 0x00976C14, 0x008E6617, 0x008F6716, 0x00906715, 0x00906816, 0x00916816, 0x00926917, 0x00936916, 0x00946A17, 0x00946A18, 0x00956B18, 0x00956B19, 0x00966B19, 
        0x009C6F17, 0x00B68C35, 0x00FBF8F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FCFAF8, 0x00B28F4B, 0x009D7017, 0x00986D1A, 0x00976C1A, 0x00976C19, 0x00976C19, 0x00976C19, 
        0x00966B19, 0x00966B19, 0x00956B18, 0x00956A17, 0x00946A16, 0x00936917, 0x00926916, 0x00916816, 0x00B08A3D, 0x00D4BA87, 0x39FEFDFD, 0xFA383838, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC86C6C6C, 0x07F0EBE4, 0x0089692E, 0x006B4D1C, 0x0065481D, 
        0x0065491D, 0x0065481D, 0x0065481D, 0x0065481D, 0x0065481D, 0x0066491D, 0x00674A1C, 0x00684A1C, 0x00694B1B, 0x006A4C1A, 0x006B4D1A, 0x006D4E19, 0x006E4F19, 0x006F5018, 0x00715117, 0x00735217, 0x00735315, 0x00755415, 0x00765514, 0x00785513, 
        0x00795612, 0x007A5712, 0x007B5812, 0x007C5913, 0x007D5913, 0x007E5A13, 0x00805B14, 0x00815C13, 0x00825D14, 0x00835E15, 0x00845F15, 0x00856015, 0x00866116, 0x00805C15, 0x0086672B, 0x00DFD7C9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00DACFB9, 0x00815D0C, 0x008B6417, 0x008C6517, 0x008D6517, 0x008E6617, 0x008F6617, 0x00906716, 0x00906715, 0x00916816, 0x00926816, 0x00926916, 0x00936916, 0x00916815, 
        0x008C6510, 0x00DCCFB6, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CCBC9C, 0x008C650E, 0x00956B18, 0x00956B18, 0x00956B18, 0x00956A18, 0x00946A17, 0x00946A17, 
        0x00936A17, 0x00936916, 0x00926916, 0x00916815, 0x00916815, 0x00906715, 0x00906715, 0x009C7423, 0x00C3A057, 0x07F6F1E6, 0xC89E9E9E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA191919, 0x3AE8E8E8, 0x00AF976E, 0x0078571E, 0x0065491D, 0x0065481D, 
        0x0065481D, 0x0065481C, 0x0064481C, 0x0065481D, 0x0065481D, 0x0065481D, 0x0066491D, 0x00674A1C, 0x00684B1C, 0x00694B1B, 0x006A4C1A, 0x006C4D1A, 0x006C4E19, 0x006D4F19, 0x006F5118, 0x00715117, 0x00725216, 0x00735316, 0x00745415, 0x00765514, 
        0x00775513, 0x00785613, 0x00795612, 0x007B5712, 0x007C5813, 0x007D5913, 0x007E5A12, 0x007F5B14, 0x00805B14, 0x00815C14, 0x00825E14, 0x00835E16, 0x00845F15, 0x00856116, 0x0084611B, 0x00A28858, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00DFD9CF, 0x006B4E15, 0x00886216, 0x008A6317, 0x008B6417, 0x008B6417, 0x008C6517, 0x008D6517, 0x008D6517, 0x008E6616, 0x008F6716, 0x008F6716, 0x00906715, 0x007B5813, 
        0x009F885C, 0x00FDFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00EEEBE5, 0x00806227, 0x008C6414, 0x00936916, 0x00926916, 0x00926916, 0x00926816, 0x00916816, 0x00916816, 
        0x00916816, 0x00906715, 0x00906715, 0x008F6616, 0x008E6617, 0x008E6617, 0x008F6618, 0x00B28731, 0x00DFCAA3, 0x6AEDEDED, 0xFE1A1A1A, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x97A6A6A6, 0x01E6DFD4, 0x00866323, 0x007B5A1F, 0x0079581E, 0x0079581F, 
        0x0079581E, 0x0079581E, 0x0079581E, 0x0079581F, 0x0079581E, 0x0079581E, 0x0079581E, 0x0079581E, 0x007A591E, 0x007A591E, 0x007A591E, 0x007B591D, 0x007B5A1D, 0x007C5A1D, 0x007C5B1D, 0x007D5B1D, 0x007D5B1D, 0x007E5C1D, 0x007E5C1C, 0x007F5C1C, 
        0x007F5D1C, 0x007F5D1B, 0x00805D1B, 0x00805D1B, 0x00815E1B, 0x00815E1B, 0x00815E1B, 0x00825E1B, 0x00825E1B, 0x00825F1C, 0x00835F1C, 0x0083601C, 0x0084601D, 0x0084601D, 0x00896829, 0x00D0C2AB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00D4CAB8, 0x00735314, 0x00866116, 0x00876216, 0x00886216, 0x00896216, 0x008A6316, 0x008A6317, 0x008B6417, 0x008B6417, 0x008C6517, 0x008D6517, 0x00856015, 0x00745823, 
        0x00E8E4DD, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00A29070, 0x00765415, 0x00906716, 0x00906715, 0x00906715, 0x008F6716, 0x008F6616, 0x008E6616, 0x008E6617, 
        0x008E6617, 0x008D6517, 0x008D6517, 0x008C6517, 0x008B6417, 0x008B6417, 0x009E7317, 0x00C49C4A, 0x1CFBF9F5, 0xE9696969, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE55B5B5B, 0x15FEFEFE, 0x00FEFDFD, 0x00E4DCCF, 0x00E1D8CA, 0x00E1D8C9, 0x00E0D8C9, 
        0x00E0D8C9, 0x00E1D8C9, 0x00E0D8C9, 0x00E1D8CA, 0x00E1D8C9, 0x00E0D8C9, 0x00E0D8C9, 0x00E1D8C9, 0x00E0D8C9, 0x00E1D8C9, 0x00E0D8C9, 0x00E1D8CA, 0x00E1D8CA, 0x00E1D8C9, 0x00E1D8C9, 0x00E1D8C9, 0x00E1D8C9, 0x00E1D8C9, 0x00E0D8C9, 0x00E1D8C9, 
        0x00E1D8C9, 0x00E1D8C9, 0x00E0D8C9, 0x00E1D8C9, 0x00E0D8C9, 0x00E0D8C9, 0x00E0D8C9, 0x00E0D8C9, 0x00E0D8C9, 0x00E1D8C9, 0x00E1D8CA, 0x00E1D8C9, 0x00E1D8CA, 0x00E4DCCF, 0x00F8F6F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00B7A37F, 0x00815E1B, 0x00835E16, 0x00845F15, 0x00856015, 0x00866116, 0x00866116, 0x00876116, 0x00886217, 0x00896216, 0x008A6317, 0x008A6317, 0x00805E1A, 0x00C0B092, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DCD3C4, 0x00785919, 0x00896316, 0x008D6517, 0x008C6516, 0x008C6517, 0x008C6517, 0x008C6417, 0x008B6417, 0x008B6417, 
        0x008B6417, 0x008A6317, 0x008A6317, 0x00896317, 0x00896216, 0x008C6516, 0x00AB7B15, 0x02E9DBBD, 0xA0B6B6B6, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE121212, 0x60E6E6E6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00F4F0EB, 0x008F6F34, 0x00815D17, 0x00815C14, 0x00825D14, 0x00825E15, 0x00835E15, 0x00845F16, 0x00845F16, 0x00856016, 0x00856016, 0x00866116, 0x0086611B, 0x00997C48, 0x00F9F8F6, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F9F8F6, 0x009B7F4D, 0x00856018, 0x008A6317, 0x008A6317, 0x008A6316, 0x008A6317, 0x008A6317, 0x008A6317, 0x00896217, 0x00886216, 
        0x00886216, 0x00876216, 0x00876116, 0x00866116, 0x00856016, 0x008E6611, 0x00C0A46E, 0x43F1F1F0, 0xFA1F1F1F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xBF8F8F8F, 0x05FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFD, 0x00B8A37F, 0x00825F1C, 0x007E5A13, 0x007F5A13, 0x007F5B14, 0x00805C14, 0x00815C14, 0x00815D14, 0x00825D14, 0x00825E15, 0x00835E15, 0x00845F17, 0x00866323, 0x00DDD3C3, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00C8B89C, 0x0085621E, 0x00876116, 0x00876217, 0x00876116, 0x00876116, 0x00866116, 0x00866116, 0x00866116, 0x00866016, 0x00856015, 
        0x00856016, 0x00845F15, 0x00845F15, 0x00835E15, 0x007E5A11, 0x00997C3A, 0x16F5F2EC, 0xD6626262, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF6333333, 0x32FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00CFC2AA, 0x00856323, 0x007C5814, 0x007B5813, 0x007C5913, 0x007D5913, 0x007E5A12, 0x007E5A13, 0x007F5B13, 0x00805B14, 0x00805C14, 0x00815C14, 0x0083601C, 0x00AF986F, 0x00FEFEFE, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00EFEAE2, 0x008E6D32, 0x00846018, 0x00845F15, 0x00845F16, 0x00845F15, 0x00845F16, 0x00845F16, 0x00835E15, 0x00835E15, 0x00835E15, 0x00835E15, 
        0x00825E15, 0x00825E14, 0x00815D14, 0x00725215, 0x00886F41, 0x10EEEBE4, 0xBC8F8F8F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF030303, 0x8CC3C3C3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFD, 0x00CCBEA5, 0x00866425, 0x007A5715, 0x00795612, 0x00795712, 0x007A5712, 0x007B5712, 0x007B5812, 0x007C5813, 0x007D5913, 0x007D5913, 0x007E5A12, 0x00805C17, 0x008D6D31, 0x00F0ECE5, 0x00FEFEFE, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFDFD, 0x00AF986F, 0x0083601B, 0x00825D14, 0x00825D14, 0x00815D14, 0x00815D14, 0x00815D14, 0x00815D14, 0x00815D13, 0x00815C14, 0x00805C13, 0x00805C14, 
        0x00805B14, 0x007E5A15, 0x00775616, 0x00A59270, 0x22F2F0ED, 0xC8828282, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDE777777, 0x12FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00F4F1ED, 0x00B29C75, 0x00826020, 0x00775516, 0x00755414, 0x00765514, 0x00775513, 0x00785513, 0x00785612, 0x00795612, 0x007A5712, 0x007A5712, 0x007B5812, 0x007C5813, 0x0083601F, 0x00C9BA9F, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00DCD2C0, 0x00856322, 0x007F5B15, 0x007F5B13, 0x007F5B14, 0x007F5A13, 0x007F5A13, 0x007F5B13, 0x007E5A13, 0x007E5A13, 0x007E5A12, 0x007E5A13, 0x00805C17, 
        0x0083601E, 0x00A08657, 0x07DFD6C7, 0x67DCDCDC, 0xEC3F3F3F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE252525, 0x5CF2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F4F1ED, 
        0x00C5B497, 0x008E6E34, 0x007D5C1D, 0x00755518, 0x00755417, 0x00755517, 0x00765517, 0x00775616, 0x00785616, 0x00785616, 0x00795615, 0x00795715, 0x00795715, 0x007A5715, 0x00805D1A, 0x009F8452, 0x00FAF9F7, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 
        0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FAF9F7, 0x00997B46, 0x00805D1A, 0x007E5A15, 0x007D5A15, 0x007E5A15, 0x007E5A15, 0x007E5A15, 0x007E5B16, 0x007F5C18, 0x00805D1A, 0x00846120, 0x00967842, 0x00BBA785, 
        0x10E6DFD4, 0x64D6D6D5, 0xD4686868, 0xFF0C0C0C, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE1E1E1E, 0x65F3F3F3, 0x01FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E7E0D5, 0x00BCA987, 
        0x00B6A17C, 0x00B6A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00C5B497, 0x00F3F0EB, 0x00FFFFFF, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFE, 0x00FFFFFF, 0x00FAF9F7, 0x00BEAC8B, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B7A27D, 0x00B6A17C, 0x00B7A37E, 0x00BDAA88, 0x01C9B99E, 0x0ADAD0BD, 0x29EDE8E1, 0x62CFCFCE, 0xA98D8D8D, 
        0xED383838, 0xFF020202, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB232323, 0xC8767676, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 
        0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 
        0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 
        0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 
        0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB2808080, 0xB4808080, 0xC27E7E7E, 0xD6545454, 0xF0323232, 0xFE0A0A0A, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF53C2D0E, 0xC4684E19, 0xC4684F18, 0xEB49380F, 0xFF000000, 0xFF000000, 0xFE1E1407, 0xA1916D22, 0xE1523A13, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF030201, 0xEA493710, 0xBC694F18, 0xF0413615, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF030000, 0xDD220B04, 0xD8220B04, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF5100601, 0xC7240C04, 0xFA110401, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE7634B18, 0x74BD8D2C, 0x16E2AA34, 0xA8A87725, 0xFF000000, 0xFF000000, 0xFE2F2009, 0x53D5A031, 0xC778571A, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xB6936E22, 0x28E2AB34, 0x81A67D26, 0xEC4B3D16, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF090000, 0x6D501607, 0x55491909, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD4240D03, 0x0F4F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB493010, 0x2EE3AA34, 0xA8A77725, 0xFF000000, 0xFD140F05, 0xCA564114, 0xC6574114, 0xE0483211, 0xFF000000, 0xFB221908, 0xC9564114, 
        0x4AD29E30, 0x5BD6A131, 0xCE574214, 0xFE0F0B04, 0xFF000000, 0xFF000000, 0xFD0F0B03, 0xDE4A3911, 0xC7564114, 0xD54D3912, 0xF71C1507, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFC070201, 0xD2210A05, 0xA82C0F05, 0xA22B0F05, 0xC4240B03, 0xF60C0301, 0xD91A0903, 0xDD1B0903, 0xFF000000, 0xF5100502, 0xC81F0A04, 0xFA060201, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC060201, 0xCA1E0A03, 0xF1110602, 
        0xFF000000, 0xFF000000, 0xF90B0301, 0xCA1F0B03, 0xA32B0F05, 0xA72A0E05, 0xD6190903, 0xFC0B0201, 0xC91E0A04, 0xF00E0501, 0xFC0D0301, 0xC91E0A03, 0xE8190902, 0xBB230C04, 0xA22B0F05, 0xC3230C04, 0xF70C0401, 0xFE050101, 0xD81C0903, 0xA82B0E05, 
        0xAA2A0F05, 0xDC190903, 0xFE010000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD080201, 0xD51E0A04, 0xA9290E04, 0xA52B0F05, 0xCB1E0A03, 0xFA070301, 0xFF000000, 0xFF000000, 0xFD080100, 0xC81E0A03, 0xEE150802, 0xC8210C04, 0xA22B0F05, 0xAD290D04, 
        0xDF180803, 0xFE020100, 0xFF000000, 0xED100401, 0xC6240A04, 0x554F1808, 0x434B1A08, 0xC61E0A04, 0xDA1D0A03, 0xFF000000, 0xFF000000, 0xF3090301, 0xBE240C04, 0xA02B0F05, 0xB0260D04, 0xE4160802, 0xFF010000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xEE0E0501, 0xBA260D04, 0xA12B0F05, 0xAF280D05, 0xE2160803, 0xD4250D03, 0x0F4F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB493010, 0x2EE3AA34, 0xA8A87725, 0xFF000000, 0xFB2D210C, 0x87BE8E2C, 0x31DFAA35, 0x78BB872B, 0xFF000000, 0xF54A3A10, 0x80BE8F2C, 
        0x2CDEA733, 0x3BDEA732, 0x8BC1912C, 0xFC231A08, 0xFF010100, 0xB980601E, 0x38DDA633, 0x55D29E30, 0x7ABF8E2C, 0x66CF9B2F, 0x35E4AC35, 0x84AD8428, 0xF9261C08, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xCF1E0A03, 0x38491908, 0x52491908, 0x91391206, 0x99341206, 0x68461808, 0x334D1A09, 0x2A4D1A09, 0x6A481808, 0xFF000000, 0xD52C1005, 0x0B4F1B09, 0xE8110602, 0xFF000000, 0xFF000000, 0xFF000000, 0xF10F0502, 0x144F1B09, 0xC42E1006, 
        0xFF020100, 0xB82B0F05, 0x314D1B09, 0x5F461808, 0x97361306, 0x92371106, 0x51491908, 0x4A4E1A09, 0x0B4E1A09, 0xC0270D04, 0xF2220A01, 0x0F4E1B09, 0x244F1B09, 0x79411507, 0x98351206, 0x66461808, 0x304D1A09, 0x53431707, 0x3F4B1A08, 0x92371306, 
        0x8E391406, 0x374F1B09, 0x6B451908, 0xFC060201, 0xFF000000, 0xD6170803, 0x3F4A1908, 0x52491908, 0x91361306, 0x95361305, 0x5F4B1A08, 0x344D1A09, 0xBE2A0E04, 0xFF020000, 0xF5140501, 0x0B4E1A09, 0x274F1B09, 0x65481908, 0x97351206, 0x833B1407, 
        0x2B4E1B09, 0x733F1507, 0xFB0B0401, 0xD5280803, 0x76431607, 0x334F1A08, 0x284D1A09, 0x76421507, 0xA7401507, 0xFD040100, 0x932F1005, 0x2D4F1B09, 0x75441706, 0x9A341206, 0x873C1407, 0x3C4E1B09, 0x60441607, 0xEE120502, 0xFF000000, 0xFA050201, 
        0x7F331206, 0x2D4F1B09, 0x77401607, 0x99341206, 0x883B1506, 0x3E4F1A09, 0x474C1A09, 0x0D4F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB493010, 0x2EE3AA34, 0xA8A87725, 0xFF000000, 0xFF000000, 0xFF000000, 0x64CCA437, 0x78BC872B, 0xFF000000, 0xFF000000, 0xFF000000, 
        0x5EC7962E, 0x7ECE982C, 0xFF000000, 0xFF000000, 0xC46A501B, 0x1DE1AA34, 0xD25A4414, 0xFF020100, 0xFF000000, 0xFF010100, 0xF43B2F0B, 0x57C89630, 0x74CB992F, 0xFE161005, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xEB190702, 0x194E1B09, 0xAA3E1107, 0xFF050201, 0xFF000000, 0xFF000000, 0xFF020100, 0xD52A0B04, 0x18501A09, 0x6A481809, 0xFF000000, 0xD52B0F05, 0x0B4F1B09, 0xE8110602, 0xFF000000, 0xFF000000, 0xFF000000, 0xF10F0502, 0x144F1B09, 0xC42E1005, 
        0xD0341204, 0x164F1B09, 0xC0290D05, 0xFF020100, 0xFF000000, 0xFF000000, 0xFE050200, 0x9F301105, 0x014E1B09, 0xC0270D04, 0xF2220901, 0x0F4E1A09, 0xA2361206, 0xFF010000, 0xFF000000, 0xFF020100, 0x6E3D1506, 0x1F4E1A09, 0xF5130301, 0xFF000000, 
        0xFF000000, 0xEA200B04, 0x124E1B09, 0xC1310F04, 0xED140601, 0x204E1B09, 0xA3401306, 0xFE110501, 0xFF090301, 0xFF090301, 0xFE0C0401, 0xC1351105, 0x1A4F1B08, 0xD62A1104, 0xF5140500, 0x0B4E1A08, 0x90371305, 0xFF010000, 0xFF000000, 0xFF000000, 
        0xE61A0902, 0x184E1B09, 0xB8411105, 0xFF000000, 0xFF090000, 0x6D501607, 0x55481909, 0xFF000000, 0xFF020000, 0x9D3C1106, 0x294F1B09, 0xE92A0E04, 0xFF0A0401, 0xFF090401, 0xFF090301, 0xFA1A0802, 0x66471708, 0x554C1708, 0xFC120300, 0x84391306, 
        0x384D1A09, 0xF0180802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC0B0301, 0x70451507, 0x0A4F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB493010, 0x2EE3AA34, 0xA8A87725, 0xFF000000, 0xFF000000, 0xFF000000, 0x64CCA53B, 0x78BC872A, 0xFF000000, 0xFF000000, 0xFF000000, 
        0x5EC7962E, 0x7ECE982C, 0xFF000000, 0xFF000000, 0x69B4872A, 0x25E4AC35, 0x5DDAA532, 0x5DDAA532, 0x5DDAA532, 0x5DDAA532, 0x5DDAA532, 0x4AE2AB35, 0x2DE4AC35, 0xF6523D13, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xB1280D05, 0x1C4F1B09, 0xFA170803, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010000, 0x60461808, 0x6A481808, 0xFF000000, 0xD52C1005, 0x0B4F1B09, 0xE8110602, 0xFF000000, 0xFF000000, 0xFF000000, 0xF00F0502, 0x134F1B09, 0xC42E1006, 
        0x88471908, 0x3F481808, 0xFF030100, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB0E0200, 0x174F1B09, 0xC0270D04, 0xF2220902, 0x114E1A09, 0xC72B0F04, 0xFF000000, 0xFF000000, 0xFF000000, 0x92301006, 0x37481809, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFC130500, 0x274E1A09, 0xA73B1306, 0xB7250D04, 0x064E1B09, 0x434F1B09, 0x45501B08, 0x454F1A08, 0x45501B09, 0x45501B09, 0x444F1B09, 0x1D4F1B09, 0x99491606, 0xF5140500, 0x0C4E1A08, 0xC7270D04, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF100000, 0x384F1808, 0x93421706, 0xFF000000, 0xFF090000, 0x6D501607, 0x55481909, 0xFF000000, 0xFE030100, 0x4B4F1A08, 0x1F4E1B08, 0x45501B09, 0x45501B09, 0x45501C09, 0x45501B08, 0x454F1B09, 0x394F1B09, 0x234F1B09, 0xE8260D03, 0x354E1A08, 
        0x96481606, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDB250B03, 0x114F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB493010, 0x2EE3AA34, 0xA8A87725, 0xFF000000, 0xFF000000, 0xFF000000, 0x64CCA43B, 0x78BC872B, 0xFF000000, 0xFF000000, 0xFF000000, 
        0x5EC7962E, 0x7ECE982C, 0xFF000000, 0xFF000000, 0x8F9C7424, 0x3BD6A131, 0xF12D210A, 0xF5161105, 0xF5161105, 0xF5161105, 0xF5181205, 0xD7674D17, 0xDD6C5018, 0xFF120D03, 0xFF000000, 0xF244330E, 0xB17E5F1D, 0xCE654C17, 0xFE0B0803, 0xFF000000, 
        0xD81F0B03, 0x0E4F1A09, 0xD22D0E05, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF1120902, 0x304C1A08, 0x6A481908, 0xFF000000, 0xE02A0F04, 0x094F1B09, 0xD91C0A03, 0xFF000000, 0xFF000000, 0xFF000000, 0xDA240903, 0x0A4F1B09, 0xC42E0F05, 
        0xB23A1406, 0x1B4F1A09, 0xE31A0803, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC8250C04, 0x044E1A09, 0xC0270D04, 0xF2220901, 0x114E1A08, 0xC72B0E05, 0xFF000000, 0xFF000000, 0xFF000000, 0x92301106, 0x37481808, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFC130500, 0x274E1A08, 0xA73B1306, 0xDA1D0903, 0x104F1B09, 0xC5331205, 0xF7080301, 0xF7080301, 0xF7080301, 0xF7080301, 0xDE200A03, 0x8E401806, 0xEB250B03, 0xF5140500, 0x0C4E1A08, 0xC9260C04, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF0F0000, 0x394F1908, 0x93421706, 0xFF000000, 0xFF090000, 0x6D501607, 0x55481809, 0xFF000000, 0xFF010000, 0x7A451507, 0x404E1B09, 0xF1180803, 0xF7080301, 0xF7080301, 0xF7080301, 0xF60B0401, 0xAF3A1306, 0xA7401706, 0xFD100401, 0x614A1607, 
        0x5B4C1A09, 0xFC0C0401, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010000, 0xA2341206, 0x0C4F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF0362B0A, 0xC283611B, 0x24E2AB34, 0x83BC8A29, 0xD94C3A11, 0xFC120D04, 0xC94D3A12, 0x4DD4A738, 0x5DC8952D, 0xCE4D3A12, 0xF9251C08, 0xC94D3A12, 
        0x48D09D30, 0x61D49F2F, 0xCD5D4912, 0xFD0E0A03, 0xF7171105, 0x59D39F30, 0x4CC9972E, 0xBC614917, 0xDF4C3912, 0xCF544014, 0x7DA67E26, 0x31E4AC35, 0xD083631C, 0xFF040300, 0xFF150F05, 0x60DDA533, 0x00E1A934, 0x09E3AB34, 0xD3684D19, 0xFF000000, 
        0xFF000000, 0x9F351005, 0x274D1A09, 0x91341105, 0xD11B0903, 0xD81A0903, 0xAB2D0F05, 0x40491908, 0x144E1B09, 0x6A481908, 0xFF000000, 0xFC0D0401, 0x5B4C1A09, 0x43481908, 0xC1210B04, 0xDF1A0903, 0xBB270D04, 0x3E4B1A08, 0x034F1A09, 0xC42E1005, 
        0xFC0C0401, 0x7B461709, 0x2E4E1B09, 0xA02E1005, 0xD61B0903, 0xD21B0903, 0x90341206, 0x2C4E1B09, 0x064E1B09, 0xC0270D04, 0xF2220A02, 0x114E1A09, 0xC72B0E05, 0xFF000000, 0xFF000000, 0xFF000000, 0x92301106, 0x37481808, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFC130500, 0x274F1A09, 0xA73B1306, 0xFF000000, 0xA0331105, 0x254D1A09, 0x8E341106, 0xD21C0903, 0xD61C0903, 0xA1321106, 0x314D1B09, 0x82421708, 0xFD0A0301, 0xF5140500, 0x0C4E1A08, 0xC9260C04, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF0F0000, 0x394F1908, 0x93421806, 0xFF000000, 0xFF090000, 0x6D501607, 0x55481908, 0xFF000000, 0xFF000000, 0xF1100601, 0x50451808, 0x464C1A08, 0xB5280E04, 0xDA1A0903, 0xC8200B04, 0x73431608, 0x304D1A09, 0xCF240D03, 0xFF000000, 0xE9150602, 
        0x434F1808, 0x55461808, 0xBB240D05, 0xDC1A0903, 0xCC210C03, 0x7B401407, 0x244F1B09, 0x0C4F1B09, 0xE82E0802, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDE5D4616, 0x7FA37C26, 0x7FA27A26, 0x7FA37B26, 0xAD9F7925, 0xF9251C09, 0x87A27B25, 0x7FA27A26, 0x7FA27A26, 0x93A27A25, 0xF34C3A12, 0x88A27A26, 
        0x7FA27B25, 0x7FA27A26, 0x92A47C26, 0xFC1D1607, 0xFF000000, 0xFE211908, 0xC67A5C1C, 0x7EB48829, 0x62C7972E, 0x6FC0912C, 0xA7906C21, 0xF43E2D0E, 0xFF000000, 0xFF000000, 0xFF050301, 0xCA8A681D, 0x62C8972E, 0x84AF8429, 0xF72E220B, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xE51B0803, 0x953A1205, 0x68451707, 0x61451808, 0x843C1407, 0xD2240C04, 0xAA331106, 0xB4331106, 0xFF000000, 0xFF000000, 0xFB0C0401, 0xB2311005, 0x6F431607, 0x5D451808, 0x77421507, 0xC9340F05, 0x8A381307, 0xE1200B04, 
        0xF2210A02, 0x8E391406, 0xB73D1306, 0x8A3C1406, 0x63451808, 0x67451707, 0x95391306, 0xCE321104, 0x084F1B09, 0xD6270A03, 0xF8180801, 0x87381306, 0xE21F0A04, 0xFF000000, 0xFF000000, 0xFF000000, 0xC8220C04, 0x9B341206, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFE0E0500, 0x92381306, 0xD22A0D05, 0xFF000000, 0xFF000000, 0xE41C0903, 0x92391406, 0x66451707, 0x63451808, 0x883F1206, 0xD8200B04, 0xFE030100, 0xFF000000, 0xFA0E0500, 0x84381306, 0xE31B0803, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF0F0000, 0x9B3A1106, 0xC8301004, 0xFF000000, 0xFF090000, 0xB53D1005, 0xA9331106, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD040100, 0xC1290D05, 0x7A3E1406, 0x5F451808, 0x6E421707, 0xA8311005, 0xF40E0502, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFB060201, 0xBB340D04, 0x78431407, 0x5F451808, 0x6D421607, 0xA6301006, 0xE02A0C04, 0x86381306, 0xF3270602, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFE0B0401, 0x7D491707, 0x294C1909, 0xA8280E04, 0xE5140601, 0xEA150501, 0xBC240C05, 0x3A4A1908, 0x654D1909, 0xFE050200, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFE0A0301, 0xCD1F0A03, 0x7C361206, 0x553F1507, 0x533F1507, 0x703B1306, 0xBD220C04, 0xFD0C0401, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000
};

GUI_CONST_STORAGE GUI_BITMAP bmSTLogo = {
  140, /* XSize */
  70, /* YSize */
  560, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)acSTLogo,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

static GUI_CONST_STORAGE unsigned long acSTLogo_70x35[] = {
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD101010, 
        0xE03E3E3E, 0xC65A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A59, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 
        0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 0xBF5A5A5A, 
        0xBF5A5A5A, 0xBF5A5A5A, 0xC25A5A5A, 0xF51F1F1F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF040404, 0xC9595959, 0x5FAAA397, 0x16C9B798, 
        0x00BBA072, 0x00B3945D, 0x00B29259, 0x00B59359, 0x00B7955A, 0x00B9965A, 0x00BC985B, 0x00BE995B, 0x00BF9B5C, 0x00C09C5F, 0x00C09D61, 0x00C19E63, 0x00C2A065, 0x00C3A268, 0x00C4A36A, 0x00C5A46C, 0x00C5A66E, 0x00C6A770, 0x00C7A971, 0x00C7AA74, 
        0x00C8AB76, 0x00C9AC77, 0x00C8AC77, 0x00C9AC76, 0x00C9AB75, 0x00C9AA73, 0x00C8A972, 0x00C8A970, 0x00C8A76E, 0x00C7A66D, 0x00C7A66B, 0x00C7A469, 0x00C6A367, 0x00C6A265, 0x00C5A263, 0x00C6A162, 0x00C4A061, 0x00C29E61, 0x00C09E61, 0x00BF9D61, 
        0x00BD9B61, 0x00BB9A60, 0x00CBB284, 0x8FA7A7A7, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF5191919, 0x61A8A39C, 0x02A99169, 0x0098743A, 0x009C7636, 
        0x00A07836, 0x00A47B37, 0x00A77D38, 0x00AB7F39, 0x00AE8239, 0x00B28539, 0x00B6873B, 0x00B98A3B, 0x00BB8C3C, 0x00BD8E41, 0x00BD9046, 0x00BF9248, 0x00C0954D, 0x00C19751, 0x00C29954, 0x00C39B58, 0x00C49E5C, 0x00C5A05F, 0x00C7A362, 0x00C7A566, 
        0x00C8A76A, 0x00C9A96D, 0x00C9A96C, 0x00C8A66A, 0x00C7A566, 0x00C6A362, 0x00C5A05E, 0x00C49E5B, 0x00C39B57, 0x00C29954, 0x00C19751, 0x00C0944C, 0x00BF9248, 0x00BD9045, 0x00BC8E40, 0x00BC8C3C, 0x00B98A3B, 0x00B5863A, 0x00B18539, 0x00AE8239, 
        0x00AA7F39, 0x00A47E3D, 0x1DD8CCB8, 0xF12E2E2E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB131313, 0x47C3BDB4, 0x008B6D3C, 0x00967136, 0x00997337, 0x009D7636, 
        0x00A07836, 0x00A37B37, 0x00A37A36, 0x009C7534, 0x00997333, 0x009A7435, 0x009C7534, 0x009E7735, 0x009F7836, 0x00A07939, 0x00A07A3B, 0x00A17B3D, 0x00A27C40, 0x00A27E43, 0x00A38044, 0x00A48147, 0x00A48349, 0x00A5844B, 0x00A6864D, 0x00A6874F, 
        0x00A78852, 0x00A78952, 0x00A78953, 0x00A78851, 0x00A7874F, 0x00A7864D, 0x00A6854C, 0x00A6844A, 0x00A68348, 0x00A58246, 0x00A58144, 0x00A57F42, 0x00A47E40, 0x00A37D3D, 0x00A37C3B, 0x00A37C39, 0x00A17A39, 0x009F7938, 0x009D7738, 0x009C7638, 
        0x00997538, 0x01A89169, 0xA980807F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x92949392, 0x00987D50, 0x00926D35, 0x00967036, 0x00997237, 0x009C7536, 
        0x00967134, 0x00A99066, 0x00CBBFAB, 0x00E7E1D9, 0x00F6F3F0, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 
        0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F6, 0x00F9F8F7, 0x00F9F8F7, 0x00F9F8F7, 0x00FAF8F7, 
        0x00F9F8F6, 0x46DADAD9, 0xFE080808, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE53B3B3B, 0x11C8B89B, 0x008D6A34, 0x00916D35, 0x00957036, 0x00987237, 0x009D7C47, 
        0x00E1D9CB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x08FEFEFE, 0xD8474747, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF070707, 0x5ACBC6BD, 0x0098753C, 0x008D6A35, 0x00906D36, 0x00946F36, 0x009E793D, 0x00EDE7DD, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x7CA1A1A1, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xBC7B7B7B, 0x01C1A474, 0x008A6934, 0x008B6A35, 0x008F6C35, 0x00946F35, 0x00CCB389, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F7F4EF, 0x00EDE5D8, 0x00EDE6D8, 0x00EEE6D8, 0x00EEE6D9, 0x00EEE7D9, 0x00EFE7D9, 0x00EFE7DA, 0x00EFE8DA, 0x00F0E8DA, 0x00F0E9DB, 0x00F1E9DB, 0x00F1E9DB, 0x00F1E9DB, 0x00F1EADC, 0x00F2EADC, 
        0x00F2EADC, 0x00F2EADC, 0x00F2EADC, 0x00F2EADC, 0x00F3ECDE, 0x00FEFEFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFEFE, 0x00EBE5DA, 0x00E8E0D4, 0x00EAE2D5, 0x00ECE4D7, 0x00EDE5D8, 0x00EFE7DA, 0x00F1E9DB, 0x00F4EDE1, 0x22FAFAFA, 
        0xF5292929, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF8161616, 0x29DDCFB8, 0x00967136, 0x00886734, 0x008A6934, 0x008E6B35, 0x009A7337, 0x00E4D2B5, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F6F3F0, 0x009C7D48, 0x009E783B, 0x00A27B3B, 0x00A47D3C, 0x00A77F3C, 0x00AB813D, 0x00AE843E, 0x00B0863E, 0x00B4883F, 0x00B78A40, 0x00B98C40, 0x00BB8E42, 0x00BC8F44, 0x00BD9147, 0x00BD924A, 0x00BE944C, 
        0x00BF954D, 0x00BF954E, 0x00C09650, 0x00C09650, 0x00C2A776, 0x00FEFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C6B89F, 0x00B28843, 0x00B88C41, 0x00B88B3F, 0x00B88B3F, 0x00B78A3F, 0x00B58940, 0x00B48942, 0x01CEB68A, 0xB1878686, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x869E9D9B, 0x00B89863, 0x00836333, 0x00866533, 0x00896834, 0x008C6A35, 0x00957139, 0x00E1CEAD, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C5B8A3, 0x008F6F39, 0x009C7636, 0x00A17937, 0x00A47B37, 0x00A77D38, 0x00AA7F38, 0x00AD8239, 0x00B0843A, 0x00B3853A, 0x00B6873A, 0x00B9893B, 0x00BB8B3C, 0x00BB8C3D, 0x00BC8D3F, 0x00BD8E42, 
        0x00BD8F44, 0x00BE9046, 0x00BE9146, 0x00A47F40, 0x00E1DACC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBE7E1, 0x009D793D, 0x00BC8D3D, 0x00BB8B3C, 0x00B98A3B, 0x00B7883B, 0x00B4863A, 0x00B0843A, 0x00AC884A, 0x4FCBC6BC, 0xFF090909, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDE3E3E3E, 0x0CDFCEAF, 0x00896A3A, 0x00826233, 0x00846433, 0x00876634, 0x008A6934, 0x008D6B35, 0x00BFA16E, 0x00FDFBF8, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EFEBE4, 0x00AD946C, 0x009A7434, 0x00A17937, 0x00A47B37, 0x00A67D38, 0x00A97F39, 0x00AD8139, 0x00AF8339, 0x00B2853A, 0x00B4863A, 0x00B7883B, 0x00B9893B, 0x00BB8B3C, 0x00BC8C3C, 
        0x00BC8C3E, 0x00BC8D3F, 0x00B7893E, 0x00AF9A77, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AF966C, 0x00B8893A, 0x00B98A3B, 0x00B7883B, 0x00B5863A, 0x00B2853A, 0x00B08339, 0x00A87E3A, 0x0CC5B598, 0xDD474747, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE0A0A0A, 0x4FCBC4B9, 0x00A07F4A, 0x007D5F31, 0x00806133, 0x00836333, 0x00856533, 0x00886734, 0x008B6934, 0x00937039, 0x00CBAF80, 
        0x00FAF7F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DBCEB8, 0x00A98448, 0x00A17936, 0x00A37B37, 0x00A57C37, 0x00A97E38, 0x00AB8039, 0x00AD813A, 0x00AF833A, 0x00B2843A, 0x00B4863A, 0x00B6873A, 0x00B8883B, 
        0x00B9893B, 0x00B9893B, 0x00A98341, 0x00F4F0EB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E4D6BF, 0x00AF8239, 0x00B6873A, 0x00B4863A, 0x00B2853A, 0x00B08339, 0x00AE8239, 0x00AC8038, 0x00967948, 0x859E9D9A, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB0807F7F, 0x01BC9A55, 0x0071521F, 0x00745623, 0x00795A28, 0x007E5E2E, 0x00836232, 0x00866533, 0x00896834, 0x008C6A35, 0x00906C36, 
        0x00AF894E, 0x00E6D5BB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F9F6F1, 0x00CEB286, 0x00AA7F3B, 0x00A27A37, 0x00A47B37, 0x00A67C37, 0x00A97E39, 0x00AB8039, 0x00AD8139, 0x00AF8239, 0x00B0833A, 0x00B2843A, 
        0x00B3853A, 0x00B3853A, 0x00DAC8A9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFBF8, 0x00C29A59, 0x00B2843A, 0x00B08339, 0x00AE8134, 0x00AA7D2E, 0x00A67928, 0x00A37622, 0x00906818, 0x29BDB3A2, 0xF7151515, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF4272727, 0x22CDBFA2, 0x0075551A, 0x006A4D1B, 0x006D4F1A, 0x00705119, 0x00735317, 0x00765516, 0x007A5818, 0x007F5C1D, 0x00836022, 0x00876427, 
        0x008A672B, 0x00946E30, 0x00B89C6C, 0x00F2EDE5, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EFE4D3, 0x00BF9D64, 0x00A27A38, 0x00A27A37, 0x00A47B37, 0x00A67C38, 0x00A87D38, 0x00AA7F38, 0x00AB8039, 0x00AC8139, 
        0x00AE823B, 0x00C9A76E, 0x00FEFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DECAA4, 0x00AB7E28, 0x00A5771E, 0x00A3751A, 0x00A17319, 0x009F721A, 0x009D701B, 0x009B6F1B, 0x01B19356, 0xBB666666, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF030303, 0x79B5B3B0, 0x007C602E, 0x00664A1D, 0x00684B1C, 0x006B4D1B, 0x006D4F1A, 0x00705119, 0x00735317, 0x00765515, 0x00795713, 0x007B5813, 0x007E5A14, 
        0x00805C14, 0x00835E15, 0x00856016, 0x008A671E, 0x00C2B397, 0x00FDFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3EC, 0x00B8944E, 0x00986F23, 0x00997024, 0x009B7124, 0x009D7224, 0x009D7224, 0x009F7323, 0x00A07321, 
        0x00B08535, 0x00F2EADA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F4ECDC, 0x00AF842F, 0x009F711A, 0x009E711A, 0x009D701B, 0x009B6F1B, 0x009A6E1B, 0x00986D1A, 0x00AA7D21, 0x58C7C0B2, 0xFF020202, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD6535353, 0x07AF9D7B, 0x0066491D, 0x0065491D, 0x00664A1D, 0x00694C1C, 0x006B4D1B, 0x006E4F1A, 0x00705119, 0x00735317, 0x00765515, 0x00785613, 0x007B5813, 
        0x007D5A13, 0x00805C14, 0x00825E15, 0x00846015, 0x00835E16, 0x00927847, 0x00E5DFD4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DFCCA4, 0x00946A16, 0x00916817, 0x00936917, 0x00946A18, 0x00966B19, 0x00976C1A, 0x009A6E19, 
        0x00D0B275, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFEFE, 0x00BC9A51, 0x009A6F1B, 0x00996D1A, 0x00986D1A, 0x00976C1A, 0x00966B18, 0x00946A18, 0x009C7322, 0x0FDDC9A0, 0xE4444444, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE070707, 0x43BDB6AA, 0x00755622, 0x0065491D, 0x0065481D, 0x0065481D, 0x00674A1D, 0x00694B1C, 0x006B4D1B, 0x006D4F1A, 0x006F5119, 0x00735317, 0x00755516, 0x00785614, 
        0x007A5713, 0x007D5913, 0x007F5B14, 0x00815C14, 0x00835E15, 0x00856016, 0x0084621D, 0x00E0D7C8, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EEEAE2, 0x00805D14, 0x008C6517, 0x008E6617, 0x008F6617, 0x00916816, 0x00926816, 0x008E6C26, 
        0x00F6F3ED, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CFC3AA, 0x00916814, 0x00946A17, 0x00946917, 0x00936917, 0x00926816, 0x00906716, 0x00906717, 0x00BD9A54, 0x8EA7A6A3, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xA4808080, 0x01D4C7B1, 0x00AE9975, 0x00AD9874, 0x00AD9874, 0x00AD9875, 0x00AD9874, 0x00AE9974, 0x00AE9974, 0x00AE9974, 0x00AF9A73, 0x00AF9A73, 0x00B09A73, 0x00B09B73, 
        0x00B19B72, 0x00B19B72, 0x00B19B72, 0x00B19B72, 0x00B29C73, 0x00B39C74, 0x00BBA782, 0x00F4F0EA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E3DBCE, 0x00805C17, 0x00876116, 0x00886216, 0x008A6317, 0x008B6417, 0x00876218, 0x00C7BBA5, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F7F4F0, 0x0087682E, 0x008F6617, 0x008E6617, 0x008E6517, 0x008D6517, 0x008C6417, 0x008B6417, 0x00996E17, 0x30D8CAAD, 0xFA1B1B1B, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEF292929, 0x1AF9F9F9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0996F, 0x00805C15, 0x00815D15, 0x00835E15, 0x00845F16, 0x00856016, 0x00A18553, 0x00FEFEFD, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B9A580, 0x00886218, 0x00896217, 0x00886217, 0x00886217, 0x00876116, 0x00866116, 0x00845F15, 0x06B79E6A, 0xC55D5D5D, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010101, 0x6DBDBDBD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C8B99D, 0x007E5B18, 0x007B5813, 0x007D5913, 0x007E5A13, 0x007F5B14, 0x0085621E, 0x00E7E0D5, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E7E0D4, 0x0086631F, 0x00835E15, 0x00835E15, 0x00835E15, 0x00825D15, 0x00825D15, 0x007F5B15, 0x09A5916D, 0xA5807F7E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCE646464, 0x05FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EEE9E1, 0x00AD966D, 0x0079581A, 0x00765516, 0x00785615, 0x00795614, 0x007A5714, 0x007D5916, 0x00BAA682, 0x00FFFFFF, 0x00FFFFFF, 
        0x00FEFEFE, 0x00FFFFFF, 0x00FEFEFD, 0x009F8451, 0x007F5B15, 0x007F5B15, 0x007F5B15, 0x007F5C16, 0x00866422, 0x04AA9164, 0x50B0A797, 0xD54A4A4A, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD84E4E4E, 0x5FBDBDBD, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 
        0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59C0C0C0, 0x59BAB8B5, 0x599D9381, 0x599C917F, 0x599C917F, 0x599C917F, 0x599C917F, 0x599C917F, 0x599C917F, 0x599F9685, 0x59BDBCBB, 0x59BFBFBF, 0x59C0C0C0, 
        0x59BFBFBF, 0x59C0C0C0, 0x59BFBEBE, 0x599E9482, 0x599C917F, 0x599C917F, 0x5E9D9381, 0x758B8479, 0xA372716F, 0xE5323232, 0xFF010101, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7281F0A, 0x859C7625, 0xE53D2C0E, 0xFF140D05, 0xA78D6821, 0xFF000000, 0xED261C09, 0x948F6C21, 0xF7241D0B, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF040000, 0x9E381206, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xF20D0502, 0xAF2D0D05, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x95966D22, 0xD4543C13, 0xD3564015, 0x948F6A22, 0xFC1B1506, 0x70B18629, 0x7CB48729, 0xFF0D0A03, 0xBC5C4516, 0x9E8D6A21, 0x9A886620, 0xDF35280C, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xC11C0A04, 0x98341206, 0x9A331106, 0x8C301006, 0xD2190903, 0xA82B0F05, 0xF9060201, 0xFF000000, 0xB3210B04, 0xED110602, 0xB9210C04, 0x99321206, 0x99311106, 0x87321006, 0xE81A0802, 0x7A361306, 0x9C311106, 0x94311106, 0x9A2C0F05, 0x9D321206, 
        0x9F2C1006, 0xFF020101, 0xC51B0904, 0x99321106, 0x99331206, 0xBB200B04, 0xFC080201, 0x7A351206, 0x9A331206, 0x8F331206, 0xDB130702, 0xC0280B04, 0x3D4E1A09, 0xB0301006, 0xE40D0502, 0x95311106, 0x9D311106, 0xA02B0F05, 0xFB050201, 0xDE0E0502, 
        0x93311106, 0x9D311106, 0x8F361306, 0x7C3F1206, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x95966D22, 0xD4543C13, 0xFF000000, 0x6EC49632, 0xFF000000, 0xAF644B17, 0xBF674C16, 0xCB483612, 0x5DBF902C, 0xAE6E5319, 0xAC7C5F1C, 0x51D7A233, 0xFD1A1407, 0xFF000000, 0xFF000000, 0xE7110602, 
        0x773D1407, 0xFF020101, 0xFF010100, 0x94311006, 0xB5240C05, 0x703E1607, 0xF4090301, 0xFF000000, 0x832F1006, 0xB8361306, 0x85311106, 0xFF010100, 0xFF020100, 0x6D371306, 0xD9250B03, 0x63401608, 0xFF010000, 0xC01C0A03, 0x932B0E05, 0xFF000000, 
        0x88341206, 0xC32A0E04, 0x444B1909, 0xA22F1005, 0xA22E1005, 0x50491908, 0xD7270D03, 0x5C3F1607, 0xFF010000, 0xF90B0301, 0x67491707, 0xFF050000, 0x614C1808, 0xFF020100, 0x4C4B1908, 0x9D351206, 0xA22D1005, 0x78401607, 0x98351105, 0x62471808, 
        0xFC060201, 0xFF000000, 0xD21E0903, 0x7B3F1206, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC0E0B03, 0x84A57A25, 0xC16C4F18, 0xF1181206, 0x62C99A33, 0xF21D1607, 0x9C7A5C1C, 0xAB80601C, 0xE131240B, 0x75A87E27, 0xE237290D, 0xCE4A3911, 0xAE8F6C21, 0xFF0B0803, 0x81A07825, 0xAA6F531A, 0xF6080301, 
        0x6A401507, 0xD8140702, 0xE1120602, 0x5E3E1607, 0xB5240D04, 0x91351206, 0xB7220C04, 0xE6110602, 0x4A441608, 0xCE290E05, 0x6A401508, 0xDD130702, 0xD8140703, 0x40441708, 0xD9250C03, 0x6C3D1407, 0xFF000000, 0xC9180903, 0x9B240C04, 0xFF000000, 
        0x92311005, 0xCA250C04, 0x67411707, 0xD4180803, 0xDA180803, 0x883C1507, 0xF5160602, 0x6B3A1306, 0xFF000000, 0xFF080000, 0x66491907, 0xFF050000, 0x614C1808, 0xFF010000, 0x7F3B1407, 0xB9250D04, 0xE4130703, 0x93361207, 0xDD1D0B03, 0x7A3F1407, 
        0xC31E0B04, 0xEA0F0602, 0x91321106, 0x7A3F1206, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7181206, 0xBF523E13, 0xCB513D13, 0xE032260C, 0xBF513D13, 0xE13C2D0E, 0xC2513E13, 0xC4523E13, 0xFF080602, 0xF1271E09, 0xB85F4816, 0xC5544014, 0xFD100C04, 0xFF020101, 0xCB554013, 0xDF382A0D, 0xFF000000, 
        0xF9070201, 0xBF200B03, 0xB9210B04, 0xDF160803, 0xED0D0502, 0xFE030101, 0xC81D0A03, 0xB5220C04, 0xD51B0903, 0xF5140702, 0x7B431607, 0x9F301005, 0xA92E0F05, 0x5E471808, 0xF3120501, 0xDA160803, 0xFF000000, 0xF2090301, 0xE60D0502, 0xFF000000, 
        0xE4120602, 0xF40B0402, 0xF9070301, 0xBE200B04, 0xBB210B04, 0xF5090301, 0xFE040200, 0xDA150703, 0xFF000000, 0xFF040000, 0xD91B0903, 0xFF030000, 0xD71C0903, 0xFF000000, 0xFF010100, 0xCF1A0903, 0xB3220C04, 0xE7100602, 0xFF000000, 0xFE020101, 
        0xCD1E0903, 0xB3220C04, 0xE1170703, 0xDE180702, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF30B0401, 0xB41E0A04, 0xB11F0A04, 0xEE0C0402, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000
};

GUI_CONST_STORAGE GUI_BITMAP bmSTLogo70x35 = {
  70, /* XSize */
  35, /* YSize */
  280, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)acSTLogo_70x35,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*************************** End of file ****************************/
