#ifndef __MWAVE_FILES_H
#define __MWAVE_FILES_H 					   
#include "main.h"

#define FILE_ID_STRING_INSPECTION 	"fileType:INSPECTION_WAVE"

uint8_t File_MWave_SaveWaveData(char *path, char *filename, uint8_t isSelWave, uint8_t isSecret);
uint8_t File_EPT_AddGaugeRes(char *path, char *filename);
uint8_t File_EPT_SaveGaugeWaveData(char *path, char *filename);
uint8_t File_MW_ReadModelWave(char *path, int16_t *buf, uint16_t len, int *modelLen);

uint8_t File_MWave_ReadWave(char* path, uint8_t isDP, uint8_t waveType, int16_t* buf, uint16_t buflen, int *bgn_pt, int *len_pt);
uint8_t File_MWave_ReadPara(char* path, uint8_t *isDP, int *fs, int32_t* zero_ns, float* temperature, float *F, float *tofL1, float *tofS1, BOLT_COEF_T *coef);


uint8_t File_MW_ReadMarkWave(int16_t *buf, uint16_t len);
uint8_t File_MWave_SaveMarkWave(int16_t *buf, uint16_t len);

#endif


