#ifndef __TI_BATTERY_H
#define __TI_BATTERY_H

#include "sys.h"
#include "netconn_udp.h"

//////////////////////////////////////////////////////////////////////////////////	 
// TI Smart Lithium Battery Management Module
// This module handles communication with TI smart lithium batteries
// and transmits battery data via UDP on port 8090
////////////////////////////////////////////////////////////////////////////////// 

// Battery communication interface definitions
#define BATTERY_I2C_ADDR        0x16    // Default TI battery I2C address (7-bit)
#define BATTERY_UPDATE_PERIOD   1000    // Battery data update period in ms

// TI Smart Battery Standard Commands (SMBus compatible)
// Note: These are now defined in smbus_sw.h as SMBUS_*_CMD
// Keeping these for backward compatibility
#define BATTERY_CMD_VOLTAGE     0x09    // Battery voltage
#define BATTERY_CMD_CURRENT     0x0A    // Battery current
#define BATTERY_CMD_TEMP        0x08    // Battery temperature
#define BATTERY_CMD_SOC         0x0D    // State of charge
#define BATTERY_CMD_SOH         0x0E    // State of health (if supported)
#define BATTERY_CMD_CYCLE_COUNT 0x17    // Cycle count
#define BATTERY_CMD_STATUS      0x16    // Battery status
#define BATTERY_CMD_SAFETY      0x51    // Safety status

// Battery module status
typedef enum {
    BATTERY_STATUS_OK = 0,
    BATTERY_STATUS_COMM_ERROR,
    BATTERY_STATUS_DATA_ERROR,
    BATTERY_STATUS_NOT_INITIALIZED
} Battery_Status_t;

// Battery module structure
typedef struct {
    TI_Battery_Data_t current_data;     // Current battery data
    Battery_Status_t status;            // Module status
    uint32_t last_update_time;          // Last update timestamp
    uint8_t comm_error_count;           // Communication error counter
    uint8_t initialized;                // Initialization flag
} Battery_Module_t;

// Function declarations
Battery_Status_t Battery_Init(void);
Battery_Status_t Battery_ReadData(TI_Battery_Data_t *data);
Battery_Status_t Battery_UpdateData(void);
Battery_Status_t Battery_TransmitData(void);
void Battery_Task(void);
TI_Battery_Data_t* Battery_GetCurrentData(void);

// Simulated battery data functions (for testing without actual hardware)
void Battery_SimulateData(TI_Battery_Data_t *data);
void Battery_EnableSimulation(uint8_t enable);

// Battery communication functions
Battery_Status_t Battery_ReadRegister(uint8_t reg, uint16_t *value);
Battery_Status_t Battery_WriteRegister(uint8_t reg, uint16_t value);

// Utility functions
uint32_t Battery_GetTimestamp(void);
void Battery_PrintData(TI_Battery_Data_t *data);

extern Battery_Module_t g_Battery;

#endif /* __TI_BATTERY_H */
