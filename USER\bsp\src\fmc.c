/**
  ******************************************************************************
  * Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com
  * @file 	 : fmc.c
  * @project ：PREMAT_M4
  * <AUTHOR> YL
  * @date    ：2021/11/11
  * @version ：v1.0
  * @brief
  * @history :
  *
  *
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#include "includes.h"
#include "fmc.h" 



SRAM_HandleTypeDef SRAM_Handler;    //SRAM句柄(用于控制LCD)

//配置MPU的region(SRAM区域为透写模式)
static void MPU_Config(void)
{
	MPU_Region_InitTypeDef MPU_Initure;

	HAL_MPU_Disable();							//配置MPU之前先关闭MPU,配置完成以后在使能MPU	
	//外部SRAM为region0，大小为2MB，此区域可读写
	MPU_Initure.Enable = MPU_REGION_ENABLE;	    //使能region
	MPU_Initure.Number = LCD_REGION_NUMBER;		//设置region，外部SRAM使用的region0
	MPU_Initure.BaseAddress = LCD_ADDRESS_START;	//region基地址
	MPU_Initure.Size = LCD_REGION_SIZE;			//region大小
	MPU_Initure.SubRegionDisable = 0X00;
	MPU_Initure.TypeExtField = MPU_TEX_LEVEL0;
	MPU_Initure.AccessPermission = MPU_REGION_FULL_ACCESS;	//此region可读写
	MPU_Initure.DisableExec = MPU_INSTRUCTION_ACCESS_ENABLE;	//允许读取此区域中的指令
	MPU_Initure.IsShareable = MPU_ACCESS_NOT_SHAREABLE;
	MPU_Initure.IsCacheable = MPU_ACCESS_NOT_CACHEABLE;
	MPU_Initure.IsBufferable = MPU_ACCESS_BUFFERABLE;
	HAL_MPU_ConfigRegion(&MPU_Initure);
	HAL_MPU_Enable(MPU_PRIVILEGED_DEFAULT);     //开启MPU
}
//SRAM底层驱动，时钟使能，引脚分配
//此函数会被HAL_SRAM_Init()调用
//hsram:SRAM句柄
void HAL_SRAM_MspInit(SRAM_HandleTypeDef* hsram)
{
	GPIO_InitTypeDef GPIO_Initure;

	__HAL_RCC_FMC_CLK_ENABLE();
	__HAL_RCC_GPIOC_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();
	__HAL_RCC_GPIOE_CLK_ENABLE();
	__HAL_RCC_GPIOF_CLK_ENABLE();

	//使用PC7替代FMC_NE1
	GPIO_Initure.Pin = GPIO_PIN_7;
	GPIO_Initure.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_Initure.Pull = GPIO_PULLUP;
	GPIO_Initure.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOC, &GPIO_Initure);
	FMC_CS(1);

	//FMC_NE1(PD7未连接), FMC_NWE(PD5), FMC_NOE(PD4), FMC_D0~FMC_D7(PD14/PD15/PD0/PD1/PE7~PE10)
	//FMC_D8 ~ FMC_D15 (PE11 ~ PE15 / PD8 ~ PD10)
	//初始化PD0,1,4,5,14,15
	GPIO_Initure.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_14 | GPIO_PIN_15;
	GPIO_Initure.Mode = GPIO_MODE_AF_PP; 		//推挽复用
	GPIO_Initure.Pull = GPIO_PULLUP;			//上拉
	GPIO_Initure.Speed = GPIO_SPEED_FREQ_VERY_HIGH;		//高速
	GPIO_Initure.Alternate = GPIO_AF12_FMC;	//复用为FMC
	HAL_GPIO_Init(GPIOD, &GPIO_Initure);     //初始化

	//初始化PE7,8,9,10
	GPIO_Initure.Pin = GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10;
	HAL_GPIO_Init(GPIOE, &GPIO_Initure);
	
	//FMC_D8 ~ FMC_D15 (PE11 ~ PE15 / PD8 ~ PD10)
	GPIO_Initure.Pin = GPIO_PIN_11 | GPIO_PIN_12 | GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15;
    HAL_GPIO_Init(GPIOE, &GPIO_Initure);
	GPIO_Initure.Pin = GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10;
    HAL_GPIO_Init(GPIOD, &GPIO_Initure);

	//由于FMC_NE1未连接默认PD7, 由FMC-NE1(PC7)替代, 编程时手动设置

}

//uint8_t tstbuf[20];
uint16_t tstregval = 0;
uint16_t test_ReadStateReg();
//初始化lcd
//该初始化函数可以初始化各种型号的LCD(详见本.c文件最前面的描述)
void FPGA_FMC_DataRxTx_Init(void)
{
	GPIO_InitTypeDef GPIO_Initure;
	FMC_NORSRAM_TimingTypeDef FSMC_ReadWriteTim;
	FMC_NORSRAM_TimingTypeDef FSMC_WriteTim;

	MPU_Config();                       								//使能MPU保护LCD区域
	SRAM_Handler.Instance = FMC_NORSRAM_DEVICE;
	SRAM_Handler.Extended = FMC_NORSRAM_EXTENDED_DEVICE;

	SRAM_Handler.Init.NSBank = FMC_NORSRAM_BANK1;     					//使用NE1
	SRAM_Handler.Init.DataAddressMux = FMC_DATA_ADDRESS_MUX_DISABLE; 		//不复用数据线
	SRAM_Handler.Init.MemoryType = FMC_MEMORY_TYPE_SRAM;   				//SRAM
	SRAM_Handler.Init.MemoryDataWidth = FMC_NORSRAM_MEM_BUS_WIDTH_16;
	SRAM_Handler.Init.BurstAccessMode = FMC_BURST_ACCESS_MODE_DISABLE; 	//是否使能突发访问,仅对同步突发存储器有效,此处未用到
	SRAM_Handler.Init.WaitSignalPolarity = FMC_WAIT_SIGNAL_POLARITY_LOW;	//等待信号的极性,仅在突发模式访问下有用
	SRAM_Handler.Init.WaitSignalActive = FMC_WAIT_TIMING_BEFORE_WS;   	//存储器是在等待周期之前的一个时钟周期还是等待周期期间使能NWAIT
	SRAM_Handler.Init.WriteOperation = FMC_WRITE_OPERATION_ENABLE;    	//存储器写使能
	SRAM_Handler.Init.WaitSignal = FMC_WAIT_SIGNAL_DISABLE;           	//等待使能位,此处未用到
	SRAM_Handler.Init.ExtendedMode = FMC_EXTENDED_MODE_ENABLE;        	//读写使用不同的时序
	SRAM_Handler.Init.AsynchronousWait = FMC_ASYNCHRONOUS_WAIT_DISABLE;	//是否使能同步传输模式下的等待信号,此处未用到
	SRAM_Handler.Init.WriteBurst = FMC_WRITE_BURST_DISABLE;          	 	//禁止突发写
	SRAM_Handler.Init.ContinuousClock = FMC_CONTINUOUS_CLOCK_SYNC_ASYNC;

    // FMC读时序控制寄存器
    FSMC_ReadWriteTim.AddressSetupTime = 0; //地址建立时间(ADDSET)
    FSMC_ReadWriteTim.AddressHoldTime = 1; //地址保持时间 1 * 5ns(200MHz)
    FSMC_ReadWriteTim.DataSetupTime = 1; //数据保存时间(DATAST)为 1 * 5ns(200MHz)
    FSMC_ReadWriteTim.AccessMode = FMC_ACCESS_MODE_A; //模式A
//	FSMC_ReadWriteTim.CLKDivision = 2;
//	FSMC_ReadWriteTim.DataLatency = 2;
//	FSMC_ReadWriteTim.BusTurnAroundDuration = 0;
    // FMC写时序控制寄存器
    FSMC_WriteTim.AddressSetupTime = 0; //地址建立时间(ADDSET)为21个HCLK=105ns
    FSMC_WriteTim.AddressHoldTime = 1;
    FSMC_WriteTim.DataSetupTime = 1; //数据保存时间(DATAST)为5ns*21个HCLK=105ns
//	FSMC_WriteTim.CLKDivision = 2;
//	FSMC_WriteTim.DataLatency = 2;
    FSMC_WriteTim.AccessMode = FMC_ACCESS_MODE_A; //模式A

    HAL_SRAM_Init(&SRAM_Handler, &FSMC_ReadWriteTim, &FSMC_WriteTim);
    delay_us(50000); // delay 50 ms

	//	while(1) {
	////		memset(tstbuf, 1, 20);
	//		delay_ms(50); 
	//		tstregval = test_ReadStateReg();
	//		
	////		for(uint32_t i = 0; i < 20; i++) {
	////			tstbuf[i] = *(volatile uint8_t *)FPGA_DATA_BASE;
	////		}
	//		delay_ms(50); 
	//	}

}


uint16_t test_ReadStateReg() {
	uint16_t regval = 0;
	FMC_CS(0);
	*(volatile uint8_t*)FPGA_DATA_BASE = 0x30;
	delay_us(1);
	regval = *(volatile uint8_t*)FPGA_DATA_BASE;
	regval = regval << 8;
	regval |= *(volatile uint8_t*)FPGA_DATA_BASE;
	FMC_CS(1);
	return regval;
}




