#ifndef __AD9106_H
#define __AD9106_H	 
#include "bsp.h"




// DDS 输入时钟
#define CLK_KHZ 150000


#define AD9106_PATTERNTYPE		 0x001F // Register 0x1F,  Data=0x0000(Continuous)								
#define AD9106_DGAIN1			 0x0035 //			//DACx_DGAIN 0x35, Data=0x4000  Very important,the maximum value is 0x4000
#define AD9106_DGAIN2			 0x0034 //
#define AD9106_DGAIN3			 0x0033 //        
#define AD9106_DGAIN4			 0x0032 //
#define AD9106_DOF1				 0x0025 //      //???????,0xXXX0
#define AD9106_DOF2				 0x0024 //
#define AD9106_DOF3				 0x0023 //
#define AD9106_DOF4				 0x0022 //        //	 
#define AD9106_DDS4_PW			 0x0040 //    //Register 0x40, DDS4  degree	  adjust the DDS4 and DDS3 to have the same phase
#define AD9106_DDS3_PW			 0x0041 //
#define AD9106_DDS2_PW			 0x0042 // 0x00421500;
#define AD9106_DDS1_PW			 0x0043 // 0x00430000;
#define AD9106_UPDATE			 0x001D //		          //Register 0x1D, Data=0xE600,???????RAMUPDATE,
#define AD9106_DAC4RSET			 0x0009 //		        //Register 0x09, Data=	,DAC4 inside Rset value.
#define AD9106_DAC3RSET			 0x000A //
#define AD9106_DAC2RSET			 0x000B //
#define AD9106_DAC1RSET			 0x000C //		        //Register 0x0C, Data=	,DAC1 inside Rset value.
#define AD9106_WAV2_1CONFIG		 0x0027 //		    //Register 0x27, Data=0x3131,(wavefrom typ)	DAC2 and DAC1 select the DDS prestore wave form.
#define AD9106_WAV4_3CONFIG		 0x0026 //
#define AD9106_DDSTW32			 0x003E //		      	//Register 0x3E, Data=	,DDS frequency change MSB 0xXXXX
#define AD9106_DDSTW1 			 0x003F //		     		//Register 0x3E, Data=	,DDS frequency change LSB 0xXX00
#define AD9106_WAV2_1PATX 		 0x002B //
#define AD9106_WAV4_3PATX 		 0x002A //	
#define AD9106_PAT_DLY 			 0x0020 //
#define AD9106_PAT_TIMEBASE		 0x0028 //
#define AD9106_PAT_PERIOD		 0x0029 //


#define AD9106_DDS_CONFIG		 0x0045 //
#define AD9106_TW_RAM_CONFIG	 0x0047 //
#define AD9106_PAT_STATUS		 0x001E //

#define AD9106_START_DLY4		 0x0050 //
#define AD9106_START_ADDR4		 0x0051 //
#define AD9106_STOP_ADDR4		 0x0052 //
#define AD9106_DDS_CYC4			 0x0053 //

#define AD9106_START_DLY3		 0x0054 //
#define AD9106_START_ADDR3		 0x0055 //
#define AD9106_STOP_ADDR3		 0x0056 //
#define AD9106_DDS_CYC3			 0x0057 //

#define AD9106_START_DLY2		 0x0058 //
#define AD9106_START_ADDR2		 0x0059 //
#define AD9106_STOP_ADDR2		 0x005A //
#define AD9106_DDS_CYC2			 0x005B //

#define AD9106_START_DLY1		 0x005C //
#define AD9106_START_ADDR1		 0x005D //
#define AD9106_STOP_ADDR1		 0x005E //
#define AD9106_DDS_CYC1			 0x005F //


////带参宏，可以像内联函数一样使用
//#define LED1(a)	if (a)	\
//					GPIO_SetBits(GPIOC,GPIO_Pin_3);\
//					else		\
//					GPIO_ResetBits(GPIOC,GPIO_Pin_3)

//#define LED2(a)	if (a)	\
//					GPIO_SetBits(GPIOC,GPIO_Pin_4);\
//					else		\
//					GPIO_ResetBits(GPIOC,GPIO_Pin_4)

//#define LED3(a)	if (a)	\
//					GPIO_SetBits(GPIOC,GPIO_Pin_11);\
//					else		\
//					GPIO_ResetBits(GPIOC,GPIO_Pin_11)

void AD9106_Init(void);
void WriteToAD9106(unsigned int InstruAndData);
void AD9106_Config(u32 freq);
u32 AD9106_PatternMode(u8 ch, u32 freq, u8 nRep);
u32 AD9106_Chirp(u8 ch, u32 freq_start, u32 freq_stop, u16 nRep, u8 brakePulNum, u8 existBrakeHalfPul, u8 brakeDlyHalfPul, int16_t brakePulFreq_khz);
void DDS_SetPara();
void DDS_SetParaDetail(float freq_MHz, u8 nRep);
void DDS_SetParaChirp(float freq1_MHz, float freq2_MHz, u8 nRep, u8 existHalfRep);
#endif

