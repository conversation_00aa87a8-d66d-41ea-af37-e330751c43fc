#include "netconn_udp.h"
#include "lwip_comm.h"
#include "usart.h"
#include "led.h"
#include "includes.h"
#include "lwip/api.h"
#include "lwip/lwip_sys.h"
#include "string.h"
//////////////////////////////////////////////////////////////////////////////////	 
//本程序只供学习使用，未经作者许可，不得用于其它任何用途
//ALIENTEK STM32F4&F7开发板
//NETCONN API编程方式的UDP测试代码	   
//正点原子@ALIENTEK
//技术论坛:www.openedv.com
//创建日期:2016/8/5
//版本：V1.0
//版权所有，盗版必究。
//Copyright(C) 广州市星翼电子科技有限公司 2009-2019
//All rights reserved									  
//*******************************************************************************
//修改信息
//无
////////////////////////////////////////////////////////////////////////////////// 	   
 
//UDP任务
#define UDP_PRIO		6
//任务堆栈大小
#define UDP_STK_SIZE	300
//任务控制块
OS_TCB	UdpTaskTCB;
//任务堆栈
CPU_STK UDP_TASK_STK[UDP_STK_SIZE];


u8 udp_demo_recvbuf[UDP_DEMO_RX_BUFSIZE];	//UDP接收数据缓冲区
//UDP发送数据内容
static char udp_demo_sendbuf[100];
u8 udp_flag;							//UDP数据发送标志位


//UDP电池数据任务
#define UDP_BATTERY_PRIO		7
//任务堆栈大小
#define UDP_BATTERY_STK_SIZE	300
//任务控制块
OS_TCB	UdpBatteryTaskTCB;
//任务堆栈
CPU_STK UDP_BATTERY_TASK_STK[UDP_BATTERY_STK_SIZE];
// Battery UDP variables
u8 udp_battery_flag;					//UDP电池数据发送标志位
static u8 udp_battery_connected = 0;
struct netconn *pUdpBatteryConn;

static u8 udp_conneted = 0;
struct netconn *pUdpConn;
uint8_t udp_senddata(uint8_t *buf, uint16_t len)  {
	err_t err;
	static struct netbuf  *sentbuf;
	char str[30];
//	LED1_Toggle;
	if(udp_conneted) {
		
		sentbuf = netbuf_new();
		netbuf_alloc(sentbuf, len);
		memcpy(sentbuf->p->payload, buf, len);
//		LED1(1);
		err = (OS_ERR)netconn_send(pUdpConn,sentbuf);  	//将netbuf中的数据发送出去
//		LED1(0);
		if(err != ERR_OK)
		{
//			printf("发送失败\r\n");
			sprintf(str, "send failed!%d", err);
//			OLED_DispStringInLine(str, 1, GUI_TA_CENTER);
			netbuf_delete(sentbuf);      //删除buf
			return 2;
		}

		netbuf_delete(sentbuf);     
		return 0;
	}
	else {
//		OLED_DispStringInLine("send failed! no connect!", 1, GUI_TA_CENTER);
		return 1;
	}
}

// Battery data send function
uint8_t udp_send_battery_data(TI_Battery_Data_t *battery_data) {
	err_t err;
	static struct netbuf *sentbuf;
	char str[30];

	if(udp_battery_connected) {
		sentbuf = netbuf_new();
		netbuf_alloc(sentbuf, sizeof(TI_Battery_Data_t));
		memcpy(sentbuf->p->payload, battery_data, sizeof(TI_Battery_Data_t));

		err = (OS_ERR)netconn_send(pUdpBatteryConn, sentbuf);
		if(err != ERR_OK) {
			sprintf(str, "battery send failed!%d", err);
			netbuf_delete(sentbuf);
			return 2;
		}

		netbuf_delete(sentbuf);
		return 0;
	}
	else {
		return 1; // Not connected
	}
}

int tstRxCmdCnt = 0;
//udp任务函数
static void udp_thread(void *arg)
{
	OS_ERR err;
	CPU_SR_ALLOC();
	
	static struct netconn *udpconn;
	static struct netbuf  *recvbuf;
	static struct netbuf  *sentbuf;
	struct ip_addr destipaddr;
	u32 data_len = 0;
	struct pbuf *q;
	static ip_addr_t ipaddr;
	static u16_t 			port;
	u8 addr[4];
	char str[52];
	
	LWIP_UNUSED_ARG(arg);
	udpconn = netconn_new(NETCONN_UDP);  //创建一个UDP链接
	udpconn->recv_timeout = 10;  		
	
	if(udpconn != NULL)  //创建UDP连接成功
	{
		err = (OS_ERR)netconn_bind(udpconn,IP_ADDR_ANY,UDP_DEMO_PORT); 
		IP4_ADDR(&destipaddr,lwipdev.remoteip[0],lwipdev.remoteip[1], lwipdev.remoteip[2],lwipdev.remoteip[3]); //构造目的IP地址
        netconn_connect(udpconn,&destipaddr,UDP_DEMO_PORT); 	//连接到远端主机
		udp_conneted = 0;
		if(err == ERR_OK)//绑定完成
		{
			pUdpConn = udpconn;
			udp_conneted = 1;
			
			//local ip port
			netconn_getaddr(udpconn,&ipaddr,&port, 1); //获取远端IP地址和端口号
			addr[3] = lwipdev.ip[3];//(uint8_t)(ipaddr.addr >> 24); 
			addr[2] = lwipdev.ip[2];//(uint8_t)(ipaddr.addr >> 16);
			addr[1] = lwipdev.ip[1];//(uint8_t)(ipaddr.addr >> 8);
			addr[0] = lwipdev.ip[0];//(uint8_t)(ipaddr.addr);
//			printf("主机%d.%d.%d.%d连接上服务器,主机端口号为:%d\r\n",remot_addr[0], remot_addr[1],remot_addr[2],remot_addr[3],port);
			sprintf(str, "local  ip: %d.%d.%d.%d (%4d)", addr[0], addr[1], addr[2], addr[3], port);//remote ip port
//			OLED_DispStringInLine(str, 3, GUI_TA_LEFT);
			
			//remote ip port
			netconn_getaddr(udpconn,&ipaddr,&port,0);
			addr[3] = (uint8_t)(ipaddr.addr >> 24); 
			addr[2] = (uint8_t)(ipaddr.addr >> 16);
			addr[1] = (uint8_t)(ipaddr.addr >> 8);
			addr[0] = (uint8_t)(ipaddr.addr);
			sprintf(str, "remote ip: %d.%d.%d.%d (%4d)", addr[0], addr[1], addr[2], addr[3], port);
//			OLED_DispStringInLine(str, 4, GUI_TA_LEFT);
			
			while(1)
			{
				if((udp_flag & LWIP_SEND_DATA) == LWIP_SEND_DATA) //有数据要发送
				{
//					sprintf(udp_demo_sendbuf, "UDP RECV Cmd Cnt = %d \r\n", tstRxCmdCnt);
//					
//					sentbuf = netbuf_new();
//					netbuf_alloc(sentbuf,strlen((char *)udp_demo_sendbuf));
//					memcpy(sentbuf->p->payload,(void*)udp_demo_sendbuf,strlen((char*)udp_demo_sendbuf));
//					err = (OS_ERR)netconn_send(udpconn,sentbuf);  	//将netbuf中的数据发送出去
//					if(err != ERR_OK)
//					{
//						printf("发送失败\r\n");
//						netbuf_delete(sentbuf);      //删除buf
//					}
//					udp_flag &= ~LWIP_SEND_DATA;	//清除数据发送标志
//					netbuf_delete(sentbuf);      	//删除buf
				}	
				
				netconn_recv(udpconn,&recvbuf); //接收数据
				if(recvbuf != NULL)          	//接收到数据
				{ 
					OS_CRITICAL_ENTER();		//进入临界区
					memset(udp_demo_recvbuf,0,UDP_DEMO_RX_BUFSIZE);  //数据接收缓冲区清零
					for(q=recvbuf->p;q!=NULL;q=q->next)  //遍历完整个pbuf链表
					{
						//判断要拷贝到UDP_DEMO_RX_BUFSIZE中的数据是否大于UDP_DEMO_RX_BUFSIZE的剩余空间，如果大于
						//的话就只拷贝UDP_DEMO_RX_BUFSIZE中剩余长度的数据，否则的话就拷贝所有的数据
						if(q->len > (UDP_DEMO_RX_BUFSIZE-data_len)) memcpy(udp_demo_recvbuf+data_len,q->payload,(UDP_DEMO_RX_BUFSIZE-data_len));//拷贝数据
						else memcpy(udp_demo_recvbuf+data_len,q->payload,q->len);
						data_len += q->len;  	
						if(data_len > UDP_DEMO_RX_BUFSIZE) break; //超出TCP客户端接收数组,跳出	
					}
					OS_CRITICAL_EXIT();					//退出临界区
					
					Comm_RxOneFrameDone(udp_demo_recvbuf, data_len, COM_NET); //转存收到的数据
					data_len=0;  //复制完成后data_len要清零。
//					printf("%s\r\n",udp_demo_recvbuf);  //打印接收到的数据
					netbuf_delete(recvbuf);      		//删除buf
					tstRxCmdCnt++;
//					udp_flag |= LWIP_SEND_DATA; //标记LWIP有数据要发送
				}else OSTimeDlyHMSM(0,0,0,5,OS_OPT_TIME_HMSM_STRICT,&err); //延时5ms
			}
		}else printf("UDP绑定失败\r\n");
	}else printf("UDP连接创建失败\r\n");
}

//创建UDP线程
//返回值:0 UDP创建成功
//		其他 UDP创建失败
u8 udp_demo_init(void)
{
	OS_ERR err;
	CPU_SR_ALLOC();
	
	OS_CRITICAL_ENTER();//进入临界区
	//创建UDP任务
	OSTaskCreate((OS_TCB 	* )&UdpTaskTCB,		
				 (CPU_CHAR	* )"udp task", 		
                 (OS_TASK_PTR )udp_thread, 			
                 (void		* )0,					
                 (OS_PRIO	  )UDP_PRIO,     
                 (CPU_STK   * )&UDP_TASK_STK[0],	
                 (CPU_STK_SIZE)UDP_STK_SIZE/10,	
                 (CPU_STK_SIZE)UDP_STK_SIZE,		
                 (OS_MSG_QTY  )0,
                 (OS_TICK	  )0,
                 (void   	* )0,
                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
                 (OS_ERR 	* )&err);
	OS_CRITICAL_EXIT();	//退出临界区
	return err;
}

// Battery UDP thread function
static void udp_battery_thread(void *arg)
{
	OS_ERR err;
	CPU_SR_ALLOC();

	static struct netconn *udp_battery_conn;
	struct ip_addr destipaddr;
	static ip_addr_t ipaddr;
	static u16_t port;
	u8 addr[4];
	char str[52];

	LWIP_UNUSED_ARG(arg);
	udp_battery_conn = netconn_new(NETCONN_UDP);  //创建一个UDP链接
	udp_battery_conn->recv_timeout = 10;

	if(udp_battery_conn != NULL)  //创建UDP连接成功
	{
		err = (OS_ERR)netconn_bind(udp_battery_conn, IP_ADDR_ANY, UDP_BATTERY_PORT);
		IP4_ADDR(&destipaddr, lwipdev.remoteip[0], lwipdev.remoteip[1], lwipdev.remoteip[2], lwipdev.remoteip[3]); //构造目的IP地址
		netconn_connect(udp_battery_conn, &destipaddr, UDP_BATTERY_PORT); 	//连接到远端主机
		udp_battery_connected = 0;

		if(err == ERR_OK)//绑定完成
		{
			pUdpBatteryConn = udp_battery_conn;
			udp_battery_connected = 1;

			//local ip port
			netconn_getaddr(udp_battery_conn, &ipaddr, &port, 1); //获取本地IP地址和端口号
			addr[3] = lwipdev.ip[3];
			addr[2] = lwipdev.ip[2];
			addr[1] = lwipdev.ip[1];
			addr[0] = lwipdev.ip[0];
			sprintf(str, "battery local  ip: %d.%d.%d.%d (%4d)", addr[0], addr[1], addr[2], addr[3], port);

			//remote ip port
			netconn_getaddr(udp_battery_conn, &ipaddr, &port, 0);
			addr[3] = (uint8_t)(ipaddr.addr >> 24);
			addr[2] = (uint8_t)(ipaddr.addr >> 16);
			addr[1] = (uint8_t)(ipaddr.addr >> 8);
			addr[0] = (uint8_t)(ipaddr.addr);
			sprintf(str, "battery remote ip: %d.%d.%d.%d (%4d)", addr[0], addr[1], addr[2], addr[3], port);

			// Battery UDP thread main loop - only for sending data
			while(1)
			{
				// Battery data is sent via udp_send_battery_data() function
				// This thread just maintains the connection
				OSTimeDlyHMSM(0,0,1,0,OS_OPT_TIME_HMSM_STRICT,&err); //延时1秒
			}
		}
		else printf("Battery UDP绑定失败\r\n");
	}
	else printf("Battery UDP连接创建失败\r\n");
}

//创建Battery UDP线程
//返回值:0 UDP创建成功
//		其他 UDP创建失败
u8 udp_battery_init(void)
{
	OS_ERR err;
	CPU_SR_ALLOC();

	OS_CRITICAL_ENTER();//进入临界区
	//创建Battery UDP任务
	OSTaskCreate((OS_TCB 	* )&UdpBatteryTaskTCB,
				 (CPU_CHAR	* )"udp battery task",
                 (OS_TASK_PTR )udp_battery_thread,
                 (void		* )0,
                 (OS_PRIO	  )UDP_BATTERY_PRIO,
                 (CPU_STK   * )&UDP_BATTERY_TASK_STK[0],
                 (CPU_STK_SIZE)UDP_BATTERY_STK_SIZE/10,
                 (CPU_STK_SIZE)UDP_BATTERY_STK_SIZE,
                 (OS_MSG_QTY  )0,
                 (OS_TICK	  )0,
                 (void   	* )0,
                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
                 (OS_ERR 	* )&err);
	OS_CRITICAL_EXIT();	//退出临界区
	return err;
}

