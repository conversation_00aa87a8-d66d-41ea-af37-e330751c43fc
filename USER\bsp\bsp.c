/*
*********************************************************************************************************
* @file bsp.c
* @project HSACUT_V2024
* <AUTHOR>
* @date    2021/06/03
* @version v1.0
* @brief
* @modify
*
* Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com
*********************************************************************************************************
*/
#include "includes.h"
#include "bsp.h"

/**
  * @brief  板级支持包初始化
  * @date   2021-06-03
  * @param  None
  * @retval None
  */
void Bsp_Init(void) {
	//	SCB->VTOR = FLASH_BASE | 0x10000;//设置偏移量
	//	CPU_SR_ALLOC();
	//	Write_Through(); //Cahce强制透写
	//	MPU_Memory_Protection(); //保护相关存储区域
	//	Cache_Enable(); //打开L1-Cache
	HAL_Init(); //初始化HAL库
	Stm32_Clock_Init(432, 25, 2, 9); //设置时钟,216Mhz 
	delay_init(216); //延时初始化

	//用于指定重复次数内触发的定时器
	TIM2_Init(100 - 1, 108 - 1);
	//用于Modbus 停止字符检测的定时器
	TIM3_Init(303 - 1, 108 - 1); //定时器3初始化，定时器时钟为108M，分频系数为10800-1

	//串口初始化
	Uart1_Init(115200); //RS485
	Uart7_Init(115200); //与FPGA通讯串口初始化

	LED_Init();
	Fan_Init();
	DAC1_Init();

	Dev_GuardInit();

	AT24CXX_Init();
	if (AT24CXX_Check())//检测不到
	{
		//错误
		Dev_SetFaultCode(FAULT_HARDWARE);
	}

	// Emat_Init();
	AD9246_Init();
	FPGA_IO_Init();
	SGM5374_Init();
	AFE_init();
	// AD9704_Init();

	//DDS配置
	AD9106_Init();

	// RTC_RX8010_Init();

	
	my_mem_init(SRAMIN); //初始化内部内存池
	//	my_mem_init(SRAMEX); //初始化外部内存池
	my_mem_init(SRAMDTCM); //初始化DTCM内存池
}


/*************************** End of file ****************************/
