
#include "includes.h"


#define CH12 (1 << 0)
#define CH34 (1 << 1)

uint16_t phaseOffset_tst = 0;
static unsigned int InstruAndData_PatternType		= 0x001F0000; //Register 0x1F,  Data=0x0000(Continuous)	

static unsigned int InstruAndData_DGAIN1			= 0x00354000; //DACx_DGAIN 0x35, Data=0x4000  Very important,the maximum value is 0x4000
static unsigned int InstruAndData_DGAIN2			= 0x00344000;
static unsigned int InstruAndData_DGAIN3			= 0x00333000;			        
static unsigned int InstruAndData_DGAIN4			= 0x00323000;
static unsigned int InstruAndData_DOF1				= 0x00250000; //???????,0xXXX0
static unsigned int InstruAndData_DOF2				= 0x00240000;
static unsigned int InstruAndData_DOF3				= 0x00230000;
static unsigned int InstruAndData_DOF4				= 0x00220000; //	 
	
static unsigned int InstruAndData_DDSMSB			= 0x003E0E38; //Register 0x3E, Data=0x0E38
static unsigned int InstruAndData_DDSLSB			= 0x003FE600; //Register 0x3F, Data=0xE600	   10MHz output,180MHz fsys
static unsigned int InstruAndData_DDS4_PW			= 0x00401500; //Register 0x40, DDS4  degree	  adjust the DDS4 and DDS3 to have the same phase
static unsigned int InstruAndData_DDS3_PW			= 0x00410000;
static unsigned int InstruAndData_DDS2_PW			= 0x00421500; //0x00421500;
static unsigned int InstruAndData_DDS1_PW			= 0x00430000; //0x00430000;
static unsigned int InstruAndData_Run				= 0x001E0001; //PAT_STATUS 0x1E, run bit=1,????????	   Very important
static unsigned int InstruAndData_update			= 0x001D0001; //Register 0x1D, Data=0xE600,???????RAMUPDATE,
static unsigned int InstruAndData_DAC4RSET			= 0x0009800A; //Register 0x09, Data=	,DAC4 inside Rset value.
static unsigned int InstruAndData_DAC3RSET			= 0x000A800A;
static unsigned int InstruAndData_DAC2RSET			= 0x000B800A;
static unsigned int InstruAndData_DAC1RSET			= 0x000C800A; //Register 0x0C, Data=	,DAC1 inside Rset value.
static unsigned int InstruAndData_WAV2_1CONFIG		= 0x00273131; //Register 0x27, Data=0x3131,(wavefrom typ)	DAC2 and DAC1 select the DDS prestore wave form.
static unsigned int InstruAndData_WAV4_3CONFIG		= 0x00263131;
static unsigned int InstruAndData_DDSTW32			= 0x003E0CCC; //Register 0x3E, Data=	,DDS frequency change MSB 0xXXXX
static unsigned int InstruAndData_DDSTW1	 		= 0x003FCC00; //Register 0x3E, Data=	,DDS frequency change LSB 0xXX00

static unsigned int InstruAndData_WAV2_1PATx 		= 0x002B0010;
static unsigned int InstruAndData_WAV4_3PATx 		= 0x002A0001;	
static unsigned int InstruAndData_PAT_DLY 			= 0x00200005;
static unsigned int InstruAndData_PAT_TIMEBASE		= 0x00280111;
static unsigned int InstruAndData_PAT_PERIOD		= 0x00290010;
static unsigned int InstruAndData_START_DLY1		= 0x005C0164;
static unsigned int InstruAndData_START_ADDR1		= 0x005D0000;
static unsigned int InstruAndData_STOP_ADDR1		= 0x005E0100;
static unsigned int InstruAndData_DDS_CYC1			= 0x005F0002;
static unsigned int InstruAndData_START_DLY2		= 0x00580164;
static unsigned int InstruAndData_START_ADDR2		= 0x00590000;
static unsigned int InstruAndData_STOP_ADDR2		= 0x005A0100;
static unsigned int InstruAndData_DDS_CYC2			= 0x005B0002;
static unsigned int InstruAndData_DDS_CONFIG		= 0x00450001;
static unsigned int InstruAndData_TW_RAM_CONFIG		= 0x0047000C;
static unsigned int InstruAndData_PAT_STATUS		= 0x001E0002;
static unsigned int InstruAndData_DDS_CYC4			= 0x00530002;
static unsigned int InstruAndData_START_ADDR4		= 0x00510000;
static unsigned int InstruAndData_STOP_ADDR4		= 0x00520100;
static unsigned int InstruAndData_DDS_CYC3			= 0x00570002;
static unsigned int InstruAndData_START_ADDR3		= 0x00550000;
static unsigned int InstruAndData_STOP_ADDR3		= 0x00560100;

// CS(PC11),SDO(PC12),SDIO(PD2),SCLK(PD3),RST(PC10)
#define DDS_RST(n)		(n?HAL_GPIO_WritePin(GPIOC,GPIO_PIN_10,GPIO_PIN_SET)	:HAL_GPIO_WritePin(GPIOC,GPIO_PIN_10,GPIO_PIN_RESET))
#define DDS_CS(n)		(n?HAL_GPIO_WritePin(GPIOC,GPIO_PIN_11,GPIO_PIN_SET)	:HAL_GPIO_WritePin(GPIOC,GPIO_PIN_11,GPIO_PIN_RESET))
#define DDS_SDO(n)		(n?HAL_GPIO_WritePin(GPIOC,GPIO_PIN_12,GPIO_PIN_SET)	:HAL_GPIO_WritePin(GPIOC,GPIO_PIN_12,GPIO_PIN_RESET))
#define DDS_SDIO(n)		(n?HAL_GPIO_WritePin(GPIOD,GPIO_PIN_2,GPIO_PIN_SET)		:HAL_GPIO_WritePin(GPIOD,GPIO_PIN_2,GPIO_PIN_RESET))
#define DDS_SCLK(n)		(n?HAL_GPIO_WritePin(GPIOD,GPIO_PIN_3,GPIO_PIN_SET)		:HAL_GPIO_WritePin(GPIOD,GPIO_PIN_3,GPIO_PIN_RESET))

u8 ChirpModel_Calc();
/*
 * 函数名：AD9106_Init
 * 描述  ：CS(PC11),SDO(PC12),SDIO(PD2),SCLK(PD3),RST(PC10)
 * 输入  ：无
 * 输出  ：无
 */
void AD9106_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStruct;
	__HAL_RCC_GPIOC_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();

	GPIO_InitStruct.Pin = GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

	GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;
	HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

	DDS_CS(1);
	ChirpModel_Calc();

	AD9106_PatternMode(CH12, 4000000, 3);
}


void WriteToAD9106(unsigned int InstruAndData)
{
	unsigned int SendValue = 0;
	unsigned int i = 0;

	SendValue = InstruAndData;
	
	DDS_CS(0);	                            //bring CS low
	delay_us(2);
	DDS_SCLK(0);
	delay_us(1);
	for(i=0;i<32;i++)
	{
		if(SendValue&0x80000000){
			DDS_SDIO(1);	//Send 1 to SDIO pin
		}else{
			DDS_SDIO(0);	//Send 0 to SDIO pin
		}
		delay_us(1);		     
		DDS_SCLK(1);	//SCLK Rising
		delay_us(2);
		DDS_SCLK(0);	//SCLK falling
		delay_us(1);
		SendValue <<= 1;	//Rotate data
	}
	DDS_CS(1);	                     		//bring CS high again
	DDS_SDIO(0);	//!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!避免串口接收问题
	delay_us(5);
}



static void WriteAD9106(uint16_t addr, uint16_t val)
{
	uint32_t SendValue;
	uint32_t i;
	
	#if SYSTEM_SUPPORT_OS //使用OS
	CPU_SR_ALLOC();  		//定义局部变量
	OS_CRITICAL_ENTER(); 	//进入任务保护
	#endif

	SendValue = (((uint32_t)addr << 16) & 0xffff0000) | ((uint32_t)val & 0x0000ffff);
	
	DDS_CS(0);	                            //bring CS low
	delay_us(2);
	DDS_SCLK(0);
	delay_us(1);
	for(i = 0; i < 32; i++)
	{
		if(SendValue & 0x80000000){
			DDS_SDIO(1);	//Send 1 to SDIO pin
		}else{
			DDS_SDIO(0);	//Send 0 to SDIO pin
		}
		delay_us(1);		     
		DDS_SCLK(1);	//SCLK Rising
		delay_us(2);
		DDS_SCLK(0);	//SCLK falling
		delay_us(1);
		SendValue <<= 1;	//Rotate data
	}
	DDS_CS(1);	                     		//bring CS high again
	DDS_SDIO(0);	//!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!避免串口接收问题
	delay_us(5);
	
	#if SYSTEM_SUPPORT_OS //使用OS
	OS_CRITICAL_EXIT(); 	//退出任务保护
	#endif
}


void AD9106_Config(u32 freq)
{
//	unsigned int InstruAndData_PatternType=0x001F0000;	      //Register 0x1F,  Data=0x0000(Continuous)	
//								
//	unsigned int InstruAndData_DGAIN1=	0x00353000;						//DACx_DGAIN 0x35, Data=0x4000  Very important,the maximum value is 0x4000
//	unsigned int InstruAndData_DGAIN2=	0x00343000;
//	unsigned int InstruAndData_DGAIN3=	0x00333000;			        
//	unsigned int InstruAndData_DGAIN4=	0x00323000;
//	unsigned int InstruAndData_DOF1=		0x00250000;		          //???????,0xXXX0
//	unsigned int InstruAndData_DOF2=		0x00240000;
//	unsigned int InstruAndData_DOF3=		0x00230000;
//	unsigned int InstruAndData_DOF4=		0x00220000;			        //	 

//	unsigned int InstruAndData_DDSMSB=	0x003E0E38;		          //Register 0x3E, Data=0x0E38
//	unsigned int InstruAndData_DDSLSB=	0x003FE600;		          //Register 0x3F, Data=0xE600	   10MHz output,180MHz fsys
//	unsigned int InstruAndData_DDS4_PW=	0x00401300;		        //Register 0x40, DDS4  degree	  adjust the DDS4 and DDS3 to have the same phase
//	unsigned int InstruAndData_DDS3_PW=	0x00411300;
//	unsigned int InstruAndData_DDS2_PW=	0x00420f00;
//	unsigned int InstruAndData_DDS1_PW=	0x00430000;
//	unsigned int InstruAndData_Run=			0x001E0001;                //PAT_STATUS 0x1E, run bit=1,????????	   Very important
//	unsigned int InstruAndData_update=	0x001D0001;		          //Register 0x1D, Data=0xE600,???????RAMUPDATE,
//	unsigned int InstruAndData_DAC4RSET=0x0009800A;		        //Register 0x09, Data=	,DAC4 inside Rset value.
//	unsigned int InstruAndData_DAC3RSET=0x000A800A;
//	unsigned int InstruAndData_DAC2RSET=0x000B800A;
//	unsigned int InstruAndData_DAC1RSET=0x000C800A;		        //Register 0x0C, Data=	,DAC1 inside Rset value.
//	unsigned int InstruAndData_WAV2_1CONFIG=0x00273131;		    //Register 0x27, Data=0x3131,(wavefrom typ)	DAC2 and DAC1 select the DDS prestore wave form.
//	unsigned int InstruAndData_WAV4_3CONFIG=0x00263131;
//	unsigned int InstruAndData_DDSCYC1=	0x005F0fff;		      	//Register 0x5F, Data=
//	unsigned int InstruAndData_DDSTW32=	0x003E0CCC;		      	//Register 0x3E, Data=	,DDS frequency change MSB 0xXXXX
//	unsigned int InstruAndData_DDSTW1 =	0x003FCC00;		     		//Register 0x3E, Data=	,DDS frequency change LSB 0xXX00

	InstruAndData_DDSTW32 = InstruAndData_DDSTW32&0xFFFF0000;
	InstruAndData_DDSTW32 = InstruAndData_DDSTW32 | ((((unsigned int)(freq*11.18481))&0x00FFFF00)>>8);
	InstruAndData_DDSTW1 = InstruAndData_DDSTW1&0xFFFF0000;
	InstruAndData_DDSTW1 = InstruAndData_DDSTW1 | ((((unsigned int)(freq*11.18481))&0x000000FF)<<8);
	
	AD9106_Init();
	DDS_RST(0);
	delay_us(500);
	DDS_RST(1);
	delay_us(500);
//	DDS_TRIGGER = 1; 
	delay_us(50);
	WriteToAD9106(InstruAndData_PatternType);
	WriteToAD9106(InstruAndData_WAV2_1CONFIG);
//	WriteToAD9106(InstruAndData_WAV4_3CONFIG);
//	WriteToAD9106(InstruAndData_DGAIN4); 
//	WriteToAD9106(InstruAndData_DGAIN3);
	WriteToAD9106(InstruAndData_DGAIN2); 
	WriteToAD9106(InstruAndData_DGAIN1); 
//	WriteToAD9106(InstruAndData_DDSMSB);
//	WriteToAD9106(InstruAndData_DDSLSB);
	WriteToAD9106(InstruAndData_DAC1RSET);
	WriteToAD9106(InstruAndData_DAC2RSET);
//	WriteToAD9106(InstruAndData_DAC3RSET);
//	WriteToAD9106(InstruAndData_DAC4RSET);
	WriteToAD9106(InstruAndData_DOF1);
	WriteToAD9106(InstruAndData_DOF2);
//	WriteToAD9106(InstruAndData_DOF3);
//	WriteToAD9106(InstruAndData_DOF4);
	WriteToAD9106(InstruAndData_DDSTW32);
	WriteToAD9106(InstruAndData_DDSTW1);

	WriteToAD9106(InstruAndData_DDS1_PW);
	WriteToAD9106(InstruAndData_DDS2_PW);
//	WriteToAD9106(InstruAndData_DDS3_PW);
//	WriteToAD9106(InstruAndData_DDS4_PW);

	WriteToAD9106(InstruAndData_update);
	delay_us(20);
	WriteToAD9106(InstruAndData_Run);
	delay_us(20);
	WriteToAD9106(InstruAndData_update);
	delay_us(20);
//	DDS_TRIGGER = 0;  
	WriteToAD9106(InstruAndData_update); 
}

// DDS 普通模式
// freq单位 Hz
u32 AD9106_PatternMode(u8 ch, u32 freq, u8 nRep)
{
	u32 need_time;
	u32 PAT_period_tmp=0;
	InstruAndData_DDSTW32 = InstruAndData_DDSTW32&0xFFFF0000;
	InstruAndData_DDSTW32 = InstruAndData_DDSTW32 | ((((unsigned int)(freq/100.0*11.18481))&0x00FFFF00)>>8);//150MHz 111.8481
	InstruAndData_DDSTW1 = InstruAndData_DDSTW1&0xFFFF0000;
	InstruAndData_DDSTW1 = InstruAndData_DDSTW1 | ((((unsigned int)(freq/100.0*11.18481))&0x000000FF)<<8);//180MHz  93.206756(f单位KHz)

	DDS_RST(0);
	delay_us(100);
	DDS_RST(1);
	delay_us(100);
	InstruAndData_PatternType=0x001F0001;
	WriteToAD9106(InstruAndData_PatternType);//模式重复DAC4_3PATx和DAC2_1PATx所定义的次数
	if(ch & CH12) {
		InstruAndData_WAV2_1PATx = 0x002B0101;
		WriteToAD9106(InstruAndData_WAV2_1PATx);
		InstruAndData_WAV2_1CONFIG=0x00273232;
		WriteToAD9106(InstruAndData_WAV2_1CONFIG);//选择波形源
	}
	else if(ch & CH34) {
		InstruAndData_WAV4_3PATx = 0x002A0101;
		WriteToAD9106(InstruAndData_WAV4_3PATx);
		InstruAndData_WAV4_3CONFIG = 0x00263232;
		WriteToAD9106(InstruAndData_WAV4_3CONFIG);//选择波形源
	}
	InstruAndData_DDS_CONFIG = 0x00450000;
	WriteToAD9106(InstruAndData_DDS_CONFIG);

	if(ch & CH12) {
		WriteToAD9106(InstruAndData_DGAIN2); 
		WriteToAD9106(InstruAndData_DGAIN1);
		WriteToAD9106(InstruAndData_DAC1RSET);
		WriteToAD9106(InstruAndData_DAC2RSET);
	}
	else if(ch & CH34) {
		WriteToAD9106(InstruAndData_DGAIN4); 
		WriteToAD9106(InstruAndData_DGAIN3);
		WriteToAD9106(InstruAndData_DAC4RSET);
		WriteToAD9106(InstruAndData_DAC3RSET);
	}

	WriteToAD9106(InstruAndData_DDSTW32);
	WriteToAD9106(InstruAndData_DDSTW1);
	if(ch & CH12) {
		InstruAndData_DDS2_PW=	0x00420000;
		InstruAndData_DDS1_PW=	0x00430000+ 0x7333;
		WriteToAD9106(InstruAndData_DDS1_PW);
		WriteToAD9106(InstruAndData_DDS2_PW);
	}
	else if(ch & CH34) {
		InstruAndData_DDS4_PW=	0x00400000;//0x00400000+phaseOffset_tst;
		InstruAndData_DDS3_PW=	0x00410000 + 0x7333;
		WriteToAD9106(InstruAndData_DDS4_PW);
		WriteToAD9106(InstruAndData_DDS3_PW);
	}

	InstruAndData_PAT_DLY =	0x0020000E;
	WriteToAD9106(InstruAndData_PAT_DLY);//触发开始至真正模式开始延迟时间
	PAT_period_tmp = (CLK_KHZ/6)*10*(nRep+5)/(freq/100.0);//freq的单位为Hz
	PAT_period_tmp = (PAT_period_tmp>=0xffff) ? 0xffff : PAT_period_tmp;
	InstruAndData_PAT_TIMEBASE=0x00280161;
	InstruAndData_PAT_PERIOD=0x00290000 + (PAT_period_tmp&0x0000FFFF);
	need_time = (PAT_period_tmp*6/150) + 100;//us
	WriteToAD9106(InstruAndData_PAT_TIMEBASE);
	WriteToAD9106(InstruAndData_PAT_PERIOD);

//	InstruAndData_START_DLY1=	0x005C0064;
	if(ch & CH12) {
		InstruAndData_DDS_CYC1 		= 0x005F0003 + nRep;
		WriteToAD9106(InstruAndData_DDS_CYC1);
		InstruAndData_DDS_CYC2    	= 0x005B0003 + nRep;
		WriteToAD9106(InstruAndData_DDS_CYC2);
	}
	else if(ch & CH34) {
		InstruAndData_DDS_CYC4 		= 0x00530003 + nRep;
		WriteToAD9106(InstruAndData_DDS_CYC4);
		InstruAndData_DDS_CYC3    	= 0x00570003 + nRep;
		WriteToAD9106(InstruAndData_DDS_CYC3);
	}
	WriteToAD9106(InstruAndData_update);
	delay_us(50);
	WriteToAD9106(InstruAndData_Run);//允许产生模式

	return need_time;
}



//freq_start单位Hz, freq_stop单位Hz, nRep为实际波头的两倍
u32 AD9106_Chirp(u8 ch, u32 freq_start, u32 freq_stop, u16 nRep, u8 brakePulNum, u8 existBrakeHalfPul, u8 brakeDlyHalfPul, int16_t brakePulFreq_khz)//单位Hz
{
	u32 need_time;
	u32 PAT_period_tmp=0;
	u32 freq_max, freq_min;
	u32 DDSTW_min, DDSTW_max, DDSTW_brake;
	u32 DDSTW_step;
	u32 Xor_tmp,XorSame_bit;
	u8 tw_ram_config;
	u32 DDS1TW_val;
	u32 write_ram = 0x60000000;
	u32 ram_addr = 0x60000000;
	u32 ram_val = 0x00000000;
	u32 i = 0;
	float f_div_2e24;
	u32 ram_data_start;
	uint8_t pulNum_brake;
	uint32_t brake_hz;
	pulNum_brake = brakePulNum*2 + existBrakeHalfPul + brakeDlyHalfPul;
	brake_hz = brakePulFreq_khz*1000*2;

	freq_max = (freq_start > freq_stop) ? freq_start : freq_stop;
	freq_min = (freq_start > freq_stop) ? freq_stop : freq_start;
	f_div_2e24 = 0.1118481067;//8.9406967163;//
	DDSTW_max = freq_max * f_div_2e24;//0.1118481067;// 0.1118481067 = 2^24/150M
	DDSTW_min = freq_min * f_div_2e24;//0.1118481067;
	DDSTW_step = (DDSTW_max - DDSTW_min)/(nRep-1);
	Xor_tmp = DDSTW_max ^ DDSTW_min;
	
	if(pulNum_brake != 0) {
		DDSTW_brake = brake_hz * f_div_2e24;
		if(DDSTW_brake > DDSTW_max) {
			Xor_tmp = DDSTW_brake ^ DDSTW_min;
		}
		else if(DDSTW_brake < DDSTW_min) {
			Xor_tmp = DDSTW_max ^ DDSTW_brake;
		}
		else {
			Xor_tmp = DDSTW_max ^ DDSTW_brake;
		}
	}
	
	for(i=0;i<=24;i++){
		if(!(Xor_tmp>>i)){
			XorSame_bit = i;
			break;
		}
	}
	if(XorSame_bit < 12) XorSame_bit = 12;
	switch(XorSame_bit){
		case 0:;
		case 1:;
		case 2:;
		case 3:;
		case 4:;
		case 5:;
		case 6:;
		case 7:;
		case 8:;
		case 9:;
		case 10:;
		case 11:;
		case 12: tw_ram_config = 0x0c; DDS1TW_val = DDSTW_max & 0xfffff000; //ram_data_start = (DDSTW_max & 0x00000fff)<<4; DDSTW_step = DDSTW_step<<4;			//?????12????,????????
			break;
		case 13: tw_ram_config = 0x0b; DDS1TW_val = DDSTW_max & 0xffffe000; //ram_data_start = ((DDSTW_max & 0x00001ffe)>>1)<<4; DDSTW_step = DDSTW_step<<3;
			break;
		case 14: tw_ram_config = 0x0a; DDS1TW_val = DDSTW_max & 0xffffc000; //ram_data_start = ((DDSTW_max & 0x00003ffc)>>2)<<4; DDSTW_step = DDSTW_step<<2;
			break;
		case 15: tw_ram_config = 0x09; DDS1TW_val = DDSTW_max & 0xffff8000; //ram_data_start = ((DDSTW_max & 0x00007ff8)>>3)<<4; DDSTW_step = DDSTW_step<<1;
			break;
		case 16: tw_ram_config = 0x08; DDS1TW_val = DDSTW_max & 0xffff0000; //ram_data_start = ((DDSTW_max & 0x0000fff0)>>4)<<4; DDSTW_step = DDSTW_step>>0;
			break;
		case 17: tw_ram_config = 0x07; DDS1TW_val = DDSTW_max & 0xfffe0000; //ram_data_start = ((DDSTW_max & 0x0001ffe0)>>5)<<4; DDSTW_step = DDSTW_step>>1;
			break;
		case 18: tw_ram_config = 0x06; DDS1TW_val = DDSTW_max & 0xfffc0000; //ram_data_start = ((DDSTW_max & 0x0003ffc0)>>6)<<4; DDSTW_step = DDSTW_step>>2;
			break;
		case 19: tw_ram_config = 0x05; DDS1TW_val = DDSTW_max & 0xfff80000; //ram_data_start = ((DDSTW_max & 0x0007ff80)>>7)<<4; DDSTW_step = DDSTW_step>>3;
			break;
		case 20: tw_ram_config = 0x04; DDS1TW_val = DDSTW_max & 0xfff00000; //ram_data_start = ((DDSTW_max & 0x000fff00)>>8)<<4; DDSTW_step = DDSTW_step>>4;
			break;
		case 21: tw_ram_config = 0x03; DDS1TW_val = DDSTW_max & 0xffe00000; //ram_data_start = ((DDSTW_max & 0x001ffe00)>>9)<<4; DDSTW_step = DDSTW_step>>5;
			break;
		case 22: tw_ram_config = 0x02; DDS1TW_val = DDSTW_max & 0xffc00000; //ram_data_start = ((DDSTW_max & 0x003ffc00)>>10)<<4; DDSTW_step = DDSTW_step>>6;
			break;
		case 23: tw_ram_config = 0x01; DDS1TW_val = DDSTW_max & 0xff800000; //ram_data_start = ((DDSTW_max & 0x007ff800)>>11)<<4; DDSTW_step = DDSTW_step>>7;
			break;
		case 24: tw_ram_config = 0x00; DDS1TW_val = DDSTW_max & 0xff000000; //ram_data_start = ((DDSTW_max & 0x00fff000)>>12)<<4; DDSTW_step = DDSTW_step>>8;
			break;
		default:break;		
	}
	
	InstruAndData_DDSTW32 = (InstruAndData_DDSTW32&0xFFFF0000)|((DDS1TW_val&0x00ffffff)>>8);
	InstruAndData_DDSTW1 = (InstruAndData_DDSTW1&0xFFFF0000)|((DDS1TW_val&0x000000ff)<<8);
//	DDS_RST(0);
//	delay_us(100);
//	DDS_RST(1);
//	delay_us(100);
	//RAM
	InstruAndData_PAT_STATUS=0x001E0004;
	WriteToAD9106(InstruAndData_PAT_STATUS);
	if(freq_start>freq_stop){
		ram_data_start = (DDSTW_max << (32-XorSame_bit)) >> (32-XorSame_bit);
		for(i=0,ram_addr=0x60000000; i<(nRep+2+pulNum_brake); i++){
			if(i<nRep){ //主脉冲计算
				ram_val = ((ram_data_start - i*DDSTW_step) >> (XorSame_bit-12)) << 4;		
			}
			else { //非主脉冲计算(刹车脉冲)
				ram_val = ((ram_data_start + DDSTW_brake - DDSTW_max) >> (XorSame_bit-12)) << 4;
//				ram_val = ((ram_data_start + (int)(211848.1067)) >> (XorSame_bit-12)) << 4;
			}
			write_ram = ram_addr + ram_val;		// (write_ram&0xffff0000) + ram_data_start - i*DDSTW_step;
			WriteToAD9106(write_ram);
			ram_addr = ram_addr + 0x00010000;
//			tst_tmp[i]=ram_val;
		}
	}else{
		ram_data_start = (DDSTW_min << (32-XorSame_bit)) >> (32-XorSame_bit);
		for(i=0,ram_addr=0x60000000; i<(nRep+2+pulNum_brake); i++){
			if(i<nRep){
				ram_val = ((ram_data_start + i*DDSTW_step) >> (XorSame_bit-12)) << 4;	
			}
			else { //非主脉冲计算(刹车脉冲)
				ram_val = ((ram_data_start +  DDSTW_brake - DDSTW_min) >> (XorSame_bit-12)) << 4;
//				ram_val = ((ram_data_start + (int)(211848.1067)) >> (XorSame_bit-12)) << 4;
			}
			write_ram = ram_addr + ram_val;		// (write_ram&0xffff0000) + ram_data_start - i*DDSTW_step;
			WriteToAD9106(write_ram);
			ram_addr = ram_addr + 0x00010000;
//				tst_tmp[i]=ram_val;
		}
	}
	InstruAndData_PAT_STATUS=0x001E0001;
	WriteToAD9106(InstruAndData_PAT_STATUS);
	
	InstruAndData_PatternType=0x001F0001;
	WriteToAD9106(InstruAndData_PatternType);//????DAC4_3PATx?DAC2_1PATx??????
	if(ch & CH12) {
		InstruAndData_WAV2_1PATx = 0x002B0101;
		WriteToAD9106(InstruAndData_WAV2_1PATx);
		InstruAndData_WAV2_1CONFIG=0x00273232;//0x00273232;
		WriteToAD9106(InstruAndData_WAV2_1CONFIG);//?????
	}
	if(ch & CH34) {
		InstruAndData_WAV4_3PATx = 0x002A0101;
		WriteToAD9106(InstruAndData_WAV4_3PATx);
		InstruAndData_WAV4_3CONFIG=0x00263232;
		WriteToAD9106(InstruAndData_WAV4_3CONFIG);//选择波形源
	}
	
	if(ch & CH12) {
		InstruAndData_DGAIN1=	0x00354000;						//DACx_DGAIN 0x35, Data=0x4000  Very important,the maximum value is 0x4000
		InstruAndData_DGAIN2=	0x00344000;
		WriteToAD9106(InstruAndData_DGAIN2); 
		WriteToAD9106(InstruAndData_DGAIN1);
		WriteToAD9106(InstruAndData_DAC1RSET);
		WriteToAD9106(InstruAndData_DAC2RSET);
	}
	if(ch & CH34) {
		InstruAndData_DGAIN3= 0x00334000;
		InstruAndData_DGAIN4= 0x00324000;
		WriteToAD9106(InstruAndData_DGAIN4); 
		WriteToAD9106(InstruAndData_DGAIN3);
		WriteToAD9106(InstruAndData_DAC4RSET);
		WriteToAD9106(InstruAndData_DAC3RSET);
	}
//	WriteToAD9106(InstruAndData_DOF1);
//	WriteToAD9106(InstruAndData_DOF2);
	WriteToAD9106(InstruAndData_DDSTW32);
	WriteToAD9106(InstruAndData_DDSTW1);
	InstruAndData_DDS_CONFIG=	0x00455555;
	InstruAndData_TW_RAM_CONFIG=	0x00470000 + tw_ram_config;
	WriteToAD9106(InstruAndData_DDS_CONFIG);
	WriteToAD9106(InstruAndData_TW_RAM_CONFIG);
	
	InstruAndData_PAT_DLY =	0x0020000e;
	WriteToAD9106(InstruAndData_PAT_DLY);//???????????????
	InstruAndData_PAT_TIMEBASE=0x00280161;
	InstruAndData_PAT_PERIOD= 0x00298000;
//	PAT_period_tmp = CLK_KHZ*(nRep+10)/((freq_start+freq_stop)/2000);//freq的单位为Hz
//	PAT_period_tmp = (PAT_period_tmp>=0xffff) ? 0xffff : PAT_period_tmp;

//	if(freq_start<freq_stop){
//		PAT_period_tmp = CLK_KHZ*(nRep+10)/((freq_start+0)/2000);//freq的单位为Hz
//	}else{
//		PAT_period_tmp = CLK_KHZ*(nRep+10)/((freq_stop+0)/2000);//freq的单位为Hz
//	}
	freq_start = freq_start/1000;
	freq_stop = freq_stop/1000;
	PAT_period_tmp = (nRep+10)*((1000000/freq_stop + 1000000/freq_start)/2)/40;
	PAT_period_tmp = (PAT_period_tmp>=0xffff) ? 0xffff : PAT_period_tmp;
	InstruAndData_PAT_TIMEBASE=0x00280161;
	InstruAndData_PAT_PERIOD=0x00290000 + (PAT_period_tmp&0x0000FFFF);
	need_time = (PAT_period_tmp*6/150) + 100;//us
	WriteToAD9106(InstruAndData_PAT_TIMEBASE);
	WriteToAD9106(InstruAndData_PAT_PERIOD);

	if(ch & CH12) {
		InstruAndData_START_ADDR1	= 0x005D0000;
		InstruAndData_STOP_ADDR1	= 0x005E0000 + (nRep+pulNum_brake)*16;//nRep<<4,输出比设置多一周期波
		WriteToAD9106(InstruAndData_START_ADDR1);
		WriteToAD9106(InstruAndData_STOP_ADDR1);

		InstruAndData_START_ADDR2	= 0x00590000;
		InstruAndData_STOP_ADDR2  	= 0x005A0000  + (nRep+pulNum_brake)*16;//nRep<<4
		WriteToAD9106(InstruAndData_START_ADDR2);
		WriteToAD9106(InstruAndData_STOP_ADDR2);
		
		InstruAndData_DDS2_PW=	0x00420000 + 0xffff/4;//DDS2辅助DDS1
		InstruAndData_DDS1_PW=	0x00430000 + 0xffff/2;// + 0xffff/2;//DDS2输出
		WriteToAD9106(InstruAndData_DDS2_PW);
		WriteToAD9106(InstruAndData_DDS1_PW);
	}
	if(ch & CH34) {
		InstruAndData_START_ADDR3	= 0x00550000;
		InstruAndData_STOP_ADDR3	= 0x00560000 + (nRep)*16;//nRep<<4,比设置多一周期波
		WriteToAD9106(InstruAndData_START_ADDR3);
		WriteToAD9106(InstruAndData_STOP_ADDR3);

		InstruAndData_START_ADDR4	= 0x00510000;
		InstruAndData_STOP_ADDR4  	= 0x00520000  + (nRep)*16;//nRep<<4
		WriteToAD9106(InstruAndData_START_ADDR4);
		WriteToAD9106(InstruAndData_STOP_ADDR4);
		
		InstruAndData_DDS4_PW=	0x00400000 + 0xffff/4;//DDS4辅助DDS3
		InstruAndData_DDS3_PW=	0x00410000 + 0xffff/2;//DDS3输出调整180度
		WriteToAD9106(InstruAndData_DDS4_PW);
		WriteToAD9106(InstruAndData_DDS3_PW);
	}
	
	WriteToAD9106(InstruAndData_update);
	delay_us(50);
	WriteToAD9106(InstruAndData_Run);

	return need_time;
}



//freq_start单位Hz, freq_stop单位Hz, nRep为实际波头的两倍
//u32 AD9106_Chirp_WithEncode(u32 freq_start, u32 freq_stop, u16 nRep)//单位Hz
//{
//	u32 			need_time;
//	u32 			PAT_period_tmp=0;
//	u32 			freq_max, freq_min, freq_ref;
//	u32 			DDSTW_min, DDSTW_max, DDSTW_brake, DDSTW_enc;
//	u32 			DDSTW_step;
//	u32 			Xor_tmp, XorSame_bit;
//	u8 				tw_ram_config;
//	u32 			DDS1TW_val;
//	u32 			write_ram 	= 0x60000000;
//	u32 			ram_addr 	= 0x60000000;
//	u32 			ram_val 	= 0x00000000;
//	u32 			i;
//	float 			f_div_2e24;
//	u32 			ram_data_start;
//	uint8_t 		pulNum_brake;
//	uint32_t 		brake_hz;
//	uint8_t 		encEmit_en = 0;
//	
//	//编码频率
//	uint32_t 		freq_enc = g_Emat.encEmit_fe2 * 1000000 * 2;//2870000*2;
//	uint8_t 		enc_pos = 3*2;
//	
//	pulNum_brake 	= g_Emat.PulBake_Num*2 + g_Emat.brake_halfPul_en + g_Emat.brake_dlyHalfPul_en;
//	brake_hz 		= g_Emat.brake_comp_khz*1000*2;

////	freq_ref		= 4000000;
//	f_div_2e24 		= 0.1118481067;//8.9406967163;//2^24/150M
//	
//	//Chirp波参数计算
//	freq_max 		= (freq_start > freq_stop) ? freq_start : freq_stop;
//	freq_min 		= (freq_start > freq_stop) ? freq_stop : freq_start;
//	DDSTW_step 		= (freq_max - freq_min) * f_div_2e24 / (nRep-1);
//	DDSTW_max 		= freq_max * f_div_2e24;
//	DDSTW_min 		= freq_min * f_div_2e24;
//	
//	//有插入脉冲, 包含其频率
//	if(encEmit_en) {
//		DDSTW_enc = freq_enc * f_div_2e24;
//		if(DDSTW_enc > DDSTW_max)
//			DDSTW_max 	= DDSTW_enc;
//		else if(DDSTW_enc < DDSTW_min)
//			DDSTW_min 	= DDSTW_enc;
//	}
//	
//	//有刹车, 需要包含其频率
//	if(pulNum_brake != 0) {
//		DDSTW_brake 	= brake_hz * f_div_2e24;
//		if(DDSTW_brake > DDSTW_max) {
//			Xor_tmp 	= DDSTW_brake ^ DDSTW_min;
//			DDSTW_max 	= DDSTW_brake;
//		}
//		else if(DDSTW_brake < DDSTW_min) {
//			Xor_tmp 	= DDSTW_max ^ DDSTW_brake;
//			DDSTW_min 	= DDSTW_brake;
//		}
//	}
//	
//	Xor_tmp = DDSTW_max ^ DDSTW_min;
//	for(i=0; i<=24; i++) {
//		if(!(Xor_tmp>>i)) {
//			XorSame_bit = i;
//			break;
//		}
//	}
//	if(XorSame_bit < 12) XorSame_bit = 12;
//	switch(XorSame_bit) {
//		case 0:;
//		case 1:;
//		case 2:;
//		case 3:;
//		case 4:;
//		case 5:;
//		case 6:;
//		case 7:;
//		case 8:;
//		case 9:;
//		case 10:;
//		case 11:;
//		case 12: tw_ram_config = 0x0c; DDS1TW_val = DDSTW_max & 0xfffff000; //ram_data_start = (DDSTW_max & 0x00000fff)<<4; DDSTW_step = DDSTW_step<<4;			//?????12????,????????
//			break;
//		case 13: tw_ram_config = 0x0b; DDS1TW_val = DDSTW_max & 0xffffe000; //ram_data_start = ((DDSTW_max & 0x00001ffe)>>1)<<4; DDSTW_step = DDSTW_step<<3;
//			break;
//		case 14: tw_ram_config = 0x0a; DDS1TW_val = DDSTW_max & 0xffffc000; //ram_data_start = ((DDSTW_max & 0x00003ffc)>>2)<<4; DDSTW_step = DDSTW_step<<2;
//			break;
//		case 15: tw_ram_config = 0x09; DDS1TW_val = DDSTW_max & 0xffff8000; //ram_data_start = ((DDSTW_max & 0x00007ff8)>>3)<<4; DDSTW_step = DDSTW_step<<1;
//			break;
//		case 16: tw_ram_config = 0x08; DDS1TW_val = DDSTW_max & 0xffff0000; //ram_data_start = ((DDSTW_max & 0x0000fff0)>>4)<<4; DDSTW_step = DDSTW_step>>0;
//			break;
//		case 17: tw_ram_config = 0x07; DDS1TW_val = DDSTW_max & 0xfffe0000; //ram_data_start = ((DDSTW_max & 0x0001ffe0)>>5)<<4; DDSTW_step = DDSTW_step>>1;
//			break;
//		case 18: tw_ram_config = 0x06; DDS1TW_val = DDSTW_max & 0xfffc0000; //ram_data_start = ((DDSTW_max & 0x0003ffc0)>>6)<<4; DDSTW_step = DDSTW_step>>2;
//			break;
//		case 19: tw_ram_config = 0x05; DDS1TW_val = DDSTW_max & 0xfff80000; //ram_data_start = ((DDSTW_max & 0x0007ff80)>>7)<<4; DDSTW_step = DDSTW_step>>3;
//			break;
//		case 20: tw_ram_config = 0x04; DDS1TW_val = DDSTW_max & 0xfff00000; //ram_data_start = ((DDSTW_max & 0x000fff00)>>8)<<4; DDSTW_step = DDSTW_step>>4;
//			break;
//		case 21: tw_ram_config = 0x03; DDS1TW_val = DDSTW_max & 0xffe00000; //ram_data_start = ((DDSTW_max & 0x001ffe00)>>9)<<4; DDSTW_step = DDSTW_step>>5;
//			break;
//		case 22: tw_ram_config = 0x02; DDS1TW_val = DDSTW_max & 0xffc00000; //ram_data_start = ((DDSTW_max & 0x003ffc00)>>10)<<4; DDSTW_step = DDSTW_step>>6;
//			break;
//		case 23: tw_ram_config = 0x01; DDS1TW_val = DDSTW_max & 0xff800000; //ram_data_start = ((DDSTW_max & 0x007ff800)>>11)<<4; DDSTW_step = DDSTW_step>>7;
//			break;
//		case 24: tw_ram_config = 0x00; DDS1TW_val = DDSTW_max & 0xff000000; //ram_data_start = ((DDSTW_max & 0x00fff000)>>12)<<4; DDSTW_step = DDSTW_step>>8;
//			break;
//		default:break;		
//	}
//	
//	DDS_RST(0);
//	delay_us(100);
//	DDS_RST(1);
//	delay_us(100);
//	WriteAD9106(AD9106_PAT_STATUS,	0x0004);
//	
//	DDSTW_max 		= freq_max * f_div_2e24;
//	DDSTW_min 		= freq_min * f_div_2e24;
//	
//	//向RAM中设置打标信号的频率(addr: 0x6100~0x610F)
////	ram_data_start 	= ( ((u32)(freq_ref * f_div_2e24)) << (32-XorSame_bit) ) >> (32-XorSame_bit);
////	ram_val 		= (ram_data_start >> (XorSame_bit-12)) << 4;
////	for(i = 0, ram_addr = 0x60000000; i <= 0x01; i++) {
////		WriteAD9106(ram_addr + ram_val);
////		ram_addr 	= ram_addr + 0x00010000;
////	}
//	if(encEmit_en) {
//		if(freq_start > freq_stop) {
//			ram_data_start = (DDSTW_max << (32-XorSame_bit)) >> (32-XorSame_bit);
//			for(i = 0, ram_addr = 0x6000; i < (nRep+2+pulNum_brake); i++) {
//				if(i < nRep) { //主脉冲计算
//					ram_val = ((ram_data_start - i*DDSTW_step) >> (XorSame_bit-12)) << 4;	
//					if((i>=enc_pos) && (i<=enc_pos+1)){
//						ram_val = ((ram_data_start + DDSTW_enc - DDSTW_max) >> (XorSame_bit-12)) << 4;
//					}
//				}
//				else { //非主脉冲计算(刹车脉冲)
//					ram_val = ((ram_data_start + DDSTW_brake - DDSTW_max) >> (XorSame_bit-12)) << 4;
//				}
//				WriteAD9106(ram_addr, ram_val);
//				ram_addr 	= ram_addr + 1;
//			}
//		}
//		else {
//			ram_data_start = (DDSTW_min << (32-XorSame_bit)) >> (32-XorSame_bit);
//			for(i = 0, ram_addr = 0x6000; i < (nRep+2+pulNum_brake); i++) {
//				if(i < nRep) {
//					ram_val = ((ram_data_start + i*DDSTW_step) >> (XorSame_bit-12)) << 4;
//					if((i>=enc_pos) && (i<=enc_pos+1)){
//						ram_val = ((ram_data_start + DDSTW_enc - DDSTW_min) >> (XorSame_bit-12)) << 4;
//					}
//				}
//				else { //非主脉冲计算(刹车脉冲)
//					ram_val = ((ram_data_start + DDSTW_brake - DDSTW_min) >> (XorSame_bit-12)) << 4;
//				}
//				WriteAD9106(ram_addr, ram_val);
//				ram_addr 	= ram_addr + 1;
//			}
//		}
//	}
//	else {
//		if(freq_start>freq_stop){
//			ram_data_start = (DDSTW_max << (32-XorSame_bit)) >> (32-XorSame_bit);
//			for(i=0,ram_addr=0x6000; i<(nRep+2+pulNum_brake); i++){
//				if(i<nRep){ //主脉冲计算
//					ram_val = ((ram_data_start - i*DDSTW_step) >> (XorSame_bit-12)) << 4;		
//				}
//				else { //非主脉冲计算(刹车脉冲)
//					ram_val = ((ram_data_start + DDSTW_brake - DDSTW_max) >> (XorSame_bit-12)) << 4;
//				}
//				write_ram = ram_addr + ram_val;
//				WriteAD9106(ram_addr, ram_val);
//				ram_addr 	= ram_addr + 1;
//			}
//		}else{
//			ram_data_start = (DDSTW_min << (32-XorSame_bit)) >> (32-XorSame_bit);
//			for(i=0,ram_addr=0x6000; i<(nRep+2+pulNum_brake); i++){
//				if(i<nRep){
//					ram_val = ((ram_data_start + i*DDSTW_step) >> (XorSame_bit-12)) << 4;	
//				}
//				else { //非主脉冲计算(刹车脉冲)
//					ram_val = ((ram_data_start +  DDSTW_brake - DDSTW_min) >> (XorSame_bit-12)) << 4;
//				}
//				write_ram = ram_addr + ram_val;
//				WriteAD9106(ram_addr, ram_val);
//				ram_addr 	= ram_addr + 1;
//			}
//		}
//		
//	}
//	
////	DDSTW_max 		= freq_max * f_div_2e24;
////	DDSTW_min 		= freq_min * f_div_2e24;
////	
////	ram_data_start = (DDSTW_max << (32-XorSame_bit)) >> (32-XorSame_bit);
////	for(i = 0, ram_addr = 0x6000; i < (nRep+2+pulNum_brake); i++) {
////		if(i < nRep) {
////			ram_val = ((ram_data_start - i*DDSTW_step) >> (XorSame_bit-12)) << 4;
////			if((i==encode_pos) || (i==encode_pos+1)){
////				ram_val = ( (ram_data_start - (int32_t)((freq_min - encode_freq) * f_div_2e24) ) >> (XorSame_bit-12) ) << 4;
////			}
////			
////		}
////		else { //非主脉冲计算(刹车脉冲)
////			ram_val = ((ram_data_start + DDSTW_brake - DDSTW_max) >> (XorSame_bit-12)) << 4;
////		}
////		WriteAD9106(ram_addr, ram_val);
////		ram_addr 	= ram_addr + 1;
////	}

//	
//	WriteAD9106(AD9106_PAT_STATUS		, 0x0001);
//	WriteAD9106(AD9106_PATTERNTYPE	, 0x0001);
//	WriteAD9106(AD9106_WAV2_1PATX		, 0x0101);
//	WriteAD9106(AD9106_WAV2_1CONFIG	, 0x3232);
//	WriteAD9106(AD9106_WAV4_3PATX		, 0x0101);
//	WriteAD9106(AD9106_WAV4_3CONFIG	, 0x3232);
//	
//	WriteAD9106(AD9106_DGAIN2			, 0x4000);
//	WriteAD9106(AD9106_DGAIN1			, 0x4000);
//	WriteAD9106(AD9106_DAC1RSET		, 0x800A);
//	WriteAD9106(AD9106_DAC2RSET		, 0x800A);
//	
//	WriteAD9106(AD9106_DGAIN4			, 0x4000); 
//	WriteAD9106(AD9106_DGAIN3			, 0x4000);
//	WriteAD9106(AD9106_DAC3RSET		, 0x800A);
//	WriteAD9106(AD9106_DAC4RSET		, 0x800A);
//	
//	WriteAD9106(AD9106_DDSTW32		, ((DDS1TW_val&0x00ffffff)>>8));
//	WriteAD9106(AD9106_DDSTW1			, ((DDS1TW_val&0x000000ff)<<8));
//	WriteAD9106(AD9106_DDS_CONFIG		, 0x5555);
//	WriteAD9106(AD9106_TW_RAM_CONFIG	, tw_ram_config);
//	
//	freq_start 			= freq_start/1000;
//	freq_stop 			= freq_stop/1000;
//	PAT_period_tmp 		= (nRep+10)*((1000000/freq_stop + 1000000/freq_start)/2)/40;
//	PAT_period_tmp 		= (PAT_period_tmp>=0xffff) ? 0xffff : PAT_period_tmp;

//	need_time 			= (PAT_period_tmp*6/150) + 100;//us
//	
//	WriteAD9106(AD9106_PAT_DLY		, 0x000e);
//	WriteAD9106(AD9106_PAT_TIMEBASE	, 0x0161);
//	WriteAD9106(AD9106_PAT_PERIOD		, (PAT_period_tmp&0x0000FFFF));

//	WriteAD9106(AD9106_START_DLY1		, 0x0000);
//	WriteAD9106(AD9106_START_ADDR1	, 0x0000);
//	WriteAD9106(AD9106_STOP_ADDR1		, (nRep+pulNum_brake)*16);

//	WriteAD9106(AD9106_START_ADDR2	, 0x0000);
//	WriteAD9106(AD9106_STOP_ADDR2		, (nRep+pulNum_brake)*16);

//	WriteAD9106(AD9106_START_DLY3		, 0x0000);
//	WriteAD9106(AD9106_START_ADDR3	, 0x0000);
//	WriteAD9106(AD9106_STOP_ADDR3		, (0)*16);

//	WriteAD9106(AD9106_START_ADDR4	, 0x0000);
//	WriteAD9106(AD9106_STOP_ADDR4		, (0)*16);
//	
//	WriteAD9106(AD9106_DDS2_PW		, 0xffff/4); //DDS2辅助DDS1
//	WriteAD9106(AD9106_DDS1_PW		, 0xffff/2); //DDS2输出
//	WriteAD9106(AD9106_DDS4_PW		, 0xffff/2); //DDS4辅助DDS3
//	WriteAD9106(AD9106_DDS3_PW		, 0xffff/2); //DDS3输出调整180度
//	
//	WriteAD9106(AD9106_UPDATE			, 0x0001);
//	delay_us(50);
//	WriteAD9106(AD9106_PAT_STATUS		, 0x0001);//允许产生模式（RUN）

//	return need_time;
//}




#define SAMPLE_POINTS 100/4 //样本ROM点数
u16 sin_buf[100];
//#define PI 3.14
// Chirp模型计算
// 参数说明:
// buf[][0] 存放起点. buf[][1] 存放步进
// f_start:起始频率
// f_end:终止频率
// pul_num:脉冲数,单峰个数
// f_sample:采样频率
//u8 ChirpModel_Calc(u8* buf[][2], float f_start, float f_end, u8 pul_num, u16 f_sample) {
u8 ChirpModel_Calc() {
	u8 i = 0;
//	float f_step; //相邻波头频率步进值,单位MHz
//	float f_curr;
//	u8 needPointsNum;
//	f_step = (f_end - f_start) / (pul_num - 1);
	
	for(i = 0; i < 100; i++) {
		sin_buf[i] = 2000*sin(2*PI*i/100);
	}
	
	
//	for(i = 0; i < pul_num; i++) {
//		f_curr = f_start + i*f_step;
//		needPointsNum = f_sample / f_curr;
//		buf[i][0] = ; //存放每个波头的起点
//		buf[i][1] = ; //存放每个波头的步进
//		1024*sin();
//	}
}
// mode = 0:普通模式;1:Chirp; EmitFreq单位MHz
void DDS_SetPara()
{
	float scale = 0.1;
	double freq_first, freq_last; //单位Hz
	CHANNEL_T *chn;

	DDS_RST(0);
	delay_us(100);
	DDS_RST(1);
	delay_us(100);
	
//	if(mode == 0) { //普通模式
//		AD9106_Chirp(EmitFreq * 2000000, EmitFreq * 2000000, rep * 2);//单位Hz
//	}
//	else if(mode == 1) { //Chirp模式
	
//	freq_first = EmitFreq + g_Emat.chirpEmatFreqRangeMHz;
//	freq_last  = EmitFreq - g_Emat.chirpEmatFreqRangeMHz;
	chn = &g_CHN[0];
	AD9106_PatternMode(CH12, chn->emat->emitFreq_MHz * 1000000, chn->emat->emitPulNum);

//	chn = &g_CHN1;
//	freq_first = chn->emat->emitFreq_MHz + 0;
//	freq_last  = chn->emat->emitFreq_MHz + 0;
//	AD9106_Chirp(CH12, freq_first * 2000000, freq_last * 2000000, chn->emat->emitHalfPul_en ? (chn->emat->emitPulNum * 2 + 1) : (chn->emat->emitPulNum * 2), 
//		chn->emat->brakePulNum, chn->emat->brakeHalfPul_en, chn->emat->brakeDlyHalfPul_en, chn->emat->brakeFreq_KHz);
//	chn = &g_CHN2;
//	freq_first = chn->emat->emitFreq_MHz + 0;
//	freq_last  = chn->emat->emitFreq_MHz + 0;
//	AD9106_Chirp(CH34, freq_first * 2000000, freq_last * 2000000, chn->emat->emitHalfPul_en ? (chn->emat->emitPulNum * 2 + 1) : (chn->emat->emitPulNum * 2), 
//		chn->emat->brakePulNum, chn->emat->brakeHalfPul_en, chn->emat->brakeDlyHalfPul_en, chn->emat->brakeFreq_KHz);
}

/**
  * @brief  设置DDS参数, 仅使用有死区模式
  * @param  None
  * @retval None
  */
void DDS_SetParaDetail(float freq_MHz, u8 nRep)
{
	DDS_RST(0);
	delay_us(100);
	DDS_RST(1);
	delay_us(100);

	AD9106_PatternMode(CH12, freq_MHz * 1000000, nRep);
}

/**
  * @brief  设置DDS参数, Chirp模式
  * @param  None
  * @retval None
  */
void DDS_SetParaChirp(float freq1_MHz, float freq2_MHz, u8 nRep, u8 existHalfRep)
{
	DDS_RST(0);
	delay_us(100);
	DDS_RST(1);
	delay_us(100);

//	AD9106_Chirp(u8 ch, u32 freq_start, u32 freq_stop, u16 nRep, u8 brakePulNum, u8 existBrakeHalfPul, u8 brakeDlyHalfPul, int16_t brakePulFreq_khz)
	AD9106_Chirp(CH12, freq1_MHz * 2000000, freq2_MHz * 2000000, existHalfRep ? (nRep * 2 + 1) : (nRep * 2), 0, 0, 0, 0);
}




