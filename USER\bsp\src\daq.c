/**
  ****************************************************************************** 
  * @file    daq.c
  * <AUTHOR>
  * @version v1.0
  * @date    2021.04.15
  * @brief   数据采集、数据初步处理等
  * @history   
  ****************************************************************************** 
  */
#include "includes.h"


DAQ_T g_Daq[DAQ_CH_NUM];

/**
  * @brief  数据采集相关变量初始化
  * @param  插值后的点号
  * @retval 插值前的点号
  */
//void DAQ_Init() {
//	g_Daq.fs_MHz 		= 50; //初始采用频率50MHz
//	g_Daq.avg			= 64;
//	g_Daq.waveRecvStage	= RECV_IDLE;
//	g_Daq.data.dataValid	= 0;
//}

int32_t Time2Point(float t_us, int16_t fs) {
	int32_t point;
	point = (t_us * fs);
	return point;
}

uint8_t DAQ_SetAvg(uint8_t ch, uint16_t avg) {
	if((avg==1) || (avg==2) || (avg==4) || (avg==8) || (avg==16) || (avg==32) || (avg==64) || (avg==128) || (avg==256) || (avg==512) || (avg==1024)) {
		g_Daq[ch - 1].avg = avg; 
		return 0; //OK 
	}
	else {
		return 1; //失败
	}
}

uint8_t DAQ_SetFs(uint8_t ch, uint16_t fs_MHz) {
	if(ch > DAQ_CH_NUM)
		return 1;
	g_Daq[ch-1].fs_MHz = fs_MHz;
	return 0; //OK 
}

uint8_t DAQ_SetGain(uint8_t ch, float val) {
	if(ch > DAQ_CH_NUM)
		return 1;
	g_Daq[ch-1].gainVal = val;
	
	Gain_SetGain(ch, val);
	return 0; //OK 
}

uint8_t DAQ_SetWaveBgnTime(uint8_t ch, uint32_t time_ns) {
	if(ch > DAQ_CH_NUM)
		return 1;
	g_Daq[ch-1].waveBgn_ns = time_ns;
	g_Daq[ch-1].waveBgn_pt = time_ns * g_Daq[ch-1].fs_MHz / 1000;
	return 0; //OK 
}

uint8_t DAQ_SetWaveLenTime(uint8_t ch, uint32_t time_ns) {
	if(ch > DAQ_CH_NUM)
		return 1;
	g_Daq[ch-1].waveLen_ns = time_ns;
	g_Daq[ch-1].waveLen_pt = time_ns * g_Daq[ch-1].fs_MHz / 1000;
	return 0; //OK 
}

uint8_t DAQ_SetWaveBgnPt(uint8_t ch, uint32_t pt) {
	if(ch > DAQ_CH_NUM)
		return 1;
	g_Daq[ch-1].waveBgn_pt = pt;
	g_Daq[ch-1].waveBgn_ns = pt * 1000 / g_Daq[ch-1].fs_MHz;
	return 0; //OK 
}

uint8_t DAQ_SetWaveLenPt(uint8_t ch, uint32_t pt) {
	if(ch > DAQ_CH_NUM)
		return 1;
	if(pt > DATA_BUF_LEN)
		return 2;
	
	g_Daq[ch-1].waveLen_pt = pt;
	g_Daq[ch-1].waveLen_ns = pt * 1000 / g_Daq[ch-1].fs_MHz;
	return 0; //OK 
}


/**
  * @brief  设置平均
  * @param  
  * @retval None
  */
//uint8_t DAQ_SetAvg(int32_t avg) {
//	uint8_t res;
//	
//	if((avg==1) || (avg==2) || (avg==4) || (avg==8) || (avg==16) || (avg==32) || (avg==64) || (avg==128) || (avg==256) || (avg==512) || (avg==1024)) {
//		g_Daq.avg = avg; res = 0; //OK 
//	}
//	else res = 1; //失败
//	return res;
//}

/**
  * @brief  插值后的点号转换成插值前的点号
  * @param  插值后的点号
  * @retval 插值前的点号
  */
static int32_t ExpandedPtsNum_Convert2_SrcPtsNum(int32_t num) {
//	if(g_Daq.fs_MHz == g_Daq.fs_MHz_ADC) {
//		return num;
//	}
//	else {
//		return num/(g_Daq.fs_MHz/g_Daq.fs_MHz_ADC); 
//	}
}

//坐标单位统一换算 & 转换为16的倍数, 时间-点数
//void ADCData_Unit_Unify()
//{
//	uint16_t fs = g_Daq.fs_MHz;
//	#if IS_GUI_V1
//	g_Wave.SP_waveAll.waveBgn_pt= ((uint32_t)(g_Wave.SP_waveAll.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.SP_waveAll.waveEnd_pt= ((uint32_t)(g_Wave.SP_waveAll.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.SP_wave1.waveBgn_pt	= ((uint32_t)(g_Wave.SP_wave1.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.SP_wave1.waveEnd_pt	= ((uint32_t)(g_Wave.SP_wave1.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.SP_wave2.waveBgn_pt	= ((uint32_t)(g_Wave.SP_wave2.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.SP_wave2.waveEnd_pt	= ((uint32_t)(g_Wave.SP_wave2.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.SP_wave3.waveBgn_pt	= ((uint32_t)(g_Wave.SP_wave3.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.SP_wave3.waveEnd_pt	= ((uint32_t)(g_Wave.SP_wave3.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.SP_wave4.waveBgn_pt	= ((uint32_t)(g_Wave.SP_wave4.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.SP_wave4.waveEnd_pt	= ((uint32_t)(g_Wave.SP_wave4.waveEnd_us * fs) / 16 + 1) * 16;
//	
//	g_Wave.DP_waveAll.waveBgn_pt= ((uint32_t)(g_Wave.DP_waveAll.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.DP_waveAll.waveEnd_pt= ((uint32_t)(g_Wave.DP_waveAll.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.DP_wave1.waveBgn_pt	= ((uint32_t)(g_Wave.DP_wave1.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.DP_wave1.waveEnd_pt	= ((uint32_t)(g_Wave.DP_wave1.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.DP_wave2.waveBgn_pt	= ((uint32_t)(g_Wave.DP_wave2.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.DP_wave2.waveEnd_pt	= ((uint32_t)(g_Wave.DP_wave2.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.DP_wave3.waveBgn_pt	= ((uint32_t)(g_Wave.DP_wave3.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.DP_wave3.waveEnd_pt	= ((uint32_t)(g_Wave.DP_wave3.waveEnd_us * fs) / 16 + 1) * 16;
//	g_Wave.DP_wave4.waveBgn_pt	= ((uint32_t)(g_Wave.DP_wave4.waveBgn_us * fs) / 16 + 0) * 16;
//	g_Wave.DP_wave4.waveEnd_pt	= ((uint32_t)(g_Wave.DP_wave4.waveEnd_us * fs) / 16 + 1) * 16;
//	
//	if((IS_PRESTRESS) && (!g_Cali.calibration_running)) {
//		g_Wave.SP_wave4.waveEnd_pt = g_Wave.SP_wave4.waveBgn_pt;
//		g_Wave.DP_wave4.waveEnd_pt = g_Wave.DP_wave4.waveBgn_pt;
//	}
//	#else
//	
//	#endif
//}



                                                  