#include "cali_files.h"
#if !IS_GUI_V1
#include "App_Cali.h"
#endif
//单波文件存储

/***

逐条保存结果文件结构：
L1	第一行文件类型
	fileType:ps_fl_result_table
L2	工程信息
	工程名:xxxx,风机名:xxxx,法兰名:xxxx
L3	内容title
	__________________________________________________________________________________________________________________________________
	|1   |2   |3      |4        |5      |6        |7         |8          |9       |10         |11          |12     |13     |14
	--------------------------------------------------------------------------------------------------------------------------------
	|N/A |N/A |螺栓名 |日期时间 |主机SN |软件版本 |D-TOF(ns) |D-轴力(KN) |TOF(ns) |此波文件名 |比对波文件名|系数K1 |系数K2 |温度(℃)
	--------------------------------------------------------------------------------------------------------------------------------
L4	数据
L5	数据
...

***/

/**
  * @brief  校准(标定)数据保存：参数、波形
  * @param  None
  * @retval None
  */
static uint8_t SaveWaveParas_Secret(FIL* file, uint8_t isSecret) {
	FRESULT res;
	char strData[40] = "";
	uint32_t fs_ns = 1000 / g_Daq.fs_MHz;
	char* str;
	uint8_t isDoubleProbe = Probe_isDoubleProbes();

	str = (char*)mymalloc(SRAMEX, 250);//申请内存

	//Line1---写入文件类型编号; "FileId:PS_WAVE_S1"单波波形文件
	sprintf(str, "fileType:CALI_WAVE\n"); if (isSecret) EncryptionString(str, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, str, strlen(str), &bw);

	//Line2---写入参数
//	sprintf(str, "Date:%s,", g_FileManage.SaveFileName);//写入时间
	GetTime_yymmdd_hhmmss(strData);
	sprintf(str, "Date:%s,", strData);//写入时间
	sprintf(strData, "SN:%s,", g_SysSet.DevSN); 				strcat(str, strData);//设备ID
	sprintf(strData, "Firmware:%s,", FIRMWARE_VERSION); 		strcat(str, strData);//软件版本
	sprintf(strData, "ID:%d,", g_Developer.diffCode); 			strcat(str, strData);//写入编号
	if (isDoubleProbe) {
		sprintf(strData, "Begin:%dns,", g_Wave.DP_wave3.waveBgn_pt * fs_ns); 	strcat(str, strData);//起点,ns
		sprintf(strData, "End:%dns,", g_Wave.DP_wave3.waveEnd_pt * fs_ns); 	strcat(str, strData);//终点,ns
	}
	else {
		sprintf(strData, "Begin:%dns,", g_Wave.SP_wave3.waveBgn_pt * fs_ns); 	strcat(str, strData);//起点,ns
		sprintf(strData, "End:%dns,", g_Wave.SP_wave3.waveEnd_pt * fs_ns); 	strcat(str, strData);//终点,ns
	}

	sprintf(strData, "fe:%.1fMHz,", g_Emat.EmatFreqMHz); 			strcat(str, strData);//发射频率
	sprintf(strData, "Rep:%d,", g_Emat.PulNum); 				strcat(str, strData);//发射脉冲数
	sprintf(strData, "fs:%dMHz,", g_Daq.fs_MHz); 			strcat(str, strData);//采样频率
	sprintf(strData, "Zero:%dns,", g_Cali.zeroStored_ns); 			strcat(str, strData);//零点,ns
	sprintf(strData, "Avg:%d,", g_Emat.Avg); 				strcat(str, strData);//平均次数
	sprintf(strData, "Gain:%.1fdB,", g_Gain.gainVal); 			strcat(str, strData);//增益
	if (isDoubleProbe) {
		sprintf(strData, "TOF-L:%.1fns,", (float)g_Wave.DP_wave1.tof_ns); 			strcat(str, strData);
		sprintf(strData, "TOF-S:%.1fns\n", (float)g_Wave.DP_wave3.tof_ns); 			strcat(str, strData);
	}
	else {
		sprintf(strData, "TOF-L:%.1fns,", (float)g_Wave.SP_wave1.tof_ns); 			strcat(str, strData);
		sprintf(strData, "TOF-S:%.1fns\n", (float)g_Wave.SP_wave3.tof_ns); 			strcat(str, strData);
	}
	if (isSecret) EncryptionString(str, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, str, strlen(str), &bw);

	//Line3---波形起点,ns、 波形长度,ns (已去除盲区)
	if (isDoubleProbe) {
		sprintf(strData, "Begin:%dns,Len:%dns,Temp:%.2fC\n",
			(g_Wave.DP_wave3.waveBgn_pt * fs_ns - g_Cali.zeroStored_ns), (g_Wave.DP_wave3.waveEnd_pt - g_Wave.DP_wave3.waveBgn_pt) * fs_ns, Temp_GetCurrUsedTemperature());
	}
	else {
		sprintf(strData, "Begin:%dns,Len:%dns,Temp:%.2fC\n",
			(g_Wave.SP_wave3.waveBgn_pt * fs_ns - g_Cali.zeroStored_ns), (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt) * fs_ns, Temp_GetCurrUsedTemperature());
	}

	if (isSecret) EncryptionString(strData, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, strData, strlen(strData), &bw);
	//Line4---波形起点标志
	sprintf(strData, "Wave_Data\n"); if (isSecret) EncryptionString(strData, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, strData, strlen(strData), &bw);

	myfree(SRAMEX, str);//释放存储
	return 0;
}

/**
  * @brief  多波保存波形--纵波、转换波、横波1、横波2
  * @param  None
  * @retval None
  */
uint8_t File_Cali_SavePointData(char* path, char* filename, uint8_t isSecret)
{
	FRESULT res;
	FIL fnew;
	u32 total, free;
	char* pPath;
	char strData[40] = "";
	uint16_t i = 0;

	pPath = (char*)mymalloc(SRAMIN, strlen(path) + strlen(filename) + 5);//申请内存, 比文件大一些,以在末尾存放0x00
	sprintf(pPath, "%s/%s", path, filename);

	res = f_open(&fnew, pPath, FA_WRITE);
	if (res == FR_OK) {
		if (0 == fat_del_folder(pPath)) { //删除成功

		}
	}
	res = f_open(&fnew, pPath, FA_WRITE);

	if (res == FR_NO_FILE) {
		res = f_open(&fnew, pPath, FA_CREATE_NEW | FA_WRITE);
	}
	if (res == FR_OK) {
		SaveWaveParas_Secret(&fnew, isSecret); //写入参数
		//纵波
		if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "L-Wave Data,Strong/Orignal/Light\n");
		else if (g_Developer.showOriginalWave) strcpy(strData, "L-Wave Data,Strong/Orignal\n");
		else if (g_Developer.showFilteredCalcWave) strcpy(strData, "L-Wave Data,Strong/Light\n");
		else strcpy(strData, "L-Wave Data\n");
		if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //L波形说明
		sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.SP_wave1.waveBgn_pt, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //L波形起点、长度

		Save_int16(&fnew, g_Wave.SP_wave1.waveFirO2_buf, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt), isSecret); //强滤波波形
		if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.SP_wave1.waveSrc_buf, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt), isSecret); //原始波形
		if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.SP_wave1.waveFirO1_buf, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt), isSecret); //轻滤波波形

		//转换波
		if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "C-Wave Data,Strong/Orignal/Light\n");
		else if (g_Developer.showOriginalWave) strcpy(strData, "C-Wave Data,Strong/Orignal\n");
		else if (g_Developer.showFilteredCalcWave) strcpy(strData, "C-Wave Data,Strong/Light\n");
		else strcpy(strData, "C-Wave Data\n");
		if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
		sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.SP_wave2.waveBgn_pt, (g_Wave.SP_wave2.waveEnd_pt - g_Wave.SP_wave2.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //C波形起点、长度
		Save_int16(&fnew, g_Wave.SP_wave2.waveFirO2_buf, (g_Wave.SP_wave2.waveEnd_pt - g_Wave.SP_wave2.waveBgn_pt), isSecret); //强滤波波形
		if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.SP_wave2.waveSrc_buf, (g_Wave.SP_wave2.waveEnd_pt - g_Wave.SP_wave2.waveBgn_pt), isSecret); //原始波形
		if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.SP_wave2.waveFirO1_buf, (g_Wave.SP_wave2.waveEnd_pt - g_Wave.SP_wave2.waveBgn_pt), isSecret); //轻滤波波形

		//横波1
		if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "S1-Wave Data,Strong/Orignal/Light\n");
		else if (g_Developer.showOriginalWave) strcpy(strData, "S1-Wave Data,Strong/Orignal\n");
		else if (g_Developer.showFilteredCalcWave) strcpy(strData, "S1-Wave Data,Strong/Light\n");
		else strcpy(strData, "S1-Wave Data\n");
		if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
		sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.SP_wave3.waveBgn_pt, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //S1波形起点、长度
		Save_int16(&fnew, g_Wave.SP_wave3.waveFirO2_buf, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt), isSecret); //强滤波波形
		if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.SP_wave3.waveSrc_buf, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt), isSecret); //原始波形
		if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.SP_wave3.waveFirO1_buf, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt), isSecret); //轻滤波波形

		//横波2
		if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "S2-Wave Data,Strong/Orignal/Light\n");
		else if (g_Developer.showOriginalWave) strcpy(strData, "S2-Wave Data,Strong/Orignal\n");
		else if (g_Developer.showFilteredCalcWave) strcpy(strData, "S2-Wave Data,Strong/Light\n");
		else strcpy(strData, "S2-Wave Data\n");
		if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
		sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.SP_wave4.waveBgn_pt, (g_Wave.SP_wave4.waveEnd_pt - g_Wave.SP_wave4.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //S2波形起点、长度
		Save_int16(&fnew, g_Wave.SP_wave4.waveFirO2_buf, (g_Wave.SP_wave4.waveEnd_pt - g_Wave.SP_wave4.waveBgn_pt), isSecret); //强滤波波形
		if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.SP_wave4.waveSrc_buf, (g_Wave.SP_wave4.waveEnd_pt - g_Wave.SP_wave4.waveBgn_pt), isSecret); //原始波形
		if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.SP_wave4.waveFirO1_buf, (g_Wave.SP_wave4.waveEnd_pt - g_Wave.SP_wave4.waveBgn_pt), isSecret); //轻滤波波形

		//双探头数据存储
		if (Probe_isDoubleProbes()) {
			//纵波
			if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "L1-Wave Data,Strong/Orignal/Light\n");
			else if (g_Developer.showOriginalWave) strcpy(strData, "L1-Wave Data,Strong/Orignal\n");
			else if (g_Developer.showFilteredCalcWave) strcpy(strData, "L1-Wave Data,Strong/Light\n");
			else strcpy(strData, "L1-Wave Data\n");
			if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //L波形说明
			sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.DP_wave1.waveBgn_pt, (g_Wave.DP_wave1.waveEnd_pt - g_Wave.DP_wave1.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //L波形起点、长度

			Save_int16(&fnew, g_Wave.DP_wave1.waveFirO2_buf, (g_Wave.DP_wave1.waveEnd_pt - g_Wave.DP_wave1.waveBgn_pt), isSecret); //强滤波波形
			if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.DP_wave1.waveSrc_buf, (g_Wave.DP_wave1.waveEnd_pt - g_Wave.DP_wave1.waveBgn_pt), isSecret); //原始波形
			if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.DP_wave1.waveFirO1_buf, (g_Wave.DP_wave1.waveEnd_pt - g_Wave.DP_wave1.waveBgn_pt), isSecret); //轻滤波波形

			//转换波
			if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "L2-Wave Data,Strong/Orignal/Light\n");
			else if (g_Developer.showOriginalWave) strcpy(strData, "L2-Wave Data,Strong/Orignal\n");
			else if (g_Developer.showFilteredCalcWave) strcpy(strData, "L2-Wave Data,Strong/Light\n");
			else strcpy(strData, "L2-Wave Data\n");
			if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
			sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.DP_wave2.waveBgn_pt, (g_Wave.DP_wave2.waveEnd_pt - g_Wave.DP_wave2.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //C波形起点、长度
			Save_int16(&fnew, g_Wave.DP_wave2.waveFirO2_buf, (g_Wave.DP_wave2.waveEnd_pt - g_Wave.DP_wave2.waveBgn_pt), isSecret); //强滤波波形
			if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.DP_wave2.waveSrc_buf, (g_Wave.DP_wave2.waveEnd_pt - g_Wave.DP_wave2.waveBgn_pt), isSecret); //原始波形
			if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.DP_wave2.waveFirO1_buf, (g_Wave.DP_wave2.waveEnd_pt - g_Wave.DP_wave2.waveBgn_pt), isSecret); //轻滤波波形

			//横波1
			if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "S1-Wave Data,Strong/Orignal/Light\n");
			else if (g_Developer.showOriginalWave) strcpy(strData, "S1-Wave Data,Strong/Orignal\n");
			else if (g_Developer.showFilteredCalcWave) strcpy(strData, "S1-Wave Data,Strong/Light\n");
			else strcpy(strData, "S1-Wave Data\n");
			if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
			sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.DP_wave3.waveBgn_pt, (g_Wave.DP_wave3.waveEnd_pt - g_Wave.DP_wave3.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //S1波形起点、长度
			Save_int16(&fnew, g_Wave.DP_wave3.waveFirO2_buf, (g_Wave.DP_wave3.waveEnd_pt - g_Wave.DP_wave3.waveBgn_pt), isSecret); //强滤波波形
			if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.DP_wave3.waveSrc_buf, (g_Wave.DP_wave3.waveEnd_pt - g_Wave.DP_wave3.waveBgn_pt), isSecret); //原始波形
			if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.DP_wave3.waveFirO1_buf, (g_Wave.DP_wave3.waveEnd_pt - g_Wave.DP_wave3.waveBgn_pt), isSecret); //轻滤波波形

			//横波2
			if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "S2-Wave Data,Strong/Orignal/Light\n");
			else if (g_Developer.showOriginalWave) strcpy(strData, "S2-Wave Data,Strong/Orignal\n");
			else if (g_Developer.showFilteredCalcWave) strcpy(strData, "S2-Wave Data,Strong/Light\n");
			else strcpy(strData, "S2-Wave Data\n");
			if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
			sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.DP_wave4.waveBgn_pt, (g_Wave.DP_wave4.waveEnd_pt - g_Wave.DP_wave4.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
			res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //S2波形起点、长度
			Save_int16(&fnew, g_Wave.DP_wave4.waveFirO2_buf, (g_Wave.DP_wave4.waveEnd_pt - g_Wave.DP_wave4.waveBgn_pt), isSecret); //强滤波波形
			if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.DP_wave4.waveSrc_buf, (g_Wave.DP_wave4.waveEnd_pt - g_Wave.DP_wave4.waveBgn_pt), isSecret); //原始波形
			if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.DP_wave4.waveFirO1_buf, (g_Wave.DP_wave4.waveEnd_pt - g_Wave.DP_wave4.waveBgn_pt), isSecret); //轻滤波波形
		}
		f_close(&fnew);
	}

	myfree(SRAMIN, pPath);//释放存储
	f_close(&fnew);
	return 0;
}

/**
  * @brief  读取文件波形、起点时间、时间长度、零点、温度
  * @note
  * @param  None
  * @retval None
  */
//static uint8_t File_Cali_ReadPointData(char* path, int16_t* buf1, uint16_t len1, int16_t* buf2, uint16_t len2, int32_t* start_ns, int32_t* len_ns, int32_t* zero_ns,
//	float* temperature, float* gain, float* tof_L, float* tof_S)
static uint8_t File_Cali_ReadPointData(char* path, WAVE_DATA_T *waveData_L, WAVE_DATA_T *waveData_S, float* temperature)
{
	FRESULT res;
	FIL file;
	uint16_t i = 0;
	char* str, * p, * p1;
	uint8_t isSecret = 0;

	str = (char*)mymalloc(SRAMEX, 250);//申请内存
//	str = tstbuf;

	//读取第1行---文件标识 fileType:PS_WAVE_S1
	//正向读取、解密读取 以判断是否为加密文件
	if (fat_readLineString_fromFile(path, str, 200, 1) == 0) {
		//解析 fileType:PS_WAVE_S1
		if (strncmp(FILE_ID_STRING_CALI, str, strlen(FILE_ID_STRING_CALI)) == 0) {
			//吻合、未加密
			isSecret = 0;
		}
		else {
			DecryptionString(str, 0);
			if (strncmp(FILE_ID_STRING_CALI, str, strlen(FILE_ID_STRING_CALI)) == 0) {
				//吻合、已加密
				isSecret = 1;
			}
			else {
				//不吻合，退出
				myfree(SRAMEX, str);//释放存储
				return 1; //
			}
		}
	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //
	}

	//读取第2行
	if (fat_readLineString_fromFile(path, str, 250, 2) == 0) {
		if (isSecret) DecryptionString(str, 0); //解密字符串
		//解析零点, 解析Zero:xxxns
		p = strstr(str, "Zero:") + 5;
		p1 = strchr(p, 'n'); *p1 = 0x00;
		waveData_L->zero_ns = atoi(p); *p1 = 'n';
		//采样频率, 解析fs:xxxMHz
		p = strstr(str, "fs:") + 5;
		p1 = strchr(p, 'M'); *p1 = 0x00;
		waveData_L->fs_MHz = atoi(p); *p1 = 'M';
		//解析增益, Gain:xxxdB
		p = strstr(str, "Gain:") + 5;
		p1 = strchr(p, 'd'); *p1 = 0x00;
		waveData_L->gain = atof(p); *p1 = 'd';
		//TOF-L:xxxns
		p = strstr(str, "TOF-L:") + 6;
		p1 = strchr(p, 'n'); *p1 = 0x00;
		waveData_L->tof_ns = atof(p); *p1 = 'n';
		//TOF-S:xxxns
		p = strstr(str, "TOF-S:") + 6;
		p1 = strchr(p, 'n'); *p1 = 0x00;
		waveData_S->tof_ns = atof(p); *p1 = 'n';

	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //读取失败
	}

	//读取第3行---波形起点时间、波形时间长度
	if (fat_readLineString_fromFile(path, str, 200, 3) == 0) {
		if (isSecret) DecryptionString(str, 0);
		//温度
		p = strstr(str, "Temp:") + 5;
		p1 = strchr(p, 'C'); *p1 = 0x00;
		*temperature = atof(p); *p1 = 'C';
//		//波形起点
//		p = strstr(str, "Len:") + 4;
//		p1 = strchr(p, 'n'); *p1 = 0x00;
//		*len_ns = atoi(p); *p1 = 'n';
//		//波形长度
//		p = strstr(str, "Begin:") + 6;
//		p1 = strchr(p, 'n'); *p1 = 0x00;
//		*start_ns = atoi(p); *p1 = 'n';
	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //读取起点、时间长度失败
	}

	//读取波形
	res = f_open(&file, path, FA_READ);
	if (res == FR_NO_FILE) {
		myfree(SRAMEX, str);//释放存储
		return 1;
	}
	else if (res == FR_OK) {
		res = f_lseek(&file, 0);
		for (i = 0; i < 10; i++) {
			f_gets(str, 200, &file);
			if (isSecret) DecryptionString(str, 0);
			if (strncmp("L-Wave Data", str, strlen("L-Wave Data")) == 0)
				break;
		}
		//读取L
		f_gets(str, 200, &file); //跳过 Begin...End...
		for (i = 0; i < waveData_L->bufLen; i++) {
			f_gets(str, 200, &file);
			if (isSecret) DecryptionString(str, 0);
			waveData_L->pBuf[i] = atoi(str);
			if (f_eof(&file) != 0) { //文件读完
				break;
			}
		}
		for (; i < waveData_L->bufLen; i++) {
			waveData_L->pBuf[i] = 0;
		}

		f_close(&file);
		myfree(SRAMEX, str);//释放存储
		return 0;
	}
	myfree(SRAMEX, str);//释放存储
	return 1;
}

/**
  * @brief  读取文件波形、起点时间、时间长度、零点、温度
  * @note
  * @param  *path:完整路径(包括文件名)
  * @retval 0:OK; 1:failed;
  */
uint8_t File_Cali_ReadPointData_TOF(char* path, float* tof_L, float* tof_S)
{
	float temperature;
	WAVE_DATA_T waveData_L, waveData_S;
	waveData_L.bufLen = 0;
	waveData_S.bufLen = 0;

	if (File_Cali_ReadPointData(path, &waveData_L, &waveData_S, &temperature) == 0) {
		*tof_L = waveData_L.tof_ns;
		*tof_S = waveData_S.tof_ns;
		return 0;
	}
	return 1;
}


/**
  * @brief  校准(标定)数据保存：参数、波形
  * @param  None
  * @retval None
  */
static uint8_t SaveResParas_Secret(FIL* file, uint8_t isSecret) {
	FRESULT res;
	char strData[60] = "";
	uint32_t fs_ns = 1000 / g_Daq.fs_MHz;
	char* str;
	uint8_t isDoubleProbe = Probe_isDoubleProbes();

	str = (char*)mymalloc(SRAMEX, 500);//申请内存

	//Line1---写入文件类型编号; "FileId:PS_WAVE_S1"单波波形文件
	sprintf(str, "%s\n", FILE_ID_STRING_LIB_BOLT); if (isSecret) EncryptionString(str, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, str, strlen(str), &bw);

	//Line2---写入参数
	GetTime_yymmdd_hhmmss(strData);
	sprintf(str, "Date:%s,", strData);//写入时间
	sprintf(strData, "SN:%s,", g_SysSet.DevSN); 				strcat(str, strData);//设备ID
	sprintf(strData, "firmWare:%s,", FIRMWARE_VERSION); 		strcat(str, strData);//软件版本
	sprintf(strData, "ID:%d,", g_Developer.diffCode); 			strcat(str, strData);//写入编号

	sprintf(strData, "fe:%.1fMHz,", g_Emat.EmatFreqMHz); 		strcat(str, strData);//发射频率
	sprintf(strData, "PNum:%d,", g_Emat.PulNum); 				strcat(str, strData);//发射脉冲数
	sprintf(strData, "fs:%dMHz,", g_Daq.fs_MHz); 				strcat(str, strData);//采样频率
	sprintf(strData, "Zero:%dns,", g_Cali.zeroStored_ns); 		strcat(str, strData);//零点,ns

	sprintf(strData, "Coef_K:%.1f,", Cali_GetPrjRes_K()); 			strcat(str, strData);
	sprintf(strData, "Coef_R0:%.5f,", Cali_GetPrjRes_R0()); 		strcat(str, strData);
	sprintf(strData, "Coef_TL0:%.1f,", Cali_GetPrjCoef()->TL0); 	strcat(str, strData);
	sprintf(strData, "Coef_TS0:%.1f,", Cali_GetPrjCoef()->TS0); 	strcat(str, strData);
	sprintf(strData, "Coef_KFL:%.10f,", Cali_GetPrjCoef()->KFL); 	strcat(str, strData);
	sprintf(strData, "Coef_KFS:%.10f,", Cali_GetPrjCoef()->KFS); 	strcat(str, strData);
	sprintf(strData, "Coef_KFDL:%.5f,", Cali_GetPrjCoef()->KFDL); 	strcat(str, strData);
	sprintf(strData, "Coef_KFDS:%.5f,\n", Cali_GetPrjCoef()->KFDS); strcat(str, strData);

	if (isSecret) EncryptionString(str, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, str, strlen(str), &bw);

	//Line3---波形起点,ns、 波形长度,ns (已去除盲区)
	sprintf(strData, "Begin:%dns,Len:%dns,Temp:%.2fC\n",
			(g_Wave.SP_wave3.waveBgn_pt * fs_ns - g_Cali.zeroStored_ns), (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt) * fs_ns, Temp_GetCurrUsedTemperature());

	if (isSecret) EncryptionString(strData, 0);
	res = f_lseek(file, f_size(file)); res = f_write(file, strData, strlen(strData), &bw);
//	//Line4---波形起点标志
//	sprintf(strData, "Wave_Data\n"); if (isSecret) EncryptionString(strData, 0);
//	res = f_lseek(file, f_size(file)); res = f_write(file, strData, strlen(strData), &bw);

	myfree(SRAMEX, str);//释放存储
	return 0;
}
/**
  * @brief  校准(标定)数据保存：标定数据
  * @param  None
  * @retval None
  */
static uint8_t SaveResParas_PointsData_Secret(FIL* file, CALI_POINT_T *pts[], U8 ptsdepth, U8 ptsmax, uint8_t isSecret) {
	FRESULT res;
	char strData[60] = "";
	uint8_t i, j;
	uint32_t fs_ns = 1000 / g_Daq.fs_MHz;
	char* str;
	uint8_t isDoubleProbe = Probe_isDoubleProbes();

	str = (char*)mymalloc(SRAMEX, 500);//申请内存
	
	for(i = 0; i < ptsdepth; i++) {
		sprintf(str, "BTP_%02d:", i+1); //写入标识
		for(j = 0; j < ptsmax; j++) {
			if((pts[i] + j)->state == PT_STATE_GAUGE_DONE) {
				sprintf(strData, "%.1f/%.1f/%.1f,", (pts[i] + j)->stressVal, (pts[i] + j)->TOF_L_ns, (pts[i] + j)->TOF_S_ns); strcat(str, strData);
			}
		}
		strcat(str, "\n");
		if (isSecret) EncryptionString(str, 0);
		res = f_lseek(file, f_size(file)); res = f_write(file, str, strlen(str), &bw);
	}

	myfree(SRAMEX, str);//释放存储
	return 0;
}
/**
  * @brief  多波保存波形--纵波、转换波、横波1、横波2
  * @param  None
  * @retval None
  */
uint8_t File_Cali_SaveResData(char* path, char* filename, CALI_POINT_T *pts[], U8 ptsdepth, U8 ptsmax, uint8_t isSecret)
{
	FRESULT res;
	FIL fnew;
	u32 total, free;
	char* pPath;
	char strData[40] = "";
	uint16_t i = 0;

	pPath = (char*)mymalloc(SRAMIN, strlen(path) + strlen(filename) + 5);//申请内存, 比文件大一些,以在末尾存放0x00
	sprintf(pPath, "%s/%s", path, filename);

	res = f_open(&fnew, pPath, FA_WRITE);
	if (res == FR_OK) {
		if (0 == fat_del_folder(pPath)) { //删除成功

		}
	}
	res = f_open(&fnew, pPath, FA_WRITE|FA_CREATE_ALWAYS);
	if(res == FR_NO_PATH) {
		if(f_mkfullpath(path) == 0) {
			res = f_open(&fnew, pPath, FA_WRITE|FA_CREATE_ALWAYS);
		}
	}

	if (res == FR_NO_FILE) {
		res = f_open(&fnew, pPath, FA_CREATE_NEW | FA_WRITE);
	}
	if (res == FR_OK) {
		//写入参数
		SaveResParas_Secret(&fnew, isSecret); //写入参数
		//写入标定数据点
		SaveResParas_PointsData_Secret(&fnew, pts, ptsdepth, ptsmax, isSecret);
		
		//写入波形标识
		sprintf(strData, "Wave_Data\n"); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(file, f_size(file)); res = f_write(file, strData, strlen(strData), &bw);
		
		//纵波
		if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "L-Wave Data,Strong/Orignal/Light\n");
		else if (g_Developer.showOriginalWave) strcpy(strData, "L-Wave Data,Strong/Orignal\n");
		else if (g_Developer.showFilteredCalcWave) strcpy(strData, "L-Wave Data,Strong/Light\n");
		else strcpy(strData, "L-Wave Data\n");
		if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //L波形说明
		sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.SP_wave1.waveBgn_pt, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //L波形起点、长度

		Save_int16(&fnew, g_Wave.SP_wave1.waveFirO2_buf, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt), isSecret); //强滤波波形
		if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.SP_wave1.waveSrc_buf, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt), isSecret); //原始波形
		if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.SP_wave1.waveFirO1_buf, (g_Wave.SP_wave1.waveEnd_pt - g_Wave.SP_wave1.waveBgn_pt), isSecret); //轻滤波波形

		//横波1
		if (g_Developer.showOriginalWave && g_Developer.showFilteredCalcWave) strcpy(strData, "S1-Wave Data,Strong/Orignal/Light\n");
		else if (g_Developer.showOriginalWave) strcpy(strData, "S1-Wave Data,Strong/Orignal\n");
		else if (g_Developer.showFilteredCalcWave) strcpy(strData, "S1-Wave Data,Strong/Light\n");
		else strcpy(strData, "S1-Wave Data\n");
		if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw);
		sprintf(strData, "Begin:%d, Len:%d\n", g_Wave.SP_wave3.waveBgn_pt, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt)); if (isSecret) EncryptionString(strData, 0);
		res = f_lseek(&fnew, f_size(&fnew)); res = f_write(&fnew, strData, strlen(strData), &bw); //S1波形起点、长度
		Save_int16(&fnew, g_Wave.SP_wave3.waveFirO2_buf, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt), isSecret); //强滤波波形
		if (g_Developer.showOriginalWave) 	Save_int16(&fnew, g_Wave.SP_wave3.waveSrc_buf, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt), isSecret); //原始波形
		if (g_Developer.showFilteredCalcWave) 	Save_int16(&fnew, g_Wave.SP_wave3.waveFirO1_buf, (g_Wave.SP_wave3.waveEnd_pt - g_Wave.SP_wave3.waveBgn_pt), isSecret); //轻滤波波形

		f_close(&fnew);
	}

	myfree(SRAMIN, pPath);//释放存储
	f_close(&fnew);
	return 0;
}

/**
  * @brief  读取文件波形、起点时间、时间长度、零点、温度
  * @note
  * @param  None
  * @retval None
  */
static uint8_t File_Cali_ReadResData(char* path, int16_t* wavebuf, uint16_t wavelen, int32_t* zero_ns, float* temperature, BOLT_COEF_T *coef, CALI_POINT_T *pts, uint8_t ptsnum, uint8_t ptsidx)
{
	FRESULT res;
	FIL file;
	uint16_t i = 0;
	char* str, * p, * p1;
	uint8_t isSecret = 0;

	str = (char*)mymalloc(SRAMEX, 500);//申请内存

	//读取第1行---文件标识 fileType:PS_WAVE_S1
	//正向读取、解密读取 以判断是否为加密文件
	if (fat_readLineString_fromFile(path, str, 500, 1) == 0) {
		//解析 fileType:PS_WAVE_S1
		if (strncmp(FILE_ID_STRING_LIB_BOLT, str, strlen(FILE_ID_STRING_LIB_BOLT)) == 0) {
			//吻合、未加密
			isSecret = 0;
		}
		else {
			DecryptionString(str, 0);
			if (strncmp(FILE_ID_STRING_LIB_BOLT, str, strlen(FILE_ID_STRING_LIB_BOLT)) == 0) {
				//吻合、已加密
				isSecret = 1;
			}
			else {
				//不吻合，退出
				myfree(SRAMEX, str);//释放存储
				return 1; //
			}
		}
	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //
	}

	//读取第2行
	if (fat_readLineString_fromFile(path, str, 500, 2) == 0) {
		if (isSecret) DecryptionString(str, 0); //解密字符串
		//解析零点, 解析Zero:xxxns
		p = strstr(str, "Zero:") + 5;
		p1 = strchr(p, 'n'); *p1 = 0x00;
		*zero_ns = atoi(p); *p1 = 'n';
		//解析K
		p = strstr(str, "Coef_K:") + 7;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->K = atof(p); *p1 = ',';
		//解析R0
		p = strstr(str, "Coef_R0:") + 8;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->R0 = atof(p); *p1 = ',';
		//解析TL0
		p = strstr(str, "Coef_TL0:") + 9;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->TL0 = atof(p); *p1 = ',';
		//解析TS0
		p = strstr(str, "Coef_TS0:") + 9;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->TS0 = atof(p); *p1 = ',';
		//解析KFL
		p = strstr(str, "Coef_KFL:") + 9;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->KFL = atof(p); *p1 = ',';
		//解析KFS
		p = strstr(str, "Coef_KFS:") + 9;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->KFS = atof(p); *p1 = ',';
		//解析KFDL
		p = strstr(str, "Coef_KFDL:") + 10;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->KFDL = atof(p); *p1 = ',';
		//解析KFDS
		p = strstr(str, "Coef_KFDS:") + 10;
		p1 = strchr(p, ','); *p1 = 0x00;
		coef->KFDS = atof(p); *p1 = ',';
	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //读取失败
	}

	//读取第3行---波形起点时间、波形时间长度
	if (fat_readLineString_fromFile(path, str, 500, 3) == 0) {
		if (isSecret) DecryptionString(str, 0);
		//温度
		p = strstr(str, "Temp:") + 5;
		p1 = strchr(p, 'C'); *p1 = 0x00;
		*temperature = atof(p); *p1 = 'C';
	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //读取起点、时间长度失败
	}

	//读取标定数据表
	uint8_t lineoffset = 5;
	if (fat_readLineString_fromFile(path, str, 500, lineoffset + ptsidx - 1) == 0) {
		if (strncmp("BTP_", str, strlen("BTP_")) == 0) {
			p = strchr(str, ':') + 1;
			for(i = 0; i < ptsnum; i++) {
				pts[i].state = PT_STATE_GAUGE_DONE;
				p1 = strchr(p, '/'); *p1 = 0x00; p1++;
				pts[i].stressVal = atof(p); p = p1;
				p1 = strchr(p, '/'); *p1 = 0x00; p1++;
				pts[i].TOF_L_ns = atof(p); p = p1;
				p1 = strchr(p, ','); *p1 = 0x00; p1++;
				pts[i].TOF_S_ns = atof(p); p = p1;
				if(strchr(p, '/') == NULL) {
					i++;
					break;
				}
			}
			for(; i < ptsnum; i++) {
				pts[i].state = PT_STATE_UNUSE;
			}
		}
	}
	else {
		myfree(SRAMEX, str);//释放存储
		return 1; //读取起点、时间长度失败
	}
	
	
	//读取波形
	res = f_open(&file, path, FA_READ);

	if (res == FR_NO_FILE) {
		myfree(SRAMEX, str);//释放存储
		return 1;
	}
	else if (res == FR_OK) {
		res = f_lseek(&file, 0);
		for (i = 0; i < 10; i++) {
			f_gets(str, 200, &file);
			if (isSecret) DecryptionString(str, 0);
			if (strncmp("Wave_Data", str, strlen("Wave_Data")) == 0)
				break;
		}
		for (i = 0; i < wavelen; i++) {
			f_gets(str, 200, &file);
			if (isSecret) DecryptionString(str, 0);
			wavebuf[i] = atoi(str);
			if (f_eof(&file) != 0) { //文件读完
				break;
			}
		}
		for (; i < wavelen; i++) {
			wavebuf[i] = 0;
		}

		f_close(&file);
		myfree(SRAMEX, str);//释放存储
		return 0;
	}
	myfree(SRAMEX, str);//释放存储
	return 1;
}

/**
  * @brief  读取文件波形、起点时间、时间长度、零点、温度
  * @note
  * @param  *path:完整路径(包括文件名)
  * @retval 0:OK; 1:failed;
  */
uint8_t File_Cali_ReadResData_Coef(char* path, BOLT_COEF_T *coef)
{
	int16_t buf;
	int32_t zero_ns;
	float temperature, gain;
	CALI_POINT_T *pts; 
	uint8_t ptsnum = 0;
	uint8_t ptsidx = 1;
	
	if (File_Cali_ReadResData(path, &buf, 0, &zero_ns, &temperature, coef, pts, ptsnum, ptsidx) == 0) {
		return 0;
	}
	return 1;
}


/**
  * @brief  读取单颗螺栓的标定数据表
  * @note
  * @param  *path:完整路径(包括文件名)
  * @retval 0:OK; 1:failed;
  */
uint8_t File_Cali_ReadCaliPtsTable(char* path, CALI_POINT_T *pts, uint8_t ptsnum, uint8_t ptsidx)
{
	int16_t buf;
	int32_t zero_ns;
	float temperature, gain;
	BOLT_COEF_T coef;
	
	if (File_Cali_ReadResData(path, &buf, 0, &zero_ns, &temperature, &coef, pts, ptsnum, ptsidx) == 0) {
		return 0;
	}
	return 1;
}








