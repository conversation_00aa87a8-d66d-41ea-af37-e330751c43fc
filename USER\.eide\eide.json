{"name": "HSACUT_V2024", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "USER", "files": [{"path": "main.c"}, {"path": "stm32f7xx_it.c"}, {"path": "system_stm32f7xx.c"}], "folders": []}, {"name": "CORE", "files": [{"path": "../CORE/core_cm7.h"}, {"path": "../CORE/core_cmFunc.h"}, {"path": "../CORE/core_cmInstr.h"}, {"path": "../CORE/core_cmSimd.h"}, {"path": "../CORE/startup_stm32f767xx.s"}], "folders": []}, {"name": "HALLIB", "files": [{"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_cortex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_gpio.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_pwr_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rcc_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_uart.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_usart.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_fmc.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sdram.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dma2d.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_ltdc_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sram.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_tim.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_tim_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_rtc_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dac.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_dac_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_qspi.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_ll_sdmmc.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_sd.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_flash_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_adc.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_adc_ex.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_nand.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_iwdg.c"}, {"path": "../HALLIB/STM32F7xx_HAL_Driver/Src/stm32f7xx_hal_eth.c"}], "folders": []}, {"name": "MALLOC", "files": [{"path": "malloc/malloc.c"}], "folders": []}, {"name": "DSP_LIB", "files": [{"path": "../DSP_LIB/arm_cortexM7lfsp_math.lib"}, {"path": "../DSP_LIB/arm_cortexM7b_math.lib"}, {"path": "../DSP_LIB/arm_cortexM7bfdp_math.lib"}, {"path": "../DSP_LIB/arm_cortexM7bfsp_math.lib"}, {"path": "../DSP_LIB/arm_cortexM7l_math.lib"}, {"path": "../DSP_LIB/arm_cortexM7lfdp_math.lib"}], "folders": []}, {"name": "EMWIN_LIB", "files": [{"path": "../EMWIN/STemWin/Lib/STemWin540_CM7_Keil.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin532_CM7_Keil.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin532_CM7_Keil_ot.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin540_CM7_Keil_ot.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin540_CM7_OS_Keil.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin540_CM7_OS_Keil_ot.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin532_CM7_OS_Keil.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin532_CM7_OS_Keil_ot.lib"}, {"path": "../EMWIN/STemWin/Lib/STemWin_CM7_OS_wc16.a"}, {"path": "../EMWIN/STemWin/Lib/STemWin_CM7_wc16.a"}, {"path": "../EMWIN/STemWin/Lib/GUI_CM4F_L.lib"}], "folders": []}, {"name": "EMWIN_CONFIG", "files": [{"path": "../EMWIN/STemWin/OS/GUI_X.c"}, {"path": "../EMWIN/STemWin/Config/GUIConf.c"}, {"path": "../EMWIN/STemWin/Config/GUIDRV_Template.c"}, {"path": "../EMWIN/STemWin/Config/GUI_X_Touch_Analog.c"}, {"path": "../EMWIN/STemWin/Config/LCDConf_Lin_Template.c"}, {"path": "../EMWIN/STemWin/Config/LCDConf_FlexColor_Template.c"}, {"path": "../EMWIN/STemWin/OS/GUI_X_UCOSIII.c"}], "folders": []}, {"name": "UCOSIII_BSP", "files": [{"path": "../UCOSIII/UCOS-BSP/bsp_os.c"}], "folders": []}, {"name": "UCOSIII_CPU", "files": [{"path": "../UCOSIII/uC-CPU/cpu_core.c"}, {"path": "../UCOSIII/uC-CPU/ARM-Cortex-M4/RealView/cpu_a.asm"}, {"path": "../UCOSIII/uC-CPU/ARM-Cortex-M4/RealView/cpu_c.c"}], "folders": []}, {"name": "UCOSIII_LIB", "files": [{"path": "../UCOSIII/uC-LIB/lib_ascii.c"}, {"path": "../UCOSIII/uC-LIB/lib_math.c"}, {"path": "../UCOSIII/uC-LIB/lib_mem.c"}, {"path": "../UCOSIII/uC-LIB/lib_str.c"}, {"path": "../UCOSIII/uC-LIB/Ports/ARM-Cortex-M4/RealView/lib_mem_a.asm"}], "folders": []}, {"name": "UCOSIII_CORE", "files": [{"path": "../UCOSIII/uCOS-III/Source/os_cfg_app.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_core.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_dbg.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_flag.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_int.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_mem.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_msg.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_mutex.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_pend_multi.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_prio.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_q.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_sem.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_stat.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_task.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_tick.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_time.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_tmr.c"}, {"path": "../UCOSIII/uCOS-III/Source/os_var.c"}], "folders": []}, {"name": "UCOSIII_PORT", "files": [{"path": "../UCOSIII/uCOS-III/Ports/ARM-Cortex-M4/Generic/RealView/os_cpu.h"}, {"path": "../UCOSIII/uCOS-III/Ports/ARM-Cortex-M4/Generic/RealView/os_cpu_a.asm"}, {"path": "../UCOSIII/uCOS-III/Ports/ARM-Cortex-M4/Generic/RealView/os_cpu_c.c"}], "folders": []}, {"name": "UCOSIII_CONFIG", "files": [{"path": "../UCOSIII/UCOS-CONFIG/app_cfg.h"}, {"path": "../UCOSIII/UCOS-CONFIG/cpu_cfg.h"}, {"path": "../UCOSIII/UCOS-CONFIG/includes.h"}, {"path": "../UCOSIII/UCOS-CONFIG/lib_cfg.h"}, {"path": "../UCOSIII/UCOS-CONFIG/os_app_hooks.c"}, {"path": "../UCOSIII/UCOS-CONFIG/os_app_hooks.h"}, {"path": "../UCOSIII/UCOS-CONFIG/os_cfg.h"}, {"path": "../UCOSIII/UCOS-CONFIG/os_cfg_app.h"}], "folders": []}, {"name": "FATFS", "files": [{"path": "../FATFS/src/ff.c"}, {"path": "../FATFS/src/diskio.c"}, {"path": "../FATFS/src/option/cc936.c"}, {"path": "../FATFS/exfuns/exfuns.c"}, {"path": "../FATFS/exfuns/fattester.c"}], "folders": []}, {"name": "USB_OTG", "files": [{"path": "../USB/STM32_USB_OTG_Driver/src/usb_core.c"}, {"path": "../USB/STM32_USB_OTG_Driver/src/usb_dcd.c"}, {"path": "../USB/STM32_USB_OTG_Driver/src/usb_dcd_int.c"}], "folders": []}, {"name": "USB_DEVICE", "files": [{"path": "../USB/STM32_USB_Device_Library/Core/src/usbd_core.c"}, {"path": "../USB/STM32_USB_Device_Library/Core/src/usbd_ioreq.c"}, {"path": "../USB/STM32_USB_Device_Library/Core/src/usbd_req.c"}, {"path": "../USB/STM32_USB_Device_Library/Class/msc/src/usbd_msc_bot.c"}, {"path": "../USB/STM32_USB_Device_Library/Class/msc/src/usbd_msc_core.c"}, {"path": "../USB/STM32_USB_Device_Library/Class/msc/src/usbd_msc_data.c"}, {"path": "../USB/STM32_USB_Device_Library/Class/msc/src/usbd_msc_scsi.c"}], "folders": []}, {"name": "USB_APP", "files": [{"path": "../USB/USB_APP/usb_bsp.c"}, {"path": "../USB/USB_APP/usbd_desc.c"}, {"path": "../USB/USB_APP/usbd_storage_msd.c"}, {"path": "../USB/USB_APP/usbd_usr.c"}], "folders": []}, {"name": "BSP", "files": [{"path": "bsp/bsp.c"}, {"path": "bsp/src/delay.c"}, {"path": "bsp/src/led.c"}, {"path": "bsp/src/sys.c"}, {"path": "bsp/src/usart.c"}, {"path": "bsp/src/24cxx.c"}, {"path": "bsp/src/utils.c"}, {"path": "bsp/src/ad5304.c"}, {"path": "bsp/src/ad9106.c"}, {"path": "bsp/src/iwdg.c"}, {"path": "bsp/src/qspi.c"}, {"path": "bsp/src/rtc_rx8010.c"}, {"path": "bsp/src/sdmmc_sdcard.c"}, {"path": "bsp/src/sdram.c"}, {"path": "bsp/src/timer.c"}, {"path": "bsp/src/daq.c"}, {"path": "bsp/src/emat.c"}, {"path": "bsp/src/fpga.c"}, {"path": "bsp/src/ad9258.c"}, {"path": "bsp/src/ds18b20.c"}, {"path": "bsp/src/dp83848.c"}, {"path": "bsp/src/mbcrc.c"}, {"path": "bsp/src/beep.c"}, {"path": "bsp/src/oled.c"}, {"path": "bsp/src/sgm5347.c"}, {"path": "bsp/src/mpu.c"}, {"path": "bsp/src/dev.c"}, {"path": "bsp/src/dac.c"}, {"path": "bsp/src/fmc.c"}, {"path": "bsp/src/ad9246.c"}, {"path": "bsp/src/afe.c"}, {"path": "bsp/src/ad9704.c"}, {"path": "bsp/src/fan.c"}], "folders": []}, {"name": "APP", "files": [{"path": "app/app.c"}, {"path": "app/src/comm.c"}], "folders": []}, {"name": "LWIP_APP", "files": [{"path": "../LWIP/lwip_app/src/lwip_comm.c"}, {"path": "../LWIP/lwip_app/src/netconn_tcp_server.c"}, {"path": "../LWIP/lwip_app/src/netconn_udp.c"}], "folders": []}, {"name": "LWIP_NETIF", "files": [{"path": "../LWIP/lwip-1.4.1/src/netif/ethernetif.c"}, {"path": "../LWIP/lwip-1.4.1/src/netif/etharp.c"}], "folders": []}, {"name": "LWIP_CORE", "files": [{"path": "../LWIP/lwip-1.4.1/src/core/ipv4/autoip.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/icmp.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/igmp.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/inet.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/inet_chksum.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/ip.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/ip_addr.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/ipv4/ip_frag.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/def.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/dhcp.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/dns.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/init.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/mem.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/memp.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/netif.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/pbuf.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/raw.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/stats.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/tcp.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/tcp_in.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/tcp_out.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/timers.c"}, {"path": "../LWIP/lwip-1.4.1/src/core/udp.c"}], "folders": []}, {"name": "LWIP_ARCH", "files": [{"path": "../LWIP/arch/sys_arch.c"}], "folders": []}, {"name": "LWIP_API", "files": [{"path": "../LWIP/lwip-1.4.1/src/api/api_lib.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/api_msg.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/err.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/netbuf.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/netdb.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/netifapi.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/sockets.c"}, {"path": "../LWIP/lwip-1.4.1/src/api/tcpip.c"}], "folders": []}, {"name": "::Data Exchange", "files": [{"path": "RTE/Data_Exchange/jansson_config.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "e0a8eac2b3c86847551de266e0e0c63d"}, "targets": {"HSACUT_V2024": {"excludeList": ["<virtual_root>/DSP_LIB/arm_cortexM7lfsp_math.lib", "<virtual_root>/DSP_LIB/arm_cortexM7b_math.lib", "<virtual_root>/DSP_LIB/arm_cortexM7bfdp_math.lib", "<virtual_root>/DSP_LIB/arm_cortexM7bfsp_math.lib", "<virtual_root>/DSP_LIB/arm_cortexM7l_math.lib", "<virtual_root>/EMWIN_LIB", "<virtual_root>/EMWIN_LIB/STemWin540_CM7_Keil.lib", "<virtual_root>/EMWIN_LIB/STemWin532_CM7_Keil.lib", "<virtual_root>/EMWIN_LIB/STemWin540_CM7_Keil_ot.lib", "<virtual_root>/EMWIN_LIB/STemWin540_CM7_OS_Keil.lib", "<virtual_root>/EMWIN_LIB/STemWin540_CM7_OS_Keil_ot.lib", "<virtual_root>/EMWIN_LIB/STemWin532_CM7_OS_Keil.lib", "<virtual_root>/EMWIN_LIB/STemWin532_CM7_OS_Keil_ot.lib", "<virtual_root>/EMWIN_LIB/STemWin_CM7_OS_wc16.a", "<virtual_root>/EMWIN_LIB/STemWin_CM7_wc16.a", "<virtual_root>/EMWIN_LIB/GUI_CM4F_L.lib", "<virtual_root>/EMWIN_CONFIG", "<virtual_root>/EMWIN_CONFIG/GUI_X.c", "<virtual_root>/EMWIN_CONFIG/GUIDRV_Template.c", "<virtual_root>/EMWIN_CONFIG/LCDConf_FlexColor_Template.c", "<virtual_root>/FATFS", "<virtual_root>/USB_OTG", "<virtual_root>/USB_DEVICE", "<virtual_root>/USB_APP", "<virtual_root>/BSP/ad5304.c", "<virtual_root>/BSP/qspi.c", "<virtual_root>/BSP/sdmmc_sdcard.c", "<virtual_root>/BSP/ad9258.c", "<virtual_root>/BSP/ds18b20.c", "<virtual_root>/BSP/beep.c", "<virtual_root>/BSP/oled.c", "<virtual_root>/::Data Exchange"], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M7", "floatingPointHardware": "double", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20020000", "size": "0x60000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x20000000", "size": "0x20000"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x100000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x200000", "size": "0x100000"}, "isChecked": false, "isStartup": false}]}, "options": "null", "archExtensions": ""}, "uploader": "STLink", "uploadConfig": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/hsacut_v2024.st.option.bytes.ini", "otherCmds": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "ST", "cpuName": "STM32F767IG"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["../CORE", "../OBJ", ".", "../HALLIB/STM32F7xx_HAL_Driver/Inc", "../USMART", "malloc", "../DSP_LIB/Include", "../UCOSIII/uC-LIB", "../UCOSIII/uC-CPU", "../UCOSIII/UCOS-BSP", "../UCOSIII/UCOS-CONFIG", "../UCOSIII/uCOS-III/Source", "../UCOSIII/uC-CPU/ARM-Cortex-M4/RealView", "../UCOSIII/uC-LIB/Ports/ARM-Cortex-M4/RealView", "../UCOSIII/uCOS-III/Ports/ARM-Cortex-M4/Generic/RealView", "../FATFS/exfuns", "../FATFS/src", "../FATFS/src/option", "../USB/STM32_USB_Device_Library/Class/msc/inc", "../USB/STM32_USB_Device_Library/Core/inc", "../USB/STM32_USB_OTG_Driver/inc", "../USB/USB_APP", "bsp", "bsp/inc", "app", "app/inc", "../LWIP", "../LWIP/arch", "../LWIP/lwip-1.4.1/src/include", "../LWIP/lwip-1.4.1/src/include/ipv4", "../LWIP/lwip-1.4.1/src/include/netif", "../LWIP/lwip_app/inc", ".cmsis/include", "RTE/_HSACUT_V2024"], "libList": [], "defineList": ["STM32F767xx", "USE_HAL_DRIVER", "ARM_MATH_CM7", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING", "__FPU_PRESENT=1", "USE_USB_OTG_FS"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${KEIL_OUTPUT_NAME}.axf\"", "disable": true, "abortAfterFailed": true}, {"name": "D:\\Keil_v5\\ARM\\ARMCC\\bin\\fromelf.exe  --bin -o  ..\\OBJ\\ST100V4_1_GUI2.bin ..\\OBJ\\ST100V4_1_GUI2.axf", "command": "D:\\Keil_v5\\ARM\\ARMCC\\bin\\fromelf.exe  --bin -o  ..\\OBJ\\ST100V4_1_GUI2.bin ..\\OBJ\\ST100V4_1_GUI2.axf", "disable": true, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-2", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--locale=english", "CXX_FLAGS": "--locale=english", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20010000"}}}}}, "version": "3.6"}