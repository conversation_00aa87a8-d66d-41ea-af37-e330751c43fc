#include "bsp.h"

DAC_HandleTypeDef DAC1_Handler;//DAC句柄

//初始化DAC1
void DAC1_Init(void)
{
    DAC_ChannelConfTypeDef DACCH1_Config;
    
    DAC1_Handler.Instance=DAC;
    HAL_DAC_Init(&DAC1_Handler);                 //初始化DAC
    
    DACCH1_Config.DAC_Trigger=DAC_TRIGGER_NONE;             //不使用触发功能
    DACCH1_Config.DAC_OutputBuffer=DAC_OUTPUTBUFFER_DISABLE;//DAC1输出缓冲关闭
    HAL_DAC_ConfigChannel(&DAC1_Handler,&DACCH1_Config,DAC_CHANNEL_1);//DAC通道1配置
//	HAL_DAC_ConfigChannel(&DAC1_Handler,&DACCH1_Config,DAC_CHANNEL_2);//DAC通道2配置
    HAL_DAC_Start(&<PERSON><PERSON><PERSON>_Handler,DAC_CHANNEL_1);  //开启DAC通道1
//	HAL_DAC_Start(&<PERSON><PERSON><PERSON>_Hand<PERSON>,DAC_CHANNEL_2);  //开启DAC通道2
	
//	delay_ms(1000);
//	while(1) {
////		DAC1_Set_Vol(0);
////		DAC2_Set_Vol(0);
////		delay_ms(500);
////		DAC1_Set_Vol(500);
////		DAC2_Set_Vol(500);
////		delay_ms(500);
////		DAC1_Set_Vol(1250);
////		DAC2_Set_Vol(1250);
////		delay_ms(500);
////		DAC1_Set_Vol(3300);
////		DAC2_Set_Vol(3300);
//		HAL_DAC_SetValue(&DAC1_Handler,DAC_CHANNEL_1,DAC_ALIGN_12B_R, 0xfff);
//		HAL_DAC_SetValue(&DAC1_Handler,DAC_CHANNEL_2,DAC_ALIGN_12B_R, 0xfff);
//		HAL_DAC_Start(&DAC1_Handler,DAC_CHANNEL_1);  //开启DAC通道1
//		HAL_DAC_Start(&DAC1_Handler,DAC_CHANNEL_2);  //开启DAC通道2
//		delay_ms(500);
////		HAL_DAC_SetValue(&DAC1_Handler,DAC_CHANNEL_1,DAC_ALIGN_12B_R, 0xfff/2);
////		HAL_DAC_SetValue(&DAC1_Handler,DAC_CHANNEL_2,DAC_ALIGN_12B_R, 0xfff/2);
////		delay_ms(500);
////		HAL_DAC_Start(&DAC1_Handler,DAC_CHANNEL_1);  //开启DAC通道1
////		HAL_DAC_Start(&DAC1_Handler,DAC_CHANNEL_2);  //开启DAC通道2
//	}
}


//DAC底层驱动，时钟配置，引脚 配置
//此函数会被HAL_DAC_Init()调用
//hdac:DAC句柄
//PA4(DAC1):单脉冲高压; PA5(DAC2):多脉冲高压;
void HAL_DAC_MspInit(DAC_HandleTypeDef* hdac)
{
    GPIO_InitTypeDef GPIO_Initure;
	
    __HAL_RCC_DAC_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();
	
    GPIO_Initure.Pin=GPIO_PIN_4;
    GPIO_Initure.Mode=GPIO_MODE_ANALOG;
    GPIO_Initure.Pull=GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA,&GPIO_Initure);
}

//设置通道1输出电压
//vol:0~3300,代表0~3.3V
static void DAC1_SetVol(int vol)
{
    HAL_DAC_SetValue(&DAC1_Handler,DAC_CHANNEL_1,DAC_ALIGN_12B_R, vol*4095/2990); //HV_DA_REF_VOL);//12位右对齐数据格式设置DAC值
}

void DAC_SetDacVolByEmitVol(uint32_t emitVpp) {
	uint32_t voltage; // 目标增益所需电压,单位 mV

	if(emitVpp < HV1_Vpp_MIN) {
		emitVpp = HV1_Vpp_MIN;
	}
	else if(emitVpp > HV1_Vpp_MAX) {
		emitVpp = HV1_Vpp_MAX;
	}
	voltage = emitVpp * 2500 / (HV1_Vpp_MAX - HV1_Vpp_MIN);
	DAC1_SetVol(voltage);
}


