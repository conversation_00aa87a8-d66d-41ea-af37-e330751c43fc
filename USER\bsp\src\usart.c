////////////////////////////////////////////////////////////////////////////////// 	 
//如果使用os,则包括下面的头文件即可.

#include "bsp.h"
#include "usart.h"
#if SYSTEM_SUPPORT_OS
#include "includes.h"					//os 使用	  
#endif
//////////////////////////////////////////////////////////////////////////////////	 
	  
//加入以下代码,支持printf函数,而不需要选择use MicroLIB	  
//#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)	
#if 1
#pragma import(__use_no_semihosting)             
//标准库需要的支持函数                 
struct __FILE 
{ 
	int handle; 
}; 

FILE __stdout;       
//定义_sys_exit()以避免使用半主机模式    
void _sys_exit(int x) 
{ 
	x = x; 
} 
//重定义fputc函数 
int fputc(int ch, FILE *f)
{ 	
	while((USART2->ISR&0X40)==0);//循环发送,直到发送完毕   
	USART2->TDR=(u8)ch;      
	return ch;
}
#endif 

#define RXBUFFER_SIZE   	1 //缓存大小
#define UART1_RXBUF_LEN		128

#define RS485_DE(n)		(n ? HAL_GPIO_WritePin(GPIOH,GPIO_PIN_8,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOH,GPIO_PIN_8,GPIO_PIN_RESET))
#define RS485_RE(n)		(n ? HAL_GPIO_WritePin(GPIOH,GPIO_PIN_9,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOH,GPIO_PIN_9,GPIO_PIN_RESET))
#define RS485_RXEN		{RS485_RE(0); RS485_DE(0);}
#define RS485_TXEN		{RS485_RE(1); RS485_DE(1);}


//串口1中断服务程序
//注意,读取USARTx->SR能避免莫名其妙的错误   	
uint8_t uart1RxBuf[UART1_RXBUF_LEN];     //接收缓冲
uint32_t uart1RxBytesCnt;
//接收状态
//bit15，	接收完成标志
//bit14，	接收到0x0d
//bit13~0，	接收到的有效字节数目
u16 USART_RX_STA=0;       //接收状态标记	

u8 aRxBuffer[RXBUFFER_SIZE];//HAL库使用的串口接收缓冲
UART_HandleTypeDef UART1_Handler; //UART句柄
UART_HandleTypeDef UART7_Handler; //UART句柄
//bound:波特率
void Uart1_Init(u32 bound)
{
	//UART 初始化设置
	UART1_Handler.Instance=USART1;					    //USART1
	UART1_Handler.Init.BaudRate=bound;				    //波特率
	UART1_Handler.Init.WordLength=UART_WORDLENGTH_8B;   //字长为8位数据格式
	UART1_Handler.Init.StopBits=UART_STOPBITS_1;	    //一个停止位
	UART1_Handler.Init.Parity=UART_PARITY_NONE;		    //无奇偶校验位
	UART1_Handler.Init.HwFlowCtl=UART_HWCONTROL_NONE;   //无硬件流控
	UART1_Handler.Init.Mode=UART_MODE_TX_RX;		    //收发模式
	HAL_UART_Init(&UART1_Handler);					    //HAL_UART_Init()会使能UART1

	HAL_UART_Receive_IT(&UART1_Handler, (u8 *)aRxBuffer, RXBUFFER_SIZE);//该函数会开启接收中断：标志位UART_IT_RXNE，并且设置接收缓冲以及接收缓冲接收最大数据量
}

void Uart7_Init(u32 bound)
{
	//UART 初始化设置
	UART7_Handler.Instance = UART7;
	UART7_Handler.Init.BaudRate = bound;
	UART7_Handler.Init.WordLength = UART_WORDLENGTH_8B;
	UART7_Handler.Init.StopBits = UART_STOPBITS_1;
	UART7_Handler.Init.Parity = UART_PARITY_NONE;
	UART7_Handler.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	UART7_Handler.Init.Mode = UART_MODE_TX_RX;
	HAL_UART_Init(&UART7_Handler);

	HAL_UART_Receive_IT(&UART7_Handler, (u8*)aRxBuffer, RXBUFFER_SIZE); //该函数会开启接收中断：标志位UART_IT_RXNE，并且设置接收缓冲以及接收缓冲接收最大数据量
}

//UART底层初始化，时钟使能，引脚配置，中断配置
//此函数会被HAL_UART_Init()调用
//huart:串口句柄

void HAL_UART_MspInit(UART_HandleTypeDef *huart)
{
    //GPIO端口设置
	GPIO_InitTypeDef GPIO_Initure;
	
	if (huart->Instance == USART1)
	{
		__HAL_RCC_GPIOB_CLK_ENABLE();
		__HAL_RCC_USART1_CLK_ENABLE();

		GPIO_Initure.Pin = GPIO_PIN_14 | GPIO_PIN_15;
		GPIO_Initure.Mode = GPIO_MODE_AF_PP;
		GPIO_Initure.Pull = GPIO_PULLUP;
		GPIO_Initure.Speed = GPIO_SPEED_FAST;
		GPIO_Initure.Alternate = GPIO_AF7_USART1;
		HAL_GPIO_Init(GPIOA, &GPIO_Initure);

		HAL_NVIC_EnableIRQ(USART1_IRQn);
		HAL_NVIC_SetPriority(USART1_IRQn, 3, 3);

		//RS485 控制脚初始化, PH8--DE, PH9--REn
		__HAL_RCC_GPIOH_CLK_ENABLE();
		GPIO_Initure.Pin = GPIO_PIN_8 | GPIO_PIN_9;
		GPIO_Initure.Mode = GPIO_MODE_OUTPUT_PP;
		GPIO_Initure.Pull = GPIO_PULLUP;
		GPIO_Initure.Speed = GPIO_SPEED_HIGH;
		HAL_GPIO_Init(GPIOH, &GPIO_Initure);
		HAL_GPIO_WritePin(GPIOH, GPIO_PIN_8, GPIO_PIN_SET);
		HAL_GPIO_WritePin(GPIOH, GPIO_PIN_9, GPIO_PIN_RESET);
	}
	if (huart->Instance == UART7)
	{
		__HAL_RCC_GPIOF_CLK_ENABLE();
		__HAL_RCC_UART7_CLK_ENABLE();

		GPIO_Initure.Pin = GPIO_PIN_6 | GPIO_PIN_7;
		GPIO_Initure.Mode = GPIO_MODE_AF_PP;
		GPIO_Initure.Pull = GPIO_PULLUP;
		GPIO_Initure.Speed = GPIO_SPEED_FAST;
		GPIO_Initure.Alternate = GPIO_AF8_UART7;
		HAL_GPIO_Init(GPIOF, &GPIO_Initure);
	}
}

//接收完成处理回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance==USART1) //UART7 RS485
	{
		if(uart1RxBytesCnt == 0) {
			uart1RxBuf[uart1RxBytesCnt++] = aRxBuffer[0];
			StartRTUCheckTmr();
		}
		else if(uart1RxBytesCnt >= UART1_RXBUF_LEN){ //数据超长
			Modbus_Uart1RxDone();
		}
		else {
			ClearRTUCheckTmr();
			uart1RxBuf[uart1RxBytesCnt++] = aRxBuffer[0];
		}
	}
}

void Modbus_Uart1RxDone(void) {
	StopRTUCheckTmr();
	Comm_RxOneFrameDone(uart1RxBuf, uart1RxBytesCnt, COM_RS485); 
	uart1RxBytesCnt = 0;
}

//串口1中断服务程序
void USART1_IRQHandler(void)                	
{ 
	u32 timeout=0;
	u32 maxDelay=0x1FFFF;
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntEnter();    
	#endif

	HAL_UART_IRQHandler(&UART1_Handler);	//调用HAL库中断处理公用函数

	timeout=0;
	while (HAL_UART_GetState(&UART1_Handler)!=HAL_UART_STATE_READY)//等待就绪
	{
		timeout++;////超时处理
		if(timeout>maxDelay) break;		
	}

	timeout=0;
	while(HAL_UART_Receive_IT(&UART1_Handler,(u8 *)aRxBuffer, RXBUFFER_SIZE)!=HAL_OK)//一次处理完成之后，重新开启中断并设置RxXferCount为1
	{
		timeout++; //超时处理
		if(timeout>maxDelay) break;	
	}
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntExit();  											 
	#endif
} 

void UART7_IRQHandler(void)                	
{ 
	u32 timeout=0;
	u32 maxDelay=0x1FFFF;
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntEnter();    
	#endif

	HAL_UART_IRQHandler(&UART7_Handler);	//调用HAL库中断处理公用函数

	timeout=0;
	while (HAL_UART_GetState(&UART7_Handler)!=HAL_UART_STATE_READY)//等待就绪
	{
		timeout++;////超时处理
		if(timeout>maxDelay) break;		
	}

	timeout=0;
	while(HAL_UART_Receive_IT(&UART7_Handler,(u8 *)aRxBuffer, RXBUFFER_SIZE)!=HAL_OK)//一次处理完成之后，重新开启中断并设置RxXferCount为1
	{
		timeout++; //超时处理
		if(timeout>maxDelay) break;	
	}
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntExit();  											 
	#endif
} 

//RS485
void Uart1_SendData(uint8_t* dataBuf, uint32_t len) {
#if SYSTEM_SUPPORT_OS	 	//使用OS
	CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
	OSIntEnter();
#endif
	for (uint32_t i = 0; i < len; i++) {
		HAL_UART_Transmit(&UART1_Handler, (uint8_t*)(dataBuf + i), 1, 1000);
		while (__HAL_UART_GET_FLAG(&UART1_Handler, UART_FLAG_TC) != SET);		//等待发送结束
	}
#if SYSTEM_SUPPORT_OS
	OSIntExit();
#endif
}

//FPGA
void Uart7_SendData(uint8_t* dataBuf, uint32_t len) {
#if SYSTEM_SUPPORT_OS	 	//使用OS
	CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
	OSIntEnter();
#endif
	for (uint32_t i = 0; i < len; i++) {
		HAL_UART_Transmit(&UART7_Handler, (uint8_t*)(dataBuf + i), 1, 1000);
		while (__HAL_UART_GET_FLAG(&UART7_Handler, UART_FLAG_TC) != SET);		//等待发送结束
	}
#if SYSTEM_SUPPORT_OS
	OSIntExit();
#endif
}


//发送int16组成的数据
// void Send2BytesData2PC(int16_t *pData, uint32_t length) { 
// 	uint32_t i = 0;
// 	uint8_t ByteMsb, ByteLsb;
// 	#if SYSTEM_SUPPORT_OS	 	//使用OS
// 		CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
// 		OS_CRITICAL_ENTER(); 	//进入任务保护
// 	#endif
// 	RS485TXEN; //485发送使能
// 	for(i = 0; i < length; i++) {
// 		ByteMsb = (uint8_t)(pData[i] >> 8);
// 		ByteLsb = (uint8_t)(pData[i] & 0x00ff);
// 		HAL_UART_Transmit(&UART2_Handler, &ByteMsb, 1, 1000);
// 		while(__HAL_UART_GET_FLAG(&UART2_Handler,UART_FLAG_TC)!=SET);		//等待发送结束
// 		HAL_UART_Transmit(&UART2_Handler, &ByteLsb, 1, 1000);
// 		while(__HAL_UART_GET_FLAG(&UART2_Handler,UART_FLAG_TC)!=SET);		//等待发送结束
// 	}
// 	RS485RXEN; //485接收使能
// 	#if SYSTEM_SUPPORT_OS //使用OS
// 		OS_CRITICAL_EXIT(); //退出任务保护
// 	#endif
// }



void FpgaUart_SendDatas(uint8_t *dataBuf, uint32_t len) {
	Uart7_SendData(dataBuf, len);
}

void RS485_SendDatas(uint8_t *dataBuf, uint32_t len) {
	RS485_TXEN;
	Uart1_SendData(dataBuf, len);
	RS485_RXEN;
}



