/**
  *********************************************************************************************************
  * Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com 
  * @file 
  * @project ACUT_GCV1
  * <AUTHOR> YL
  * @date    
  * @version v1.0.0
  * @brief   
  * @modify
  * 2021-11-10：
  *   1) 增加 调频、峰值保持 指令;
  *   2) 修改高压指令; 修改启停指令增加chirp模式;
  *********************************************************************************************************
  * @attention
  ******************************************************************************
  */
#include "includes.h"
#include "comm.h"

COM_T g_Com;
static uint8_t needCRC16 = 1;	//启用CRC16校验
static uint8_t databuf[2048 + 5 + TXWAVE_EXTRA_DATA_LEN];
static uint32_t dataSize, pkgSize = 1400, currPkgSize;
static uint8_t *pbuf;
static uint32_t pkgNum;
void Comm_SendWaveData(uint8_t commtype, uint8_t *pExtraBuf, uint8_t extra_len, int16_t *pWaveBuf, uint16_t wave_len) {
//	uint8_t pkgNum, tmp[2048 + 5];
	uint32_t i, j, k, n;
//	uint16_t dataSize, pkgSize = 1000, currPkgSize;
	static uint32_t pkgId = 0;

	pbuf = databuf + 10; //databuf
	dataSize = extra_len + 2 * wave_len;
	pkgNum = (dataSize % pkgSize) ? (dataSize / pkgSize + 1 ) : (dataSize / pkgSize);
	
	pkgId++;
	for(i= 0, n = 0; i < pkgNum; i++) {
		currPkgSize = ((i + 1) < pkgNum) ? pkgSize : (dataSize - i * pkgSize);
		//帧头赋值
		pbuf[0] = pExtraBuf[0]; //CH
		pbuf[1] = pkgId; 		//包ID
		pbuf[2] = pkgNum; 		//分包总数
		pbuf[3] = i + 1; 		//当前分包号
		pbuf[4] = 0xff;			//保留
		pbuf[5] = 0xff;			//保留
			
		currPkgSize = currPkgSize + 6;
		for(j = 6, k = 0; j < currPkgSize; j++) {
			if((i == 0) && (k < extra_len)) {
				pbuf[j] = pExtraBuf[k++];
			}
			else {
				pbuf[j] = (n % 2) ? (pWaveBuf[n>>1] & 0x00ff) : (pWaveBuf[n>>1] >> 8);
				n++;
			}
		}

		Modbus_Ack2Master(commtype, CMD_EMAT_DATA, pbuf, j);
		delay_us(100);
	}
}

/**
  * @brief  一帧数据接收完成
  * @param  commtype: 通讯方式; cmd: 指令码; datalen: 数据内容长度; buf:指向数据内容
  * @retval None
  */
void Comm_RxOneFrameDone(uint8_t *buf, uint16_t len, uint8_t commtype)
{
	//成功接收一帧数据
	for(uint32_t i = 0; i < len; i++) {
		g_Com.rxPC_cache[g_Com.rxPC_wrCache_id][i] 	= buf[i];
		g_Com.rxPC_cache_src[g_Com.rxPC_wrCache_id] = commtype;
	}
	g_Com.rxPC_wrCache_id = (g_Com.rxPC_wrCache_id < (CACHE_DEPTH-1)) ? (g_Com.rxPC_wrCache_id + 1) : 0;
	if(g_Com.rxPC_wrCache_id == g_Com.rxPC_rdCache_id) {
		Comm_rdCacheId_Inc();
	}
	LedTask_FlashCommIndLed();
}

void Comm_rdCacheId_Inc(void) {
	g_Com.rxPC_rdCache_id = (g_Com.rxPC_rdCache_id < (CACHE_DEPTH-1)) ? (g_Com.rxPC_rdCache_id + 1) : 0;
}

/**
  * @brief  发送数据给主机,
  * @param  commtype: 通讯方式; cmd: 指令码; datalen: 数据内容长度; buf:指向数据内容
  * @retval None
  */
uint8_t Modbus_Ack2Master(uint8_t commtype, uint8_t cmd, uint8_t *buf, uint16_t datalen) 
{
	uint16_t crc16;

	databuf[0] = g_Dev.modbus_addr; 	//地址,用于返回数据
	databuf[1] = cmd; 	//指令码
	databuf[2] = datalen >> 8; 	//数据长度
	databuf[3] = datalen & 0x00ff; 	//数据长度
	
	memcpy(&databuf[4], buf, datalen);
	
	crc16 = needCRC16 ? usMBCRC16(databuf, 4+datalen) : 0x0000;
	databuf[4+datalen] = crc16>>8; 		//CRC
	databuf[5+datalen] = crc16&0x00ff; 	//CRC
	
	if(commtype == COM_RS485) {
		RS485_SendDatas(databuf, datalen + 6);
	}
//	else if(commtype == COM_RS232) {
//		Uart8_txdata((char *)databuf, datalen + 6);
//	}
	else if(commtype == COM_NET) {
//		tcp_server_start_senddata(databuf, datalen + 6);
//		tcp_server_senddata(databuf, datalen + 6);
		udp_senddata(databuf, datalen + 6);
	}
	return 0;
}

// USB 接收指令处理
uint8_t RxDataFromUpper_Exe(uint8_t *buf, uint8_t commtype, uint8_t len)
{
	uint8_t 	id, cmd, chn, rw, res;
	uint16_t	i, j;
	uint16_t	crc_rx;
	uint16_t	crc_clc;
	uint8_t 	txbuf[60];
	uint16_t 	u16_tmp;
	
	uint8_t 	addr;
	uint8_t 	datalen;
	uint32_t	thicknessVal;
	uint8_t		*pData;	//指针指向接收到的数据
	RTC_CLOCK_T 	rct_clock;
	
	addr 		= buf[0];
	cmd 		= buf[1];
	datalen 	= ((uint16_t)buf[2] << 8) | buf[3];
	crc_rx 		= ((uint16_t)buf[4+datalen] << 8) | buf[5+datalen];
	crc_clc 	= usMBCRC16(buf, datalen+4);
	pData 		= &buf[4];
	IWDG_Feed();//喂狗
	
	memset(txbuf, 0, sizeof(txbuf));
	
	if((needCRC16) && (crc_rx != crc_clc)) { 	//CRC不一致
		txbuf[0] = CMD_MSG_CRC_ERR; 		//CRC错误
		Modbus_Ack2Master(commtype, CMD_MSG, txbuf, 8);
		return 2;
	}
	if(addr != g_Dev.modbus_addr) { //地址错误
		if(cmd != CMD_DEV_ADDR) {
			txbuf[0] = CMD_MSG_ADDR_ERR;		//地址错误
			Modbus_Ack2Master(commtype, CMD_MSG, txbuf, 8);
			return 1;
		}
	}
	
	switch (cmd) {
		case CMD_SN: //SN
			if(pData[0] == WRITE) {
				memcpy(g_Dev.sn, &pData[1], (datalen - 1));
				g_Dev.sn[sizeof(g_Dev.sn) - 1] = 0x00;
				EEPROM_SaveData(3);
			}
			else {
			}
			txbuf[0] = 0x01; //OK
			memcpy(&txbuf[1], g_Dev.sn, strlen(g_Dev.sn) + 1);
			Modbus_Ack2Master(commtype, CMD_SN, txbuf, strlen(g_Dev.sn) + 1 + 1);
			break;
		case CMD_DEV_ADDR: //设备地址
			if(pData[0] == WRITE) {
				res = Dev_SetModbusAddr(pData[1]);
				if(res == 0) {
					EEPROM_SaveData(4);
					txbuf[0] = 0x01; //OK
				}
				else {
					txbuf[0] = 0x00; //Failed
				}
			}
			else { 
				txbuf[0] = 0x01; //OK
			}
			
			txbuf[1] = g_Dev.modbus_addr;
			Modbus_Ack2Master(commtype, CMD_DEV_ADDR, txbuf, 10);
			break;
		case CMD_READ_FIRMWARE_VER: //固件版本
			txbuf[0] = 0x01; //OK
			strcpy((char *)&txbuf[1], FIRMWARE_VERSION);
			u16_tmp = FPGA_ReadVersionReg();
			txbuf[strlen(FIRMWARE_VERSION) + 2] = u16_tmp >> 8;
			txbuf[strlen(FIRMWARE_VERSION) + 3] = u16_tmp & 0x00ff;
			Modbus_Ack2Master(commtype, CMD_READ_FIRMWARE_VER, txbuf, strlen(FIRMWARE_VERSION) + 1 + 1 + 2);
			break;
		case CMD_READ_DEV_PAR: //参数列表
			txbuf[0] 	= 0x01; //OK
			pData 		= &txbuf[1];
			pData[ 0] 	= g_CHN[0].enable;
			pData[ 1] 	= 0;
			pData[ 2] 	= 0;
			pData[ 3] 	= 0;
			pData[ 4] 	= (uint16_t)(g_Daq[0].gainVal * 10) >> 8;
			pData[ 5] 	= (uint16_t)(g_Daq[0].gainVal * 10) & 0x00ff;
			pData[ 6] 	= 0;
			pData[ 7] 	= 0;
			pData[ 8] 	= 0;
			pData[ 9] 	= 0;
			pData[10] 	= 0;
			pData[11] 	= 0;
			pData[12] 	= (uint16_t)(g_Emat[0].emitVoltage) >> 8;
			pData[13] 	= (uint16_t)(g_Emat[0].emitVoltage) & 0x00ff;
			pData[14] 	= (uint16_t)(g_Emat[0].emitVoltage_HVM2) >> 8;
			pData[15] 	= (uint16_t)(g_Emat[0].emitVoltage_HVM2) & 0x00ff;
			pData[16] 	= 0;
			pData[17] 	= 0;
			pData[18] 	= 0;
			pData[19] 	= 0;
			//B20~B29保留
			pData[30]	= g_Daq[0].fs_MHz;
			pData[31]	= (uint32_t)(g_Daq[0].waveBgn_pt) >> 24;
			pData[32]	= (uint32_t)(g_Daq[0].waveBgn_pt) >> 16;
			pData[33]	= (uint32_t)(g_Daq[0].waveBgn_pt) >> 8;
			pData[34]	= (uint32_t)(g_Daq[0].waveBgn_pt) & 0x00ff;
			pData[35]	= (uint16_t)(g_Daq[0].waveLen_pt) >> 8;
			pData[36]	= (uint16_t)(g_Daq[0].waveLen_pt) & 0x00ff;
			pData[37]	= (uint16_t)(g_Daq[0].avg) >> 8;
			pData[38]	= (uint16_t)(g_Daq[0].avg) & 0x00ff;
			pData[39]	= (uint16_t)(g_Emat[0].emitFreq_MHz*1000) >> 8;
			pData[40]	= (uint16_t)(g_Emat[0].emitFreq_MHz*1000) & 0x00ff;
			pData[41]	= g_Emat[0].emitPulNum;
			pData[42]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) >> 24;
			pData[43]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) >> 16;
			pData[44]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) >> 8;
			pData[45]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) & 0x00ff;
			pData[46]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) >> 24;
			pData[47]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) >> 16;
			pData[48]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) >> 8;
			pData[59]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) & 0x00ff;
			pData[50]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) >> 24;
			pData[51]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) >> 16;
			pData[52]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) >> 8;
			pData[53]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) & 0x00ff;
			Modbus_Ack2Master(commtype, CMD_READ_DEV_PAR, txbuf, 55);
			break;
//		case CMD_RTC:
//			if(pData[0] == WRITE) { //设置时间
//				RX8010_SetTime(pData[1]+2000, pData[2], pData[3], pData[4], pData[5], pData[6]);
//				txbuf[0] = OK; txbuf[1] = WRITE;
//			}
//			else { //读取时间
//				txbuf[0] = OK; txbuf[1] = READ;
//			}
//			rct_clock = RX8010_GetTime();
//			txbuf[2] = rct_clock.year; txbuf[3] = rct_clock.month; txbuf[4] = rct_clock.day;
//			txbuf[5] = rct_clock.hour; txbuf[6] = rct_clock.min;   txbuf[7] = rct_clock.sec;
//			break;
		case CMD_DBG_WAVEDATA: //调试波形开关
			if(pData[0] == WRITE) { //设置时间
				txbuf[0] 				= WRITE;
				FPGA_SetAdcTestData(pData[1]);
			}
			else { //读取时间
				txbuf[0] 				= READ;
			}
			txbuf[1] = 0x00;
			Modbus_Ack2Master(commtype, CMD_DBG_WAVEDATA, txbuf, 10);
			break;
		case CMD_DBG_CRC: //CRC16开关指令
			if(pData[0] == WRITE) { //设置时间
				txbuf[0]	= WRITE;
				needCRC16	= pData[1];
			}
			else { //读取时间
				txbuf[0] 				= READ;
			}
			txbuf[1] = needCRC16;
			Modbus_Ack2Master(commtype, CMD_DBG_CRC, txbuf, 10);
			break;
		case CMD_CHN_ENABLE: //通道开关
			if(pData[0] == WRITE) {
				g_CHN[0].enable	= pData[1];
			}
			txbuf[0] = 1; //OK
			txbuf[1] = g_CHN[0].enable;

			Modbus_Ack2Master(commtype, CMD_CHN_ENABLE, txbuf, 10);
			g_Dev.ematParasChanged	= 1;
			EEPROM_SaveData(1);
			break;
		case CMD_EMAT_RUN: //启停
			if(pData[0] == READ) {
				txbuf[0] 				= 1; //OK
			}
			else if(pData[0] == WRITE) {
				res				= 1; //ok
				if(pData[1]) { 	//启动
					if(g_Dev.sys_state == STATE_IDLE) {
						g_Dev.sys_state = STATE_RUN_PREPARE;
					}
					else {
						res				= 0; //failed
					}
				}
				else { 			//停止
					if(g_Dev.sys_state == STATE_RUN_ING) {
						g_Dev.sys_state = STATE_STOP_PREPARE;
					}
					else {
						res				= 0; //failed
					}
				}
				if(res == 1) {
					g_Dev.emit_en			= pData[1];
					g_Dev.runMode			= pData[2];
					g_Dev.trig_mode			= pData[3];
					g_Dev.repeat_times 		= (((uint16_t)pData[4]<<8) | pData[5]);
					g_Dev.encTrigPul_num 	= (((uint16_t)pData[6]<<8) | pData[7]);
					g_CHN[0].emat->chirp_en	= (g_Dev.runMode == RUN_MODE_COMMON) || (g_Dev.runMode == RUN_MODE_SWEEP_FRQ) || (g_Dev.runMode == RUN_MODE_CHIRP);
				}
				txbuf[0] 				= res;
//				g_Dev.ematParasChanged	= 1;
			}
			
			txbuf[1] = g_Dev.emit_en;
			txbuf[2] = g_Dev.runMode;
			txbuf[3] = g_Dev.trig_mode;
			txbuf[4] = (uint16_t)(g_Dev.repeat_times) >> 8;
			txbuf[5] = (uint16_t)(g_Dev.repeat_times) & 0x00ff;
			txbuf[6] = (uint16_t)(g_Dev.encTrigPul_num) >> 8;
			txbuf[7] = (uint16_t)(g_Dev.encTrigPul_num) & 0x00ff;
			Modbus_Ack2Master(commtype, CMD_EMAT_RUN, txbuf, 10);
			EEPROM_SaveData(1);
			break;
		case CMD_EMAT_SWEEP: //扫频参数
			if(pData[0] == READ) {
				txbuf[0] 				= 1; //OK
			}
			else if(pData[0] == WRITE) {
				g_CHN[0].emat->sweepFreq_bgnFreq  = (((uint32_t)pData[1]<<24) | ((uint32_t)pData[2]<<16) | ((uint32_t)pData[3]<<8) | ((uint32_t)pData[4]<<0));
				g_CHN[0].emat->sweepFreq_endFreq  = (((uint32_t)pData[5]<<24) | ((uint32_t)pData[6]<<16) | ((uint32_t)pData[7]<<8) | ((uint32_t)pData[8]<<0));
				g_CHN[0].emat->sweepFreq_stepFreq = (((uint32_t)pData[9]<<24) | ((uint32_t)pData[10]<<16) | ((uint32_t)pData[11]<<8) | ((uint32_t)pData[12]<<0));
				txbuf[0] 				= 1; //OK
				g_Dev.ematParasChanged	= 1;
			}
			txbuf[1]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) >> 24;
			txbuf[2]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) >> 16;
			txbuf[3]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) >> 8;
			txbuf[4]	= (uint32_t)(g_CHN[0].emat->sweepFreq_bgnFreq ) & 0x00ff;
			txbuf[5]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) >> 24;
			txbuf[6]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) >> 16;
			txbuf[7]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) >> 8;
			txbuf[8]	= (uint32_t)(g_CHN[0].emat->sweepFreq_endFreq ) & 0x00ff;
			txbuf[9]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) >> 24;
			txbuf[10]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) >> 16;
			txbuf[11]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) >> 8;
			txbuf[12]	= (uint32_t)(g_CHN[0].emat->sweepFreq_stepFreq) & 0x00ff;
			EEPROM_SaveData(1);
			Modbus_Ack2Master(commtype, CMD_EMAT_SWEEP, txbuf, 13);
			break;
		case CMD_EMAT_CHIRP: //调频参数
			if(pData[0] == READ) {
				txbuf[0] 				= 1; //OK
			}
			else if(pData[0] == WRITE) {
				g_CHN[0].emat->chirpFreq_bgnFreq  = (((uint32_t)pData[1]<<24) | ((uint32_t)pData[2]<<16) | ((uint32_t)pData[3]<<8) | ((uint32_t)pData[4]<<0));
				g_CHN[0].emat->chirpFreq_endFreq  = (((uint32_t)pData[5]<<24) | ((uint32_t)pData[6]<<16) | ((uint32_t)pData[7]<<8) | ((uint32_t)pData[8]<<0));
				txbuf[0] 				= 1; //OK
				g_Dev.ematParasChanged	= 1;
			}
			txbuf[1]				= (uint32_t)(g_CHN[0].emat->chirpFreq_bgnFreq ) >> 24;
			txbuf[2]				= (uint32_t)(g_CHN[0].emat->chirpFreq_bgnFreq ) >> 16;
			txbuf[3]				= (uint32_t)(g_CHN[0].emat->chirpFreq_bgnFreq ) >> 8;
			txbuf[4]				= (uint32_t)(g_CHN[0].emat->chirpFreq_bgnFreq ) & 0x00ff;
			txbuf[5]				= (uint32_t)(g_CHN[0].emat->chirpFreq_endFreq ) >> 24;
			txbuf[6]				= (uint32_t)(g_CHN[0].emat->chirpFreq_endFreq ) >> 16;
			txbuf[7]				= (uint32_t)(g_CHN[0].emat->chirpFreq_endFreq ) >> 8;
			txbuf[8]				= (uint32_t)(g_CHN[0].emat->chirpFreq_endFreq ) & 0x00ff;
			EEPROM_SaveData(1);
			Modbus_Ack2Master(commtype, CMD_EMAT_CHIRP, txbuf, 13);
			break;
		case CMD_EMAT_PPKEEP: //峰值保持
			if(pData[0] == READ) {
				txbuf[0] 				= 1; //OK
			}
			else if(pData[0] == WRITE) {
				g_CHN[0].emat->ppkeep_en		= pData[2];
				g_CHN[0].emat->ppkeep_bgn_pt  	= ((uint32_t)pData[3]<<24) | ((uint32_t)pData[4]<<16) | ((uint32_t)pData[5]<<8) | ((uint32_t)pData[6]);
				g_CHN[0].emat->ppkeep_end_pt  	= ((uint32_t)pData[7]<<24) | ((uint32_t)pData[8]<<16) | ((uint32_t)pData[9]<<8) | ((uint32_t)pData[10]);
				txbuf[0] 				= 1; //OK
				g_Dev.ematParasChanged	= 1;
			}
			txbuf[2]				= g_CHN[0].emat->ppkeep_en;
			txbuf[3]				= (g_CHN[0].emat->ppkeep_bgn_pt >> 24) & 0x000000ff;
			txbuf[4]				= (g_CHN[0].emat->ppkeep_bgn_pt >> 16) & 0x000000ff;
			txbuf[5]				= (g_CHN[0].emat->ppkeep_bgn_pt >>  8) & 0x000000ff;
			txbuf[6]				= (g_CHN[0].emat->ppkeep_bgn_pt >>  0) & 0x000000ff;
			txbuf[7]				= (g_CHN[0].emat->ppkeep_end_pt >> 24) & 0x000000ff;
			txbuf[8]				= (g_CHN[0].emat->ppkeep_end_pt >> 16) & 0x000000ff;
			txbuf[9]				= (g_CHN[0].emat->ppkeep_end_pt >>  8) & 0x000000ff;
			txbuf[10]				= (g_CHN[0].emat->ppkeep_end_pt >>  0) & 0x000000ff;
//			EEPROM_SaveData(1);
			Modbus_Ack2Master(commtype, CMD_EMAT_PPKEEP, txbuf, 11);
			SendCmd2FPGA_PPKeep();
			break;
		case CMD_EMAT_FILTER: //滤波器
			if(pData[0] == WRITE) {
				res = 0;
				res |= AFE_SetFilter_LP(((uint16_t)pData[1]<<8) | pData[2]);
				res |= AFE_SetFilter_HP(((uint16_t)pData[3]<<8) | pData[4]);
				txbuf[0] = res ? 0 : 1; //1:OK, 0:failed
			}
			else if(pData[0] == READ) {
				txbuf[0] = 1; //OK
			}
			txbuf[1]				= (uint16_t)(g_Daq[0].filter_LP_KHz) >> 8;
			txbuf[2]				= (uint16_t)(g_Daq[0].filter_LP_KHz) & 0x00ff;
			txbuf[3]				= (uint16_t)(g_Daq[0].filter_HP_KHz) >> 8;
			txbuf[4]				= (uint16_t)(g_Daq[0].filter_HP_KHz) & 0x00ff;
		
			Modbus_Ack2Master(commtype, CMD_EMAT_FILTER, txbuf, 10);
			break;
		case CMD_EMAT_GAIN: //增益
			if(pData[0] == WRITE) {
				float tmp;
				tmp = (((uint16_t)(pData[1]&0x7f)<<8) | pData[2]) / 10.0;
				if(pData[1] & 0x80) tmp = -tmp;
				res = 0;
				res |= DAQ_SetGain(1, tmp);
				txbuf[0] = res ? 0 : 1; //1:OK, 0:failed
			}
			else if(pData[0] == READ) {
				txbuf[0] = 1; //OK
			}
			u16_tmp = (uint16_t)(fabs(g_Daq[0].gainVal) * 10) | (g_Daq[0].gainVal < 0 ? 0x8000 : 0x0000);
			txbuf[1] = (u16_tmp >> 8) & 0x00ff;
			txbuf[2] = u16_tmp & 0x00ff;
		
			Modbus_Ack2Master(commtype, CMD_EMAT_GAIN, txbuf, 10);
			break;
		case CMD_PREAMP: //前放
			if(pData[0] == WRITE) {
				if(pData[1] == 0) {
					AFE_SetExtPreampValid();
				}
				else {
					AFE_SetInnerPreampValid();
				}
				txbuf[0] = 1; //1:OK
			}
			else if(pData[0] == READ) {
				txbuf[0] = 1; //OK
			}
			txbuf[1] = AFE_IsUseInnerPreamp();
		
			Modbus_Ack2Master(commtype, CMD_EMAT_GAIN, txbuf, 10);
			break;
		case CMD_EMAT_VOL: //调压
			if(pData[0] == WRITE) {
				res = 0;
				res |= Emat_SetEmitVol	(1, (((uint16_t)pData[1]<<8) | pData[2]));
				res |= Emat_SetEmitVol2	(1, (((uint16_t)pData[3]<<8) | pData[4]));
				txbuf[0] = res ? 0 : 1; //1:OK, 0:failed
			}
			else if(pData[0] == READ) {
				txbuf[0] = 1; //OK
			}
			txbuf[1]				= (uint16_t)(g_Emat[0].emitVoltage		) >> 	8;
			txbuf[2]				= (uint16_t)(g_Emat[0].emitVoltage		) & 	0x00ff;
			txbuf[3]				= (uint16_t)(g_Emat[0].emitVoltage_HVM2	) >> 	8;
			txbuf[4]				= (uint16_t)(g_Emat[0].emitVoltage_HVM2	) & 	0x00ff;
		
			Modbus_Ack2Master(commtype, CMD_EMAT_VOL, txbuf, 10);
			break;	
		case CMD_EMAT_HV_SW: //高压通道开关
			if(pData[0] == WRITE) {
				res = 0;
				g_Emat[0].HV1_en	= pData[1];
				g_Emat[0].HV2_en	= pData[2];
				
				txbuf[0] = 1; //1:OK, 0:failed
			}
			else if(pData[0] == READ) {
				txbuf[0] = 1; //OK
			}
			txbuf[1]				= g_Emat[0].HV1_en;
			txbuf[2]				= g_Emat[0].HV2_en;
			Modbus_Ack2Master(commtype, CMD_EMAT_HV_SW, txbuf, 10);
			break;	
		case CMD_EMAT_DAQ: //EMAT_DAQ 参数配置
			if(pData[0] == READ) {
				CHANNEL_T *chn;
				for(i = 0; i < CHN_NUM; i++) {
					if(pData[1] & (1 << i)) {
						if(i == 0) {
							chn = &g_CHN[0]; j = 1;
						}
						u16_tmp = (uint16_t)(fabs(chn->daq->gainVal) * 10) | (chn->daq->gainVal < 0 ? 0x8000 : 0x0000);
						
						txbuf[0] 		= 1; //OK
						txbuf[1] 		= j; //chn
						txbuf[2]		= (uint16_t)(chn->emat->emitVoltage) >> 8;
						txbuf[3]		= (uint16_t)(chn->emat->emitVoltage) & 0x00ff;
						txbuf[4] 		= chn->emat->emitPulNum;
						txbuf[5]		= (uint16_t)(chn->emat->emitFreq_MHz*1000) >> 8;
						txbuf[6]		= (uint16_t)(chn->emat->emitFreq_MHz*1000) & 0x00ff;
						txbuf[7]		= (uint16_t)(chn->emat->emitDly_ns) >> 8;
						txbuf[8]		= (uint16_t)(chn->emat->emitDly_ns) & 0x00ff;
						txbuf[9]		= (u16_tmp >> 8) & 0x00ff;
						txbuf[10]		= u16_tmp & 0x00ff;
						txbuf[11]		= (uint32_t)(chn->daq->waveBgn_pt) >> 24;
						txbuf[12]		= (uint32_t)(chn->daq->waveBgn_pt) >> 16;
						txbuf[13]		= (uint32_t)(chn->daq->waveBgn_pt) >> 8;
						txbuf[14]		= (uint32_t)(chn->daq->waveBgn_pt) & 0x00ff;
						txbuf[15]		= (uint16_t)(chn->daq->waveLen_pt) >> 8;
						txbuf[16]		= (uint16_t)(chn->daq->waveLen_pt) & 0x00ff;
						txbuf[17] 		= chn->daq->fs_MHz;
						txbuf[18] 		= 0;
						txbuf[19]		= (uint16_t)(chn->daq->avg) >> 8;
						txbuf[20]		= (uint16_t)(chn->daq->avg) & 0x00ff;
						txbuf[21] 		= 0;
						
						Modbus_Ack2Master(commtype, CMD_EMAT_DAQ, txbuf, 22);
					}
				}
			}
			else if(pData[0] == WRITE) {
				CHANNEL_T *chn;
				uint8_t *p;
				float tmp;
				res = 0;
				p = &pData[2];
				
				for(i = 0; i < CHN_NUM; i++) {
					if(pData[1] & (0x01 << i)) {
						if(i == 0) 
							chn = &g_CHN[0];
						
						chn->emat->emitVoltage		= (((uint16_t)p[0]<<8) | p[1]);
//						chn->emat->emitPulNum 		= p[2];
						Emat_SetEmitPulNum(i + 1, p[2]);
						chn->emat->emitFreq_MHz 	= (((uint16_t)p[3]<<8) | p[4]) / 1000.0;
						chn->emat->emitDly_ns 		= (((uint16_t)p[5]<<8) | p[6]);
//						chn->daq->gainVal 			= (((uint16_t)p[7]<<8) | p[8]) / 10.0;
						tmp = (((uint16_t)(pData[7]&0x7f)<<8) | pData[8]) / 10.0;
						if(pData[7] & 0x80) tmp = -tmp;
						chn->daq->gainVal 			= tmp;
						
						chn->daq->fs_MHz 			= p[15];
//						chn->daq->waveBgn_pt 		= (((uint32_t)p[9]<<24) | ((uint32_t)p[10]<<16) | ((uint32_t)p[11]<<8) | ((uint32_t)p[12]<<0));
//						chn->daq->waveLen_pt 		= (((uint16_t)p[13]<<8) | p[14]);
						res |= DAQ_SetWaveBgnPt(i+1, (((uint32_t)p[9]<<24) | ((uint32_t)p[10]<<16) | ((uint32_t)p[11]<<8) | ((uint32_t)p[12]<<0)));
						res |= DAQ_SetWaveLenPt(i+1, (((uint16_t)p[13]<<8) | p[14]));
						chn->daq->avg 				= (((uint16_t)p[17]<<8) | p[18]);
						
//						chn->daq->waveBgn_ns		= chn->daq->waveBgn_pt * 1000 / chn->daq->fs_MHz;
//						chn->daq->waveLen_ns		= chn->daq->waveLen_pt * 1000 / chn->daq->fs_MHz;
					}
				}
				txbuf[0] 		= res ? 0 : 1; //1:OK, 0:failed
				txbuf[1] 		= pData[1]; //chn
				txbuf[2]		= (uint16_t)(chn->emat->emitVoltage) >> 8;
				txbuf[3]		= (uint16_t)(chn->emat->emitVoltage) & 0x00ff;
				txbuf[4] 		= chn->emat->emitPulNum;
				txbuf[5]		= (uint16_t)(chn->emat->emitFreq_MHz*1000) >> 8;
				txbuf[6]		= (uint16_t)(chn->emat->emitFreq_MHz*1000) & 0x00ff;
				txbuf[7]		= (uint16_t)(chn->emat->emitDly_ns) >> 8;
				txbuf[8]		= (uint16_t)(chn->emat->emitDly_ns) & 0x00ff;
				txbuf[9]		= ((uint16_t)(fabs(chn->daq->gainVal) * 10) >> 8) | ((chn->daq->gainVal<0) ? 0x80 : 0x00);
				txbuf[10]		= (uint16_t)(fabs(chn->daq->gainVal) * 10) & 0x00ff;
				txbuf[11]		= (uint32_t)(chn->daq->waveBgn_pt) >> 24;
				txbuf[12]		= (uint32_t)(chn->daq->waveBgn_pt) >> 16;
				txbuf[13]		= (uint32_t)(chn->daq->waveBgn_pt) >> 8;
				txbuf[14]		= (uint32_t)(chn->daq->waveBgn_pt) & 0x00ff;
				txbuf[15]		= (uint16_t)(chn->daq->waveLen_pt) >> 8;
				txbuf[16]		= (uint16_t)(chn->daq->waveLen_pt) & 0x00ff;
				txbuf[17] 		= chn->daq->fs_MHz;
				txbuf[18] 		= 0;
				txbuf[19]		= (uint16_t)(chn->daq->avg) >> 8;
				txbuf[20]		= (uint16_t)(chn->daq->avg) & 0x00ff;
				txbuf[21] 		= 0;
				Modbus_Ack2Master(commtype, CMD_EMAT_DAQ, txbuf, 22);
				DAQ_SetGain(1, (g_CHN[0].daq->gainVal));
				g_Dev.ematParasChanged	= 1;
				EEPROM_SaveData(1);
			}
			break;
		case CMD_COM_MODE:
//			if(pData[0] == READ) { // 读取通讯方式
//				if(pData[0] == 1) {
//					
//				}
//			}
//			else if(pData[0] == WRITE) { // 写入通讯方式
//				g_Dev.resOutputPort = pData[1];
//				EEPROM_SaveData(1);
//				txbuf[0] = WRITE;
//			}
//			txbuf[1] = g_Dev.resOutputPort;
			break;
		case CMD_PAR_RESET:
			EEPROM_Para_Reset();
			txbuf[0] = 1;
			Modbus_Ack2Master(commtype, CMD_PAR_RESET, txbuf, 10);
			break;
		case CMD_ENC_CLR:	//清除编码器数据
			FPGA_ClearEncoderData();
			txbuf[0] = 1; //OK
			Modbus_Ack2Master(commtype, CMD_ENC_CLR, txbuf, 10);
			break;
		case CMD_EMIT_ENCODE: //编码发射指令(230728新增)
			if(pData[0] == WRITE) {
				if(pData[1] == 1) {
					g_CHN[0].emat->emitEncode = ((uint32_t)pData[2]<<24) | ((uint32_t)pData[3]<<16) | ((uint32_t)pData[4]<<8) | ((uint32_t)pData[5]);
				}
				else {
					g_CHN[0].emat->emitEncode = 0x00000000;
				}
			}
			txbuf[0] = 1; //OK
			txbuf[1] = pData[1];
			txbuf[2] = (uint32_t)(g_CHN[0].emat->emitEncode) >> 24;
			txbuf[3] = (uint32_t)(g_CHN[0].emat->emitEncode) >> 16;
			txbuf[4] = (uint32_t)(g_CHN[0].emat->emitEncode) >> 8;
			txbuf[5] = (uint32_t)(g_CHN[0].emat->emitEncode) & 0x00ff;

			g_Dev.ematParasChanged	= 1;
	
			Modbus_Ack2Master(commtype, CMD_EMIT_ENCODE, txbuf, 6);
			break;
		default:break;
	}

}

