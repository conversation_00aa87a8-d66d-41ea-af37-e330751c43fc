/**
  *********************************************************************************************************
  * Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com 
  * @file    afe.c
  * @project ACUT_GCV1
  * <AUTHOR> YL
  * @date    
  * @version v1.0.0
  * @brief   
  * @modify
  * 2021-11-29：
  *   1) 首次创建;
  *********************************************************************************************************
  * @attention
  ******************************************************************************
  */
#include "includes.h"

#define INNER_PREAMP_PIN_VAL	0
#define EXT_PREAMP_PIN_VAL		1

#define WRITE_PREAMP_SW_PIN(n)	(n ? HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, GPIO_PIN_RESET))
#define SET_USE_INNER_PREAMP	WRITE_PREAMP_SW_PIN(INNER_PREAMP_PIN_VAL)
#define SET_USE_EXT_PREAMP		WRITE_PREAMP_SW_PIN(EXT_PREAMP_PIN_VAL)

#define READ_PREAMP_SW_PIN 		HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_12)
#define IS_USE_INNER_PREAMP		(READ_PREAMP_SW_PIN == INNER_PREAMP_PIN_VAL)
#define IS_USE_EXT_PREAMP		(READ_PREAMP_SW_PIN == EXT_PREAMP_PIN_VAL)

/*************************************************************
* 电源部分IO初始化
* 低通滤波器档位编码：A1 A0
*	00:400KHz;	01:800KHz;	10:1.6MHz;	11:2.5MHz
* 编码输出至FPGA，FPGA动作滤波器档位。                    
***************************************************************/
// LP-A0(PG4)、LP-A1(PG3)、LP-A2(PG2)、HP-A0(PI2)、HP-A1(PI1)、HP-A2(PI0)
// 前置放大切换引脚: PD12
void AFE_init(void)
{
	GPIO_InitTypeDef GPIO_InitStruct;
	__HAL_RCC_GPIOG_CLK_ENABLE();
	__HAL_RCC_GPIOI_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();

	GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3 | GPIO_PIN_4;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLDOWN;
	GPIO_InitStruct.Speed = GPIO_SPEED_HIGH;
	HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

	GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2;
	HAL_GPIO_Init(GPIOI, &GPIO_InitStruct);

	GPIO_InitStruct.Pin = GPIO_PIN_12;
	HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
	
	AFE_SetInnerPreampValid();
}

uint8_t AFE_SetFilter_LP(uint16_t KHz) {
	uint8_t code;
	
	if(KHz == 200) 			code = FILTER_LP_200K;
	else if(KHz == 800) 	code = FILTER_LP_800K;
	else if(KHz == 1000) 	code = FILTER_LP_1000K;
	else if(KHz == 2000) 	code = FILTER_LP_2000K;
	else return 1;
	
	LP_A0(code & 0x01);
	LP_A1(code & 0x02);
	LP_A2(code & 0x04);
	
	g_Daq[0].filter_LP_KHz = KHz;
	return 0;
}

uint8_t AFE_SetFilter_HP(uint16_t KHz) {
	uint8_t code;
	
	if(KHz == 40) 			code = FILTER_HP_40K;
	else if(KHz == 200) 	code = FILTER_HP_200K;
	else if(KHz == 400) 	code = FILTER_HP_400K;
	else if(KHz == 800) 	code = FILTER_HP_800K;
	else return 1;
	
	HP_A0(code & 0x01);
	HP_A1(code & 0x02);
	HP_A2(code & 0x04);
	
	g_Daq[0].filter_HP_KHz = KHz;
	return 0;
}

void AFE_SetInnerPreampValid(void) {
	SET_USE_INNER_PREAMP;
}

void AFE_SetExtPreampValid(void) {
	SET_USE_EXT_PREAMP;
}

// void AFE_SetValidPreamp(uint8_t isInner) {
// 	SET_USE_EXT_PREAMP;
// }

uint8_t AFE_IsUseInnerPreamp(void) {
	return IS_USE_INNER_PREAMP;
}





/***********************END OF FILE********************************************/
