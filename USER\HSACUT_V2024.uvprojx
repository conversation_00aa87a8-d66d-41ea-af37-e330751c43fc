<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>HSACUT_V2024</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060528::V5.06 update 5 (build 528)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F767IGTx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F7xx_DFP.3.0.0</PackID>
          <PackURL>https://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20020000,0x60000) IRAM2(0x20000000,0x20000) IROM(0x08000000,0x100000) IROM2(0x00200000,0x100000) CPUTYPE("Cortex-M7") FPU3(DFPU) CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN2 -FF0STM32F7x_1024 -********** -********* -FF1STM32F7x_1024dual -********** -********* -FP0($$Device:STM32F767IGTx$CMSIS\Flash\STM32F7x_1024.FLM) -FP1($$Device:STM32F767IGTx$CMSIS\Flash\STM32F7x_1024dual.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F767IGTx$Drivers\CMSIS\Device\ST\STM32F7xx\Include\stm32f7xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F767IGTx$CMSIS\SVD\STM32F7x7_v1r2.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\OBJ\</OutputDirectory>
          <OutputName>HSACUT_V2023</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\OBJ\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>D:\Keil_v5\ARM\ARMCC\bin\fromelf.exe  --bin -o  ..\OBJ\ST100V4_1_GUI2.bin ..\OBJ\ST100V4_1_GUI2.axf</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM7</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM7</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M7"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>3</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20020000</StartAddress>
                <Size>0x60000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x200000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20020000</StartAddress>
                <Size>0x60000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>3</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>0</vShortEn>
            <vShortWch>0</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>--locale=english</MiscControls>
              <Define>STM32F767xx,USE_HAL_DRIVER,ARM_MATH_CM7,__CC_ARM,ARM_MATH_MATRIX_CHECK,ARM_MATH_ROUNDING,__FPU_PRESENT=1,USE_USB_OTG_FS</Define>
              <Undefine></Undefine>
              <IncludePath>..\CORE;..\OBJ;..\USER;..\HALLIB\STM32F7xx_HAL_Driver\Inc;..\USMART;.\malloc;..\DSP_LIB\Include;..\UCOSIII\uC-LIB;..\UCOSIII\uC-CPU;..\UCOSIII\UCOS-BSP;..\UCOSIII\UCOS-CONFIG;..\UCOSIII\uCOS-III\Source;..\UCOSIII\uC-CPU\ARM-Cortex-M4\RealView;..\UCOSIII\uC-LIB\Ports\ARM-Cortex-M4\RealView;..\UCOSIII\uCOS-III\Ports\ARM-Cortex-M4\Generic\RealView;..\FATFS\exfuns;..\FATFS\src;..\FATFS\src\option;..\USB\STM32_USB_Device_Library\Class\msc\inc;..\USB\STM32_USB_Device_Library\Core\inc;..\USB\STM32_USB_OTG_Driver\inc;..\USB\USB_APP;.\bsp;.\bsp\inc;.\app;.\app\inc;..\LWIP;..\LWIP\arch;..\LWIP\lwip-1.4.1\src\include;..\LWIP\lwip-1.4.1\src\include\ipv4;..\LWIP\lwip-1.4.1\src\include\netif;..\LWIP\lwip_app\inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20010000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>USER</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\main.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\stm32f7xx_it.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32f7xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\system_stm32f7xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CORE</GroupName>
          <Files>
            <File>
              <FileName>core_cm7.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CORE\core_cm7.h</FilePath>
            </File>
            <File>
              <FileName>core_cmFunc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CORE\core_cmFunc.h</FilePath>
            </File>
            <File>
              <FileName>core_cmInstr.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CORE\core_cmInstr.h</FilePath>
            </File>
            <File>
              <FileName>core_cmSimd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\CORE\core_cmSimd.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f767xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\CORE\startup_stm32f767xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HALLIB</GroupName>
          <Files>
            <File>
              <FileName>stm32f7xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_ll_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_ll_fmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_sdram.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_ltdc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_ltdc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_sram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_sram.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_ll_sdmmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_ll_sdmmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_sd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_sd.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_nand.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_nand.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f7xx_hal_eth.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HALLIB\STM32F7xx_HAL_Driver\Src\stm32f7xx_hal_eth.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>MALLOC</GroupName>
          <Files>
            <File>
              <FileName>malloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\malloc\malloc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DSP_LIB</GroupName>
          <Files>
            <File>
              <FileName>arm_cortexM7lfsp_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DSP_LIB\arm_cortexM7lfsp_math.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>arm_cortexM7b_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DSP_LIB\arm_cortexM7b_math.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>arm_cortexM7bfdp_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DSP_LIB\arm_cortexM7bfdp_math.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>arm_cortexM7bfsp_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DSP_LIB\arm_cortexM7bfsp_math.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>arm_cortexM7l_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DSP_LIB\arm_cortexM7l_math.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>arm_cortexM7lfdp_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\DSP_LIB\arm_cortexM7lfdp_math.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>EMWIN_LIB</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>STemWin540_CM7_Keil.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin540_CM7_Keil.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin532_CM7_Keil.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin532_CM7_Keil.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin532_CM7_Keil_ot.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin532_CM7_Keil_ot.lib</FilePath>
            </File>
            <File>
              <FileName>STemWin540_CM7_Keil_ot.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin540_CM7_Keil_ot.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin540_CM7_OS_Keil.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin540_CM7_OS_Keil.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin540_CM7_OS_Keil_ot.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin540_CM7_OS_Keil_ot.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin532_CM7_OS_Keil.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin532_CM7_OS_Keil.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin532_CM7_OS_Keil_ot.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin532_CM7_OS_Keil_ot.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin_CM7_OS_wc16.a</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin_CM7_OS_wc16.a</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>STemWin_CM7_wc16.a</FileName>
              <FileType>2</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\STemWin_CM7_wc16.a</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Aads>
                    <interw>2</interw>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <thumb>2</thumb>
                    <SplitLS>2</SplitLS>
                    <SwStkChk>2</SwStkChk>
                    <NoWarn>2</NoWarn>
                    <uSurpInc>2</uSurpInc>
                    <useXO>2</useXO>
                    <ClangAsOpt>0</ClangAsOpt>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Aads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>GUI_CM4F_L.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\EMWIN\STemWin\Lib\GUI_CM4F_L.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>EMWIN_CONFIG</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>GUI_X.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\OS\GUI_X.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>GUIConf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\Config\GUIConf.c</FilePath>
            </File>
            <File>
              <FileName>GUIDRV_Template.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\Config\GUIDRV_Template.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <v6Lto>0</v6Lto>
                    <v6WtE>0</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>GUI_X_Touch_Analog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\Config\GUI_X_Touch_Analog.c</FilePath>
            </File>
            <File>
              <FileName>LCDConf_Lin_Template.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\Config\LCDConf_Lin_Template.c</FilePath>
            </File>
            <File>
              <FileName>LCDConf_FlexColor_Template.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\Config\LCDConf_FlexColor_Template.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <v6Lto>0</v6Lto>
                    <v6WtE>0</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>GUI_X_UCOSIII.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\EMWIN\STemWin\OS\GUI_X_UCOSIII.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>UCOSIII_BSP</GroupName>
          <Files>
            <File>
              <FileName>bsp_os.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\UCOS-BSP\bsp_os.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>UCOSIII_CPU</GroupName>
          <Files>
            <File>
              <FileName>cpu_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uC-CPU\cpu_core.c</FilePath>
            </File>
            <File>
              <FileName>cpu_a.asm</FileName>
              <FileType>2</FileType>
              <FilePath>..\UCOSIII\uC-CPU\ARM-Cortex-M4\RealView\cpu_a.asm</FilePath>
            </File>
            <File>
              <FileName>cpu_c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uC-CPU\ARM-Cortex-M4\RealView\cpu_c.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>UCOSIII_LIB</GroupName>
          <Files>
            <File>
              <FileName>lib_ascii.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uC-LIB\lib_ascii.c</FilePath>
            </File>
            <File>
              <FileName>lib_math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uC-LIB\lib_math.c</FilePath>
            </File>
            <File>
              <FileName>lib_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uC-LIB\lib_mem.c</FilePath>
            </File>
            <File>
              <FileName>lib_str.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uC-LIB\lib_str.c</FilePath>
            </File>
            <File>
              <FileName>lib_mem_a.asm</FileName>
              <FileType>2</FileType>
              <FilePath>..\UCOSIII\uC-LIB\Ports\ARM-Cortex-M4\RealView\lib_mem_a.asm</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>UCOSIII_CORE</GroupName>
          <Files>
            <File>
              <FileName>os_cfg_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_cfg_app.c</FilePath>
            </File>
            <File>
              <FileName>os_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_core.c</FilePath>
            </File>
            <File>
              <FileName>os_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_dbg.c</FilePath>
            </File>
            <File>
              <FileName>os_flag.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_flag.c</FilePath>
            </File>
            <File>
              <FileName>os_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_int.c</FilePath>
            </File>
            <File>
              <FileName>os_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_mem.c</FilePath>
            </File>
            <File>
              <FileName>os_msg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_msg.c</FilePath>
            </File>
            <File>
              <FileName>os_mutex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_mutex.c</FilePath>
            </File>
            <File>
              <FileName>os_pend_multi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_pend_multi.c</FilePath>
            </File>
            <File>
              <FileName>os_prio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_prio.c</FilePath>
            </File>
            <File>
              <FileName>os_q.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_q.c</FilePath>
            </File>
            <File>
              <FileName>os_sem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_sem.c</FilePath>
            </File>
            <File>
              <FileName>os_stat.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_stat.c</FilePath>
            </File>
            <File>
              <FileName>os_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_task.c</FilePath>
            </File>
            <File>
              <FileName>os_tick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_tick.c</FilePath>
            </File>
            <File>
              <FileName>os_time.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_time.c</FilePath>
            </File>
            <File>
              <FileName>os_tmr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_tmr.c</FilePath>
            </File>
            <File>
              <FileName>os_var.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Source\os_var.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>UCOSIII_PORT</GroupName>
          <Files>
            <File>
              <FileName>os_cpu.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Ports\ARM-Cortex-M4\Generic\RealView\os_cpu.h</FilePath>
            </File>
            <File>
              <FileName>os_cpu_a.asm</FileName>
              <FileType>2</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Ports\ARM-Cortex-M4\Generic\RealView\os_cpu_a.asm</FilePath>
            </File>
            <File>
              <FileName>os_cpu_c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\uCOS-III\Ports\ARM-Cortex-M4\Generic\RealView\os_cpu_c.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>UCOSIII_CONFIG</GroupName>
          <Files>
            <File>
              <FileName>app_cfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\app_cfg.h</FilePath>
            </File>
            <File>
              <FileName>cpu_cfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\cpu_cfg.h</FilePath>
            </File>
            <File>
              <FileName>includes.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\includes.h</FilePath>
            </File>
            <File>
              <FileName>lib_cfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\lib_cfg.h</FilePath>
            </File>
            <File>
              <FileName>os_app_hooks.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\os_app_hooks.c</FilePath>
            </File>
            <File>
              <FileName>os_app_hooks.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\os_app_hooks.h</FilePath>
            </File>
            <File>
              <FileName>os_cfg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\os_cfg.h</FilePath>
            </File>
            <File>
              <FileName>os_cfg_app.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\UCOSIII\UCOS-CONFIG\os_cfg_app.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FATFS</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>ff.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FATFS\src\ff.c</FilePath>
            </File>
            <File>
              <FileName>diskio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FATFS\src\diskio.c</FilePath>
            </File>
            <File>
              <FileName>cc936.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FATFS\src\option\cc936.c</FilePath>
            </File>
            <File>
              <FileName>exfuns.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FATFS\exfuns\exfuns.c</FilePath>
            </File>
            <File>
              <FileName>fattester.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FATFS\exfuns\fattester.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USB_OTG</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>usb_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_OTG_Driver\src\usb_core.c</FilePath>
            </File>
            <File>
              <FileName>usb_dcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_OTG_Driver\src\usb_dcd.c</FilePath>
            </File>
            <File>
              <FileName>usb_dcd_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_OTG_Driver\src\usb_dcd_int.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USB_DEVICE</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Core\src\usbd_core.c</FilePath>
            </File>
            <File>
              <FileName>usbd_ioreq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Core\src\usbd_ioreq.c</FilePath>
            </File>
            <File>
              <FileName>usbd_req.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Core\src\usbd_req.c</FilePath>
            </File>
            <File>
              <FileName>usbd_msc_bot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Class\msc\src\usbd_msc_bot.c</FilePath>
            </File>
            <File>
              <FileName>usbd_msc_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Class\msc\src\usbd_msc_core.c</FilePath>
            </File>
            <File>
              <FileName>usbd_msc_data.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Class\msc\src\usbd_msc_data.c</FilePath>
            </File>
            <File>
              <FileName>usbd_msc_scsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\STM32_USB_Device_Library\Class\msc\src\usbd_msc_scsi.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USB_APP</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>usb_bsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USB_APP\usb_bsp.c</FilePath>
            </File>
            <File>
              <FileName>usbd_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USB_APP\usbd_desc.c</FilePath>
            </File>
            <File>
              <FileName>usbd_storage_msd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USB_APP\usbd_storage_msd.c</FilePath>
            </File>
            <File>
              <FileName>usbd_usr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\USB\USB_APP\usbd_usr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BSP</GroupName>
          <Files>
            <File>
              <FileName>bsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\bsp.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\delay.c</FilePath>
            </File>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\led.c</FilePath>
            </File>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\sys.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\usart.c</FilePath>
            </File>
            <File>
              <FileName>24cxx.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\24cxx.c</FilePath>
            </File>
            <File>
              <FileName>utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\utils.c</FilePath>
            </File>
            <File>
              <FileName>ad5304.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\ad5304.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>ad9106.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\ad9106.c</FilePath>
            </File>
            <File>
              <FileName>iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\iwdg.c</FilePath>
            </File>
            <File>
              <FileName>qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\qspi.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>rtc_rx8010.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\rtc_rx8010.c</FilePath>
            </File>
            <File>
              <FileName>sdmmc_sdcard.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\sdmmc_sdcard.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\sdram.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\timer.c</FilePath>
            </File>
            <File>
              <FileName>daq.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\daq.c</FilePath>
            </File>
            <File>
              <FileName>emat.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\emat.c</FilePath>
            </File>
            <File>
              <FileName>fpga.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\fpga.c</FilePath>
            </File>
            <File>
              <FileName>ad9258.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\ad9258.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>ds18b20.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\ds18b20.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>dp83848.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\dp83848.c</FilePath>
            </File>
            <File>
              <FileName>mbcrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\mbcrc.c</FilePath>
            </File>
            <File>
              <FileName>beep.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\beep.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>oled.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\oled.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>sgm5347.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\sgm5347.c</FilePath>
            </File>
            <File>
              <FileName>mpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\mpu.c</FilePath>
            </File>
            <File>
              <FileName>dev.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\dev.c</FilePath>
            </File>
            <File>
              <FileName>dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\dac.c</FilePath>
            </File>
            <File>
              <FileName>fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\fmc.c</FilePath>
            </File>
            <File>
              <FileName>ad9246.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\ad9246.c</FilePath>
            </File>
            <File>
              <FileName>afe.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\afe.c</FilePath>
            </File>
            <File>
              <FileName>ad9704.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\ad9704.c</FilePath>
            </File>
            <File>
              <FileName>fan.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsp\src\fan.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>APP</GroupName>
          <Files>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\app\app.c</FilePath>
            </File>
            <File>
              <FileName>comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\app\src\comm.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LWIP_APP</GroupName>
          <Files>
            <File>
              <FileName>lwip_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip_app\src\lwip_comm.c</FilePath>
            </File>
            <File>
              <FileName>netconn_tcp_server.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip_app\src\netconn_tcp_server.c</FilePath>
            </File>
            <File>
              <FileName>netconn_udp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip_app\src\netconn_udp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LWIP_NETIF</GroupName>
          <Files>
            <File>
              <FileName>ethernetif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\netif\ethernetif.c</FilePath>
            </File>
            <File>
              <FileName>etharp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\netif\etharp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LWIP_CORE</GroupName>
          <Files>
            <File>
              <FileName>autoip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\autoip.c</FilePath>
            </File>
            <File>
              <FileName>icmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\icmp.c</FilePath>
            </File>
            <File>
              <FileName>igmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\igmp.c</FilePath>
            </File>
            <File>
              <FileName>inet.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\inet.c</FilePath>
            </File>
            <File>
              <FileName>inet_chksum.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\inet_chksum.c</FilePath>
            </File>
            <File>
              <FileName>ip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\ip.c</FilePath>
            </File>
            <File>
              <FileName>ip_addr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\ip_addr.c</FilePath>
            </File>
            <File>
              <FileName>ip_frag.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\ipv4\ip_frag.c</FilePath>
            </File>
            <File>
              <FileName>def.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\def.c</FilePath>
            </File>
            <File>
              <FileName>dhcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\dhcp.c</FilePath>
            </File>
            <File>
              <FileName>dns.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\dns.c</FilePath>
            </File>
            <File>
              <FileName>init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\init.c</FilePath>
            </File>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\mem.c</FilePath>
            </File>
            <File>
              <FileName>memp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\memp.c</FilePath>
            </File>
            <File>
              <FileName>netif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\netif.c</FilePath>
            </File>
            <File>
              <FileName>pbuf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\pbuf.c</FilePath>
            </File>
            <File>
              <FileName>raw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\raw.c</FilePath>
            </File>
            <File>
              <FileName>stats.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\stats.c</FilePath>
            </File>
            <File>
              <FileName>tcp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\tcp.c</FilePath>
            </File>
            <File>
              <FileName>tcp_in.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\tcp_in.c</FilePath>
            </File>
            <File>
              <FileName>tcp_out.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\tcp_out.c</FilePath>
            </File>
            <File>
              <FileName>timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\timers.c</FilePath>
            </File>
            <File>
              <FileName>udp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\core\udp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LWIP_ARCH</GroupName>
          <Files>
            <File>
              <FileName>sys_arch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\arch\sys_arch.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LWIP_API</GroupName>
          <Files>
            <File>
              <FileName>api_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\api_lib.c</FilePath>
            </File>
            <File>
              <FileName>api_msg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\api_msg.c</FilePath>
            </File>
            <File>
              <FileName>err.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\err.c</FilePath>
            </File>
            <File>
              <FileName>netbuf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\netbuf.c</FilePath>
            </File>
            <File>
              <FileName>netdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\netdb.c</FilePath>
            </File>
            <File>
              <FileName>netifapi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\netifapi.c</FilePath>
            </File>
            <File>
              <FileName>sockets.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\sockets.c</FilePath>
            </File>
            <File>
              <FileName>tcpip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LWIP\lwip-1.4.1\src\api\tcpip.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::Data Exchange</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="Data Exchange" Cgroup="JSON" Csub="Jansson" Cvendor="Keil" Cversion="2.7.0" condition="ARMCC">
        <package license="doc\license.txt" name="Jansson" schemaVersion="1.0" url="http://www.keil.com/pack/" vendor="Keil" version="1.0.0"/>
        <targetInfos>
          <targetInfo excluded="1" name="HSACUT_V2024"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="source" name="src\jansson_config.c" version="2.7.0">
        <instance index="0">RTE\Data_Exchange\jansson_config.c</instance>
        <component Cclass="Data Exchange" Cgroup="JSON" Csub="Jansson" Cvendor="Keil" Cversion="2.7.0" condition="ARMCC"/>
        <package name="Jansson" schemaVersion="1.0" url="http://www.keil.com/pack/" vendor="Keil" version="1.0.0"/>
        <targetInfos>
          <targetInfo excluded="1" name="HSACUT_V2024"/>
        </targetInfos>
      </file>
    </files>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>HSACUT_V2024</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
