/*********************************************************************
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                           www.segger.com                           *
**********************************************************************
*                                                                    *
* C-file generated by                                                *
*                                                                    *
*        Bitmap converter for emWin V5.18.                           *
*        Compiled Sep 24 2012, 15:52:34                              *
*        (C) 1998 - 2012 Segger Microcontroller GmbH & Co. KG        *
*                                                                    *
**********************************************************************
*                                                                    *
* Source file: cancel                                                *
* Dimensions:  36 * 36                                               *
* NumColors:   32bpp: 16777216 + 256                                 *
*                                                                    *
**********************************************************************
*/

#include <stdlib.h>

#include "GUI.h"

#ifndef GUI_CONST_STORAGE
  #define GUI_CONST_STORAGE const
#endif

extern GUI_CONST_STORAGE GUI_BITMAP bm_fccancel;

static GUI_CONST_STORAGE unsigned char _accancel[] = {
  /* RLE: 185 Pixels @ 000,000 */ 185, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 008 Pixels @ 005,005 */ 0, 8, 0xDD, 0xF6, 0xFE, 0xFF, 0xA5, 0xE8, 0xFD, 0xFF, 0xA5, 0xE7, 0xFD, 0xFF, 0xA0, 0xE4, 0xFD, 0xFF, 0x91, 0xDB, 0xF9, 0xE3, 0x90, 0xDA, 0xF9, 0x96, 0x9C, 0xE2, 0xFC, 0xFF, 0xA4, 0xE7, 0xFD, 0xFF, 
  /* RLE: 010 Pixels @ 013,005 */ 10, 0xA5, 0xE8, 0xFD, 0xFF, 
  /* ABS: 008 Pixels @ 023,005 */ 0, 8, 0xA2, 0xE6, 0xFD, 0xFF, 0x95, 0xDD, 0xFA, 0xFB, 0x8E, 0xD9, 0xF8, 0x7B, 0x96, 0xDE, 0xFA, 0xEA, 0xA2, 0xE6, 0xFD, 0xFF, 0xA5, 0xE8, 0xFD, 0xFF, 0xA5, 0xE8, 0xFD, 0xFF, 0xDD, 0xF6, 0xFE, 0xFF, 
  /* RLE: 010 Pixels @ 031,005 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 009 Pixels @ 005,006 */ 0, 9, 0xD8, 0xF5, 0xFE, 0xFF, 0x95, 0xE4, 0xFD, 0xFF, 0x90, 0xE0, 0xFC, 0xFF, 0x7E, 0xD4, 0xF7, 0xE4, 0x81, 0xD6, 0xF8, 0x25, 0x87, 0xDB, 0xFA, 0x00, 0x84, 0xD8, 0xF9, 0x85, 0x8C, 0xDD, 0xFB, 0xFF, 
        0x95, 0xE3, 0xFD, 0xFF, 
  /* RLE: 008 Pixels @ 014,006 */ 8, 0x95, 0xE4, 0xFD, 0xFF, 
  /* ABS: 009 Pixels @ 022,006 */ 0, 9, 0x93, 0xE2, 0xFC, 0xFF, 0x82, 0xD7, 0xF8, 0xFB, 0x7E, 0xD4, 0xF7, 0x59, 0x87, 0xDB, 0xFA, 0x00, 0x86, 0xDA, 0xFA, 0x2E, 0x87, 0xDA, 0xFA, 0xEA, 0x93, 0xE2, 0xFC, 0xFF, 0x95, 0xE4, 0xFD, 0xFF, 
        0xD8, 0xF5, 0xFE, 0xFF, 
  /* RLE: 010 Pixels @ 031,006 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 005,007 */ 0, 10, 0xD6, 0xF4, 0xFE, 0xFF, 0x8D, 0xDF, 0xFB, 0xFF, 0x7B, 0xD3, 0xF7, 0xE5, 0x7E, 0xD6, 0xF8, 0x26, 0x84, 0xDD, 0xFB, 0x00, 0x84, 0xDE, 0xFC, 0x00, 0x85, 0xDD, 0xFB, 0x00, 0x83, 0xD8, 0xF9, 0x84, 
        0x8A, 0xDC, 0xFA, 0xFF, 0x92, 0xE2, 0xFC, 0xFF, 
  /* RLE: 006 Pixels @ 015,007 */ 6, 0x93, 0xE2, 0xFC, 0xFF, 
  /* ABS: 010 Pixels @ 021,007 */ 0, 10, 0x90, 0xE1, 0xFC, 0xFF, 0x80, 0xD6, 0xF8, 0xFB, 0x7B, 0xD3, 0xF7, 0x59, 0x83, 0xDC, 0xFB, 0x00, 0x83, 0xDE, 0xFB, 0x00, 0x85, 0xDE, 0xFB, 0x00, 0x84, 0xDA, 0xFA, 0x2E, 0x85, 0xD9, 0xF9, 0xEA, 
        0x90, 0xE1, 0xFC, 0xFF, 0xD7, 0xF4, 0xFE, 0xFF, 
  /* RLE: 010 Pixels @ 031,007 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 011 Pixels @ 005,008 */ 0, 11, 0xD2, 0xF2, 0xFD, 0xFF, 0x7A, 0xD2, 0xF6, 0xE5, 0x7B, 0xD5, 0xF7, 0x27, 0x7D, 0xDB, 0xFA, 0x00, 0x7A, 0xDB, 0xFA, 0x00, 0x78, 0xDB, 0xFA, 0x00, 0x7C, 0xDC, 0xFA, 0x00, 0x81, 0xDB, 0xFA, 0x00, 
        0x80, 0xD7, 0xF8, 0x83, 0x87, 0xDB, 0xF9, 0xFF, 0x8F, 0xE1, 0xFB, 0xFF, 
  /* RLE: 004 Pixels @ 016,008 */ 4, 0x90, 0xE1, 0xFB, 0xFF, 
  /* ABS: 011 Pixels @ 020,008 */ 0, 11, 0x8D, 0xDF, 0xFB, 0xFF, 0x7E, 0xD5, 0xF7, 0xFB, 0x78, 0xD2, 0xF6, 0x59, 0x7E, 0xDA, 0xFA, 0x00, 0x7A, 0xDB, 0xFA, 0x00, 0x78, 0xDB, 0xFA, 0x00, 0x7A, 0xDB, 0xFA, 0x00, 0x7F, 0xDC, 0xFA, 0x00, 
        0x82, 0xD9, 0xF9, 0x2E, 0x83, 0xD8, 0xF8, 0xEA, 0xD4, 0xF3, 0xFD, 0xFF, 
  /* RLE: 010 Pixels @ 031,008 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,009 */ 0, 26, 0xCC, 0xEE, 0xFC, 0xE6, 0x7D, 0xD5, 0xF7, 0x27, 0x7D, 0xDA, 0xFA, 0x00, 0x75, 0xDA, 0xF9, 0x00, 0x70, 0xD8, 0xF9, 0x00, 0x6F, 0xD8, 0xF9, 0x00, 0x71, 0xD8, 0xF9, 0x00, 0x77, 0xDA, 0xF9, 0x00, 
        0x7D, 0xDA, 0xF9, 0x00, 0x7D, 0xD5, 0xF7, 0x83, 0x84, 0xDA, 0xF9, 0xFF, 0x8C, 0xE0, 0xFA, 0xFF, 0x8C, 0xE0, 0xFA, 0xFF, 0x8C, 0xE0, 0xFA, 0xFF, 0x8A, 0xDE, 0xFA, 0xFF, 0x7B, 0xD3, 0xF6, 0xFB, 0x75, 0xD1, 0xF6, 0x59, 0x7A, 0xD8, 0xF9, 0x00, 
        0x75, 0xD9, 0xF9, 0x00, 0x71, 0xD8, 0xF9, 0x00, 0x6F, 0xD8, 0xF9, 0x00, 0x70, 0xD8, 0xF9, 0x00, 0x76, 0xDA, 0xF9, 0x00, 0x7F, 0xDB, 0xFA, 0x00, 0x84, 0xDB, 0xF9, 0x2E, 0xD1, 0xF1, 0xFD, 0xEA, 
  /* RLE: 010 Pixels @ 031,009 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,010 */ 0, 26, 0xB8, 0xEA, 0xFB, 0x86, 0x84, 0xDC, 0xF9, 0x00, 0x7A, 0xDA, 0xF9, 0x00, 0x70, 0xD7, 0xF8, 0x00, 0x6B, 0xD6, 0xF8, 0x00, 0x69, 0xD5, 0xF8, 0x00, 0x69, 0xD5, 0xF8, 0x00, 0x6D, 0xD6, 0xF8, 0x00, 
        0x73, 0xD8, 0xF9, 0x00, 0x79, 0xD8, 0xF8, 0x00, 0x7A, 0xD4, 0xF6, 0x82, 0x81, 0xD8, 0xF8, 0xFF, 0x88, 0xDD, 0xFA, 0xFF, 0x86, 0xDC, 0xF9, 0xFF, 0x78, 0xD2, 0xF6, 0xFB, 0x73, 0xCF, 0xF5, 0x59, 0x76, 0xD7, 0xF8, 0x00, 0x71, 0xD7, 0xF8, 0x00, 
        0x6C, 0xD6, 0xF8, 0x00, 0x69, 0xD5, 0xF8, 0x00, 0x69, 0xD5, 0xF8, 0x00, 0x6B, 0xD6, 0xF8, 0x00, 0x70, 0xD7, 0xF8, 0x00, 0x7A, 0xDA, 0xF9, 0x00, 0x82, 0xDA, 0xF9, 0x00, 0xB7, 0xE9, 0xFB, 0x82, 
  /* RLE: 010 Pixels @ 031,010 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,011 */ 0, 26, 0xCF, 0xF0, 0xFC, 0xFE, 0x7B, 0xD5, 0xF6, 0x6C, 0x78, 0xD7, 0xF8, 0x00, 0x6F, 0xD6, 0xF8, 0x00, 0x68, 0xD4, 0xF7, 0x00, 0x64, 0xD3, 0xF7, 0x00, 0x64, 0xD3, 0xF7, 0x00, 0x64, 0xD4, 0xF7, 0x00, 
        0x68, 0xD4, 0xF7, 0x00, 0x6E, 0xD6, 0xF8, 0x00, 0x74, 0xD6, 0xF7, 0x00, 0x74, 0xD1, 0xF6, 0x81, 0x75, 0xD1, 0xF5, 0xFF, 0x70, 0xCD, 0xF4, 0xFB, 0x6E, 0xCD, 0xF4, 0x59, 0x71, 0xD5, 0xF7, 0x00, 0x6D, 0xD6, 0xF8, 0x00, 0x67, 0xD4, 0xF7, 0x00, 
        0x64, 0xD3, 0xF7, 0x00, 0x64, 0xD3, 0xF7, 0x00, 0x64, 0xD3, 0xF7, 0x00, 0x68, 0xD4, 0xF7, 0x00, 0x6E, 0xD6, 0xF8, 0x00, 0x75, 0xD6, 0xF7, 0x00, 0x73, 0xCF, 0xF4, 0x63, 0xCB, 0xED, 0xFB, 0xFD, 
  /* RLE: 010 Pixels @ 031,011 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,012 */ 0, 26, 0xCF, 0xF1, 0xFC, 0xFF, 0x79, 0xD4, 0xF6, 0xFE, 0x74, 0xD2, 0xF5, 0x6C, 0x70, 0xD5, 0xF6, 0x00, 0x69, 0xD4, 0xF7, 0x00, 0x63, 0xD2, 0xF6, 0x00, 0x5F, 0xD1, 0xF6, 0x00, 0x5E, 0xD1, 0xF6, 0x00, 
        0x5F, 0xD1, 0xF6, 0x00, 0x63, 0xD2, 0xF6, 0x00, 0x69, 0xD4, 0xF7, 0x00, 0x6C, 0xD3, 0xF6, 0x00, 0x68, 0xCD, 0xF4, 0x7E, 0x67, 0xCD, 0xF3, 0x59, 0x6A, 0xD2, 0xF6, 0x00, 0x67, 0xD3, 0xF7, 0x00, 0x62, 0xD2, 0xF6, 0x00, 0x5F, 0xD1, 0xF6, 0x00, 
        0x5E, 0xD1, 0xF6, 0x00, 0x5F, 0xD1, 0xF6, 0x00, 0x62, 0xD2, 0xF6, 0x00, 0x68, 0xD4, 0xF7, 0x00, 0x6E, 0xD3, 0xF6, 0x00, 0x6D, 0xCC, 0xF3, 0x63, 0x73, 0xD0, 0xF4, 0xFD, 0xCF, 0xF1, 0xFC, 0xFF, 
  /* RLE: 010 Pixels @ 031,012 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,013 */ 0, 26, 0xCE, 0xF1, 0xFC, 0xFF, 0x7C, 0xD9, 0xF6, 0xFF, 0x75, 0xD3, 0xF5, 0xFE, 0x70, 0xCF, 0xF4, 0x6C, 0x6C, 0xD2, 0xF5, 0x00, 0x64, 0xD2, 0xF6, 0x00, 0x5E, 0xD0, 0xF5, 0x00, 0x5A, 0xCF, 0xF5, 0x00, 
        0x59, 0xCE, 0xF5, 0x00, 0x5A, 0xCF, 0xF5, 0x00, 0x5D, 0xD0, 0xF5, 0x00, 0x60, 0xD1, 0xF5, 0x00, 0x61, 0xD0, 0xF5, 0x00, 0x61, 0xD0, 0xF5, 0x00, 0x5F, 0xD0, 0xF5, 0x00, 0x5C, 0xD0, 0xF5, 0x00, 0x5A, 0xCF, 0xF5, 0x00, 0x59, 0xCE, 0xF5, 0x00, 
        0x5A, 0xCF, 0xF5, 0x00, 0x5D, 0xD0, 0xF5, 0x00, 0x63, 0xD1, 0xF6, 0x00, 0x69, 0xD1, 0xF5, 0x00, 0x69, 0xCA, 0xF2, 0x63, 0x6F, 0xCE, 0xF3, 0xFD, 0x7B, 0xD8, 0xF6, 0xFF, 0xCE, 0xF1, 0xFC, 0xFF, 
  /* RLE: 010 Pixels @ 031,013 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,014 */ 0, 26, 0xCC, 0xF0, 0xFC, 0xFF, 0x78, 0xD7, 0xF6, 0xFF, 0x78, 0xD6, 0xF6, 0xFF, 0x71, 0xD1, 0xF4, 0xFE, 0x6C, 0xCE, 0xF3, 0x6C, 0x67, 0xD1, 0xF4, 0x00, 0x5F, 0xD0, 0xF4, 0x00, 0x58, 0xCE, 0xF4, 0x00, 
        0x54, 0xCD, 0xF3, 0x00, 0x53, 0xCC, 0xF3, 0x00, 0x54, 0xCC, 0xF3, 0x00, 0x55, 0xCD, 0xF3, 0x00, 0x56, 0xCD, 0xF3, 0x00, 0x56, 0xCD, 0xF3, 0x00, 0x55, 0xCD, 0xF3, 0x00, 0x54, 0xCC, 0xF3, 0x00, 0x53, 0xCC, 0xF3, 0x00, 0x54, 0xCC, 0xF3, 0x00, 
        0x58, 0xCD, 0xF4, 0x00, 0x5E, 0xCF, 0xF4, 0x00, 0x65, 0xCF, 0xF4, 0x00, 0x65, 0xC8, 0xF1, 0x63, 0x6C, 0xCC, 0xF2, 0xFD, 0x77, 0xD6, 0xF6, 0xFF, 0x78, 0xD7, 0xF6, 0xFF, 0xCC, 0xF0, 0xFC, 0xFF, 
  /* RLE: 010 Pixels @ 031,014 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 005,015 */ 0, 10, 0xCB, 0xF0, 0xFB, 0xFF, 0x74, 0xD5, 0xF5, 0xFF, 0x74, 0xD5, 0xF5, 0xFF, 0x73, 0xD5, 0xF5, 0xFF, 0x6D, 0xCF, 0xF3, 0xFE, 0x68, 0xCC, 0xF1, 0x6C, 0x62, 0xCE, 0xF3, 0x00, 0x59, 0xCD, 0xF3, 0x00, 
        0x51, 0xCB, 0xF2, 0x00, 0x4E, 0xCA, 0xF2, 0x00, 
  /* RLE: 006 Pixels @ 015,015 */ 6, 0x4D, 0xCA, 0xF2, 0x00, 
  /* ABS: 010 Pixels @ 021,015 */ 0, 10, 0x4E, 0xCA, 0xF2, 0x00, 0x51, 0xCB, 0xF2, 0x00, 0x58, 0xCD, 0xF3, 0x00, 0x60, 0xCD, 0xF2, 0x00, 0x62, 0xC7, 0xF0, 0x63, 0x68, 0xCA, 0xF1, 0xFD, 0x72, 0xD4, 0xF4, 0xFF, 0x74, 0xD5, 0xF5, 0xFF, 
        0x74, 0xD5, 0xF5, 0xFF, 0xCB, 0xF0, 0xFB, 0xFF, 
  /* RLE: 010 Pixels @ 031,015 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 005,016 */ 0, 10, 0xC9, 0xEF, 0xFB, 0xFF, 0x6F, 0xD3, 0xF4, 0xFF, 0x6F, 0xD3, 0xF4, 0xFF, 0x6F, 0xD3, 0xF4, 0xFF, 0x6F, 0xD3, 0xF3, 0xFF, 0x69, 0xCD, 0xF2, 0xFE, 0x62, 0xCA, 0xF1, 0x6C, 0x59, 0xCC, 0xF2, 0x00, 
        0x50, 0xCA, 0xF2, 0x00, 0x49, 0xC8, 0xF1, 0x00, 
  /* RLE: 006 Pixels @ 015,016 */ 6, 0x47, 0xC7, 0xF1, 0x00, 
  /* ABS: 010 Pixels @ 021,016 */ 0, 10, 0x49, 0xC8, 0xF1, 0x00, 0x4F, 0xCA, 0xF2, 0x00, 0x58, 0xCA, 0xF1, 0x00, 0x5D, 0xC5, 0xEF, 0x63, 0x64, 0xC8, 0xF0, 0xFD, 0x6E, 0xD2, 0xF3, 0xFF, 0x6F, 0xD3, 0xF4, 0xFF, 0x6F, 0xD3, 0xF4, 0xFF, 
        0x6F, 0xD3, 0xF4, 0xFF, 0xC9, 0xEF, 0xFB, 0xFF, 
  /* RLE: 010 Pixels @ 031,016 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 005,017 */ 1, 0xC7, 0xEE, 0xFB, 0xFF, 
  /* RLE: 005 Pixels @ 006,017 */ 5, 0x6A, 0xD1, 0xF3, 0xFF, 
  /* ABS: 004 Pixels @ 011,017 */ 0, 4, 0x66, 0xCE, 0xF2, 0xFE, 0x5A, 0xCB, 0xF1, 0x6B, 0x4D, 0xC8, 0xF0, 0x00, 0x44, 0xC5, 0xEF, 0x00, 
  /* RLE: 006 Pixels @ 015,017 */ 6, 0x41, 0xC4, 0xEF, 0x00, 
  /* ABS: 005 Pixels @ 021,017 */ 0, 5, 0x44, 0xC5, 0xEF, 0x00, 0x4C, 0xC8, 0xF0, 0x00, 0x56, 0xC7, 0xF0, 0x63, 0x61, 0xC8, 0xEF, 0xFD, 0x69, 0xD0, 0xF2, 0xFF, 
  /* RLE: 004 Pixels @ 026,017 */ 4, 0x6A, 0xD1, 0xF3, 0xFF, 
  /* RLE: 001 Pixels @ 030,017 */ 1, 0xC7, 0xEE, 0xFB, 0xFF, 
  /* RLE: 010 Pixels @ 031,017 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 005,018 */ 1, 0xC5, 0xED, 0xFA, 0xFF, 
  /* RLE: 004 Pixels @ 006,018 */ 4, 0x66, 0xCF, 0xF2, 0xFF, 
  /* ABS: 006 Pixels @ 010,018 */ 0, 6, 0x64, 0xCE, 0xF1, 0xFF, 0x5C, 0xC5, 0xEE, 0xFB, 0x51, 0xC5, 0xEE, 0x59, 0x46, 0xC5, 0xEF, 0x00, 0x3E, 0xC3, 0xEE, 0x00, 0x3B, 0xC2, 0xEE, 0x00, 
  /* RLE: 004 Pixels @ 016,018 */ 4, 0x3A, 0xC2, 0xEE, 0x00, 
  /* ABS: 006 Pixels @ 020,018 */ 0, 6, 0x3B, 0xC2, 0xEE, 0x00, 0x3E, 0xC3, 0xEE, 0x00, 0x47, 0xC6, 0xEF, 0x00, 0x55, 0xC9, 0xF0, 0x68, 0x62, 0xCC, 0xF0, 0xFE, 0x65, 0xCF, 0xF1, 0xFF, 
  /* RLE: 004 Pixels @ 026,018 */ 4, 0x66, 0xCF, 0xF2, 0xFF, 
  /* RLE: 001 Pixels @ 030,018 */ 1, 0xC5, 0xED, 0xFA, 0xFF, 
  /* RLE: 010 Pixels @ 031,018 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 011 Pixels @ 005,019 */ 0, 11, 0xC4, 0xEC, 0xF9, 0xFF, 0x61, 0xCD, 0xF0, 0xFF, 0x61, 0xCD, 0xF0, 0xFF, 0x61, 0xCD, 0xF0, 0xFF, 0x60, 0xCC, 0xF0, 0xFF, 0x59, 0xC2, 0xEC, 0xFB, 0x51, 0xBF, 0xEB, 0x59, 0x48, 0xC3, 0xED, 0x00, 
        0x3E, 0xC2, 0xED, 0x00, 0x37, 0xC0, 0xEC, 0x00, 0x35, 0xBF, 0xEC, 0x00, 
  /* RLE: 004 Pixels @ 016,019 */ 4, 0x34, 0xBF, 0xEC, 0x00, 
  /* ABS: 006 Pixels @ 020,019 */ 0, 6, 0x35, 0xBF, 0xEC, 0x00, 0x38, 0xC0, 0xEC, 0x00, 0x3E, 0xC2, 0xED, 0x00, 0x4A, 0xC5, 0xEE, 0x00, 0x55, 0xC4, 0xED, 0x6A, 0x5C, 0xC7, 0xEE, 0xFE, 
  /* RLE: 004 Pixels @ 026,019 */ 4, 0x61, 0xCD, 0xF0, 0xFF, 
  /* RLE: 001 Pixels @ 030,019 */ 1, 0xC4, 0xEC, 0xF9, 0xFF, 
  /* RLE: 010 Pixels @ 031,019 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 005,020 */ 0, 10, 0xC2, 0xEC, 0xF9, 0xFF, 0x5C, 0xCB, 0xEF, 0xFF, 0x5C, 0xCB, 0xEF, 0xFF, 0x5B, 0xCA, 0xEF, 0xFF, 0x55, 0xC1, 0xEB, 0xFB, 0x4F, 0xBD, 0xEA, 0x59, 0x47, 0xC2, 0xEC, 0x00, 0x3C, 0xC1, 0xEC, 0x00, 
        0x34, 0xBF, 0xEB, 0x00, 0x30, 0xBE, 0xEB, 0x00, 
  /* RLE: 006 Pixels @ 015,020 */ 6, 0x2F, 0xBD, 0xEB, 0x00, 
  /* ABS: 010 Pixels @ 021,020 */ 0, 10, 0x30, 0xBE, 0xEB, 0x00, 0x35, 0xBF, 0xEB, 0x00, 0x3D, 0xC1, 0xEC, 0x00, 0x49, 0xC3, 0xED, 0x00, 0x53, 0xC2, 0xEC, 0x6C, 0x58, 0xC6, 0xED, 0xFE, 0x5C, 0xCB, 0xEF, 0xFF, 0x5C, 0xCB, 0xEF, 0xFF, 
        0x5C, 0xCB, 0xEF, 0xFF, 0xC2, 0xEC, 0xF9, 0xFF, 
  /* RLE: 010 Pixels @ 031,020 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,021 */ 0, 26, 0xC0, 0xEB, 0xF9, 0xFF, 0x58, 0xC9, 0xEE, 0xFF, 0x57, 0xC8, 0xEE, 0xFF, 0x51, 0xBF, 0xEA, 0xFB, 0x4C, 0xBC, 0xE9, 0x59, 0x42, 0xC0, 0xEB, 0x00, 0x37, 0xBF, 0xEB, 0x00, 0x2F, 0xBC, 0xEA, 0x00, 
        0x2B, 0xBB, 0xE9, 0x00, 0x2A, 0xBA, 0xE9, 0x00, 0x2B, 0xBB, 0xE9, 0x00, 0x2C, 0xBC, 0xEA, 0x00, 0x2D, 0xBC, 0xEA, 0x00, 0x2D, 0xBC, 0xEA, 0x00, 0x2C, 0xBC, 0xEA, 0x00, 0x2B, 0xBB, 0xE9, 0x00, 0x2A, 0xBA, 0xE9, 0x00, 0x2B, 0xBB, 0xE9, 0x00, 
        0x30, 0xBD, 0xEA, 0x00, 0x38, 0xBF, 0xEB, 0x00, 0x44, 0xC1, 0xEB, 0x00, 0x4F, 0xC0, 0xEB, 0x6E, 0x54, 0xC4, 0xEC, 0xFE, 0x58, 0xC9, 0xEE, 0xFF, 0x58, 0xC9, 0xEE, 0xFF, 0xC0, 0xEB, 0xF9, 0xFF, 
  /* RLE: 010 Pixels @ 031,021 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,022 */ 0, 26, 0xBF, 0xEA, 0xF8, 0xFF, 0x53, 0xC6, 0xED, 0xFF, 0x4E, 0xBD, 0xE9, 0xFB, 0x48, 0xBA, 0xE8, 0x59, 0x3E, 0xBE, 0xEA, 0x00, 0x32, 0xBC, 0xEA, 0x00, 0x2A, 0xBA, 0xE9, 0x00, 0x26, 0xB9, 0xE8, 0x00, 
        0x24, 0xB8, 0xE8, 0x00, 0x26, 0xB9, 0xE8, 0x00, 0x29, 0xBA, 0xE9, 0x00, 0x2E, 0xBB, 0xE9, 0x00, 0x31, 0xBB, 0xE9, 0x00, 0x31, 0xBC, 0xE9, 0x00, 0x2E, 0xBB, 0xE9, 0x00, 0x29, 0xBA, 0xE9, 0x00, 0x26, 0xB9, 0xE8, 0x00, 0x24, 0xB8, 0xE8, 0x00, 
        0x26, 0xB9, 0xE8, 0x00, 0x2A, 0xBA, 0xE9, 0x00, 0x33, 0xBD, 0xEA, 0x00, 0x40, 0xC0, 0xEA, 0x00, 0x4C, 0xBE, 0xEA, 0x70, 0x51, 0xC2, 0xEB, 0xFE, 0x53, 0xC7, 0xED, 0xFF, 0xBF, 0xEA, 0xF8, 0xFF, 
  /* RLE: 010 Pixels @ 031,022 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,023 */ 0, 26, 0xBD, 0xE9, 0xF8, 0xFF, 0x4B, 0xBB, 0xE8, 0xFB, 0x45, 0xB8, 0xE7, 0x59, 0x3A, 0xBC, 0xE9, 0x00, 0x2D, 0xBA, 0xE8, 0x00, 0x24, 0xB8, 0xE7, 0x00, 0x20, 0xB7, 0xE7, 0x00, 0x1F, 0xB6, 0xE7, 0x00, 
        0x20, 0xB7, 0xE7, 0x00, 0x25, 0xB8, 0xE7, 0x00, 0x2D, 0xBA, 0xE8, 0x00, 0x35, 0xBA, 0xE8, 0x00, 0x39, 0xB7, 0xE6, 0x63, 0x39, 0xB8, 0xE7, 0x6C, 0x36, 0xBB, 0xE9, 0x00, 0x2D, 0xBB, 0xE8, 0x00, 0x25, 0xB8, 0xE8, 0x00, 0x20, 0xB7, 0xE7, 0x00, 
        0x1F, 0xB6, 0xE7, 0x00, 0x20, 0xB7, 0xE7, 0x00, 0x25, 0xB8, 0xE8, 0x00, 0x2E, 0xBB, 0xE8, 0x00, 0x3C, 0xBD, 0xE9, 0x00, 0x48, 0xBD, 0xE9, 0x72, 0x4D, 0xC0, 0xEA, 0xFE, 0xBD, 0xE9, 0xF8, 0xFF, 
  /* RLE: 010 Pixels @ 031,023 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,024 */ 0, 26, 0xBA, 0xE5, 0xF6, 0xFB, 0x46, 0xB9, 0xE7, 0x59, 0x39, 0xBB, 0xE8, 0x00, 0x2A, 0xB9, 0xE8, 0x00, 0x20, 0xB6, 0xE7, 0x00, 0x1B, 0xB4, 0xE6, 0x00, 0x19, 0xB3, 0xE6, 0x00, 0x1B, 0xB4, 0xE6, 0x00, 
        0x20, 0xB6, 0xE6, 0x00, 0x29, 0xB8, 0xE7, 0x00, 0x35, 0xBA, 0xE8, 0x00, 0x3F, 0xB5, 0xE6, 0x63, 0x45, 0xB7, 0xE6, 0xFD, 0x46, 0xB9, 0xE7, 0xFE, 0x41, 0xBA, 0xE8, 0x6C, 0x36, 0xBB, 0xE8, 0x00, 0x2A, 0xB9, 0xE7, 0x00, 0x20, 0xB6, 0xE6, 0x00, 
        0x1B, 0xB4, 0xE6, 0x00, 0x1A, 0xB4, 0xE6, 0x00, 0x1B, 0xB4, 0xE6, 0x00, 0x20, 0xB6, 0xE7, 0x00, 0x2B, 0xB9, 0xE8, 0x00, 0x3B, 0xBD, 0xE9, 0x00, 0x48, 0xBD, 0xE8, 0x74, 0xBB, 0xE7, 0xF7, 0xFE, 
  /* RLE: 010 Pixels @ 031,024 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,025 */ 0, 26, 0x8F, 0xD7, 0xF1, 0x79, 0x42, 0xBF, 0xE9, 0x00, 0x30, 0xBA, 0xE7, 0x00, 0x20, 0xB5, 0xE5, 0x00, 0x18, 0xB3, 0xE4, 0x00, 0x15, 0xB2, 0xE4, 0x00, 0x16, 0xB2, 0xE4, 0x00, 0x1B, 0xB4, 0xE5, 0x00, 
        0x24, 0xB7, 0xE6, 0x00, 0x32, 0xB8, 0xE6, 0x00, 0x3F, 0xB5, 0xE5, 0x63, 0x44, 0xB9, 0xE6, 0xFD, 0x47, 0xC1, 0xEA, 0xFF, 0x47, 0xC1, 0xEA, 0xFF, 0x45, 0xBD, 0xE8, 0xFE, 0x41, 0xBA, 0xE7, 0x6C, 0x33, 0xBA, 0xE7, 0x00, 0x25, 0xB7, 0xE6, 0x00, 
        0x1B, 0xB4, 0xE5, 0x00, 0x17, 0xB2, 0xE4, 0x00, 0x15, 0xB2, 0xE4, 0x00, 0x18, 0xB3, 0xE5, 0x00, 0x21, 0xB6, 0xE6, 0x00, 0x31, 0xBB, 0xE7, 0x00, 0x43, 0xC0, 0xE9, 0x00, 0x9B, 0xDD, 0xF3, 0x8E, 
  /* RLE: 010 Pixels @ 031,025 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 026 Pixels @ 005,026 */ 0, 26, 0xB7, 0xE6, 0xF6, 0xE9, 0x40, 0xBC, 0xE8, 0x2D, 0x30, 0xB9, 0xE7, 0x00, 0x1F, 0xB5, 0xE5, 0x00, 0x16, 0xB2, 0xE4, 0x00, 0x14, 0xB1, 0xE4, 0x00, 0x17, 0xB2, 0xE4, 0x00, 0x20, 0xB5, 0xE5, 0x00, 
        0x2E, 0xB7, 0xE6, 0x00, 0x3C, 0xB4, 0xE4, 0x63, 0x41, 0xB7, 0xE6, 0xFD, 0x43, 0xC0, 0xE9, 0xFF, 0x44, 0xC1, 0xE9, 0xFF, 0x44, 0xC1, 0xE9, 0xFF, 0x44, 0xC1, 0xE9, 0xFF, 0x42, 0xBB, 0xE7, 0xFE, 0x3E, 0xB8, 0xE6, 0x6C, 0x2F, 0xB8, 0xE6, 0x00, 
        0x21, 0xB5, 0xE5, 0x00, 0x17, 0xB2, 0xE4, 0x00, 0x14, 0xB1, 0xE4, 0x00, 0x16, 0xB2, 0xE4, 0x00, 0x20, 0xB5, 0xE5, 0x00, 0x30, 0xB9, 0xE6, 0x00, 0x40, 0xB8, 0xE6, 0x2B, 0xB7, 0xE3, 0xF5, 0xE8, 
  /* RLE: 010 Pixels @ 031,026 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 011 Pixels @ 005,027 */ 0, 11, 0xB7, 0xE6, 0xF6, 0xFF, 0x3F, 0xB8, 0xE6, 0xEB, 0x38, 0xB7, 0xE5, 0x30, 0x28, 0xB6, 0xE5, 0x00, 0x1C, 0xB3, 0xE4, 0x00, 0x18, 0xB2, 0xE4, 0x00, 0x1E, 0xB3, 0xE4, 0x00, 0x2B, 0xB5, 0xE5, 0x00, 
        0x3A, 0xB2, 0xE4, 0x63, 0x3F, 0xB6, 0xE5, 0xFD, 0x40, 0xBE, 0xE8, 0xFF, 
  /* RLE: 004 Pixels @ 016,027 */ 4, 0x40, 0xC0, 0xE8, 0xFF, 
  /* ABS: 011 Pixels @ 020,027 */ 0, 11, 0x40, 0xBF, 0xE8, 0xFF, 0x40, 0xBA, 0xE7, 0xFE, 0x3B, 0xB7, 0xE5, 0x6C, 0x2C, 0xB7, 0xE5, 0x00, 0x1F, 0xB4, 0xE5, 0x00, 0x18, 0xB2, 0xE4, 0x00, 0x1B, 0xB3, 0xE4, 0x00, 0x28, 0xB5, 0xE5, 0x00, 
        0x37, 0xB4, 0xE4, 0x2D, 0x3E, 0xB4, 0xE4, 0xE9, 0xB7, 0xE5, 0xF6, 0xFF, 
  /* RLE: 010 Pixels @ 031,027 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 005,028 */ 0, 10, 0xB6, 0xE7, 0xF6, 0xFF, 0x3D, 0xBD, 0xE8, 0xFF, 0x3C, 0xB7, 0xE5, 0xED, 0x36, 0xB6, 0xE5, 0x33, 0x2A, 0xB6, 0xE5, 0x00, 0x26, 0xB6, 0xE5, 0x00, 0x2C, 0xB5, 0xE4, 0x00, 0x38, 0xB1, 0xE3, 0x63, 
        0x3D, 0xB5, 0xE4, 0xFD, 0x3D, 0xBD, 0xE8, 0xFF, 
  /* RLE: 007 Pixels @ 015,028 */ 7, 0x3D, 0xBE, 0xE8, 0xFF, 
  /* ABS: 009 Pixels @ 022,028 */ 0, 9, 0x3D, 0xB9, 0xE6, 0xFE, 0x39, 0xB6, 0xE4, 0x6C, 0x2D, 0xB7, 0xE5, 0x00, 0x26, 0xB6, 0xE5, 0x00, 0x29, 0xB5, 0xE5, 0x00, 0x35, 0xB3, 0xE3, 0x2D, 0x3C, 0xB2, 0xE3, 0xEA, 0x3D, 0xBC, 0xE7, 0xFF, 
        0xB6, 0xE7, 0xF6, 0xFF, 
  /* RLE: 010 Pixels @ 031,028 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 009 Pixels @ 005,029 */ 0, 9, 0xB5, 0xE6, 0xF6, 0xFF, 0x3B, 0xBD, 0xE8, 0xFF, 0x3B, 0xBC, 0xE7, 0xFF, 0x3A, 0xB6, 0xE4, 0xEF, 0x38, 0xB5, 0xE4, 0x36, 0x37, 0xB5, 0xE4, 0x00, 0x39, 0xB1, 0xE2, 0x63, 0x3A, 0xB4, 0xE3, 0xFD, 
        0x3B, 0xBC, 0xE7, 0xFF, 
  /* RLE: 008 Pixels @ 014,029 */ 8, 0x3B, 0xBD, 0xE8, 0xFF, 
  /* ABS: 009 Pixels @ 022,029 */ 0, 9, 0x3B, 0xBD, 0xE7, 0xFF, 0x3A, 0xB8, 0xE5, 0xFE, 0x39, 0xB5, 0xE4, 0x6C, 0x37, 0xB5, 0xE4, 0x00, 0x38, 0xB2, 0xE3, 0x2E, 0x3A, 0xB1, 0xE2, 0xEA, 0x3B, 0xBB, 0xE6, 0xFF, 0x3B, 0xBD, 0xE8, 0xFF, 
        0xB5, 0xE6, 0xF6, 0xFF, 
  /* RLE: 010 Pixels @ 031,029 */ 10, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 008 Pixels @ 005,030 */ 0, 8, 0xBF, 0xE9, 0xF7, 0xFF, 0x53, 0xC5, 0xEA, 0xFF, 0x53, 0xC5, 0xEA, 0xFF, 0x53, 0xC4, 0xEA, 0xFF, 0x53, 0xBE, 0xE7, 0xF0, 0x53, 0xBA, 0xE6, 0x89, 0x53, 0xBD, 0xE7, 0xFD, 0x53, 0xC4, 0xEA, 0xFF, 
  /* RLE: 011 Pixels @ 013,030 */ 11, 0x53, 0xC5, 0xEA, 0xFF, 
  /* ABS: 007 Pixels @ 024,030 */ 0, 7, 0x53, 0xC0, 0xE8, 0xFE, 0x53, 0xBA, 0xE6, 0x89, 0x53, 0xBB, 0xE6, 0xEB, 0x53, 0xC3, 0xE9, 0xFF, 0x53, 0xC5, 0xEA, 0xFF, 0x53, 0xC5, 0xEA, 0xFF, 0xBF, 0xE9, 0xF7, 0xFF, 
  /* RLE: 185 Pixels @ 031,030 */ 185, 0xFF, 0xFF, 0xFF, 0xFF, 
  0
};  // 2535 for 1296 pixels

GUI_CONST_STORAGE GUI_BITMAP bm_fccancel = {
  36, // xSize
  36, // ySize
  144, // BytesPerLine
  32, // BitsPerPixel
  (unsigned char *)_accancel,  // Pointer to picture data
  NULL,  // Pointer to palette
  GUI_DRAW_RLE32
};

/*************************** End of file ****************************/
