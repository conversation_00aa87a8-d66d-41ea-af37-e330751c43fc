#include "includes.h"
//////////////////////////////////////////////////////////////////////////////////	 
//本程序只供学习使用，未经作者许可，不得用于其它任何用途
//Phaserise SuZhou. Yang Long.
////////////////////////////////////////////////////////////////////////////////// 	

void Gain_Set_DA(unsigned char chn, unsigned int Volt_value);
	
// GAIN DA: SYNC(PH10), SCLK(PH11), SDATA(PH12)
// 使用AO1
void SGM5374_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStruct;

	__HAL_RCC_GPIOH_CLK_ENABLE();

	GPIO_InitStruct.Pin = GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(GPIOH, &GPIO_InitStruct);

//	delay_ms(1000);
//	while(1) {
//		Gain_Set_DA(1, 500);
//		delay_ms(100);
//		Gain_Set_DA(1, 1000);
//		delay_ms(100);
//		Gain_Set_DA(1, 1500);
//		delay_ms(100);
//		Gain_Set_DA(1, 2000);
//		delay_ms(100);
//	}
//	while(1) {
//		HV_SetVoltage(1, 0);
//		HV_SetVoltage(2, 0);
//		HV_SetVoltage(3, 0);
//		HV_SetVoltage(4, 0);
//		delay_ms(1000);
//		HV_SetVoltage(1, 600);
//		HV_SetVoltage(2, 600);
//		HV_SetVoltage(3, 600);
//		HV_SetVoltage(4, 600);
//		delay_ms(1000);
//		HV_SetVoltage(1, 1200);
//		HV_SetVoltage(2, 1200);
//		HV_SetVoltage(3, 1200);
//		HV_SetVoltage(4, 1200);
//		delay_ms(1000);
//	}
	Gain_SetGain(1, GAIN_MIN);
}

static void Gain_DA_Reset_En(unsigned char chn)
{
	GAIN_DA_FSYNC(1);
	GAIN_DA_SCLK(1);
	GAIN_DA_FSYNC(0);
}

static void Gain_DA_Write_16Bits(unsigned char chn, unsigned int data)
{
	unsigned char i = 0;

	GAIN_DA_SCLK(1);
	GAIN_DA_FSYNC(0);
	for(i=0 ;i<16 ;i++) 
	{
		if(data & 0x8000) {
			GAIN_DA_SDATA(1); 
		}
		else {
			GAIN_DA_SDATA(0); 
		}
		GAIN_DA_SCLK(0);
		data <<= 1;
		GAIN_DA_SCLK(1);
	}
	GAIN_DA_FSYNC(1);
}

int32_t tst_vol;
void Gain_Set_DA(unsigned char chn, unsigned int Volt_value)   //Volt_value 单位为mv
{
	unsigned char ic_ch;
	unsigned int DAREG = ((unsigned int)( Volt_value *4095 / SGM5347_REF_VOL) & 0x0FFF);
//	DAREG  = DAREG <<4;
//	DAREG |= (1 << 13);
//	DAREG &= ~(1 << 12);
	tst_vol = Volt_value;
	ic_ch = chn;
	switch (ic_ch)
	{
		case 1:  DAREG = DAREG | 0x8000; //AO1
			break;
		case 2:  DAREG = DAREG | 0x4000; //AO2
			break;

//		default: DAREG = DAREG & 0x7FFF;
//			DAREG = DAREG | 0x4000;
//			break;
	}

	Gain_DA_Write_16Bits(chn, DAREG);
}


//uint32_t tst_voltage; // 目标增益所需电压,单位 mV
//设置增益,可调增益:
//单路40mv~1V ---- 7.5dB~55.5dB
//双路认为40mV~1V ---- 15dB~111dB(4dB~100dB)
//void Set_Gain(float gain) {
////	uint32_t voltage; // 目标增益所需电压,单位 mV

////	if(gain < GAIN_MIN){
////		gain = GAIN_MIN;
////	}
////	else if(gain > GAIN_MAX) {
////		gain = GAIN_MAX;
////	}
//////	voltage = ((gain - GAIN_MIN) / 2.0) * 1000 / 50;
////	voltage = 10.0*(gain-11); //mV
//////	voltage = 200;
//////	tst_voltage = voltage;
////	Set_DA(CHN_ADC_SP, voltage);
////	Set_DA(CHN_ADC_DP, voltage);
////	
////	currGain = gain;
////	delay_ms(1);
//}

void Gain_SetGain(uint8_t ch, float gain) {
	uint32_t voltage; // 目标增益所需电压,单位 mV

	if(gain < GAIN_MIN) {
		gain = GAIN_MIN;
	}
	else if(gain > GAIN_MAX) {
		gain = GAIN_MAX;
	}
	voltage = (gain - GAIN_MIN) * (GAIN_MAX_VOL - GAIN_MIN_VOL) / (GAIN_MAX - GAIN_MIN) + GAIN_MIN_VOL; //mV
	Gain_Set_DA(ch, voltage);
}




