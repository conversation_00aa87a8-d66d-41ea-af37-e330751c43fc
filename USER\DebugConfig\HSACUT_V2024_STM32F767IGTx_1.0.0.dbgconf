// File: STM32F76x_77x.dbgconf
// Version: 1.0.0
// Note: refer to STM32F76xxx STM32F77xxx reference manual (RM0410)
//       refer to STM32F76xxx STM32F77xxx datasheets

// <<< Use Configuration Wizard in Context Menu >>>

// <h> Debug MCU configuration register (DBGMCU_CR)
//   <o.2>  DBG_STANDBY              <i> Debug standby mode
//   <o.1>  DBG_STOP                 <i> Debug stop mode
//   <o.0>  DBG_SLEEP                <i> Debug sleep mode
// </h>
DbgMCU_CR = 0x00000007;

// <h> Debug MCU APB1 freeze register (DBGMCU_APB1_FZ)
//                                   <i> Reserved bits must be kept at reset value
//   <o.26> DBG_CAN2_STOP            <i> Debug CAN2 stopped when core is halted
//   <o.25> DBG_CAN1_STOP            <i> Debug CAN1 stopped when core is halted
//   <o.24> DBG_I2C4_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.23> DBG_I2C3_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.22> DBG_I2C2_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.21> DBG_I2C1_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.13> DBG_CAN3_STOP            <i> Debug CAN3 stopped when core is halted
//   <o.12> DBG_IWDG_STOP            <i> Debug independent watchdog stopped when core is halted
//   <o.11> DBG_WWDG_STOP            <i> Debug window watchdog stopped when core is halted
//   <o.10> DBG_RTC_STOP             <i> RTC stopped when core is halted
//   <o.9>  DBG_LPTIM1_STOP          <i> LPTMI1 counter stopped when core is halted
//   <o.8>  DBG_TIM14_STOP           <i> TIM14 counter stopped when core is halted
//   <o.7>  DBG_TIM13_STOP           <i> TIM13 counter stopped when core is halted
//   <o.6>  DBG_TIM12_STOP           <i> TIM12 counter stopped when core is halted
//   <o.5>  DBG_TIM7_STOP            <i> TIM7 counter stopped when core is halted
//   <o.4>  DBG_TIM6_STOP            <i> TIM6 counter stopped when core is halted
//   <o.3>  DBG_TIM5_STOP            <i> TIM5 counter stopped when core is halted
//   <o.2>  DBG_TIM4_STOP            <i> TIM4 counter stopped when core is halted
//   <o.1>  DBG_TIM3_STOP            <i> TIM3 counter stopped when core is halted
//   <o.0>  DBG_TIM2_STOP            <i> TIM2 counter stopped when core is halted
// </h>
DbgMCU_APB1_Fz = 0x00000000;

// <h> Debug MCU APB2 freeze register (DBGMCU_APB2_FZ)
//                                   <i> Reserved bits must be kept at reset value
//   <o.18> DBG_TIM11_STOP           <i> TIM11 counter stopped when core is halted
//   <o.17> DBG_TIM10_STOP           <i> TIM10 counter stopped when core is halted
//   <o.16> DBG_TIM9_STOP            <i> TIM9 counter stopped when core is halted
//   <o.1>  DBG_TIM8_STOP            <i> TIM8 counter stopped when core is halted
//   <o.0>  DBG_TIM1_STOP            <i> TIM1 counter stopped when core is halted
// </h>
DbgMCU_APB2_Fz = 0x00000000;

// <h> TPIU Pin Routing (TRACECLK fixed on Pin PE2)
//   <i> TRACECLK: Pin PE2
//   <o1> TRACED0
//     <i> ETM Trace Data 0
//       <0x00040003=> Pin PE3
//       <0x00020001=> Pin PC1
//       <0x0006000D=> Pin PG13
//   <o2> TRACED1
//     <i> ETM Trace Data 1
//       <0x00040004=> Pin PE4
//       <0x00020008=> Pin PC8
//       <0x0006000E=> Pin PG14
//   <o3> TRACED2
//     <i> ETM Trace Data 2
//       <0x00040005=> Pin PE5
//       <0x00030002=> Pin PD2
//   <o4> TRACED3
//     <i> ETM Trace Data 3
//       <0x00040006=> Pin PE6
//       <0x0002000C=> Pin PC12
// </h>
TraceClk_Pin = 0x00040002;
TraceD0_Pin  = 0x00040003;
TraceD1_Pin  = 0x00040004;
TraceD2_Pin  = 0x00040005;
TraceD3_Pin  = 0x00040006;

// <<< end of configuration section >>>
