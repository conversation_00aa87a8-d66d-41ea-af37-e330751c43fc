#ifndef _LED_H
#define _LED_H

//LED1-PI6
#define LED1(n)		(n?HAL_GPIO_WritePin(GPIOI, GPIO_PIN_6, GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOI, GPIO_PIN_6,GP<PERSON>_PIN_RESET))
#define LED1_Toggle (HAL_GPIO_TogglePin(GPIOI, GPIO_PIN_6))
#define LED1_ON		LED1(1)
#define LED1_OFF	LED1(0)

#define LED_COM_IND_OFF 	LED1_OFF
#define LED_COM_IND_ON	 	LED1_ON

#define LED_EMIT_IND_OFF 	LED1_OFF
#define LED_EMIT_IND_ON	 	LED1_ON

void LED_Init(void);

#endif
