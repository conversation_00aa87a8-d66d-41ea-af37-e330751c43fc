#ifndef __PS_FILES_H
#define __PS_FILES_H 					   
#include "main.h"



uint8_t PS_SaveGaugeResTable(void);
uint8_t File_PS_ReadWave(char *path, int16_t *buf, uint16_t len, int32_t *start_ns, int32_t *len_ns, int32_t *zero_ns, 
float *temperature, float *gain, float *k1, float *k2);

uint8_t File_PS_SaveInfoText(char *path, char *filename, char *text);
uint8_t File_PS_ReadInfoText(char *path, char *filename, char *destbuf1, uint16_t len);
uint8_t File_PS_SaveWave(char *path, char *filename, int16_t *src, uint16_t len, uint8_t isSecret);


#endif


                                                  