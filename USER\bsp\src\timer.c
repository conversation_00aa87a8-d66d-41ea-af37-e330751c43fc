#include "includes.h"

//extern __IO int32_t OS_TimeMS;

TIM_HandleTypeDef TIM2_Handler;      //定时器句柄 
TIM_HandleTypeDef TIM3_Handler;      //定时器句柄 
TIM_HandleTypeDef TIM4_Handler;      //定时器句柄


void TIM2_Init(u16 arr,u16 psc)
{
    TIM2_Handler.Instance=TIM2;                          //通用定时器3
    TIM2_Handler.Init.Prescaler=psc;                     //分频
    TIM2_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    //向上计数器
    TIM2_Handler.Init.Period=arr;                        //自动装载值
    TIM2_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;//时钟分频因子
    HAL_TIM_Base_Init(&TIM2_Handler);
    HAL_TIM_Base_Stop(&TIM2_Handler);

	__HAL_TIM_CLEAR_IT(&TIM2_Handler, TIM_IT_UPDATE);
    HAL_TIM_Base_Start_IT(&TIM2_Handler);
	__HAL_TIM_CLEAR_IT(&TIM2_Handler, TIM_IT_UPDATE);
	HAL_TIM_Base_Stop(&TIM2_Handler);
	TIM2_Handler.Instance->CNT = 0;
	
//	HAL_TIM_Base_Start(&TIM2_Handler);
}

//通用定时器3中断初始化
//arr：自动重装值。
//psc：时钟预分频数
//定时器溢出时间计算方法:Tout=((arr+1)*(psc+1))/Ft us.
//Ft=定时器工作频率,单位:Mhz
//这里使用的是定时器3!(定时器3挂在APB1上，时钟为HCLK/2)
void TIM3_Init(u16 arr,u16 psc)
{
	TIM3_Handler.Instance=TIM3;                          //通用定时器3
    TIM3_Handler.Init.Prescaler=psc;                     //分频
    TIM3_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    //向上计数器
    TIM3_Handler.Init.Period=arr;                        //自动装载值
    TIM3_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;//时钟分频因子
    HAL_TIM_Base_Init(&TIM3_Handler);
    HAL_TIM_Base_Stop(&TIM3_Handler);

	__HAL_TIM_CLEAR_IT(&TIM3_Handler, TIM_IT_UPDATE);
    HAL_TIM_Base_Start_IT(&TIM3_Handler); //使能定时器3和定时器3中断   
	__HAL_TIM_CLEAR_IT(&TIM3_Handler, TIM_IT_UPDATE);
	HAL_TIM_Base_Stop(&TIM3_Handler);
	TIM3_Handler.Instance->CNT = 0;
}

//通用定时器4中断初始化
//arr：自动重装值。
//psc：时钟预分频数
//定时器溢出时间计算方法:Tout=((arr+1)*(psc+1))/Ft us.
//Ft=定时器工作频率,单位:Mhz
//这里使用的是定时器4!(定时器4挂在APB1上，时钟为HCLK/2)
void TIM4_Init(u16 arr,u16 psc)
{  
    TIM4_Handler.Instance=TIM4;                          //通用定时器4
    TIM4_Handler.Init.Prescaler=psc;                     //分频
    TIM4_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    //向上计数器
    TIM4_Handler.Init.Period=arr;                        //自动装载值
    TIM4_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;
    HAL_TIM_Base_Init(&TIM4_Handler);
    
    HAL_TIM_Base_Start_IT(&TIM4_Handler); //使能定时器3和定时器3中断   
}

//定时器底册驱动，开启时钟，设置中断优先级
//此函数会被HAL_TIM_Base_Init()函数调用
void HAL_TIM_Base_MspInit(TIM_HandleTypeDef *htim)
{
	if(htim->Instance==TIM2)
	{
		__HAL_RCC_TIM2_CLK_ENABLE();            //使能TIM3时钟
		HAL_NVIC_SetPriority(TIM2_IRQn,2,3);    //设置中断优先级，抢占优先级1，子优先级3
		HAL_NVIC_EnableIRQ(TIM2_IRQn);          //开启ITM3中断   
	}
	else if(htim->Instance==TIM3)
	{
		__HAL_RCC_TIM3_CLK_ENABLE();            //使能TIM3时钟
		HAL_NVIC_SetPriority(TIM3_IRQn,2,3);    //设置中断优先级，抢占优先级1，子优先级3
		HAL_NVIC_EnableIRQ(TIM3_IRQn);          //开启ITM3中断   
	}
	else if(htim->Instance==TIM4)
	{ 
		__HAL_RCC_TIM4_CLK_ENABLE();            //使能TIM4时钟
		HAL_NVIC_SetPriority(TIM4_IRQn,2,3);    //设置中断优先级，抢占优先级2，子优先级0
		HAL_NVIC_EnableIRQ(TIM4_IRQn);          //开启ITM4中断
	}    
}

void TIM2_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM2_Handler);
}

//定时器3中断服务函数
void TIM3_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM3_Handler);
}

//定时器4中断服务函数
void TIM4_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM4_Handler);
}

//定时器3中断服务函数调用
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntEnter();    
	#endif
	if(htim==(&TIM2_Handler))
	{
		if(g_Dev.emit_en) {
//			LED1_Toggle;
			EMIT_Trig(1); delay_us(10); EMIT_Trig(0);
			LedTask_FlashEmitIndLed();
		}
		__HAL_TIM_CLEAR_IT(&TIM2_Handler, TIM_IT_UPDATE);
	}
	if(htim==(&TIM3_Handler))
	{
		Modbus_Uart1RxDone();
		__HAL_TIM_CLEAR_IT(&TIM3_Handler, TIM_IT_UPDATE);
	}
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntExit();  											 
	#endif
}

// PWM 调整液晶背光亮度
TIM_HandleTypeDef TIM12_Handler;         //定时器句柄 
TIM_OC_InitTypeDef TIM12_CH1Handler;     //定时器3通道4句柄
//TIM3 PWM部分初始化 
//PWM输出初始化
//arr：自动重装值
//psc：时钟预分频数
void TIM12_PWM_Init(u16 arr,u16 psc)
{ 
    TIM12_Handler.Instance=TIM12;            //定时器3
    TIM12_Handler.Init.Prescaler=psc;       //定时器分频
    TIM12_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;//向上计数模式
    TIM12_Handler.Init.Period=arr;          //自动重装载值
    TIM12_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;
    HAL_TIM_PWM_Init(&TIM12_Handler);       //初始化PWM
    
    TIM12_CH1Handler.OCMode=TIM_OCMODE_PWM1; //模式选择PWM1
    TIM12_CH1Handler.Pulse=arr/2;            //设置比较值,此值用来确定占空比，
                                            //默认比较值为自动重装载值的一半,即占空比为50%
    TIM12_CH1Handler.OCPolarity=TIM_OCPOLARITY_LOW; //输出比较极性为低 
    HAL_TIM_PWM_ConfigChannel(&TIM12_Handler,&TIM12_CH1Handler,TIM_CHANNEL_1);//配置TIM3通道4
    HAL_TIM_PWM_Start(&TIM12_Handler,TIM_CHANNEL_1);//开启PWM通道4
}

//定时器底层驱动，时钟使能，引脚配置
//此函数会被HAL_TIM_PWM_Init()调用
//htim:定时器句柄
void HAL_TIM_PWM_MspInit(TIM_HandleTypeDef *htim)
{
	GPIO_InitTypeDef GPIO_Initure;
	__HAL_RCC_TIM12_CLK_ENABLE();			//使能定时器3
	__HAL_RCC_GPIOH_CLK_ENABLE();			//开启GPIOB时钟

	GPIO_Initure.Pin=GPIO_PIN_6;           	//PB1
	GPIO_Initure.Mode=GPIO_MODE_AF_PP;  	//复用推完输出
	GPIO_Initure.Pull=GPIO_PULLUP;          //上拉
	GPIO_Initure.Speed=GPIO_SPEED_HIGH;     //高速
	GPIO_Initure.Alternate=GPIO_AF9_TIM12;	//PB1复用为TIM3_CH4
	HAL_GPIO_Init(GPIOH,&GPIO_Initure);
}

//设置TIM通道4的占空比
//compare:比较值
void TIM_SetTIM12Compare1(u32 compare)
{
	TIM12->CCR1=compare; 
}
int32_t testccc=0;
void TIM_SetTIM12Pwm(u8 pwm)
{
	uint32_t arr;
	arr = TIM12->ARR;

	TIM_SetTIM12Compare1(arr - pwm*arr/100);
}

// 设置重复次数所对应的定时器参数
// timer3 分频100us
void Timer_setEmitRate(uint16_t rate) {
	if(rate == 0) {
		HAL_TIM_Base_Stop(&TIM2_Handler);
		TIM2->CNT = 0;
	}
	else {
		HAL_TIM_Base_Stop(&TIM2_Handler);
		TIM2->ARR = (1000000 / rate) - 1;
		TIM2->CNT = 0;
		HAL_TIM_Base_Start(&TIM2_Handler);
	}
}

void StartRTUCheckTmr(void)
{
	TIM3_Handler.Instance=TIM3;
	TIM3_Handler.Instance->CNT = 0;
//	HAL_TIM_Base_Start_IT(&TIM3_Handler); //使能定时器3和定时器3更新中断：TIM_IT_UPDATE    
	HAL_TIM_Base_Start(&TIM3_Handler);
}

void StopRTUCheckTmr(void)
{
	TIM3_Handler.Instance=TIM3;
	TIM3_Handler.Instance->CNT = 0;
//	HAL_TIM_Base_Stop_IT(&TIM3_Handler); //使能定时器3和定时器3更新中断：TIM_IT_UPDATE
	HAL_TIM_Base_Stop(&TIM3_Handler);
}

void ClearRTUCheckTmr(void)
{
	TIM3_Handler.Instance=TIM3;
	TIM3_Handler.Instance->CNT = 0;
}

