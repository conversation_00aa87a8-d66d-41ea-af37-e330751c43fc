##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.1"
options:
    HSACUT_V2024:
        files: {}
        virtualPathFiles:
            <virtual_root>/EMWIN_CONFIG/GUI_X.c: ""
            <virtual_root>/EMWIN_CONFIG/GUIDRV_Template.c: ""
            <virtual_root>/EMWIN_CONFIG/LCDConf_FlexColor_Template.c: ""
            <virtual_root>/BSP/ad5304.c: ""
            <virtual_root>/BSP/qspi.c: ""
            <virtual_root>/BSP/sdmmc_sdcard.c: ""
            <virtual_root>/BSP/ad9258.c: ""
            <virtual_root>/BSP/ds18b20.c: ""
            <virtual_root>/BSP/beep.c: ""
            <virtual_root>/BSP/oled.c: ""
