/**
******************************************************************************
* Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
* @file    : main.c
* @project : HSACUT_V2024
* @brief   : 主函数
* <AUTHOR>
* @since   ：2024/04/15
* @version : V1.0 
* @history :
* 2024/04/15: 
*  1) 首次建立, 源工程来自 ACUT_GCV1,, 大规模编写适配代码;
*  2) 版本号为: "V1.0(2401a)";
* 2024/04/30: 
*  1) 调整可控增益范围为 -21dB ~ 75dB
*  
*
******************************************************************************
* @attention
* 1. 远端ip *************(8089), 本地 ************(8089)
******************************************************************************
* @feature
*
******************************************************************************
*/

#include "includes.h"
#include "malloc.h"
#include "lwip/netif.h"
#include "lwip_comm.h"
#include "lwipopts.h"
#include "includes.h"


//启动任务
#define 	START_TASK_PRIO						3
#define 	START_STK_SIZE 						128
OS_TCB 		StartTaskTCB;
CPU_STK 	START_TASK_STK[START_STK_SIZE];
void 		start_task(void *p_arg);

//LED任务
#define 	LED_TASK_PRIO						9
#define 	LED_STK_SIZE						64
OS_TCB 		LedTaskTCB;
CPU_STK		LED_TASK_STK[LED_STK_SIZE];
void 		led_task(void *pdata);  

//App主任务
#define 	APP_MAIN_TASK_PRIO					10
#define 	APP_MAIN_TASK_STK_SIZE				128
OS_TCB 		MainTaskTCB;
CPU_STK		APP_MAIN_TASK_STK[APP_MAIN_TASK_STK_SIZE];
void 		app_main_task(void *pdata);


int main(void)
{
	OS_ERR err;
	CPU_SR_ALLOC();
	Write_Through();                //Cahce强制透写
	MPU_Memory_Protection();        //保护相关存储区域
	Cache_Enable();                 //打开L1-Cache
	
	Bsp_Init();
	
	OSInit(&err); //初始化UCOSIII
	
	while(lwip_comm_init()) //lwip初始化
	{
		delay_ms(500);
	}
	while(udp_demo_init()) //初始化udp_demo(创建udp_demo线程)
	{
		delay_ms(500);
	}
	while(udp_battery_init()) //初始化udp_battery(创建udp_battery线程)
	{
		delay_ms(500);
	}
	
	
	OS_CRITICAL_ENTER();            //进入临界区
	//创建开始任务
	OSTaskCreate((OS_TCB 	* )&StartTaskTCB,		//任务控制块
		(CPU_CHAR	* )"start task", 		//任务名字
		(OS_TASK_PTR )start_task, 			//任务函数
		(void		* )0,					//传递给任务函数的参数
		(OS_PRIO	  )START_TASK_PRIO,     //任务优先级
		(CPU_STK   * )&START_TASK_STK[0],	//任务堆栈基地址
		(CPU_STK_SIZE)START_STK_SIZE/10,	//任务堆栈深度限位
		(CPU_STK_SIZE)START_STK_SIZE,		//任务堆栈大小
		(OS_MSG_QTY  )0,					//任务内部消息队列能够接收的最大消息数目,为0时禁止接收消息
		(OS_TICK	  )0,					//当使能时间片轮转时的时间片长度，为0时为默认长度，
		(void   	* )0,					//用户补充的存储区
		(OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR, //任务选项
		(OS_ERR 	* )&err);				//存放该函数错误时的返回值
	OS_CRITICAL_EXIT(); //退出临界区	 
	OSStart(&err); //开启UCOSIII
}

//开始任务函数
void start_task(void *p_arg)
{
	OS_ERR err;
	CPU_SR_ALLOC();
	p_arg = p_arg;

	CPU_Init();
#if OS_CFG_STAT_TASK_EN > 0u
   OSStatTaskCPUUsageInit(&err);  	//统计任务                
#endif
	
#ifdef CPU_CFG_INT_DIS_MEAS_EN		//如果使能了测量中断关闭时间
    CPU_IntDisMeasMaxCurReset();	
#endif

#if	OS_CFG_SCHED_ROUND_ROBIN_EN  //当使用时间片轮转的时候
	//使能时间片轮转调度功能,设置默认的时间片长度
	OSSchedRoundRobinCfg(DEF_ENABLED,1,&err);  
#endif
	
	#if LWIP_DHCP
	lwip_comm_dhcp_creat(); //创建DHCP任务
#endif

	//Emit task create
//	emit_task_create();
	
	__HAL_RCC_CRC_CLK_ENABLE();		//使能CRC时钟

	OS_CRITICAL_ENTER();	//进入临界区
	//LED任务
	OSTaskCreate((OS_TCB*     )&LedTaskTCB,		
				 (CPU_CHAR*   )"Led task", 		
                 (OS_TASK_PTR )led_task, 			
                 (void*       )0,					
                 (OS_PRIO	  )LED_TASK_PRIO,     
                 (CPU_STK*    )&LED_TASK_STK[0],	
                 (CPU_STK_SIZE)LED_STK_SIZE/10,	
                 (CPU_STK_SIZE)LED_STK_SIZE,		
                 (OS_MSG_QTY  )0,					
                 (OS_TICK	  )0,  					
                 (void*       )0,					
                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
                 (OS_ERR*     )&err);
	//AppMain任务
	OSTaskCreate((OS_TCB*     )&MainTaskTCB,		
				 (CPU_CHAR*   )"App Main task", 		
                 (OS_TASK_PTR )App_MainTask, 			
                 (void*       )0,					
                 (OS_PRIO	  )APP_MAIN_TASK_PRIO,     
                 (CPU_STK*    )&APP_MAIN_TASK_STK[0],	
                 (CPU_STK_SIZE)APP_MAIN_TASK_STK_SIZE/10,	
                 (CPU_STK_SIZE)APP_MAIN_TASK_STK_SIZE,		
                 (OS_MSG_QTY  )0,					
                 (OS_TICK	  )0,  					
                 (void*       )0,					
                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
                 (OS_ERR*     )&err);
//	//EMIT发射任务
//	OSTaskCreate((OS_TCB*     )&EmitTaskTCB,		
//				 (CPU_CHAR*   )"Emit task", 		
//                 (OS_TASK_PTR )emit_task_thread, 			
//                 (void*       )0,					
//                 (OS_PRIO	  )EMIT_TASK_PRIO,     
//                 (CPU_STK*    )&EMIT_TASK_STK[0],	
//                 (CPU_STK_SIZE)EMIT_STK_SIZE/10,	
//                 (CPU_STK_SIZE)EMIT_STK_SIZE,		
//                 (OS_MSG_QTY  )0,					
//                 (OS_TICK	  )0,  					
//                 (void*       )0,					
//                 (OS_OPT      )OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
//                 (OS_ERR*     )&err);
//	//创建定时器1
//	OSTmrCreate((OS_TMR	*)&tmr1,		//定时器1
//                (CPU_CHAR	*)"tmr1",		//定时器名字
//                (OS_TICK	 )0,			//0*10=10ms
//                (OS_TICK	 )1,          		//1*10=10ms
//                (OS_OPT		 )OS_OPT_TMR_PERIODIC, //周期模式
//                (OS_TMR_CALLBACK_PTR)tmr1_callback,//定时器1回调函数
//                (void	    	*)0,			//参数为0
//                (OS_ERR	    	*)&err);		//返回的错误码
	OS_TaskSuspend((OS_TCB*)&StartTaskTCB,&err);		//挂起开始任务			 
	OS_CRITICAL_EXIT();	//退出临界区
}


//led任务
//void app_main_task(void *pdata)
//{
//	OS_ERR err;
//	while(1)
//	{

//		OSTimeDlyHMSM(0,0,0,1000,OS_OPT_TIME_PERIODIC,&err);//延时500ms
// 	}
//}

//int tstcnt = 0;

#define COMM_LED_ENABLED	0
static uint8_t ledCommIndFlashNum = 0;
static uint8_t ledEmitIndFlashNum = 0;
void LedTask_FlashCommIndLed(void) {
	#if COMM_LED_ENABLED
	OS_ERR err;
	ledCommIndFlashNum++;
	if(ledCommIndFlashNum > 50) ledCommIndFlashNum = 50;
	OSTaskSemPost(&LedTaskTCB, OS_OPT_POST_NONE, &err);
	#endif
}


void LedTask_FlashEmitIndLed(void) {
	OS_ERR err;
	ledEmitIndFlashNum++;
	if(ledEmitIndFlashNum > 50) ledEmitIndFlashNum = 50;
	OSTaskSemPost(&LedTaskTCB, OS_OPT_POST_NONE, &err);
}

//led任务
static int16_t ledFlashTimeMs = 0;
void led_task(void *pdata)
{
	OS_ERR err;

	while(1)
	{
		if((ledCommIndFlashNum == 0) && (ledEmitIndFlashNum == 0)) {
			LED_EMIT_IND_ON; //常量
			OSTaskSemPend(0, OS_OPT_PEND_BLOCKING, 0, &err);
		}
		
		if((ledCommIndFlashNum >= 2) || (ledEmitIndFlashNum >= 2)) {
			ledFlashTimeMs = 50 / (ledCommIndFlashNum > ledEmitIndFlashNum ? ledCommIndFlashNum : ledEmitIndFlashNum);
		}
		else {
			ledFlashTimeMs = 50;
		}
		
		if((ledCommIndFlashNum > 0) || (ledEmitIndFlashNum > 0)) {
			if(ledCommIndFlashNum > 0) {
				LED_COM_IND_ON;
			}
			if(ledEmitIndFlashNum > 0) {
				LED_EMIT_IND_ON;
			}
			
			OSTimeDlyHMSM(0, 0, 0, ledFlashTimeMs, OS_OPT_TIME_PERIODIC, &err);
			if(ledCommIndFlashNum > 0) {
				LED_COM_IND_OFF;
				ledCommIndFlashNum--;
			}
			if(ledEmitIndFlashNum > 0) {
				LED_EMIT_IND_OFF;
				ledEmitIndFlashNum--;
			}

			OSTimeDlyHMSM(0, 0, 0, (ledFlashTimeMs > 30 ? 30 : ledFlashTimeMs), OS_OPT_TIME_PERIODIC, &err);
		}
 	}
}
