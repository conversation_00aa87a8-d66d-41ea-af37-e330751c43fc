#ifndef __UTILS_H
#define __UTILS_H	 
#include "sys.h"
#include "main.h"

void float32_cvrt2chars(char *buf, float f);
float chars_cvrt2float32(char *buf);
void float64_cvrt2chars(char *buf, double f);
double chars_cvrt2float64(char *buf);

void int32_cvrt2chars(char *buf, int32_t integer);
int32_t chars_cvrt2integer(char *buf, uint8_t len);
uint8_t isNumber_f(float v);
uint32_t Utils_GetMaxValIdx_s16(int16_t *buf, uint32_t len);
uint8_t Utils_CalcZCP_s16(uint32_t *zcp_idx, int16_t *src, uint32_t len, uint8_t dir);


#endif

