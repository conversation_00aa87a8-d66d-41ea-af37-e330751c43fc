/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : GUIDEMO_Skinning.c
Purpose     : Plays with some dialogs and uses skinning
---------------------------END-OF-HEADER------------------------------
*/

/**
  ******************************************************************************
  * @file    GUIDEMO_Skinning.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Plays with some dialogs and uses skinning
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


#include "GUIDEMO.h"

#if (SHOW_GUIDEMO_SKINNING && GUI_WINSUPPORT && GUI_SUPPORT_MEMDEV)

#include <string.h>

#include "DIALOG.h"
#include "MESSAGEBOX.h"

/*********************************************************************
*
*       Types
*
**********************************************************************
*/
typedef struct {
  int x, y, Pressed, Delay;
} ANIM;

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/
#define FRAMEWIN_RADIUS 5

//
// Segger logo 60x30
//
static GUI_CONST_STORAGE unsigned long acSeggerLogo_60x30[] = {
  0x9F292929, 0x10292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x10292929, 0x9F292929,
  0x10292929, 0x00515151, 0x00AFAFAF, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA,
        0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA,
        0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00AFAFAF, 0x00515151, 0x10292929,
  0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00AA6661, 0x00AA6661, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x009D4F49, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x00AA6661, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00D1ADAA, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00F8F3F3, 0x009D4F49, 0x0096433D, 0x00BD8A86, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00E5D0CF, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x009D4F49, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00D1ADAA, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E,
        0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E,
        0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00D8B9B6, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00D1ADAA, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00C49592, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00D1ADAA, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00C49592, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E,
        0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E,
        0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00B0726E, 0x00D8B9B6, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00D1ADAA, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x009D4F49, 0x00EBDCDB, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F,
        0x00F2F2F2, 0x00BCBCBC, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00FFFFFF, 0x00AFAFAF, 0x00363636, 0x00292929, 0x00292929, 0x00444444, 0x00BCBCBC, 0x00FFFFFF, 0x00F2F2F2, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929,
        0x00797979, 0x00FFFFFF, 0x00D7D7D7, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00444444, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00E5D0CF, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00292929, 0x00515151, 0x00E4E4E4, 0x00A1A1A1, 0x00292929,
        0x00BCBCBC, 0x00949494, 0x00292929, 0x00A1A1A1, 0x00CACACA, 0x00CACACA, 0x00D7D7D7, 0x00E4E4E4, 0x00292929, 0x00515151, 0x00BCBCBC, 0x00BCBCBC, 0x00363636, 0x005F5F5F, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00A1A1A1, 0x00CACACA, 0x00797979,
        0x00292929, 0x00CACACA, 0x00CACACA, 0x00292929, 0x00A1A1A1, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00FFFFFF, 0x00292929, 0x00797979, 0x00CACACA, 0x00BCBCBC, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00D1ADAA, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00F8F3F3, 0x009D4F49, 0x0096433D, 0x00BD8A86, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00444444, 0x00292929, 0x00515151, 0x00797979, 0x00AFAFAF,
        0x00FFFFFF, 0x00949494, 0x00292929, 0x00515151, 0x005F5F5F, 0x005F5F5F, 0x00BCBCBC, 0x00A1A1A1, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00FFFFFF, 0x00292929, 0x00515151, 0x00FFFFFF, 0x00E4E4E4, 0x00CACACA,
        0x00CACACA, 0x00E4E4E4, 0x00CACACA, 0x00292929, 0x00515151, 0x005F5F5F, 0x005F5F5F, 0x00949494, 0x00FFFFFF, 0x00292929, 0x005F5F5F, 0x00949494, 0x00878787, 0x00292929, 0x00797979, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x00AA6661, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00A1A1A1, 0x006C6C6C, 0x00363636, 0x00292929,
        0x00878787, 0x00949494, 0x00292929, 0x00797979, 0x00949494, 0x00949494, 0x00CACACA, 0x00949494, 0x00292929, 0x00BCBCBC, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00CACACA, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00A1A1A1, 0x00292929,
        0x00292929, 0x00949494, 0x00CACACA, 0x00292929, 0x00797979, 0x00949494, 0x00949494, 0x00BCBCBC, 0x00FFFFFF, 0x00292929, 0x00444444, 0x005F5F5F, 0x00363636, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00A35B55, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x009D4F49, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00363636, 0x00797979, 0x00F2F2F2, 0x00E4E4E4, 0x00292929,
        0x005F5F5F, 0x00949494, 0x00292929, 0x00A1A1A1, 0x00CACACA, 0x00CACACA, 0x00D7D7D7, 0x00D7D7D7, 0x00292929, 0x00515151, 0x00E4E4E4, 0x00E4E4E4, 0x00515151, 0x00292929, 0x00CACACA, 0x005F5F5F, 0x00292929, 0x00A1A1A1, 0x00FFFFFF, 0x00A1A1A1,
        0x00292929, 0x00949494, 0x00CACACA, 0x00292929, 0x00A1A1A1, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00FFFFFF, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00D7D7D7, 0x00292929, 0x00878787, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00AA6661, 0x00AA6661, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00878787, 0x00292929, 0x00292929, 0x00292929, 0x00363636,
        0x00BCBCBC, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x00444444, 0x00292929, 0x00CACACA, 0x00D7D7D7, 0x00444444, 0x00292929, 0x00292929, 0x00292929,
        0x00363636, 0x00949494, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00CACACA, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E4E4E4, 0x00CACACA, 0x00CACACA, 0x00F2F2F2,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00CACACA, 0x00CACACA, 0x00FFFFFF, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00CACACA, 0x00E4E4E4,
        0x00E4E4E4, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929,
  0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929,
  0x10292929, 0x00515151, 0x00AFAFAF, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA,
        0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA,
        0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00AFAFAF, 0x00515151, 0x10292929,
  0x9F292929, 0x10292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x10292929, 0x9F292929
};

GUI_CONST_STORAGE GUI_BITMAP bmSeggerLogo_60x30 = {
  60, /* XSize */
  30, /* YSize */
  240, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)acSeggerLogo_60x30,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

//
// Antialiased font
//
/* Start of unicode area <Basic Latin> */
GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0020[  1] = { /* code 0020, SPACE */
  0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0021[ 15] = { /* code 0021, EXCLAMATION MARK */
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0x00,
  0x00,
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0022[ 15] = { /* code 0022, QUOTATION MARK */
  0xFF, 0x00, 0xFF,
  0xFF, 0x00, 0xFF,
  0xEF, 0x00, 0xEE,
  0xCC, 0x00, 0xCC,
  0x9A, 0x00, 0x99
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0023[ 90] = { /* code 0023, NUMBER SIGN */
  0x00, 0x01, 0xFE, 0x00, 0x2F, 0xE0,
  0x00, 0x04, 0xFB, 0x00, 0x5F, 0xB0,
  0x00, 0x07, 0xF8, 0x00, 0x8F, 0x80,
  0x00, 0x0A, 0xF5, 0x00, 0xBF, 0x50,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x5F, 0xA0, 0x05, 0xFA, 0x00,
  0x00, 0x7F, 0x70, 0x08, 0xF7, 0x00,
  0x00, 0xAF, 0x30, 0x0B, 0xF3, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x05, 0xFB, 0x00, 0x5F, 0xA0, 0x00,
  0x08, 0xF8, 0x00, 0x8F, 0x70, 0x00,
  0x0B, 0xF5, 0x00, 0xBF, 0x40, 0x00,
  0x0E, 0xF2, 0x00, 0xEF, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0024[ 90] = { /* code 0024, DOLLAR SIGN */
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x03, 0xAE, 0xFE, 0x92, 0x00,
  0x4F, 0xFF, 0xFF, 0xFE, 0x10,
  0xBF, 0xA2, 0xF2, 0xBF, 0x90,
  0xFF, 0x10, 0xF0, 0x2F, 0xE0,
  0xEF, 0x30, 0xF0, 0x00, 0x00,
  0x8F, 0xE7, 0xF0, 0x00, 0x00,
  0x09, 0xFF, 0xFB, 0x50, 0x00,
  0x00, 0x28, 0xFF, 0xFB, 0x10,
  0x00, 0x00, 0xF3, 0xBF, 0x80,
  0x00, 0x00, 0xF0, 0x1F, 0xD0,
  0x00, 0x00, 0xF0, 0x0F, 0xF0,
  0xEF, 0x20, 0xF0, 0x3F, 0xE0,
  0xAF, 0xB2, 0xF2, 0xCF, 0x90,
  0x2E, 0xFF, 0xFF, 0xFE, 0x20,
  0x02, 0xAE, 0xFE, 0xA2, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0025[135] = { /* code 0025, PERCENT SIGN */
  0x07, 0xDF, 0xD6, 0x00, 0x00, 0x03, 0xFD, 0x00, 0x00,
  0x5F, 0x90, 0x9F, 0x50, 0x00, 0x0B, 0xF4, 0x00, 0x00,
  0xCF, 0x20, 0x2F, 0xC0, 0x00, 0x5F, 0xB0, 0x00, 0x00,
  0xFF, 0x00, 0x0F, 0xF0, 0x00, 0xDF, 0x20, 0x00, 0x00,
  0xFF, 0x00, 0x0F, 0xF0, 0x07, 0xF9, 0x00, 0x00, 0x00,
  0xCF, 0x20, 0x2F, 0xC0, 0x1E, 0xE1, 0x00, 0x00, 0x00,
  0x5F, 0x90, 0x9F, 0x50, 0x9F, 0x60, 0x00, 0x00, 0x00,
  0x06, 0xDF, 0xD5, 0x03, 0xFC, 0x07, 0xDF, 0xD6, 0x00,
  0x00, 0x00, 0x00, 0x0B, 0xF4, 0x5F, 0x90, 0x9F, 0x50,
  0x00, 0x00, 0x00, 0x5F, 0xB0, 0xCF, 0x20, 0x2F, 0xC0,
  0x00, 0x00, 0x00, 0xDF, 0x20, 0xFF, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x07, 0xF8, 0x00, 0xFF, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x2F, 0xE1, 0x00, 0xCF, 0x20, 0x2F, 0xC0,
  0x00, 0x00, 0xAF, 0x60, 0x00, 0x5F, 0x90, 0x9F, 0x50,
  0x00, 0x03, 0xFC, 0x00, 0x00, 0x06, 0xDF, 0xD5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0026[105] = { /* code 0026, AMPERSAND */
  0x00, 0x04, 0xBF, 0xFC, 0x50, 0x00, 0x00,
  0x00, 0x5F, 0xFF, 0xFF, 0xF6, 0x00, 0x00,
  0x00, 0xDF, 0x70, 0x18, 0xFE, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00, 0xFE, 0x00, 0x00,
  0x00, 0xBF, 0x40, 0x08, 0xF7, 0x00, 0x00,
  0x00, 0x3F, 0xE7, 0xDF, 0x70, 0x00, 0x00,
  0x00, 0x2C, 0xFF, 0x81, 0x00, 0x00, 0x00,
  0x06, 0xFF, 0xCF, 0x80, 0x00, 0x00, 0x00,
  0x4F, 0xE3, 0x0C, 0xF8, 0x02, 0xB8, 0x00,
  0xCF, 0x40, 0x01, 0xEF, 0x87, 0xFA, 0x00,
  0xFF, 0x00, 0x00, 0x3F, 0xFE, 0xF3, 0x00,
  0xEF, 0x50, 0x00, 0x07, 0xFF, 0x60, 0x00,
  0x8F, 0xE4, 0x01, 0x6D, 0xFF, 0xA2, 0x00,
  0x1B, 0xFF, 0xFF, 0xFF, 0x93, 0xEE, 0x70,
  0x01, 0x7D, 0xFF, 0xB4, 0x00, 0x1B, 0x60
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0027[  5] = { /* code 0027, APOSTROPHE */
  0xFF,
  0xFF,
  0xFF,
  0xCC,
  0x99
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0028[ 57] = { /* code 0028, LEFT PARENTHESIS */
  0x00, 0x06, 0xB0,
  0x00, 0x3F, 0x20,
  0x00, 0xC9, 0x00,
  0x07, 0xF2, 0x00,
  0x0E, 0xC0, 0x00,
  0x5F, 0x70, 0x00,
  0x9F, 0x40, 0x00,
  0xCF, 0x20, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00,
  0xCF, 0x20, 0x00,
  0x9F, 0x40, 0x00,
  0x5F, 0x80, 0x00,
  0x0D, 0xD0, 0x00,
  0x06, 0xF3, 0x00,
  0x00, 0xC9, 0x00,
  0x00, 0x3F, 0x20,
  0x00, 0x06, 0xA0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0029[ 57] = { /* code 0029, RIGHT PARENTHESIS */
  0xB6, 0x00, 0x00,
  0x2F, 0x30, 0x00,
  0x09, 0xC0, 0x00,
  0x02, 0xF7, 0x00,
  0x00, 0xCE, 0x00,
  0x00, 0x8F, 0x50,
  0x00, 0x4F, 0x90,
  0x00, 0x2F, 0xC0,
  0x00, 0x0F, 0xF0,
  0x00, 0x0F, 0xF0,
  0x00, 0x0F, 0xF0,
  0x00, 0x2F, 0xC0,
  0x00, 0x4F, 0x90,
  0x00, 0x8F, 0x50,
  0x00, 0xDD, 0x00,
  0x03, 0xF6, 0x00,
  0x09, 0xC0, 0x00,
  0x2F, 0x30, 0x00,
  0xA6, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_002A[ 24] = { /* code 002A, ASTERISK */
  0x00, 0x0F, 0xF0, 0x00,
  0x32, 0x0D, 0xD0, 0x23,
  0xCF, 0xCD, 0xDC, 0xFC,
  0x14, 0xAF, 0xFA, 0x52,
  0x05, 0xF9, 0xAF, 0x50,
  0x08, 0xB0, 0x0B, 0x80
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_002B[ 50] = { /* code 002B, PLUS SIGN */
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_002C[  5] = { /* code 002C, COMMA */
  0xFF,
  0xFF,
  0x1F,
  0x6B,
  0xB1
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_002D[  6] = { /* code 002D, HYPHEN-MINUS */
  0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_002E[  2] = { /* code 002E, FULL STOP */
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_002F[ 45] = { /* code 002F, SOLIDUS */
  0x00, 0x02, 0xFD,
  0x00, 0x06, 0xF9,
  0x00, 0x0A, 0xF5,
  0x00, 0x0E, 0xF1,
  0x00, 0x3F, 0xC0,
  0x00, 0x7F, 0x80,
  0x00, 0xBF, 0x40,
  0x00, 0xFF, 0x10,
  0x04, 0xFB, 0x00,
  0x08, 0xF7, 0x00,
  0x0C, 0xF3, 0x00,
  0x1F, 0xE0, 0x00,
  0x5F, 0xA0, 0x00,
  0x9F, 0x60, 0x00,
  0xDF, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0030[ 75] = { /* code 0030, DIGIT ZERO */
  0x01, 0x7C, 0xFF, 0xC7, 0x10,
  0x0B, 0xFF, 0xFF, 0xFF, 0xB0,
  0x3F, 0xE6, 0x11, 0x6E, 0xF3,
  0x8F, 0x70, 0x00, 0x06, 0xF7,
  0xCF, 0x30, 0x00, 0x01, 0xFB,
  0xDF, 0x10, 0x00, 0x00, 0xFE,
  0xFF, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0xFF,
  0xDF, 0x10, 0x00, 0x01, 0xFD,
  0xBF, 0x30, 0x00, 0x03, 0xFC,
  0x8F, 0x70, 0x00, 0x07, 0xF8,
  0x3F, 0xE6, 0x11, 0x6E, 0xF3,
  0x0B, 0xFF, 0xFF, 0xFF, 0xB0,
  0x01, 0x7B, 0xEF, 0xC8, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0031[ 45] = { /* code 0031, DIGIT ONE */
  0x00, 0x00, 0x9F,
  0x00, 0x07, 0xFF,
  0x01, 0xAF, 0xFF,
  0x7E, 0xFB, 0xFF,
  0xFD, 0x50, 0xFF,
  0x50, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0032[ 75] = { /* code 0032, DIGIT TWO */
  0x01, 0x8C, 0xFF, 0xD8, 0x10,
  0x1D, 0xFF, 0xFF, 0xFF, 0xD1,
  0x9F, 0xC3, 0x00, 0x4E, 0xF9,
  0xEF, 0x10, 0x00, 0x03, 0xFE,
  0x00, 0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0x03, 0xFB,
  0x00, 0x00, 0x00, 0x0A, 0xF5,
  0x00, 0x00, 0x00, 0x7F, 0x90,
  0x00, 0x00, 0x07, 0xFA, 0x00,
  0x00, 0x00, 0x8F, 0xA0, 0x00,
  0x00, 0x1B, 0xF8, 0x00, 0x00,
  0x03, 0xEE, 0x40, 0x00, 0x00,
  0x2E, 0xE2, 0x00, 0x00, 0x00,
  0xAF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0033[ 75] = { /* code 0033, DIGIT THREE */
  0x01, 0x8D, 0xFE, 0xA2, 0x00,
  0x1D, 0xFF, 0xFF, 0xFE, 0x30,
  0x8F, 0xC3, 0x02, 0xBF, 0xB0,
  0xDF, 0x20, 0x00, 0x1F, 0xF0,
  0x00, 0x00, 0x00, 0x1F, 0xE0,
  0x00, 0x00, 0x03, 0xBF, 0x70,
  0x00, 0x01, 0xFF, 0xF8, 0x00,
  0x00, 0x02, 0xCC, 0xFF, 0x80,
  0x00, 0x00, 0x00, 0x2B, 0xF7,
  0x00, 0x00, 0x00, 0x02, 0xFE,
  0x00, 0x00, 0x00, 0x00, 0xFF,
  0xEF, 0x30, 0x00, 0x03, 0xFD,
  0x8F, 0xC2, 0x00, 0x3D, 0xF7,
  0x1C, 0xFF, 0xFF, 0xFF, 0xA0,
  0x01, 0x7C, 0xFF, 0xC6, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0034[ 75] = { /* code 0034, DIGIT FOUR */
  0x00, 0x00, 0x00, 0x6F, 0x00,
  0x00, 0x00, 0x03, 0xFF, 0x00,
  0x00, 0x00, 0x1D, 0xFF, 0x00,
  0x00, 0x00, 0xBE, 0xFF, 0x00,
  0x00, 0x07, 0xF6, 0xFF, 0x00,
  0x00, 0x4F, 0x90, 0xFF, 0x00,
  0x02, 0xED, 0x10, 0xFF, 0x00,
  0x0C, 0xF3, 0x00, 0xFF, 0x00,
  0x9F, 0x60, 0x00, 0xFF, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0035[ 75] = { /* code 0035, DIGIT FIVE */
  0x02, 0xFF, 0xFF, 0xFF, 0xF0,
  0x06, 0xFF, 0xFF, 0xFF, 0xF0,
  0x09, 0xF6, 0x00, 0x00, 0x00,
  0x0D, 0xF3, 0x00, 0x00, 0x00,
  0x2F, 0xE0, 0x00, 0x00, 0x00,
  0x6F, 0xB7, 0xDF, 0xD7, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xB0,
  0xDF, 0x51, 0x00, 0x3B, 0xF7,
  0x00, 0x00, 0x00, 0x03, 0xFD,
  0x00, 0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0xFE,
  0xEF, 0x20, 0x00, 0x03, 0xFB,
  0x9F, 0xB2, 0x00, 0x3C, 0xF4,
  0x1D, 0xFF, 0xFF, 0xFF, 0x80,
  0x01, 0x8D, 0xFF, 0xB5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0036[ 75] = { /* code 0036, DIGIT SIX */
  0x00, 0x29, 0xEF, 0xE9, 0x20,
  0x03, 0xEF, 0xFF, 0xFF, 0xE1,
  0x0D, 0xF7, 0x20, 0x3C, 0xF9,
  0x5F, 0xB0, 0x00, 0x03, 0xFE,
  0xAF, 0x40, 0x00, 0x00, 0x00,
  0xDF, 0x26, 0xDF, 0xE8, 0x10,
  0xEF, 0x9F, 0xFF, 0xFF, 0xB0,
  0xFF, 0xE6, 0x10, 0x3C, 0xF7,
  0xFF, 0x50, 0x00, 0x03, 0xFC,
  0xEF, 0x00, 0x00, 0x00, 0xFF,
  0xCF, 0x10, 0x00, 0x00, 0xFF,
  0x8F, 0x70, 0x00, 0x04, 0xFB,
  0x2F, 0xE5, 0x00, 0x3D, 0xF6,
  0x06, 0xFF, 0xFF, 0xFF, 0xA0,
  0x00, 0x3B, 0xEF, 0xD7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0037[ 75] = { /* code 0037, DIGIT SEVEN */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFE,
  0x00, 0x00, 0x00, 0x0C, 0xF4,
  0x00, 0x00, 0x00, 0x7F, 0x70,
  0x00, 0x00, 0x02, 0xFB, 0x00,
  0x00, 0x00, 0x0B, 0xF2, 0x00,
  0x00, 0x00, 0x5F, 0x90, 0x00,
  0x00, 0x00, 0xDF, 0x10, 0x00,
  0x00, 0x05, 0xF9, 0x00, 0x00,
  0x00, 0x0C, 0xF3, 0x00, 0x00,
  0x00, 0x2F, 0xD0, 0x00, 0x00,
  0x00, 0x6F, 0x80, 0x00, 0x00,
  0x00, 0xAF, 0x50, 0x00, 0x00,
  0x00, 0xDF, 0x20, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0038[ 75] = { /* code 0038, DIGIT EIGHT */
  0x00, 0x4B, 0xFF, 0xB3, 0x00,
  0x04, 0xFF, 0xFF, 0xFF, 0x30,
  0x0B, 0xF7, 0x00, 0x7F, 0xB0,
  0x0F, 0xF0, 0x00, 0x1F, 0xF0,
  0x0E, 0xF0, 0x00, 0x0F, 0xE0,
  0x08, 0xF7, 0x00, 0x7F, 0x80,
  0x00, 0x8F, 0xFF, 0xF8, 0x00,
  0x05, 0xCF, 0xFF, 0xFC, 0x40,
  0x5F, 0xB3, 0x00, 0x3C, 0xF4,
  0xDF, 0x30, 0x00, 0x03, 0xFC,
  0xFF, 0x00, 0x00, 0x00, 0xFF,
  0xEF, 0x10, 0x00, 0x02, 0xFE,
  0x9F, 0xB4, 0x00, 0x3B, 0xFA,
  0x1D, 0xFF, 0xFF, 0xFF, 0xE2,
  0x01, 0x8D, 0xFF, 0xEA, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0039[ 75] = { /* code 0039, DIGIT NINE */
  0x00, 0x7C, 0xFE, 0xB3, 0x00,
  0x0A, 0xFF, 0xFF, 0xFF, 0x60,
  0x6F, 0xE4, 0x00, 0x3D, 0xF2,
  0xCF, 0x50, 0x00, 0x04, 0xF8,
  0xFF, 0x10, 0x00, 0x00, 0xFC,
  0xFF, 0x00, 0x00, 0x00, 0xFE,
  0xCF, 0x30, 0x00, 0x05, 0xFF,
  0x7F, 0xD3, 0x00, 0x5E, 0xFF,
  0x0B, 0xFF, 0xFF, 0xFA, 0xFF,
  0x01, 0x8E, 0xFD, 0x61, 0xFD,
  0x00, 0x00, 0x00, 0x04, 0xFA,
  0xEF, 0x20, 0x00, 0x09, 0xF6,
  0x9F, 0xB3, 0x02, 0x6E, 0xD0,
  0x2E, 0xFF, 0xFF, 0xFE, 0x30,
  0x02, 0x9E, 0xFD, 0x92, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_003A[ 11] = { /* code 003A, COLON */
  0xFF,
  0xFF,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_003B[ 14] = { /* code 003B, SEMICOLON */
  0xFF,
  0xFF,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0x00,
  0xFF,
  0xFF,
  0x1F,
  0x6B,
  0xB1
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_003C[ 55] = { /* code 003C, LESS-THAN SIGN */
  0x00, 0x00, 0x00, 0x00, 0x4B,
  0x00, 0x00, 0x00, 0x4B, 0xFF,
  0x00, 0x00, 0x4B, 0xFF, 0xA3,
  0x00, 0x4B, 0xFE, 0x82, 0x00,
  0x4B, 0xFD, 0x60, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x00, 0x00,
  0x4B, 0xFD, 0x60, 0x00, 0x00,
  0x00, 0x4B, 0xFE, 0x82, 0x00,
  0x00, 0x00, 0x4B, 0xFF, 0xA3,
  0x00, 0x00, 0x00, 0x4B, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x4B
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_003D[ 35] = { /* code 003D, EQUALS SIGN */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_003E[ 55] = { /* code 003E, GREATER-THAN SIGN */
  0xB4, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xB4, 0x00, 0x00, 0x00,
  0x3A, 0xFF, 0xB4, 0x00, 0x00,
  0x00, 0x28, 0xEF, 0xB4, 0x00,
  0x00, 0x00, 0x06, 0xDF, 0xB4,
  0x00, 0x00, 0x00, 0x08, 0xFF,
  0x00, 0x00, 0x06, 0xDF, 0xB4,
  0x00, 0x28, 0xEF, 0xB4, 0x00,
  0x3A, 0xFF, 0xB4, 0x00, 0x00,
  0xFF, 0xB4, 0x00, 0x00, 0x00,
  0xB4, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_003F[ 75] = { /* code 003F, QUESTION MARK */
  0x01, 0x8C, 0xFF, 0xD8, 0x10,
  0x1D, 0xFF, 0xFF, 0xFF, 0xE2,
  0x8F, 0xD4, 0x00, 0x4D, 0xFB,
  0xEF, 0x30, 0x00, 0x02, 0xFF,
  0x00, 0x00, 0x00, 0x02, 0xFE,
  0x00, 0x00, 0x00, 0x1D, 0xF7,
  0x00, 0x00, 0x02, 0xDF, 0xA0,
  0x00, 0x00, 0x2E, 0xF8, 0x00,
  0x00, 0x00, 0xAF, 0x90, 0x00,
  0x00, 0x00, 0xEF, 0x20, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0040[190] = { /* code 0040, COMMERCIAL AT */
  0x00, 0x00, 0x01, 0x7B, 0xDF, 0xFE, 0xB8, 0x20, 0x00, 0x00,
  0x00, 0x00, 0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0x10, 0x00,
  0x00, 0x1B, 0xFF, 0xB5, 0x20, 0x02, 0x5B, 0xFF, 0xB1, 0x00,
  0x00, 0xBF, 0xE4, 0x00, 0x00, 0x00, 0x00, 0x4E, 0xFA, 0x00,
  0x06, 0xFE, 0x20, 0x07, 0xDF, 0xC5, 0x1F, 0xD3, 0xFF, 0x40,
  0x0E, 0xF4, 0x01, 0xBF, 0xFF, 0xFF, 0xAF, 0x90, 0x8F, 0xA0,
  0x6F, 0xB0, 0x0B, 0xFD, 0x40, 0x3C, 0xFF, 0x60, 0x3F, 0xD0,
  0xAF, 0x50, 0x4F, 0xE1, 0x00, 0x03, 0xFF, 0x20, 0x0F, 0xF0,
  0xDF, 0x20, 0xAF, 0x70, 0x00, 0x00, 0xFE, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0xEF, 0x20, 0x00, 0x02, 0xFA, 0x00, 0x3F, 0xC0,
  0xFF, 0x00, 0xFF, 0x00, 0x00, 0x07, 0xF6, 0x00, 0x9F, 0x70,
  0xEF, 0x20, 0xEF, 0x30, 0x00, 0x2E, 0xF3, 0x04, 0xFE, 0x10,
  0xBF, 0x60, 0x9F, 0xC2, 0x05, 0xEF, 0xF1, 0x5E, 0xF5, 0x00,
  0x6F, 0xC0, 0x2E, 0xFF, 0xFF, 0xBF, 0xFF, 0xFF, 0x70, 0x00,
  0x1E, 0xF9, 0x03, 0xBF, 0xE7, 0x08, 0xFF, 0xB3, 0x6F, 0xB0,
  0x05, 0xFF, 0x91, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFE, 0x20,
  0x00, 0x7F, 0xFE, 0x95, 0x20, 0x01, 0x37, 0xDF, 0xE3, 0x00,
  0x00, 0x04, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x20, 0x00,
  0x00, 0x00, 0x04, 0x8C, 0xEF, 0xFE, 0xC8, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0041[120] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x00, 0x01, 0xFF, 0xF1, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x05, 0xFF, 0xF5, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x0B, 0xF9, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0xE1, 0xEF, 0x10, 0x00, 0x00,
  0x00, 0x00, 0x6F, 0xA0, 0xAF, 0x60, 0x00, 0x00,
  0x00, 0x00, 0xBF, 0x40, 0x4F, 0xB0, 0x00, 0x00,
  0x00, 0x02, 0xFE, 0x00, 0x0E, 0xF2, 0x00, 0x00,
  0x00, 0x07, 0xF9, 0x00, 0x09, 0xF7, 0x00, 0x00,
  0x00, 0x0D, 0xFF, 0xFF, 0xFF, 0xFD, 0x00, 0x00,
  0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0x30, 0x00,
  0x00, 0x8F, 0x50, 0x00, 0x00, 0x5F, 0x80, 0x00,
  0x00, 0xDE, 0x00, 0x00, 0x00, 0x0E, 0xD0, 0x00,
  0x04, 0xF9, 0x00, 0x00, 0x00, 0x0A, 0xF4, 0x00,
  0x09, 0xF4, 0x00, 0x00, 0x00, 0x04, 0xF9, 0x00,
  0x0E, 0xE0, 0x00, 0x00, 0x00, 0x00, 0xEE, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0042[ 90] = { /* code 0042, LATIN CAPITAL LETTER B */
  0xFF, 0xFF, 0xFF, 0xEB, 0x30, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF3, 0x00,
  0xFF, 0x00, 0x00, 0x2A, 0xFB, 0x00,
  0xFF, 0x00, 0x00, 0x01, 0xFF, 0x00,
  0xFF, 0x00, 0x00, 0x01, 0xFE, 0x00,
  0xFF, 0x00, 0x00, 0x2A, 0xF8, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xC4, 0x00,
  0xFF, 0x00, 0x00, 0x27, 0xEF, 0x40,
  0xFF, 0x00, 0x00, 0x00, 0x5F, 0xC0,
  0xFF, 0x00, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x00, 0x3F, 0xE0,
  0xFF, 0x00, 0x00, 0x03, 0xDF, 0x90,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x20,
  0xFF, 0xFF, 0xFF, 0xFC, 0x81, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0043[105] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x02, 0x8C, 0xFF, 0xC7, 0x10, 0x00,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xE3, 0x00,
  0x07, 0xFF, 0x83, 0x00, 0x3B, 0xFE, 0x20,
  0x2F, 0xF4, 0x00, 0x00, 0x00, 0xAF, 0xA0,
  0x8F, 0x90, 0x00, 0x00, 0x00, 0x2B, 0x70,
  0xCF, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xEF, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0x40, 0x00, 0x00, 0x00, 0x05, 0x20,
  0x8F, 0x90, 0x00, 0x00, 0x00, 0x5F, 0xD0,
  0x2F, 0xF3, 0x00, 0x00, 0x01, 0xDF, 0x70,
  0x08, 0xFF, 0x82, 0x01, 0x5D, 0xFD, 0x10,
  0x00, 0xAF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00,
  0x00, 0x04, 0xAE, 0xFF, 0xC7, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0044[ 90] = { /* code 0044, LATIN CAPITAL LETTER D */
  0xFF, 0xFF, 0xFF, 0xEC, 0x70, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x10,
  0xFF, 0x00, 0x00, 0x15, 0xDF, 0xB0,
  0xFF, 0x00, 0x00, 0x00, 0x2E, 0xF4,
  0xFF, 0x00, 0x00, 0x00, 0x07, 0xF9,
  0xFF, 0x00, 0x00, 0x00, 0x03, 0xFD,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x03, 0xFC,
  0xFF, 0x00, 0x00, 0x00, 0x08, 0xF9,
  0xFF, 0x00, 0x00, 0x00, 0x2E, 0xF3,
  0xFF, 0x00, 0x00, 0x15, 0xDF, 0xB0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x10,
  0xFF, 0xFF, 0xFF, 0xEB, 0x60, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0045[ 90] = { /* code 0045, LATIN CAPITAL LETTER E */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0046[ 75] = { /* code 0046, LATIN CAPITAL LETTER F */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0047[105] = { /* code 0047, LATIN CAPITAL LETTER G */
  0x00, 0x01, 0x7C, 0xEF, 0xEC, 0x71, 0x00,
  0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFE, 0x30,
  0x06, 0xFF, 0xB4, 0x10, 0x15, 0xBF, 0xE1,
  0x1E, 0xF7, 0x00, 0x00, 0x00, 0x0B, 0xF7,
  0x7F, 0xB0, 0x00, 0x00, 0x00, 0x03, 0xA5,
  0xBF, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xEF, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x0F, 0xFF, 0xFF, 0xFF,
  0xEF, 0x10, 0x00, 0x0F, 0xFF, 0xFF, 0xFF,
  0xCF, 0x40, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0x7F, 0xA0, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0x1E, 0xF7, 0x00, 0x00, 0x00, 0x02, 0xFF,
  0x05, 0xFF, 0xB5, 0x10, 0x14, 0x9F, 0xFE,
  0x00, 0x5E, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1,
  0x00, 0x01, 0x7B, 0xEF, 0xFC, 0x83, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0048[ 90] = { /* code 0048, LATIN CAPITAL LETTER H */
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0049[ 15] = { /* code 0049, LATIN CAPITAL LETTER I */
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_004A[ 60] = { /* code 004A, LATIN CAPITAL LETTER J */
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0x00, 0xFF,
  0xCE, 0x00, 0x00, 0xFF,
  0xEF, 0x30, 0x02, 0xFE,
  0xBF, 0xB1, 0x1A, 0xFB,
  0x4F, 0xFF, 0xFF, 0xF3,
  0x04, 0xCF, 0xFB, 0x40
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_004B[ 90] = { /* code 004B, LATIN CAPITAL LETTER K */
  0xFF, 0x00, 0x00, 0x07, 0xFF, 0x80,
  0xFF, 0x00, 0x00, 0x4F, 0xFA, 0x00,
  0xFF, 0x00, 0x02, 0xEF, 0xB0, 0x00,
  0xFF, 0x00, 0x1D, 0xFC, 0x10, 0x00,
  0xFF, 0x00, 0xBF, 0xD1, 0x00, 0x00,
  0xFF, 0x08, 0xFE, 0x20, 0x00, 0x00,
  0xFF, 0x6F, 0xFB, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0x70, 0x00, 0x00,
  0xFF, 0xF5, 0xAF, 0xF3, 0x00, 0x00,
  0xFF, 0x70, 0x1C, 0xFD, 0x10, 0x00,
  0xFF, 0x00, 0x02, 0xEF, 0xA0, 0x00,
  0xFF, 0x00, 0x00, 0x4F, 0xF6, 0x00,
  0xFF, 0x00, 0x00, 0x07, 0xFF, 0x30,
  0xFF, 0x00, 0x00, 0x00, 0xBF, 0xD1,
  0xFF, 0x00, 0x00, 0x00, 0x1D, 0xF9
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_004C[ 75] = { /* code 004C, LATIN CAPITAL LETTER L */
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_004D[120] = { /* code 004D, LATIN CAPITAL LETTER M */
  0xEB, 0x82, 0x00, 0x00, 0x00, 0x02, 0x8B, 0xE0,
  0xFF, 0xF9, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xF0,
  0xFF, 0xCE, 0x00, 0x00, 0x00, 0x0E, 0xCF, 0xF0,
  0xFF, 0x7F, 0x50, 0x00, 0x00, 0x5F, 0x7F, 0xF0,
  0xFF, 0x2F, 0xA0, 0x00, 0x00, 0xAF, 0x2F, 0xF0,
  0xFF, 0x0C, 0xE1, 0x00, 0x01, 0xFC, 0x0F, 0xF0,
  0xFF, 0x07, 0xF5, 0x00, 0x05, 0xF7, 0x0F, 0xF0,
  0xFF, 0x02, 0xFA, 0x00, 0x0A, 0xF2, 0x0F, 0xF0,
  0xFF, 0x00, 0xCF, 0x10, 0x1F, 0xC0, 0x0F, 0xF0,
  0xFF, 0x00, 0x7F, 0x60, 0x6F, 0x70, 0x0F, 0xF0,
  0xFF, 0x00, 0x2F, 0xB0, 0xBF, 0x20, 0x0F, 0xF0,
  0xFF, 0x00, 0x0C, 0xF2, 0xFC, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x07, 0xFC, 0xF7, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x02, 0xFF, 0xF2, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0xBF, 0xC0, 0x00, 0x0F, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_004E[ 90] = { /* code 004E, LATIN CAPITAL LETTER N */
  0xFF, 0x70, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0xF2, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0xFB, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x9F, 0x60, 0x00, 0x00, 0xFF,
  0xFF, 0x1D, 0xE2, 0x00, 0x00, 0xFF,
  0xFF, 0x04, 0xFB, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0xAF, 0x50, 0x00, 0xFF,
  0xFF, 0x00, 0x1E, 0xE1, 0x00, 0xFF,
  0xFF, 0x00, 0x05, 0xF9, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xBF, 0x40, 0xFF,
  0xFF, 0x00, 0x00, 0x2E, 0xD1, 0xFF,
  0xFF, 0x00, 0x00, 0x06, 0xF9, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0xBF, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x2F, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x07, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_004F[105] = { /* code 004F, LATIN CAPITAL LETTER O */
  0x00, 0x03, 0x9D, 0xFF, 0xD9, 0x20, 0x00,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF6, 0x00,
  0x06, 0xFF, 0x93, 0x00, 0x39, 0xFF, 0x60,
  0x1F, 0xF5, 0x00, 0x00, 0x00, 0x5F, 0xF1,
  0x8F, 0x90, 0x00, 0x00, 0x00, 0x0A, 0xF8,
  0xCF, 0x40, 0x00, 0x00, 0x00, 0x04, 0xFC,
  0xEF, 0x10, 0x00, 0x00, 0x00, 0x01, 0xFE,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFE,
  0xCF, 0x30, 0x00, 0x00, 0x00, 0x04, 0xFC,
  0x8F, 0x90, 0x00, 0x00, 0x00, 0x0A, 0xF8,
  0x2F, 0xF5, 0x00, 0x00, 0x00, 0x5F, 0xF1,
  0x06, 0xFF, 0x93, 0x00, 0x39, 0xFF, 0x60,
  0x00, 0x6F, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x00, 0x02, 0x8D, 0xFF, 0xD8, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0050[ 90] = { /* code 0050, LATIN CAPITAL LETTER P */
  0xFF, 0xFF, 0xFF, 0xFD, 0x92, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x20,
  0xFF, 0x00, 0x00, 0x03, 0xCF, 0x90,
  0xFF, 0x00, 0x00, 0x00, 0x3F, 0xE0,
  0xFF, 0x00, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x00, 0x3F, 0xD0,
  0xFF, 0x00, 0x00, 0x14, 0xDF, 0x80,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x10,
  0xFF, 0xFF, 0xFF, 0xFC, 0x81, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0051[112] = { /* code 0051, LATIN CAPITAL LETTER Q */
  0x00, 0x02, 0x9D, 0xFF, 0xD9, 0x30, 0x00,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x06, 0xFF, 0x93, 0x00, 0x39, 0xFF, 0x70,
  0x1F, 0xF5, 0x00, 0x00, 0x00, 0x5F, 0xF2,
  0x7F, 0x90, 0x00, 0x00, 0x00, 0x0A, 0xF8,
  0xCF, 0x40, 0x00, 0x00, 0x00, 0x04, 0xFC,
  0xEF, 0x10, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xEF, 0x10, 0x00, 0x00, 0x00, 0x01, 0xFE,
  0xCF, 0x40, 0x00, 0x00, 0x00, 0x04, 0xFB,
  0x7F, 0x90, 0x00, 0x04, 0x30, 0x0A, 0xF6,
  0x1E, 0xF5, 0x00, 0x0B, 0xFB, 0x6F, 0xD0,
  0x06, 0xFF, 0x93, 0x01, 0x9F, 0xFF, 0x30,
  0x00, 0x6F, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x00, 0x02, 0x8D, 0xFF, 0xDA, 0x6D, 0xFB,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7A
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0052[ 90] = { /* code 0052, LATIN CAPITAL LETTER R */
  0xFF, 0xFF, 0xFF, 0xFE, 0xB4, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0xFF, 0x00, 0x00, 0x02, 0xBF, 0xB0,
  0xFF, 0x00, 0x00, 0x00, 0x1F, 0xF0,
  0xFF, 0x00, 0x00, 0x00, 0x1F, 0xF0,
  0xFF, 0x00, 0x00, 0x03, 0xBF, 0xB0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x30,
  0xFF, 0xFF, 0xFF, 0xFE, 0xA3, 0x00,
  0xFF, 0x00, 0x07, 0xFC, 0x10, 0x00,
  0xFF, 0x00, 0x00, 0xCF, 0xB0, 0x00,
  0xFF, 0x00, 0x00, 0x2F, 0xF6, 0x00,
  0xFF, 0x00, 0x00, 0x08, 0xFE, 0x10,
  0xFF, 0x00, 0x00, 0x01, 0xEF, 0x70,
  0xFF, 0x00, 0x00, 0x00, 0x7F, 0xF1,
  0xFF, 0x00, 0x00, 0x00, 0x0D, 0xF9
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0053[ 90] = { /* code 0053, LATIN CAPITAL LETTER S */
  0x00, 0x17, 0xCF, 0xFE, 0xB6, 0x00,
  0x01, 0xDF, 0xFF, 0xFF, 0xFF, 0xC1,
  0x0A, 0xFD, 0x41, 0x01, 0x4D, 0xF9,
  0x0E, 0xF2, 0x00, 0x00, 0x02, 0xFE,
  0x0E, 0xF1, 0x00, 0x00, 0x00, 0x00,
  0x09, 0xFC, 0x40, 0x00, 0x00, 0x00,
  0x01, 0xBF, 0xFE, 0xB8, 0x40, 0x00,
  0x00, 0x04, 0xAE, 0xFF, 0xFE, 0x50,
  0x00, 0x00, 0x00, 0x26, 0xAF, 0xF5,
  0x00, 0x00, 0x00, 0x00, 0x04, 0xFC,
  0xEF, 0x10, 0x00, 0x00, 0x00, 0xFF,
  0xBF, 0x80, 0x00, 0x00, 0x03, 0xFE,
  0x4F, 0xFA, 0x41, 0x01, 0x5D, 0xF7,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0,
  0x00, 0x39, 0xCF, 0xFE, 0xB5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0054[ 90] = { /* code 0054, LATIN CAPITAL LETTER T */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0055[ 90] = { /* code 0055, LATIN CAPITAL LETTER U */
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0x00, 0x01, 0xFF,
  0xDF, 0x30, 0x00, 0x00, 0x03, 0xFD,
  0xAF, 0x90, 0x00, 0x00, 0x09, 0xFA,
  0x3F, 0xF9, 0x30, 0x03, 0x9F, 0xF4,
  0x05, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x00, 0x29, 0xDF, 0xFE, 0xA3, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0056[105] = { /* code 0056, LATIN CAPITAL LETTER V */
  0xDF, 0x30, 0x00, 0x00, 0x00, 0x3F, 0xD0,
  0x8F, 0x80, 0x00, 0x00, 0x00, 0x8F, 0x80,
  0x3F, 0xD0, 0x00, 0x00, 0x00, 0xDF, 0x30,
  0x0D, 0xF3, 0x00, 0x00, 0x03, 0xFD, 0x00,
  0x08, 0xF8, 0x00, 0x00, 0x08, 0xF8, 0x00,
  0x03, 0xFD, 0x00, 0x00, 0x0D, 0xF3, 0x00,
  0x00, 0xDF, 0x30, 0x00, 0x3F, 0xD0, 0x00,
  0x00, 0x8F, 0x80, 0x00, 0x8F, 0x80, 0x00,
  0x00, 0x3F, 0xD0, 0x00, 0xDF, 0x30, 0x00,
  0x00, 0x0D, 0xF3, 0x03, 0xFD, 0x00, 0x00,
  0x00, 0x08, 0xF8, 0x08, 0xF8, 0x00, 0x00,
  0x00, 0x03, 0xFD, 0x0D, 0xF3, 0x00, 0x00,
  0x00, 0x00, 0xDF, 0x7F, 0xD0, 0x00, 0x00,
  0x00, 0x00, 0x8F, 0xEF, 0x80, 0x00, 0x00,
  0x00, 0x00, 0x3F, 0xFF, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0057[165] = { /* code 0057, LATIN CAPITAL LETTER W */
  0xDF, 0x20, 0x00, 0x00, 0x2F, 0xFF, 0x20, 0x00, 0x00, 0x2F, 0xD0,
  0x9F, 0x60, 0x00, 0x00, 0x6F, 0xFF, 0x60, 0x00, 0x00, 0x7F, 0x90,
  0x5F, 0xA0, 0x00, 0x00, 0xAF, 0x9F, 0xA0, 0x00, 0x00, 0xBF, 0x50,
  0x1F, 0xE0, 0x00, 0x00, 0xEF, 0x2F, 0xE0, 0x00, 0x00, 0xEF, 0x10,
  0x0C, 0xF3, 0x00, 0x03, 0xFC, 0x0B, 0xF3, 0x00, 0x04, 0xFC, 0x00,
  0x08, 0xF7, 0x00, 0x07, 0xF7, 0x07, 0xF7, 0x00, 0x07, 0xF8, 0x00,
  0x04, 0xFC, 0x00, 0x0B, 0xF3, 0x03, 0xFB, 0x00, 0x0C, 0xF4, 0x00,
  0x00, 0xEF, 0x10, 0x1F, 0xE0, 0x00, 0xEF, 0x10, 0x1F, 0xE0, 0x00,
  0x00, 0xBF, 0x50, 0x4F, 0xA0, 0x00, 0xAF, 0x40, 0x5F, 0xB0, 0x00,
  0x00, 0x7F, 0x90, 0x9F, 0x60, 0x00, 0x6F, 0x80, 0x9F, 0x70, 0x00,
  0x00, 0x2F, 0xD0, 0xDF, 0x20, 0x00, 0x2F, 0xD0, 0xDF, 0x30, 0x00,
  0x00, 0x0E, 0xF3, 0xFD, 0x00, 0x00, 0x0D, 0xF3, 0xFE, 0x00, 0x00,
  0x00, 0x09, 0xFC, 0xF9, 0x00, 0x00, 0x09, 0xFC, 0xFA, 0x00, 0x00,
  0x00, 0x05, 0xFF, 0xF5, 0x00, 0x00, 0x04, 0xFF, 0xF6, 0x00, 0x00,
  0x00, 0x01, 0xFF, 0xF1, 0x00, 0x00, 0x01, 0xFF, 0xF1, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0058[105] = { /* code 0058, LATIN CAPITAL LETTER X */
  0x0A, 0xFD, 0x00, 0x00, 0x00, 0xDF, 0xA0,
  0x01, 0xDF, 0x80, 0x00, 0x08, 0xFD, 0x10,
  0x00, 0x3F, 0xF3, 0x00, 0x3F, 0xF3, 0x00,
  0x00, 0x07, 0xFD, 0x00, 0xDF, 0x70, 0x00,
  0x00, 0x00, 0xBF, 0x77, 0xFB, 0x00, 0x00,
  0x00, 0x00, 0x1D, 0xEE, 0xD1, 0x00, 0x00,
  0x00, 0x00, 0x04, 0xFF, 0x40, 0x00, 0x00,
  0x00, 0x00, 0x08, 0xFF, 0x80, 0x00, 0x00,
  0x00, 0x00, 0x4F, 0xCC, 0xF4, 0x00, 0x00,
  0x00, 0x01, 0xDF, 0x67, 0xFD, 0x10, 0x00,
  0x00, 0x0B, 0xFC, 0x00, 0xCF, 0xB0, 0x00,
  0x00, 0x7F, 0xE2, 0x00, 0x2E, 0xF7, 0x00,
  0x03, 0xFF, 0x50, 0x00, 0x05, 0xFF, 0x30,
  0x1D, 0xF9, 0x00, 0x00, 0x00, 0x9F, 0xD1,
  0xAF, 0xD1, 0x00, 0x00, 0x00, 0x1D, 0xFA
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0059[105] = { /* code 0059, LATIN CAPITAL LETTER Y */
  0xAF, 0xB0, 0x00, 0x00, 0x00, 0x0B, 0xFA,
  0x1D, 0xF7, 0x00, 0x00, 0x00, 0x7F, 0xD1,
  0x04, 0xFF, 0x30, 0x00, 0x03, 0xFF, 0x40,
  0x00, 0x9F, 0xC0, 0x00, 0x0C, 0xF9, 0x00,
  0x00, 0x0D, 0xF8, 0x00, 0x8F, 0xD0, 0x00,
  0x00, 0x03, 0xFE, 0x11, 0xEF, 0x30, 0x00,
  0x00, 0x00, 0x7F, 0x88, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x0C, 0xFF, 0xC0, 0x00, 0x00,
  0x00, 0x00, 0x02, 0xFF, 0x20, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_005A[ 90] = { /* code 005A, LATIN CAPITAL LETTER Z */
  0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD,
  0x0F, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8,
  0x00, 0x00, 0x00, 0x00, 0x2E, 0xD1,
  0x00, 0x00, 0x00, 0x01, 0xCF, 0x30,
  0x00, 0x00, 0x00, 0x0A, 0xF6, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0x90, 0x00,
  0x00, 0x00, 0x04, 0xFC, 0x00, 0x00,
  0x00, 0x00, 0x2E, 0xE1, 0x00, 0x00,
  0x00, 0x01, 0xCF, 0x30, 0x00, 0x00,
  0x00, 0x0A, 0xF6, 0x00, 0x00, 0x00,
  0x00, 0x7F, 0x90, 0x00, 0x00, 0x00,
  0x04, 0xFC, 0x00, 0x00, 0x00, 0x00,
  0x2E, 0xE2, 0x00, 0x00, 0x00, 0x00,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_005B[ 38] = { /* code 005B, LEFT SQUARE BRACKET */
  0xFF, 0xFF,
  0xFF, 0xFF,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0x00,
  0xFF, 0xFF,
  0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_005C[ 45] = { /* code 005C, REVERSE SOLIDUS */
  0xDF, 0x20, 0x00,
  0x9F, 0x60, 0x00,
  0x5F, 0xA0, 0x00,
  0x1F, 0xE0, 0x00,
  0x0C, 0xF3, 0x00,
  0x08, 0xF7, 0x00,
  0x04, 0xFB, 0x00,
  0x01, 0xFF, 0x00,
  0x00, 0xBF, 0x40,
  0x00, 0x7F, 0x80,
  0x00, 0x3F, 0xC0,
  0x00, 0x0E, 0xF1,
  0x00, 0x0A, 0xF5,
  0x00, 0x06, 0xF9,
  0x00, 0x02, 0xFD
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_005D[ 38] = { /* code 005D, RIGHT SQUARE BRACKET */
  0xFF, 0xFF,
  0xFF, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0xFF, 0xFF,
  0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_005E[ 32] = { /* code 005E, CIRCUMFLEX ACCENT */
  0x00, 0x3F, 0xF3, 0x00,
  0x00, 0x8F, 0xF8, 0x00,
  0x00, 0xED, 0xDE, 0x00,
  0x05, 0xF8, 0x8F, 0x50,
  0x0A, 0xF3, 0x3F, 0xA0,
  0x1F, 0xD0, 0x0D, 0xF1,
  0x7F, 0x80, 0x08, 0xF7,
  0xCF, 0x30, 0x03, 0xFC
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_005F[ 12] = { /* code 005F, LOW LINE */
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0060[  6] = { /* code 0060, GRAVE ACCENT */
  0xAF, 0x30,
  0x1E, 0x80,
  0x05, 0xD0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0061[ 66] = { /* code 0061, LATIN SMALL LETTER A */
  0x00, 0x4A, 0xEF, 0xFC, 0x70, 0x00,
  0x09, 0xFF, 0xFF, 0xFF, 0xF9, 0x00,
  0x0F, 0xF7, 0x10, 0x18, 0xFE, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xFF, 0x00,
  0x00, 0x02, 0x79, 0xBD, 0xFF, 0x00,
  0x01, 0xAF, 0xFF, 0xFF, 0xFF, 0x00,
  0x09, 0xFE, 0x97, 0x52, 0xFF, 0x00,
  0x0F, 0xF2, 0x00, 0x04, 0xFF, 0x00,
  0x0E, 0xF6, 0x01, 0x6E, 0xFF, 0x20,
  0x09, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0x00, 0x8E, 0xFE, 0xB4, 0x4F, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0062[ 75] = { /* code 0062, LATIN SMALL LETTER B */
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x1A, 0xFE, 0x91, 0x00,
  0xFF, 0xBF, 0xFF, 0xFC, 0x00,
  0xFF, 0xE4, 0x04, 0xEF, 0x70,
  0xFF, 0x60, 0x00, 0x5F, 0xC0,
  0xFF, 0x10, 0x00, 0x1F, 0xE0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x10, 0x00, 0x1F, 0xE0,
  0xFF, 0x50, 0x00, 0x6F, 0xB0,
  0xFF, 0xE3, 0x04, 0xEF, 0x60,
  0xFF, 0xAF, 0xFF, 0xFB, 0x00,
  0xFF, 0x1A, 0xFE, 0x81, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0063[ 55] = { /* code 0063, LATIN SMALL LETTER C */
  0x00, 0x7C, 0xFE, 0xA2, 0x00,
  0x0B, 0xFF, 0xFF, 0xFE, 0x10,
  0x6F, 0xE5, 0x02, 0xBF, 0x90,
  0xBF, 0x50, 0x00, 0x2F, 0xE0,
  0xEF, 0x10, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xEF, 0x10, 0x00, 0x00, 0x00,
  0xCF, 0x50, 0x00, 0x2F, 0xE0,
  0x7F, 0xE4, 0x02, 0xBF, 0x80,
  0x0B, 0xFF, 0xFF, 0xFD, 0x10,
  0x00, 0x7D, 0xFE, 0x91, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0064[ 75] = { /* code 0064, LATIN SMALL LETTER D */
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x01, 0x9E, 0xFA, 0x1F, 0xF0,
  0x0C, 0xFF, 0xFF, 0xBF, 0xF0,
  0x6F, 0xE3, 0x04, 0xEF, 0xF0,
  0xBF, 0x50, 0x00, 0x6F, 0xF0,
  0xEF, 0x10, 0x00, 0x1F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xEF, 0x10, 0x00, 0x1F, 0xF0,
  0xBF, 0x60, 0x00, 0x5F, 0xF0,
  0x6F, 0xE4, 0x03, 0xEF, 0xF0,
  0x0B, 0xFF, 0xFF, 0xAF, 0xF0,
  0x01, 0x8E, 0xFA, 0x1F, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0065[ 55] = { /* code 0065, LATIN SMALL LETTER E */
  0x00, 0x6C, 0xFF, 0xC6, 0x00,
  0x09, 0xFF, 0xFF, 0xFF, 0xA0,
  0x5F, 0xD4, 0x00, 0x3C, 0xF6,
  0xBF, 0x20, 0x00, 0x02, 0xFB,
  0xEF, 0xFF, 0xFF, 0xFF, 0xFE,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xEF, 0x10, 0x00, 0x00, 0x00,
  0xBF, 0x60, 0x00, 0x00, 0x00,
  0x6F, 0xF7, 0x10, 0x29, 0xFB,
  0x0A, 0xFF, 0xFF, 0xFF, 0xE2,
  0x00, 0x6C, 0xFF, 0xD9, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0066[ 60] = { /* code 0066, LATIN SMALL LETTER F */
  0x00, 0x2B, 0xFF, 0xB0,
  0x00, 0xBF, 0xFF, 0xB0,
  0x00, 0xFF, 0x40, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0x00,
  0xFF, 0xFF, 0xFF, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00,
  0x00, 0xFF, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0067[ 75] = { /* code 0067, LATIN SMALL LETTER G */
  0x01, 0x9E, 0xFA, 0x1F, 0xF0,
  0x0C, 0xFF, 0xFF, 0xAF, 0xF0,
  0x6F, 0xE4, 0x04, 0xEF, 0xF0,
  0xBF, 0x50, 0x00, 0x6F, 0xF0,
  0xEF, 0x10, 0x00, 0x1F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xEF, 0x10, 0x00, 0x1F, 0xF0,
  0xBF, 0x50, 0x00, 0x5F, 0xF0,
  0x6F, 0xE4, 0x04, 0xEF, 0xF0,
  0x0B, 0xFF, 0xFF, 0xFF, 0xF0,
  0x01, 0x9E, 0xFC, 0x5F, 0xF0,
  0x00, 0x00, 0x00, 0x2F, 0xD0,
  0xFF, 0x61, 0x02, 0xBF, 0x90,
  0x9F, 0xFF, 0xFF, 0xFE, 0x10,
  0x07, 0xCF, 0xFD, 0x91, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0068[ 75] = { /* code 0068, LATIN SMALL LETTER H */
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x08, 0xEF, 0xD6, 0x00,
  0xFF, 0xBF, 0xFF, 0xFF, 0x70,
  0xFF, 0xE5, 0x01, 0xAF, 0xC0,
  0xFF, 0x50, 0x00, 0x2F, 0xF0,
  0xFF, 0x10, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0069[ 15] = { /* code 0069, LATIN SMALL LETTER I */
  0xFF,
  0xFF,
  0x00,
  0x00,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_006A[ 38] = { /* code 006A, LATIN SMALL LETTER J */
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0x00,
  0x00, 0x00,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x13, 0xFE,
  0xBF, 0xFB,
  0xCF, 0xC2
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_006B[ 75] = { /* code 006B, LATIN SMALL LETTER K */
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x8E, 0x70,
  0xFF, 0x00, 0x08, 0xFA, 0x00,
  0xFF, 0x00, 0x8F, 0xA0, 0x00,
  0xFF, 0x08, 0xFA, 0x00, 0x00,
  0xFF, 0xAF, 0xF5, 0x00, 0x00,
  0xFF, 0xFD, 0xFD, 0x00, 0x00,
  0xFF, 0x72, 0xFF, 0x70, 0x00,
  0xFF, 0x00, 0x7F, 0xE1, 0x00,
  0xFF, 0x00, 0x0C, 0xF9, 0x00,
  0xFF, 0x00, 0x03, 0xFF, 0x20,
  0xFF, 0x00, 0x00, 0x9F, 0xB0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_006C[ 15] = { /* code 006C, LATIN SMALL LETTER L */
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_006D[ 77] = { /* code 006D, LATIN SMALL LETTER M */
  0xFF, 0x09, 0xEE, 0x70, 0x1A, 0xFE, 0x91,
  0xFF, 0x9F, 0xFF, 0xF6, 0xCF, 0xFF, 0xF9,
  0xFF, 0xD3, 0x08, 0xFE, 0xC2, 0x08, 0xFE,
  0xFF, 0x40, 0x01, 0xFF, 0x30, 0x01, 0xFF,
  0xFF, 0x10, 0x00, 0xFF, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_006E[ 55] = { /* code 006E, LATIN SMALL LETTER N */
  0xFF, 0x19, 0xEF, 0xD7, 0x00,
  0xFF, 0xDF, 0xFF, 0xFF, 0x70,
  0xFF, 0xE4, 0x02, 0xBF, 0xD0,
  0xFF, 0x40, 0x00, 0x2F, 0xF0,
  0xFF, 0x10, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_006F[ 55] = { /* code 006F, LATIN SMALL LETTER O */
  0x00, 0x6C, 0xFF, 0xC6, 0x00,
  0x0A, 0xFF, 0xFF, 0xFF, 0xA0,
  0x6F, 0xE6, 0x11, 0x6E, 0xF6,
  0xBF, 0x60, 0x00, 0x07, 0xFB,
  0xEF, 0x10, 0x00, 0x01, 0xFE,
  0xFF, 0x00, 0x00, 0x00, 0xFF,
  0xEF, 0x10, 0x00, 0x01, 0xFE,
  0xBF, 0x70, 0x00, 0x07, 0xFB,
  0x6F, 0xE6, 0x11, 0x6E, 0xF6,
  0x0A, 0xFF, 0xFF, 0xFF, 0xA0,
  0x00, 0x6C, 0xFF, 0xC6, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0070[ 75] = { /* code 0070, LATIN SMALL LETTER P */
  0xFF, 0x1A, 0xFE, 0x91, 0x00,
  0xFF, 0x9F, 0xFF, 0xFC, 0x00,
  0xFF, 0xE5, 0x04, 0xEF, 0x70,
  0xFF, 0x60, 0x00, 0x5F, 0xC0,
  0xFF, 0x10, 0x00, 0x1F, 0xE0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x10, 0x00, 0x1F, 0xE0,
  0xFF, 0x50, 0x00, 0x6F, 0xB0,
  0xFF, 0xE3, 0x04, 0xEF, 0x50,
  0xFF, 0xCF, 0xFF, 0xFB, 0x00,
  0xFF, 0x1B, 0xFE, 0x70, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0071[ 75] = { /* code 0071, LATIN SMALL LETTER Q */
  0x01, 0x9E, 0xF9, 0x0F, 0xF0,
  0x0D, 0xFF, 0xFF, 0x9F, 0xF0,
  0x7F, 0xE3, 0x05, 0xEF, 0xF0,
  0xCF, 0x50, 0x00, 0x6F, 0xF0,
  0xEF, 0x10, 0x00, 0x1F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xEF, 0x10, 0x00, 0x1F, 0xF0,
  0xBF, 0x60, 0x00, 0x5F, 0xF0,
  0x5F, 0xE4, 0x04, 0xEF, 0xF0,
  0x0A, 0xFF, 0xFF, 0xCF, 0xF0,
  0x00, 0x7E, 0xFA, 0x1F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0072[ 33] = { /* code 0072, LATIN SMALL LETTER R */
  0xFF, 0x2C, 0xFB,
  0xFF, 0xBF, 0xF9,
  0xFF, 0xC2, 0x00,
  0xFF, 0x40, 0x00,
  0xFF, 0x10, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00,
  0xFF, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0073[ 55] = { /* code 0073, LATIN SMALL LETTER S */
  0x05, 0xBE, 0xFE, 0xB4, 0x00,
  0x6F, 0xFF, 0xFF, 0xFF, 0x60,
  0xEF, 0x61, 0x01, 0x7F, 0xD0,
  0xFF, 0x61, 0x00, 0x00, 0x00,
  0x9F, 0xFE, 0x96, 0x41, 0x00,
  0x08, 0xEF, 0xFF, 0xFE, 0x30,
  0x00, 0x04, 0x7B, 0xEF, 0xC0,
  0x00, 0x00, 0x00, 0x1F, 0xF0,
  0xDF, 0x50, 0x01, 0x7F, 0xC0,
  0x5F, 0xFF, 0xFF, 0xFF, 0x40,
  0x04, 0xBE, 0xFE, 0xB3, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0074[ 45] = { /* code 0074, LATIN SMALL LETTER T */
  0x01, 0x80, 0x00,
  0x0E, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xF0,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF0, 0x00,
  0x0F, 0xF2, 0x00,
  0x0D, 0xFF, 0xE0,
  0x05, 0xDF, 0xE0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0075[ 55] = { /* code 0075, LATIN SMALL LETTER U */
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x0F, 0xF0,
  0xFF, 0x00, 0x00, 0x1F, 0xF0,
  0xFF, 0x20, 0x00, 0x5F, 0xF0,
  0xDF, 0xB2, 0x05, 0xEF, 0xF0,
  0x7F, 0xFF, 0xFF, 0xAF, 0xF0,
  0x06, 0xDF, 0xE9, 0x1F, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0076[ 77] = { /* code 0076, LATIN SMALL LETTER V */
  0x2F, 0xE0, 0x00, 0x00, 0x00, 0xEF, 0x20,
  0x0A, 0xF6, 0x00, 0x00, 0x06, 0xF9, 0x00,
  0x03, 0xFD, 0x00, 0x00, 0x0C, 0xF3, 0x00,
  0x00, 0xBF, 0x50, 0x00, 0x4F, 0xA0, 0x00,
  0x00, 0x4F, 0xB0, 0x00, 0xBF, 0x40, 0x00,
  0x00, 0x0C, 0xF4, 0x03, 0xFB, 0x00, 0x00,
  0x00, 0x05, 0xFA, 0x0A, 0xF5, 0x00, 0x00,
  0x00, 0x00, 0xDF, 0x6F, 0xC0, 0x00, 0x00,
  0x00, 0x00, 0x6F, 0xFF, 0x60, 0x00, 0x00,
  0x00, 0x00, 0x0E, 0xFD, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x07, 0xF7, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0077[ 99] = { /* code 0077, LATIN SMALL LETTER W */
  0x4F, 0xB0, 0x00, 0x05, 0xF5, 0x00, 0x00, 0xBF, 0x40,
  0x0D, 0xF1, 0x00, 0x09, 0xFA, 0x00, 0x01, 0xFD, 0x00,
  0x07, 0xF7, 0x00, 0x0E, 0xFE, 0x00, 0x07, 0xF7, 0x00,
  0x01, 0xFC, 0x00, 0x3F, 0xBF, 0x30, 0x0C, 0xF1, 0x00,
  0x00, 0x9F, 0x30, 0x8F, 0x4F, 0x80, 0x3F, 0xA0, 0x00,
  0x00, 0x3F, 0x80, 0xCD, 0x0D, 0xD0, 0x8F, 0x40, 0x00,
  0x00, 0x0C, 0xE2, 0xF9, 0x09, 0xF2, 0xED, 0x00, 0x00,
  0x00, 0x06, 0xFA, 0xF5, 0x05, 0xFA, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0xEF, 0xF1, 0x01, 0xFF, 0xF1, 0x00, 0x00,
  0x00, 0x00, 0x8F, 0xC0, 0x00, 0xCF, 0xA0, 0x00, 0x00,
  0x00, 0x00, 0x2F, 0x80, 0x00, 0x8F, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0078[ 66] = { /* code 0078, LATIN SMALL LETTER X */
  0x0A, 0xFA, 0x00, 0x00, 0xAF, 0xA0,
  0x01, 0xDF, 0x60, 0x06, 0xFD, 0x10,
  0x00, 0x4F, 0xE2, 0x2E, 0xF4, 0x00,
  0x00, 0x08, 0xF7, 0x7F, 0x80, 0x00,
  0x00, 0x00, 0xCC, 0xDC, 0x00, 0x00,
  0x00, 0x00, 0x6F, 0xF6, 0x00, 0x00,
  0x00, 0x02, 0xEE, 0xED, 0x10, 0x00,
  0x00, 0x0B, 0xF7, 0x6F, 0x90, 0x00,
  0x00, 0x7F, 0xE0, 0x0B, 0xF4, 0x00,
  0x03, 0xFF, 0x70, 0x03, 0xFE, 0x10,
  0x1D, 0xFE, 0x00, 0x00, 0x9F, 0xA0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_0079[ 75] = { /* code 0079, LATIN SMALL LETTER Y */
  0x3F, 0xD0, 0x00, 0x02, 0xFD,
  0x0B, 0xF4, 0x00, 0x06, 0xF9,
  0x05, 0xFB, 0x00, 0x0A, 0xF5,
  0x00, 0xEF, 0x20, 0x0E, 0xF1,
  0x00, 0x7F, 0x80, 0x3F, 0xC0,
  0x00, 0x1F, 0xE1, 0x7F, 0x80,
  0x00, 0x0A, 0xF6, 0xBF, 0x40,
  0x00, 0x04, 0xFC, 0xFF, 0x00,
  0x00, 0x00, 0xCF, 0xFB, 0x00,
  0x00, 0x00, 0x6F, 0xF7, 0x00,
  0x00, 0x00, 0x2F, 0xF3, 0x00,
  0x00, 0x00, 0x7F, 0xD0, 0x00,
  0x01, 0x04, 0xEF, 0x50, 0x00,
  0x0F, 0xFF, 0xFA, 0x00, 0x00,
  0x0D, 0xFD, 0x80, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_007A[ 55] = { /* code 007A, LATIN SMALL LETTER Z */
  0x0F, 0xFF, 0xFF, 0xFF, 0xF1,
  0x0F, 0xFF, 0xFF, 0xFF, 0xF2,
  0x00, 0x00, 0x01, 0xBF, 0xE2,
  0x00, 0x00, 0x1D, 0xFE, 0x30,
  0x00, 0x01, 0xDF, 0xD2, 0x00,
  0x00, 0x2E, 0xFC, 0x10, 0x00,
  0x02, 0xEF, 0xB1, 0x00, 0x00,
  0x3E, 0xF9, 0x00, 0x00, 0x00,
  0xEF, 0xA0, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_007B[ 57] = { /* code 007B, LEFT CURLY BRACKET */
  0x00, 0x1B, 0xFF,
  0x00, 0xBF, 0xFF,
  0x00, 0xFF, 0x50,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x02, 0xFD, 0x00,
  0x1A, 0xF9, 0x00,
  0xFF, 0x91, 0x00,
  0xFF, 0x80, 0x00,
  0x1B, 0xF7, 0x00,
  0x03, 0xFC, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xDF, 0x40,
  0x00, 0x9F, 0xFF,
  0x00, 0x1A, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_007C[ 20] = { /* code 007C, VERTICAL LINE */
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF,
  0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_007D[ 57] = { /* code 007D, RIGHT CURLY BRACKET */
  0xFF, 0xB1, 0x00,
  0xFF, 0xFB, 0x00,
  0x04, 0xFE, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xDF, 0x20,
  0x00, 0x8F, 0xA1,
  0x00, 0x19, 0xFF,
  0x00, 0x09, 0xFF,
  0x00, 0x8F, 0xB1,
  0x00, 0xEF, 0x30,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x00, 0xFF, 0x00,
  0x05, 0xFE, 0x00,
  0xFF, 0xFA, 0x00,
  0xFF, 0xA1, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_Font24_EXT_AA4_007E[ 20] = { /* code 007E, TILDE */
  0x1A, 0xFD, 0x70, 0x00, 0x05,
  0xBF, 0xFF, 0xFD, 0x40, 0x6F,
  0xF5, 0x04, 0xCF, 0xFF, 0xFA,
  0x60, 0x00, 0x06, 0xDF, 0xA1
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_Font24_EXT_AA4_CharInfo[95] = {
   {   1,   1,   0,  19,   6, acGUI_Font24_EXT_AA4_0020 } /* code 0020, SPACE */
  ,{   2,  15,   2,   4,   6, acGUI_Font24_EXT_AA4_0021 } /* code 0021, EXCLAMATION MARK */
  ,{   6,   5,   1,   4,   7, acGUI_Font24_EXT_AA4_0022 } /* code 0022, QUOTATION MARK */
  ,{  12,  15,   0,   4,  12, acGUI_Font24_EXT_AA4_0023 } /* code 0023, NUMBER SIGN */
  ,{   9,  18,   1,   3,  12, acGUI_Font24_EXT_AA4_0024 } /* code 0024, DOLLAR SIGN */
  ,{  17,  15,   1,   4,  19, acGUI_Font24_EXT_AA4_0025 } /* code 0025, PERCENT SIGN */
  ,{  13,  15,   1,   4,  14, acGUI_Font24_EXT_AA4_0026 } /* code 0026, AMPERSAND */
  ,{   2,   5,   1,   4,   4, acGUI_Font24_EXT_AA4_0027 } /* code 0027, APOSTROPHE */
  ,{   5,  19,   1,   4,   7, acGUI_Font24_EXT_AA4_0028 } /* code 0028, LEFT PARENTHESIS */
  ,{   5,  19,   1,   4,   7, acGUI_Font24_EXT_AA4_0029 } /* code 0029, RIGHT PARENTHESIS */
  ,{   8,   6,   0,   4,   8, acGUI_Font24_EXT_AA4_002A } /* code 002A, ASTERISK */
  ,{  10,  10,   1,   7,  12, acGUI_Font24_EXT_AA4_002B } /* code 002B, PLUS SIGN */
  ,{   2,   5,   2,  17,   6, acGUI_Font24_EXT_AA4_002C } /* code 002C, COMMA */
  ,{   6,   2,   0,  12,   7, acGUI_Font24_EXT_AA4_002D } /* code 002D, HYPHEN-MINUS */
  ,{   2,   2,   2,  17,   6, acGUI_Font24_EXT_AA4_002E } /* code 002E, FULL STOP */
  ,{   6,  15,   0,   4,   6, acGUI_Font24_EXT_AA4_002F } /* code 002F, SOLIDUS */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0030 } /* code 0030, DIGIT ZERO */
  ,{   6,  15,   2,   4,  12, acGUI_Font24_EXT_AA4_0031 } /* code 0031, DIGIT ONE */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0032 } /* code 0032, DIGIT TWO */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0033 } /* code 0033, DIGIT THREE */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0034 } /* code 0034, DIGIT FOUR */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0035 } /* code 0035, DIGIT FIVE */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0036 } /* code 0036, DIGIT SIX */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0037 } /* code 0037, DIGIT SEVEN */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0038 } /* code 0038, DIGIT EIGHT */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_0039 } /* code 0039, DIGIT NINE */
  ,{   2,  11,   2,   8,   6, acGUI_Font24_EXT_AA4_003A } /* code 003A, COLON */
  ,{   2,  14,   2,   8,   6, acGUI_Font24_EXT_AA4_003B } /* code 003B, SEMICOLON */
  ,{  10,  11,   1,   6,  12, acGUI_Font24_EXT_AA4_003C } /* code 003C, LESS-THAN SIGN */
  ,{  10,   7,   1,   8,  12, acGUI_Font24_EXT_AA4_003D } /* code 003D, EQUALS SIGN */
  ,{  10,  11,   1,   6,  12, acGUI_Font24_EXT_AA4_003E } /* code 003E, GREATER-THAN SIGN */
  ,{  10,  15,   1,   4,  12, acGUI_Font24_EXT_AA4_003F } /* code 003F, QUESTION MARK */
  ,{  19,  19,   1,   4,  21, acGUI_Font24_EXT_AA4_0040 } /* code 0040, COMMERCIAL AT */
  ,{  15,  15,  -1,   4,  13, acGUI_Font24_EXT_AA4_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{  11,  15,   2,   4,  14, acGUI_Font24_EXT_AA4_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{  13,  15,   1,   4,  15, acGUI_Font24_EXT_AA4_0043 } /* code 0043, LATIN CAPITAL LETTER C */
  ,{  12,  15,   2,   4,  15, acGUI_Font24_EXT_AA4_0044 } /* code 0044, LATIN CAPITAL LETTER D */
  ,{  11,  15,   2,   4,  14, acGUI_Font24_EXT_AA4_0045 } /* code 0045, LATIN CAPITAL LETTER E */
  ,{  10,  15,   2,   4,  13, acGUI_Font24_EXT_AA4_0046 } /* code 0046, LATIN CAPITAL LETTER F */
  ,{  14,  15,   1,   4,  16, acGUI_Font24_EXT_AA4_0047 } /* code 0047, LATIN CAPITAL LETTER G */
  ,{  12,  15,   1,   4,  14, acGUI_Font24_EXT_AA4_0048 } /* code 0048, LATIN CAPITAL LETTER H */
  ,{   2,  15,   2,   4,   6, acGUI_Font24_EXT_AA4_0049 } /* code 0049, LATIN CAPITAL LETTER I */
  ,{   8,  15,   1,   4,  11, acGUI_Font24_EXT_AA4_004A } /* code 004A, LATIN CAPITAL LETTER J */
  ,{  12,  15,   2,   4,  14, acGUI_Font24_EXT_AA4_004B } /* code 004B, LATIN CAPITAL LETTER K */
  ,{   9,  15,   2,   4,  12, acGUI_Font24_EXT_AA4_004C } /* code 004C, LATIN CAPITAL LETTER L */
  ,{  15,  15,   1,   4,  17, acGUI_Font24_EXT_AA4_004D } /* code 004D, LATIN CAPITAL LETTER M */
  ,{  12,  15,   1,   4,  14, acGUI_Font24_EXT_AA4_004E } /* code 004E, LATIN CAPITAL LETTER N */
  ,{  14,  15,   1,   4,  16, acGUI_Font24_EXT_AA4_004F } /* code 004F, LATIN CAPITAL LETTER O */
  ,{  11,  15,   2,   4,  14, acGUI_Font24_EXT_AA4_0050 } /* code 0050, LATIN CAPITAL LETTER P */
  ,{  14,  16,   1,   4,  16, acGUI_Font24_EXT_AA4_0051 } /* code 0051, LATIN CAPITAL LETTER Q */
  ,{  12,  15,   2,   4,  15, acGUI_Font24_EXT_AA4_0052 } /* code 0052, LATIN CAPITAL LETTER R */
  ,{  12,  15,   1,   4,  14, acGUI_Font24_EXT_AA4_0053 } /* code 0053, LATIN CAPITAL LETTER S */
  ,{  12,  15,   0,   4,  12, acGUI_Font24_EXT_AA4_0054 } /* code 0054, LATIN CAPITAL LETTER T */
  ,{  12,  15,   1,   4,  14, acGUI_Font24_EXT_AA4_0055 } /* code 0055, LATIN CAPITAL LETTER U */
  ,{  13,  15,   0,   4,  13, acGUI_Font24_EXT_AA4_0056 } /* code 0056, LATIN CAPITAL LETTER V */
  ,{  21,  15,   0,   4,  21, acGUI_Font24_EXT_AA4_0057 } /* code 0057, LATIN CAPITAL LETTER W */
  ,{  14,  15,   0,   4,  14, acGUI_Font24_EXT_AA4_0058 } /* code 0058, LATIN CAPITAL LETTER X */
  ,{  14,  15,   0,   4,  14, acGUI_Font24_EXT_AA4_0059 } /* code 0059, LATIN CAPITAL LETTER Y */
  ,{  12,  15,   0,   4,  13, acGUI_Font24_EXT_AA4_005A } /* code 005A, LATIN CAPITAL LETTER Z */
  ,{   4,  19,   1,   4,   6, acGUI_Font24_EXT_AA4_005B } /* code 005B, LEFT SQUARE BRACKET */
  ,{   6,  15,   0,   4,   6, acGUI_Font24_EXT_AA4_005C } /* code 005C, REVERSE SOLIDUS */
  ,{   4,  19,   1,   4,   6, acGUI_Font24_EXT_AA4_005D } /* code 005D, RIGHT SQUARE BRACKET */
  ,{   8,   8,   0,   4,   8, acGUI_Font24_EXT_AA4_005E } /* code 005E, CIRCUMFLEX ACCENT */
  ,{  12,   2,   0,  21,  12, acGUI_Font24_EXT_AA4_005F } /* code 005F, LOW LINE */
  ,{   3,   3,   1,   4,   7, acGUI_Font24_EXT_AA4_0060 } /* code 0060, GRAVE ACCENT */
  ,{  11,  11,   0,   8,  12, acGUI_Font24_EXT_AA4_0061 } /* code 0061, LATIN SMALL LETTER A */
  ,{   9,  15,   1,   4,  11, acGUI_Font24_EXT_AA4_0062 } /* code 0062, LATIN SMALL LETTER B */
  ,{   9,  11,   1,   8,  11, acGUI_Font24_EXT_AA4_0063 } /* code 0063, LATIN SMALL LETTER C */
  ,{   9,  15,   1,   4,  11, acGUI_Font24_EXT_AA4_0064 } /* code 0064, LATIN SMALL LETTER D */
  ,{  10,  11,   1,   8,  12, acGUI_Font24_EXT_AA4_0065 } /* code 0065, LATIN SMALL LETTER E */
  ,{   7,  15,   0,   4,   6, acGUI_Font24_EXT_AA4_0066 } /* code 0066, LATIN SMALL LETTER F */
  ,{   9,  15,   1,   8,  11, acGUI_Font24_EXT_AA4_0067 } /* code 0067, LATIN SMALL LETTER G */
  ,{   9,  15,   1,   4,  11, acGUI_Font24_EXT_AA4_0068 } /* code 0068, LATIN SMALL LETTER H */
  ,{   2,  15,   1,   4,   5, acGUI_Font24_EXT_AA4_0069 } /* code 0069, LATIN SMALL LETTER I */
  ,{   4,  19,  -1,   4,   4, acGUI_Font24_EXT_AA4_006A } /* code 006A, LATIN SMALL LETTER J */
  ,{   9,  15,   1,   4,  10, acGUI_Font24_EXT_AA4_006B } /* code 006B, LATIN SMALL LETTER K */
  ,{   2,  15,   1,   4,   4, acGUI_Font24_EXT_AA4_006C } /* code 006C, LATIN SMALL LETTER L */
  ,{  14,  11,   1,   8,  16, acGUI_Font24_EXT_AA4_006D } /* code 006D, LATIN SMALL LETTER M */
  ,{   9,  11,   1,   8,  11, acGUI_Font24_EXT_AA4_006E } /* code 006E, LATIN SMALL LETTER N */
  ,{  10,  11,   1,   8,  12, acGUI_Font24_EXT_AA4_006F } /* code 006F, LATIN SMALL LETTER O */
  ,{   9,  15,   1,   8,  11, acGUI_Font24_EXT_AA4_0070 } /* code 0070, LATIN SMALL LETTER P */
  ,{   9,  15,   1,   8,  11, acGUI_Font24_EXT_AA4_0071 } /* code 0071, LATIN SMALL LETTER Q */
  ,{   6,  11,   1,   8,   7, acGUI_Font24_EXT_AA4_0072 } /* code 0072, LATIN SMALL LETTER R */
  ,{   9,  11,   1,   8,  11, acGUI_Font24_EXT_AA4_0073 } /* code 0073, LATIN SMALL LETTER S */
  ,{   5,  15,   0,   4,   6, acGUI_Font24_EXT_AA4_0074 } /* code 0074, LATIN SMALL LETTER T */
  ,{   9,  11,   1,   8,  11, acGUI_Font24_EXT_AA4_0075 } /* code 0075, LATIN SMALL LETTER U */
  ,{  13,  11,  -1,   8,  11, acGUI_Font24_EXT_AA4_0076 } /* code 0076, LATIN SMALL LETTER V */
  ,{  17,  11,  -1,   8,  15, acGUI_Font24_EXT_AA4_0077 } /* code 0077, LATIN SMALL LETTER W */
  ,{  11,  11,  -1,   8,  10, acGUI_Font24_EXT_AA4_0078 } /* code 0078, LATIN SMALL LETTER X */
  ,{  10,  15,   0,   8,  11, acGUI_Font24_EXT_AA4_0079 } /* code 0079, LATIN SMALL LETTER Y */
  ,{  10,  11,   0,   8,   9, acGUI_Font24_EXT_AA4_007A } /* code 007A, LATIN SMALL LETTER Z */
  ,{   6,  19,   0,   4,   7, acGUI_Font24_EXT_AA4_007B } /* code 007B, LEFT CURLY BRACKET */
  ,{   2,  20,   2,   4,   6, acGUI_Font24_EXT_AA4_007C } /* code 007C, VERTICAL LINE */
  ,{   6,  19,   1,   4,   7, acGUI_Font24_EXT_AA4_007D } /* code 007D, RIGHT CURLY BRACKET */
  ,{  10,   4,   1,  10,  12, acGUI_Font24_EXT_AA4_007E } /* code 007E, TILDE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_Font24_EXT_AA4_Prop1 = {
   0x0020 /* first character */
  ,0x007E /* last character  */
  ,&GUI_Font24_EXT_AA4_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_Font24_EXT_AA4 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,24 /* height of font  */
  ,24 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_Font24_EXT_AA4_Prop1}
  ,24 /* Baseline */
  ,11 /* Height of lowercase characters */
  ,15 /* Height of capital characters */
};

//
// Animation
//
static ANIM _aAnim[] = {
  {280, 125, 0,  300},
  {280, 125, 1,  200},
  {280, 125, 0,  500},

  {225, 155, 0,  300},
  {225, 155, 1,  200},
  {225, 155, 0,  500},

  {280, 160, 0,  300},
  {280, 160, 1,  200},
  {280, 160, 0,  500},

  {225, 160, 0,  300},
  {225, 160, 1,  200},
  {225, 160, 0,  500},

  { 80, 200, 0,  300},
  { 80, 200, 1,  200},
  { 80, 200, 0, 1500},

  {250, 190, 0,  300},
  {250, 190, 1, 1000},
  {250, 190, 0, 1500},

  {170, 125, 0,  300},
  {170, 125, 1,  200},
  {170, 125, 0,  500},

  {180, 215, 0,  300},
  {180, 215, 1,  200},
  {180, 215, 0,  500},

  {210, 200, 0,  300},
  {210, 200, 1,  200},
  {210, 200, 0, 5000},
};

static char _acVehicle[40];
static int  _ReadyDialogOrder;
static int  _ReadyDialogSelect;

//
// List of cars
//
static const char _acItems[][2][20] = {
  {"AUDI",      "A6"               },
  {"AUDI",      "A8"               },
  {"AUDI",      "Quattro"          },
  {"AUDI",      "TT"               },
  {"BMW",       "325xi"            },
  {"BMW",       "330i"             },
  {"BMW",       "745i"             },
  {"BMW",       "Mini Cooper"      },
  {"CHEVROLET", "Cavalier LS Sport"},
  {"CHEVROLET", "Corvette Z06"     },
  {"CHEVROLET", "Malibu LS"        },
  {"FERRARI",   "575M"             },
  {"FORD",      "Escort"           },
  {"FORD",      "Focus"            },
  {"HYUNDAI",   "Sonata"           },
  {"HYUNDAI",   "XG350"            },
  {"JAGUAR",    "VDP"              },
  {"JAGUAR",    "S-Type"           },
  {"MAZDA",     "MX-5"             },
  {"MERCEDES",  "S"                },
  {"MERCEDES",  "CL"               },
  {"PORSCHE",   "Boxster"          },
  {"",          ""                 }
};

//
// Dialog resources
//
static const GUI_WIDGET_CREATE_INFO _aDialogOrder[] = {
  { FRAMEWIN_CreateIndirect, "Order vehicle",   0,                 20,  50, 280, 165, 0 },
  { TEXT_CreateIndirect,     "Selection",       0,                 37,  13,  80,  20, 0 },
  { TEXT_CreateIndirect,     "Drive",           0,                 10,  35,  80,  20, 0 },
  { TEXT_CreateIndirect,     "Color",           0,                108,  47,  80,  20, 0 },
  { TEXT_CreateIndirect,     "Options",         0,                 95,  77,  80,  20, 0 },
  { EDIT_CreateIndirect,     NULL,              GUI_ID_EDIT0,      85,  10, 180,  20, 0, 40},
  { RADIO_CreateIndirect,    NULL,              GUI_ID_RADIO0,     10,  50,  50,   0, 0, 0xF03 },
  { DROPDOWN_CreateIndirect, NULL,              GUI_ID_DROPDOWN0, 135,  45, 130,  45, 0 },
  { DROPDOWN_CreateIndirect, NULL,              GUI_ID_DROPDOWN1, 135,  75, 130,  45, 0 },
  { BUTTON_CreateIndirect,   "Select vehicle",  GUI_ID_BUTTON0,     6, 113, 100,  20, 0 },
  { BUTTON_CreateIndirect,   "OK",              GUI_ID_OK,        160, 113,  50,  20, 0 },
  { BUTTON_CreateIndirect,   "Cancel",          GUI_ID_CANCEL,    217, 113,  50,  20, 0 }
};

static const GUI_WIDGET_CREATE_INFO _aDialogSelect[] = {
  { FRAMEWIN_CreateIndirect, "Select vehicle",  0,                 55,  30, 210, 205, 0 },
  { TEXT_CreateIndirect,     "Available vehicles:", 0,              9,   6, 120,  20, 0 },
  { LISTVIEW_CreateIndirect, NULL,              GUI_ID_LISTVIEW0,   7,  20, 190, 129, 0 },
  { BUTTON_CreateIndirect,   "OK",              GUI_ID_OK,         90, 153,  50,  20, 0 },
  { BUTTON_CreateIndirect,   "Cancel",          GUI_ID_CANCEL,    147, 153,  50,  20, 0 }
};

static const GUI_WIDGET_CREATE_INFO _aDialogProgress[] = {
  { FRAMEWIN_CreateIndirect, "In Progress...",  0,                 80,  80, 150,  60, 0 },
  { PROGBAR_CreateIndirect,  0,                 GUI_ID_PROGBAR0,    9,   6, 120,  20, 0 },
};

/*********************************************************************
*
*       static code
*
**********************************************************************
*/
/*********************************************************************
*
*       _AddListviewItem
*/
static void _AddListviewItem(LISTVIEW_Handle hObj, const char* pMake, const char* pModel) {
  unsigned NumItems;

  NumItems = LISTVIEW_GetNumRows(hObj);
  LISTVIEW_AddRow(hObj, NULL);
  LISTVIEW_SetItemText(hObj, 0, NumItems, pMake);
  LISTVIEW_SetItemText(hObj, 1, NumItems, pModel);
}

/*********************************************************************
*
*       _FramewinDrawSkin
*/
static int _DrawSkin(const WIDGET_ITEM_DRAW_INFO * pDrawItemInfo) {
  switch (pDrawItemInfo->Cmd) {
  case WIDGET_ITEM_GET_RADIUS:
    return FRAMEWIN_RADIUS;
  default:
    return FRAMEWIN_DrawSkinFlex(pDrawItemInfo);
  }
}

/*********************************************************************
*
*       _InitDialogSelect
*/
static void _InitDialogSelect(WM_HWIN hWin) {
  WM_HWIN hItem;
  int     i = 0;

  hItem = WM_GetDialogItem(hWin, GUI_ID_LISTVIEW0);
  WM_SetScrollbarV(hItem, 1);
  LISTVIEW_SetGridVis(hItem, 1);
  LISTVIEW_SetLBorder(hItem, 3);
  LISTVIEW_SetRBorder(hItem, 3);
  LISTVIEW_AddColumn(hItem,  80, "Make",    GUI_TA_LEFT);
  LISTVIEW_AddColumn(hItem, 100, "Options", GUI_TA_LEFT);
  while (_acItems[i][0][0]) {
    _AddListviewItem(hItem, _acItems[i][0], _acItems[i][1]);
    i++;
  }
}

/*********************************************************************
*
*       _InitDialogOrder
*/
static void _InitDialogOrder(WM_HWIN hWin) {
  WM_HWIN hItem;

  hItem = WM_GetDialogItem(hWin, GUI_ID_EDIT0);
  WM_DisableWindow(hItem);
  //
  // Init dropdown box color
  //
  hItem = WM_GetDialogItem(hWin, GUI_ID_DROPDOWN0);
  DROPDOWN_AddString(hItem, "Blue");
  DROPDOWN_AddString(hItem, "Green");
  DROPDOWN_AddString(hItem, "Red");
  DROPDOWN_SetBkColor(hItem, 1, GUI_WHITE);
  DROPDOWN_SetTextColor(hItem, 1, GUI_BLACK);
  //
  // Init dropdown box model
  //
  hItem = WM_GetDialogItem(hWin, GUI_ID_DROPDOWN1);
  DROPDOWN_AddString(hItem, "Navigation system");
  DROPDOWN_AddString(hItem, "CD Player");
  DROPDOWN_AddString(hItem, "Other ...");
  DROPDOWN_SetBkColor(hItem, 1, GUI_WHITE);
  DROPDOWN_SetTextColor(hItem, 1, GUI_BLACK);
}

/*********************************************************************
*
*       _GetVehicle
*/
static int _GetVehicle(WM_HWIN hWin) {
  WM_HWIN hItem;
  int     CurSel, NumItems;

  hItem    = WM_GetDialogItem(hWin, GUI_ID_LISTVIEW0);
  NumItems = LISTVIEW_GetNumRows(hItem);
  CurSel   = LISTVIEW_GetSel(hItem);
  if ((CurSel >= 0) && (CurSel < NumItems)) {
    strcpy(_acVehicle, _acItems[CurSel][0]);
    strcat(_acVehicle, " ");
    strcat(_acVehicle, _acItems[CurSel][1]);

    hItem = WM_GetDialogItem(hWin, GUI_ID_EDIT0);
    EDIT_SetText(hItem, _acVehicle);
    return 1;
  }
  return 0;
}

/*********************************************************************
*
*       _MessageBox
*/
static void _MessageBox(const char* pText, const char* pCaption) {
  WM_HWIN hWin;

  hWin = MESSAGEBOX_Create(pText, pCaption, 0);
  GUI_ExecCreatedDialog(hWin);
}

/*********************************************************************
*
*       _cbBkWindow
*/
static void _cbBkWindow(WM_MESSAGE * pMsg) {
  int xSize, ySize;

  switch (pMsg->MsgId) {
  case WM_PAINT:
    xSize = LCD_GetXSize();
    ySize = LCD_GetYSize();
    GUI_SetColor(GUI_WHITE);
    GUI_SetTextMode(GUI_TM_TRANS);
    GUI_DrawGradientV(0, 0, xSize, ySize, 0xFF8080, 0x8080FF);
    GUI_SetFont(GUI_FONT_16_ASCII);
    GUI_DispStringHCenterAt("STemWin now supports\n", 180, 5);
    GUI_SetFont(&GUI_Font24_EXT_AA4);
    GUI_DispStringHCenterAt("S k i n n i n g", 180, GUI_GetDispPosY());
    GUI_DrawBitmap(&bmSeggerLogo_60x30, 20, 10);
  default:
    WM_DefaultProc(pMsg);
  }
}

/*********************************************************************
*
*       _cbDialogSelect
*/
static void _cbDialogSelect(WM_MESSAGE * pMsg) {
  WM_HWIN  hWin;
  GUI_RECT Rect;

  hWin = pMsg->hWin;
  switch (pMsg->MsgId) {
  case WM_PAINT:
    WM_GetClientRectEx(hWin, &Rect);
    GUI_DrawGradientV(Rect.x0, Rect.y0, Rect.x1, Rect.y1, 0x8080FF, 0xFF8080);
    break;
  case WM_INIT_DIALOG:
    _ReadyDialogSelect = 0;
    _InitDialogSelect(hWin);
    WM_SetFocus(WM_GetDialogItem(hWin, GUI_ID_OK));
    break;
  case WM_NOTIFY_PARENT:
    if (pMsg->Data.v == WM_NOTIFICATION_RELEASED) {
      int Id = WM_GetId(pMsg->hWinSrc);
      switch (Id) {
      case GUI_ID_OK:
        _ReadyDialogSelect = 1;
        if (_GetVehicle(hWin) == 0) {
          _MessageBox("You have to select a vehicle!", "ERROR");
          WM_SetFocus(hWin);
          break;
        }
      case GUI_ID_CANCEL:
        _ReadyDialogSelect = 1;
        GUI_MEMDEV_MoveOutWindow(pMsg->hWin, 80, 190, 180, 500);
        GUI_EndDialog(pMsg->hWin, 0);
        break;
      }
    }
    break;
  default:
    WM_DefaultProc(pMsg);
  }
}

/*********************************************************************
*
*       _cbDialogProgress
*/
static void _cbDialogProgress(WM_MESSAGE * pMsg) {
  static int Progress;
  WM_HWIN    hWin, hItem;

  hWin = pMsg->hWin;
  switch (pMsg->MsgId) {
  case WM_PAINT:
    GUI_SetBkColor(GUI_WHITE);
    GUI_Clear();
    break;
  case WM_TIMER:
    if (Progress < 100) {
      Progress += 2;
      hItem = WM_GetDialogItem(hWin, GUI_ID_PROGBAR0);
      PROGBAR_SetValue(hItem, Progress);
      WM_RestartTimer(pMsg->Data.v, 20);
    } else {
      WM_DeleteTimer(pMsg->Data.v);
      GUI_MEMDEV_FadeOutWindow(pMsg->hWin, 500);
      GUI_EndDialog(pMsg->hWin, 0);
    }
    break;
  case WM_INIT_DIALOG:
    Progress = 0;
    WM_CreateTimer(WM_GetClientWindow(hWin), 0, 200, 0);
    break;
  default:
    WM_DefaultProc(pMsg);
  }
}

/*********************************************************************
*
*       _cbDialogOrder
*/
static void _cbDialogOrder(WM_MESSAGE * pMsg) {
  WM_HWIN hDlg, hWin, hItem, hClient, hProg;

  hWin = pMsg->hWin;
  switch (pMsg->MsgId) {
  case WM_DELETE:
    _ReadyDialogOrder = 1;
    break;
  case WM_INIT_DIALOG:
    _ReadyDialogOrder = 0;
    _InitDialogOrder(hWin);
    //
    // RADIO widget
    //
    hItem = WM_GetDialogItem(hWin, GUI_ID_RADIO0);
    RADIO_SetText(hItem, "Front", 0);
    RADIO_SetText(hItem, "Rear", 1);
    RADIO_SetText(hItem, "Both", 2);
    //
    // EDIT widget
    //
    hItem = WM_GetDialogItem(hWin, GUI_ID_EDIT0);
    EDIT_SetTextAlign(hItem, GUI_TA_VCENTER | GUI_TA_HCENTER);
    EDIT_SetBkColor(hItem, EDIT_CI_DISABLED, 0xF0F0F0);
    //
    // Set transparency
    //
    WM_SetHasTrans(hWin);
    hClient = WM_GetClientWindow(hWin);
    WM_SetHasTrans(hClient);
    FRAMEWIN_SetClientColor(hWin, GUI_INVALID_COLOR);
    //
    // Add close button
    //
    FRAMEWIN_AddCloseButton(hWin, FRAMEWIN_BUTTON_RIGHT, 0);
    //
    // Set focus to OK button
    //
    WM_SetFocus(WM_GetDialogItem(hWin, GUI_ID_OK));
    break;
  case WM_NOTIFY_PARENT:
    if (pMsg->Data.v == WM_NOTIFICATION_RELEASED) {
      int Id = WM_GetId(pMsg->hWinSrc);
      switch (Id) {
      case GUI_ID_BUTTON0:
        hDlg = GUI_CreateDialogBox(_aDialogSelect, GUI_COUNTOF(_aDialogSelect), &_cbDialogSelect, WM_HBKWIN, 0, 0);
        FRAMEWIN_SetSkin(hDlg, _DrawSkin);
        WM_ClrHasTrans(hDlg);
        GUI_MEMDEV_MoveInWindow(hDlg, 80, 190, 180, 500);
        while ((_ReadyDialogOrder == 0) && (GUIDEMO_CheckCancel() == 0)) {
          GUI_Delay(100);
          if (_ReadyDialogSelect == 1) {
            //
            // Make sure the dialog is not deleted.
            //
            if (WM_IsWindow(hWin)) {
              _ReadyDialogSelect++;
              hItem = WM_GetDialogItem(hWin, GUI_ID_EDIT0);
              EDIT_SetText(hItem, _acVehicle);
            }
          }
        }
        if (WM_IsWindow(hDlg)) {
          WM_DeleteWindow(hDlg);
        }
        break;
      case GUI_ID_OK:
        hProg = GUI_CreateDialogBox(_aDialogProgress, GUI_COUNTOF(_aDialogProgress), &_cbDialogProgress, WM_HBKWIN, 0, 0);
        //
        // Make modal to avoid several creations of the progress window.
        //
        WM_MakeModal(hProg);
        FRAMEWIN_SetSkin(hProg, _DrawSkin);
        GUI_MEMDEV_FadeInWindow(hProg, 500);
        WM_InvalidateWindow(hWin);
        GUI_ExecCreatedDialog(hProg);
        //
        // No break here...
        //
      case GUI_ID_CANCEL:
        //
        // Make sure the dialog is not deleted.
        //
        if (WM_IsWindow(hWin)) {
          GUI_MEMDEV_FadeOutWindow(hWin, 500);
          GUI_EndDialog(hWin, 0);
        }
        _ReadyDialogOrder = 1;
        break;
      }
    }
    break;
  default:
    WM_DefaultProc(pMsg);
  }
}

/*********************************************************************
*
*       _cbAnimation
*/
static void _cbAnimation(WM_MESSAGE * pMsg) {
  static int      Index;
  GUI_PID_STATE   State = {0};
  WM_HWIN         hWin;
  ANIM          * pAnim;

  hWin = pMsg->hWin;
  pAnim = &_aAnim[Index];
  switch (pMsg->MsgId) {
  case WM_TIMER:
    if (GUIDEMO_CheckCancel()) {
      WM_RestartTimer(pMsg->Data.v, 100);
    } else {
      State.x = pAnim->x;
      State.y = pAnim->y;
      State.Pressed = pAnim->Pressed;
      GUI_PID_StoreState(&State);
      WM_RestartTimer(pMsg->Data.v, pAnim->Delay);
      Index = (Index == (GUI_COUNTOF(_aAnim) - 1)) ? 0 : Index + 1;
    }
    break;
  case WM_CREATE:
    WM_CreateTimer(hWin, 0, 1500, 0);
    break;
  default:
    WM_DefaultProc(pMsg);
  }
}

/*********************************************************************
*
*       _DemoSkinning
*/
static void _DemoSkinning(void) {
  WM_HWIN                       hWin;
  WM_HWIN                       hWinAnimation;
  WIDGET_DRAW_ITEM_FUNC       * pfOldCheckbox;
  WIDGET_DRAW_ITEM_FUNC       * pfOldDropdown;
  WIDGET_DRAW_ITEM_FUNC       * pfOldRadio;
  const WIDGET_EFFECT         * pEffectOld;
  const GUI_FONT GUI_UNI_PTR  * pFrameWinFontOld;
  int                           FrameWinTextAlignOld;
  WM_CALLBACK                 * pBkWinCBOld;

  //
  // Enable skinning
  //
  pfOldCheckbox = CHECKBOX_SetDefaultSkin(CHECKBOX_SKIN_FLEX);
  pfOldDropdown = DROPDOWN_SetDefaultSkin(DROPDOWN_SKIN_FLEX);
  pfOldRadio    = RADIO_SetDefaultSkin   (RADIO_SKIN_FLEX);
  //
  // Set some further defaults
  //
  pEffectOld           = WIDGET_SetDefaultEffect(&WIDGET_Effect_Simple);
  pFrameWinFontOld     = FRAMEWIN_GetDefaultFont();
  FRAMEWIN_SetDefaultFont(GUI_FONT_20_ASCII);
  FrameWinTextAlignOld = FRAMEWIN_SetDefaultTextAlign(GUI_TA_HCENTER);
  pBkWinCBOld          = WM_SetCallback(WM_HBKWIN, &_cbBkWindow);
  //
  // Create hidden animation window
  //
  hWinAnimation = WM_CreateWindowAsChild(0, 0, 1, 1, WM_HBKWIN, 0, _cbAnimation, 0);
  //
  // Animation
  //
  hWin = GUI_CreateDialogBox(_aDialogOrder, GUI_COUNTOF(_aDialogOrder), &_cbDialogOrder, WM_HBKWIN, 0, 0);
  FRAMEWIN_SetSkin(hWin, _DrawSkin);
  GUI_MEMDEV_MoveInWindow(hWin, 0, 0, -90, 500);
  while ((_ReadyDialogOrder == 0) && (GUIDEMO_CheckCancel() == 0)) {
    GUI_Delay(100);
  }
  //
  // Undo changes to GUI
  //
  WM_DeleteWindow             (hWin);
  WM_DeleteWindow             (hWinAnimation);
  WM_SetCallback              (WM_HBKWIN, pBkWinCBOld);
  FRAMEWIN_SetDefaultTextAlign(FrameWinTextAlignOld);
  FRAMEWIN_SetDefaultFont     (pFrameWinFontOld);
  WIDGET_SetDefaultEffect     (pEffectOld);
  CHECKBOX_SetDefaultSkin     (pfOldCheckbox);
  DROPDOWN_SetDefaultSkin     (pfOldDropdown);
  RADIO_SetDefaultSkin        (pfOldRadio);
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/
/*********************************************************************
*
*       GUIDEMO_Skinning
*/
void GUIDEMO_Skinning(void) {
  GUIDEMO_HideInfoWin();
  GUIDEMO_ShowIntro("Skinning demo",
                    "Shows some widgets\n"
                    "displayed with a new skin");
  _DemoSkinning();
}

#else

void GUIDEMO_Skinning(void) {}

#endif

/*************************** End of file ****************************/
