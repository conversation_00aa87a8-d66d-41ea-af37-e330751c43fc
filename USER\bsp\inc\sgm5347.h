#ifndef _SGM5347_H
#define _SGM5347_H
#include "bsp.h"

//端口定义
// <PERSON>Y<PERSON>(PH10), SCLK(PH11), SDATA(PH12)
#define GAIN_DA_FSYNC(n)  		(n?HAL_GPIO_WritePin(GPIOH,GPIO_PIN_10,GP<PERSON>_PIN_SET):HAL_GPIO_WritePin(GPIOH,GPIO_PIN_10,GPIO_PIN_RESET))
#define GAIN_DA_SCLK(n)   		(n?HAL_GPIO_WritePin(GPIOH,GPIO_PIN_11,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOH,GPIO_PIN_11,GPIO_PIN_RESET))
#define GAIN_DA_SDATA(n)  		(n?HAL_GPIO_WritePin(GPIOH,GPIO_PIN_12,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOH,GPIO_PIN_12,<PERSON><PERSON>_PIN_RESET))


#define SGM5347_REF_VOL			2048 	//mv

//#define GAIN_MAX				111 //dB
//#define GAIN_MIN				15 //dB
//#define GAIN_MAX_VOL			1000 //mV
//#define GAIN_MIN_VOL			0 //mV

#define GAIN_MAX				(75) //dB
#define GAIN_MIN				(-21) //dB
#define GAIN_MAX_VOL			1000 //mV
#define GAIN_MIN_VOL			40 //mV

#define AGC_VAL					1600.0 // 最大2048
#define AGC_VAL_ERR				100 //
#define AUTO_AGC_MAX			1850 //2048*0.9
#define AUTO_AGC_MIN			512




typedef struct
{
	uint8_t  	GainCtrl; /* 0:手动增益; 1:自动增益; 2:AGC中 */
	uint8_t  	gainAdjustable; //增益可调项, 0:皆不可调; 1:皆可调; 2:纵波可调; 3:横波可调; 
	uint8_t  	gainActiveId; //增益激活项目, 0:无; 1:纵波; 2:横波;
	uint8_t	 	showGainBarForced; //强制显示增益bar
	uint8_t  	needAGC_L; /* 0:不需要 1:纵波需要AGC */
	uint8_t  	needAGC_T; /* 0:不需要 1:横波波需要AGC */
	uint8_t  	needAGC_O; /* 0:不需要 1:纵览需要AGC */
	uint8_t  	whichAGCing; //增益中的项目 纵波/横波/纵览波形 NULL

	float		gainVal;
	float		gainVal_T; //横波增益 Transverse wave, EMAT中横波强于纵波
	float		gainVal_L; //纵波增益 Longitudinal wave, EMAT中横波强于纵波

	int32_t  	srcDataMax_L; //最大数值 纵波
	int32_t  	srcDataMax_T; //最大数值 横波
	int32_t  	srcDataMax_O; //最大数值 纵览
	
//	#if !IS_GUI_V1
	int32_t  	waveMax_val;
	uint8_t		gainAdj_ing;
	uint8_t		isAGC;
//	#endif
}GAIN_T;
extern GAIN_T g_Gain;


void SGM5374_Init(void);

void Gain_SetGain(uint8_t ch, float gain);

#endif
