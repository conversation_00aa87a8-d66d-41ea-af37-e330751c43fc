/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : MOTION_RadialMenu.c
Purpose     : Shows how to create a radial menu with motion support
----------------------------------------------------------------------
*/

/**
  ******************************************************************************
  * @file    MOTION_RadialMenu.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Shows how to create a radial menu with motion support
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


/*********************************************************************
*
*       Includes
*
**********************************************************************
*/
#include "GUIDEMO.h"

#if (SHOW_GUIDEMO_RADIALMENU && GUI_WINSUPPORT)

/*********************************************************************
*
*       Types
*
**********************************************************************
*/
typedef struct {
  const GUI_BITMAP * pBitmap;
  const char       * pExplanation;
} BITMAP_ITEM;

typedef struct {
  int xPos;
  int yPos;
  int Index;
} ITEM_INFO;

typedef struct {
  int                 Pos;
  int                 NumItems;
  int                 xSizeItem, ySizeItem;
  int                 xSizeWindow, ySizeWindow;
  int                 rx, ry, mx, my;
  int                 FinalMove;
  const BITMAP_ITEM * pBitmapItem;
  ITEM_INFO         * pItemInfo;
} PARA;

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/
/*********************************************************************
*
*       _bmBrowser
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acBrowserRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA931000, 0xCE8C0D00, 0x9B880B00, 0x70880800,
        0x4B8A0B00, 0x2E911200, 0x24931600, 0x24941600, 0x2A921600, 0x408B1100, 0x62860F00, 0x89821200, 0xB67A1400, 0xE85D1300, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDA8D0C00, 0x8B890600, 0x2E901000, 0x059C2310, 0x00B1462B, 0x00C26648,
        0x00D08161, 0x00DE9A78, 0x00E3A481, 0x00E2A581, 0x00E09E7B, 0x00D48763, 0x00C76F4B, 0x00B8532F, 0x02A53515, 0x19962003, 0x66801300, 0xB96E1400, 0xF5000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCD8A0A00, 0x588A0300, 0x0E97160A, 0x00BD5E44, 0x00E2A37F, 0x00F7CEA8, 0x00FFE7CC, 0x00FFF9E8,
        0x00FFFFF4, 0x00FFFFF7, 0x00FFFFF9, 0x00FFFFFA, 0x00FFFFF5, 0x00FFFEF0, 0x00FFF8E5, 0x00FFE6C9, 0x00FACFA3, 0x00EAAD82, 0x00CC7852, 0x04A33313, 0x338B1800, 0x99731800, 0xEF000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF8910900, 0x7F890200, 0x0D920F06, 0x00C0634A, 0x00EEB78D, 0x00FFE6C0, 0x00FFF8E6, 0x00FFFDF6, 0x00FBFFFC, 0x00FEFFFF,
        0x00F4FBF8, 0x00F3FAF6, 0x00FBFDFC, 0x00FFFEFF, 0x00FDFEFD, 0x00FDFEFF, 0x00FDFFFF, 0x00FBFEFC, 0x00FDF9F1, 0x00FFF2DD, 0x00FFE2B5, 0x00F5BF8D, 0x00D2825C, 0x01A53313, 0x43811700, 0xC5491200, 0xF5000000, 0xFE000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC7890600, 0x3C8A0200, 0x00AF4332, 0x00E9A981, 0x00FFE2B6, 0x00FCEFD8, 0x00FAF9F3, 0x00FFFFFF, 0x00F3F7F3, 0x00F4F8F3, 0x00EEF5EE,
        0x00EFF5EF, 0x00F9FAF7, 0x00F6F8F4, 0x00FFFEFC, 0x00FFFDFB, 0x00F9FAF5, 0x00F4F8F2, 0x00ECF4ED, 0x00ECF5EE, 0x00EFF7F4, 0x00F4F5EE, 0x00FEE9CD, 0x00FFDBA8, 0x00F3B883, 0x00C36D48, 0x15942403, 0x856B1700, 0xEB000000, 0xFB000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB4890300, 0x14900B07, 0x00CC765C, 0x00FFD39E, 0x00FDE2B9, 0x00ECF0E3, 0x00E4F2E9, 0x00ECF3EA, 0x00EBF2E9, 0x00EDF3EB, 0x00FFFDFA, 0x00F6F8F3,
        0x00FFFEFC, 0x00FFFEFC, 0x00F6F8F4, 0x00F5F9F2, 0x00E9F2E8, 0x00E4EFE4, 0x00E4EFE4, 0x00E4EFE4, 0x00E4EFE4, 0x00E3EFE3, 0x00E3F0E5, 0x00E5F3EB, 0x00F0EFE2, 0x00FDD8A8, 0x00FFCE8F, 0x00E09D73, 0x01A53613, 0x62701800, 0xE1000000, 0xFA000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB8880200, 0x09910E0A, 0x00D98C6D, 0x00FFD79A, 0x00F1DAB3, 0x00DDEDE0, 0x00DBEBDE, 0x00DCEADC, 0x00DBEADB, 0x00DAE9DA, 0x00E1ECE0, 0x00ECF2E9, 0x00F0F5ED,
        0x00F1F6EF, 0x00F5F8F3, 0x00E8F1E7, 0x00DCEADC, 0x00DBEADB, 0x00DCEADC, 0x00DCEADC, 0x00DCEBDC, 0x00DCEBDC, 0x00DCEBDC, 0x00DCEADC, 0x00DCEADC, 0x00DBEBDF, 0x00DFEEE2, 0x00F0D2A5, 0x00FDC77F, 0x00EFAE80, 0x00AA3F1A, 0x5C6C1800, 0xDE000000,
        0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB4880100, 0x0B900C08, 0x00E8A17E, 0x00F8CC89, 0x00D9D5AE, 0x00D3E8D9, 0x00D4E6D5, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D0E4D1, 0x00D3E6D4, 0x00D5E6D5,
        0x00D4E6D4, 0x00E6F0E5, 0x00DAEADA, 0x00D2E5D3, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D4E6D4, 0x00D3E6D5, 0x00D4E9DB, 0x00BFC99D, 0x00D5B263, 0x00FCBC8B, 0x00AD411B, 0x56701900,
        0xDD000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC9870200, 0x128E0A07, 0x00DC8D6F, 0x00E7C177, 0x00B6C290, 0x00C8E3D1, 0x00CBE1CC, 0x00CBE1CB, 0x00C9E0CA, 0x00C7DFC9, 0x00D0E3CF, 0x00ECF0E3, 0x00DDEADA, 0x00F6F6EF, 0x00FAFAF5,
        0x00FAFBF8, 0x00F3F8F1, 0x00CEE2CE, 0x00CBE1CB, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CC, 0x00CCE1CB, 0x00CCE1CD, 0x00CCE6D4, 0x008DB47D, 0x00AAA24A, 0x00F4B384, 0x00A93C16,
        0x6A5E1700, 0xE0000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF78E0300, 0x36870000, 0x00D0775F, 0x00F4C981, 0x008FAB69, 0x00BBDCC4, 0x00C4DEC4, 0x00C1DCC2, 0x00C0DBC1, 0x00D1E2CC, 0x00E5EAD9, 0x00F0EFE1, 0x00FFF8F0, 0x00FFF9F2, 0x00FFFAF5, 0x00FFFBF8,
        0x00FFFDFA, 0x00FEFDFB, 0x00D7E7D5, 0x00C0DBC1, 0x00C3DDC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C2DCC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C3DDC3, 0x00C5DEC4, 0x00C3E0CA, 0x00579952, 0x00B6A954, 0x00EFAA7E,
        0x0A982C08, 0x97330E00, 0xE9000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0x7D850000, 0x00AF4135, 0x00FFCC90, 0x008A9541, 0x00A4CCAB, 0x00BED8BE, 0x00B9D4B9, 0x00C2D9BE, 0x00E1E4D0, 0x00FDF1E2, 0x00FFF4E8, 0x00FFF5EB, 0x00FFF5EB, 0x00FFF7EE, 0x00FFF8F1, 0x00FFF9F4,
        0x00FFFBF5, 0x00FEFBF7, 0x00E0E9DA, 0x00BAD5B9, 0x00BBD5BA, 0x00BBD5BA, 0x00BCD6BB, 0x00BCD6BB, 0x00BCD6BB, 0x00BCD6BB, 0x00BAD5BA, 0x00BED7BC, 0x00BCD6BB, 0x00BCD6BB, 0x00BCD6BB, 0x00BCD6BB, 0x00C2D9C0, 0x00ACCFB0, 0x0033761A, 0x00E0BB74,
        0x00D57E54, 0x257E1C00, 0xC2000000, 0xF4000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xD0880000, 0x0B8E0A08, 0x00F5AB80, 0x00A79942, 0x0079A777, 0x00BBD3BA, 0x00C7D4BA, 0x00E4E1CA, 0x00F8EAD6, 0x00FFEFDE, 0x00FFF0DF, 0x00FFF0E1, 0x00FFF2E4, 0x00FFF3E7, 0x00FFF5EA, 0x00FFF6EC, 0x00FFF7EF,
        0x00FFF9F1, 0x00F2F0E5, 0x00BAD0B5, 0x00B4CDB0, 0x00BDD2B7, 0x00B9D0B5, 0x00B5CDB1, 0x00B5CDB1, 0x00B5CDB1, 0x00B5CEB2, 0x00D8DDC4, 0x00CCD8BE, 0x00B4CEB1, 0x00B7CFB3, 0x00B7CFB3, 0x00B7CFB3, 0x00B7CEB3, 0x00C0D4BC, 0x0078A879, 0x00577519,
        0x00FABE85, 0x00B0481D, 0x5A5B1700, 0xD8000000, 0xFB000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0x55840000, 0x00C56352, 0x00EABE77, 0x00215506, 0x006C9768, 0x00CAD5BC, 0x00FCE8D0, 0x00FFEBD4, 0x00FFEBD5, 0x00FFEBD7, 0x00FFEDDA, 0x00FFEEDD, 0x00FFF0E0, 0x00FFF1E2, 0x00FFF2E5, 0x00FFF3E7, 0x00FFF5EA,
        0x00FFF9EF, 0x00D3DAC5, 0x00ACC3A6, 0x00CCD6BF, 0x00F7F0E2, 0x00EFEAD9, 0x00BFCEB4, 0x00BDCDB3, 0x00BECEB3, 0x00E2DFC8, 0x00DEDCC3, 0x00B5C8AC, 0x00B1C7AB, 0x00B2C7AB, 0x00B2C7AB, 0x00B2C7AB, 0x00B3C8AD, 0x00BCD0B7, 0x007EA073, 0x000D4E02,
        0x00BCA65B, 0x00E99D72, 0x118C2200, 0xA90C0300, 0xEC000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xDC870000, 0x0A920F0C, 0x00FAB689, 0x00918535, 0x00586519, 0x0033560C, 0x00E5C392, 0x00FFE9CF, 0x00FFE8D0, 0x00FFE8CF, 0x00FFE9D2, 0x00FFEAD5, 0x00FFECD7, 0x00FFEDDA, 0x00FFEEDD, 0x00FFF0DF, 0x00FFF3E4, 0x00FFF2E4,
        0x00DEDCCA, 0x00B1C1A6, 0x00ABBDA2, 0x00BCC8AF, 0x00C1CBB2, 0x00C3CCB3, 0x00C0CAB0, 0x00ECE2CD, 0x00EDE2CD, 0x00D8D5BD, 0x00C3CAAF, 0x00ACBDA2, 0x00ACBEA3, 0x00ADBDA3, 0x00ADBEA3, 0x00B1C1A8, 0x00B1C1A8, 0x00759164, 0x001F4D07, 0x000D4200,
        0x00606A1B, 0x00FFC88F, 0x00B55024, 0x60451200, 0xD4000000, 0xFB000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x8A820000, 0x00C15D4D, 0x00ECBB76, 0x0073671A, 0x00987A2F, 0x00A08033, 0x00FFB360, 0x00FFC589, 0x00FFDFBD, 0x00FFE6CD, 0x00FFE7CE, 0x00FFE8CF, 0x00FFE9D1, 0x00FFEAD4, 0x00FFEBD6, 0x00FFF0DC, 0x00F3E7D3, 0x00C3C7AF,
        0x00A5B297, 0x00A7B49A, 0x00A9B59B, 0x00A7B499, 0x00A6B398, 0x00A5B398, 0x00A9B59B, 0x00AEB89E, 0x00ADB79D, 0x00A5B399, 0x00A5B399, 0x00A9B59B, 0x00A9B59B, 0x00ACB89E, 0x00AFBBA2, 0x009EAB8E, 0x0058703E, 0x001C3D00, 0x001F4000, 0x00204100,
        0x00244100, 0x00D8AF6C, 0x00E3996D, 0x23771F00, 0xB5000000, 0xF1000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xF88B0000, 0x2A890202, 0x00EDA480, 0x00947E39, 0x00937228, 0x00826A24, 0x00B9883D, 0x00FFB662, 0x00FFB160, 0x00FFB86F, 0x00FFCB94, 0x00FFDEBC, 0x00FFE6CB, 0x00FFE7CE, 0x00FFE7CE, 0x00FFE8D0, 0x00DDD1B8, 0x00ACB098, 0x00A0A88F,
        0x00A4AB92, 0x00A5AB92, 0x00A5AB92, 0x00A5AB92, 0x00A5AB92, 0x00A5AB92, 0x00A4AB92, 0x00A3AA92, 0x00A3AA92, 0x00A5AB93, 0x00A6AC94, 0x00AAB099, 0x00AFB39C, 0x009AA185, 0x00646E45, 0x0034430E, 0x00243400, 0x00293900, 0x002A3A00, 0x002A3A00,
        0x001D3200, 0x0081702E, 0x00FDC18E, 0x03A3370B, 0x7F290C00, 0xDE000000, 0xFE000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xCD850000, 0x02991815, 0x00F3B882, 0x0051480D, 0x00A97932, 0x00FCB05A, 0x00F3A753, 0x00FFB05D, 0x00FFB261, 0x00FFB364, 0x00FFB466, 0x00FFBC77, 0x00FFCA91, 0x00FFD8AF, 0x00FFE3C4, 0x00FBE2C8, 0x00AEAD96, 0x00A3A58E, 0x00A5A690,
        0x00A4A58E, 0x00A4A58E, 0x00A3A58D, 0x00A3A58D, 0x00A4A58E, 0x00A4A58E, 0x00A5A68F, 0x00A7A891, 0x00A8A993, 0x00A5A58E, 0x009F9F88, 0x00838466, 0x006B673C, 0x00544D18, 0x00262A00, 0x002E3100, 0x00313300, 0x00313300, 0x00313300, 0x00313300,
        0x002B2F00, 0x0051470F, 0x00FAC389, 0x00BA592B, 0x4C4D1400, 0xC6000000, 0xF8000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x9B7F0000, 0x00B03F34, 0x00EBB575, 0x00484308, 0x00CD8F41, 0x00FFB85F, 0x00FFB05A, 0x00FFAE5A, 0x00FFB05D, 0x00FFB262, 0x00FFB466, 0x00FFB569, 0x00FFB66B, 0x00FFBA73, 0x00FFC384, 0x00FBCA94, 0x00878157, 0x006D7451, 0x00828664,
        0x008A8D6E, 0x008F9275, 0x00919577, 0x00919577, 0x008F9375, 0x008A8E6E, 0x00838665, 0x00787B57, 0x00696E46, 0x00595D31, 0x00434716, 0x00303500, 0x00292E00, 0x00584D11, 0x00424008, 0x002F3500, 0x00323601, 0x002F3500, 0x002F3500, 0x00333702,
        0x00303501, 0x00343701, 0x00D8AE73, 0x00D27C4E, 0x2E641900, 0xB0000000, 0xEF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x6F7E0000, 0x00BD5C4A, 0x00FFCD82, 0x00896D28, 0x006A5B1B, 0x00B6873A, 0x00EAA14F, 0x00FFB35C, 0x00FFB25D, 0x00FFB05F, 0x00FFB262, 0x00FFB466, 0x00FFB66A, 0x00FFB76D, 0x00FFB970, 0x00FFBE76, 0x00C29552, 0x00383F06, 0x002C3901,
        0x00354007, 0x00364109, 0x0037420A, 0x00374209, 0x00364109, 0x00354007, 0x00333D04, 0x002F3A00, 0x002B3600, 0x002C3700, 0x002F3A00, 0x00313C02, 0x002E3B02, 0x0048490D, 0x00856B25, 0x002E3A02, 0x00313D03, 0x003D4308, 0x0047490D, 0x002E3B02,
        0x002D3A01, 0x00263400, 0x009F8A4F, 0x00E89D6F, 0x1D792000, 0x9E000000, 0xE7000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x48800000, 0x00D37A60, 0x00E4B46A, 0x00474D0D, 0x00243A00, 0x002B3D02, 0x00424A0C, 0x0090742B, 0x00D99B4A, 0x00FFB15D, 0x00FFB361, 0x00FFB161, 0x00FFB365, 0x00FFB568, 0x00FFB66C, 0x00FFB86F, 0x00FFC279, 0x00B9914D, 0x00314105,
        0x00253B00, 0x00283C00, 0x00263B00, 0x00263B00, 0x00304003, 0x00314104, 0x00314104, 0x00314105, 0x00324205, 0x00324206, 0x00324206, 0x00324206, 0x00314205, 0x002E3F03, 0x0097772D, 0x00575513, 0x00615B18, 0x00C19145, 0x00756723, 0x00243B00,
        0x00495012, 0x00283B00, 0x00726F32, 0x00EEB183, 0x13892B03, 0x91000000, 0xE0000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x2C870101, 0x00DF9272, 0x007D7A35, 0x00254100, 0x00324908, 0x00314807, 0x002D4606, 0x00274203, 0x00374A09, 0x0069651D, 0x00D29949, 0x00FFB360, 0x00FFB160, 0x00FFB363, 0x00FFB365, 0x00FFB568, 0x00FFB66B, 0x00FFBD71, 0x00C2974F,
        0x0062621E, 0x0061611E, 0x00726B27, 0x0083732F, 0x00364A0A, 0x00314807, 0x00324808, 0x00324808, 0x00324808, 0x00324808, 0x00324808, 0x00324808, 0x00324808, 0x00304706, 0x003B4D0B, 0x003C4D0B, 0x00364A09, 0x00927B32, 0x0069641F, 0x00967F3A,
        0x00F0B36D, 0x00505A17, 0x00707232, 0x00F7C395, 0x0C96340A, 0x86000000, 0xDB000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x25890102, 0x00E09876, 0x006D752F, 0x00294903, 0x00324E0A, 0x00324E0A, 0x00324E0A, 0x00324D09, 0x002F4C08, 0x001D4301, 0x00766D23, 0x00FFB35E, 0x00FFB25E, 0x00FFB05E, 0x00FFB160, 0x00FFB262, 0x00FFB364, 0x00FFB466, 0x00FFB76A,
        0x00FFB467, 0x00FDB466, 0x00FFB86B, 0x00FCB365, 0x004A5913, 0x002C4B07, 0x00324E0A, 0x00324E0A, 0x00324E0A, 0x00324E0A, 0x00324E0A, 0x00324E0A, 0x00324E0A, 0x00334E0A, 0x002A4A05, 0x003F540F, 0x00907A30, 0x00726D26, 0x00D5A057, 0x00FABB73,
        0x00C29C57, 0x008F8341, 0x00B49C5E, 0x00F1C597, 0x0999360C, 0x80000000, 0xD7000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x24890102, 0x00E09978, 0x00707B33, 0x00294F06, 0x0032540C, 0x0032540C, 0x0032540C, 0x0032540C, 0x0032540C, 0x0031530B, 0x0030520A, 0x007C7426, 0x00E7A551, 0x00FFB05C, 0x00FFAF5C, 0x00FFAF5D, 0x00FFB05F, 0x00FFB160, 0x00FFB261,
        0x00FFB362, 0x00FFB363, 0x00FFB363, 0x00FFB564, 0x0081782B, 0x00274E07, 0x0032540C, 0x0032540C, 0x0032540C, 0x0032540C, 0x0032540C, 0x0032540C, 0x0032540C, 0x002E520A, 0x0036550D, 0x00C89849, 0x00F1AD5E, 0x00D1A157, 0x00897E35, 0x00938841,
        0x006F732B, 0x009D904F, 0x00EABF86, 0x00EEC598, 0x0999360C, 0x7D000000, 0xD6000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x2A880100, 0x00DD9375, 0x00728238, 0x00295407, 0x0032590D, 0x0032590D, 0x0032590D, 0x0032590D, 0x0032590D, 0x0032590D, 0x0031580C, 0x00235207, 0x00496214, 0x00E7A44F, 0x00FFAF5A, 0x00FFAE5A, 0x00FFAE5B, 0x00FFAE5B, 0x00FFAF5C,
        0x00FFB05C, 0x00FFAF5C, 0x00FFAF5C, 0x00FFB461, 0x00B18E3D, 0x002A550A, 0x0032590D, 0x0032590D, 0x0032590D, 0x0032590D, 0x0032590D, 0x0032590D, 0x0030590C, 0x0031580C, 0x00B99344, 0x00FFB869, 0x00FFBA6E, 0x00F7B76F, 0x007A7B31, 0x004B661B,
        0x006A762E, 0x00D9B57B, 0x00FDD29E, 0x00F5C79B, 0x0B953409, 0x7D000000, 0xD6000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x41800000, 0x00D47E65, 0x007F9146, 0x00285907, 0x00325E0E, 0x00325E0E, 0x00325E0E, 0x00325E0E, 0x00325E0E, 0x00325E0E, 0x00315E0E, 0x002D5D0D, 0x006D7523, 0x00EDA751, 0x00FFAD57, 0x00FFAC57, 0x00FFAC57, 0x00FFAD57, 0x00FFAD58,
        0x00FFAD58, 0x00FFAD58, 0x00FFAD58, 0x00FFB05B, 0x00D69D49, 0x00366010, 0x002F5E0D, 0x00325E0E, 0x00325E0E, 0x00325E0E, 0x00325E0E, 0x00325F0E, 0x00255909, 0x00697524, 0x00FFB768, 0x00FFB86D, 0x00F1B46B, 0x00968C42, 0x00EAB674, 0x00DAB172,
        0x00E7BC81, 0x00FFD29D, 0x00FFD9AA, 0x00EDB58A, 0x10892B02, 0x80000000, 0xD7000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x62780000, 0x00CB6654, 0x00909E56, 0x00255E08, 0x00326511, 0x00326411, 0x00326411, 0x00326411, 0x00326411, 0x00326410, 0x0029600D, 0x00898430, 0x00FFB25D, 0x00FFAF5A, 0x00FFAC57, 0x00FFAC56, 0x00FFAC56, 0x00FFAC56, 0x00FFAC56,
        0x00FFAC56, 0x00FFAC56, 0x00FFAC56, 0x00FFAE58, 0x00E1A14B, 0x00396713, 0x002F6310, 0x00326411, 0x00326411, 0x00326411, 0x00316410, 0x0029610D, 0x00386613, 0x00BD9B4C, 0x00FFBA70, 0x00FFBB74, 0x00EDB56F, 0x00A99A50, 0x00FAC285, 0x00F5C389,
        0x00FFD19E, 0x00FFD7A8, 0x00FFE1B6, 0x00DE9C70, 0x18792000, 0x86000000, 0xDA000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0x8A740000, 0x00BB473C, 0x00AFAB6D, 0x002A660D, 0x00326912, 0x00326A13, 0x00326A13, 0x00326A13, 0x00316912, 0x002E6811, 0x00326912, 0x00D6A451, 0x00FFB463, 0x00FFAF5D, 0x00FFAF5B, 0x00FFAD59, 0x00FFAD58, 0x00FFAD58, 0x00FFAD57,
        0x00FFAC57, 0x00FFAC57, 0x00FFAC57, 0x00FFAF5A, 0x00E6A44F, 0x00386C14, 0x002F6912, 0x00326A13, 0x00326A13, 0x00316912, 0x00306911, 0x0072812D, 0x00AE9847, 0x00FFBA71, 0x00FFBB74, 0x00FFBF7D, 0x00DCB16D, 0x0043711B, 0x007C8C3E, 0x004F7724,
        0x00BFB174, 0x00C2B77E, 0x00FBE0B7, 0x00CE7C4F, 0x24631800, 0x8F000000, 0xDF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xBA6D0000, 0x009F1C18, 0x00E0BE8E, 0x003D761C, 0x002F6D13, 0x00326F14, 0x00326F14, 0x00326F13, 0x002E6D12, 0x002B6C10, 0x00527A21, 0x00FAB467, 0x00FFB467, 0x00FFB263, 0x00FFB160, 0x00FFB05F, 0x00FFAF5E, 0x00FFB05D, 0x00FFAF5C,
        0x00FFAF5C, 0x00FFAF5C, 0x00FFAF5C, 0x00FFB05D, 0x00FAAE5C, 0x00607E24, 0x00296C11, 0x00326F14, 0x00326F14, 0x002B6D11, 0x0068822B, 0x00878D38, 0x00C8A559, 0x00FFBE79, 0x00FFBF7C, 0x00FFC284, 0x00F9C285, 0x004A7921, 0x0023680B, 0x002E6D12,
        0x002C6C0F, 0x00658C3F, 0x00FEDDB8, 0x00BC5C2D, 0x364A1300, 0x9C000000, 0xE6000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xEB450000, 0x1A880403, 0x00EAAA8A, 0x006B9642, 0x0028700F, 0x00327516, 0x00327515, 0x00287111, 0x007E903C, 0x00C1AA5F, 0x00D2AB60, 0x00FFBA72, 0x00FFB76D, 0x00FFB66A, 0x00FFB467, 0x00FFB365, 0x00FFB364, 0x00FFB263, 0x00FFB261,
        0x00FFB161, 0x00FFB161, 0x00FFB161, 0x00FFB262, 0x00FFB567, 0x00C2A04D, 0x002F7414, 0x00317415, 0x00317415, 0x00357617, 0x0069872D, 0x00969745, 0x00FFC07E, 0x00FFC07F, 0x00FFC486, 0x00FFC98E, 0x00F6C58C, 0x004A7F24, 0x002D7313, 0x00327515,
        0x00246D0A, 0x0099AE6F, 0x00FFD3AF, 0x02A63A0B, 0x50240A00, 0xAD000000, 0xEF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFC000000, 0x6A720000, 0x00CE6B5A, 0x00BFC485, 0x002B7713, 0x00317A16, 0x002A7713, 0x005A892F, 0x00F6C285, 0x00FFC889, 0x00FFC282, 0x00FFBD7A, 0x00FFBC76, 0x00FFBA73, 0x00FFB970, 0x00FFB86E, 0x00FFB76C, 0x00FFB66B, 0x00FFB66A,
        0x00FFB669, 0x00FFB569, 0x00FFB669, 0x00FFB66A, 0x00FFB76C, 0x00FFB96F, 0x00668A2D, 0x0020750F, 0x0020750F, 0x005F882C, 0x00C0AA5E, 0x00F8BE7C, 0x00FFC385, 0x00FFC78A, 0x00EABF82, 0x00D6BB7D, 0x00FBCD99, 0x003F7F1F, 0x0024750F, 0x002F7814,
        0x003A7F1F, 0x00E9DFB7, 0x00E5A178, 0x14792000, 0x70010000, 0xC3000000, 0xF8000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xBD5F0000, 0x04981511, 0x00F5C49F, 0x00669B43, 0x00277B12, 0x00318019, 0x00CAB878, 0x00FFCF9A, 0x00FFC88D, 0x00FFC588, 0x00FFC284, 0x00FFC080, 0x00FFBF7D, 0x00FFBE7A, 0x00FFBC77, 0x00FFBB76, 0x00FFBB74, 0x00FFBA73,
        0x00FFBA73, 0x00FFBA73, 0x00FFBA73, 0x00FFBA73, 0x00FFBB74, 0x00FFBE7A, 0x00AFA553, 0x00608E30, 0x00839A42, 0x00DEB771, 0x00FFC88A, 0x00FFC58A, 0x00FFC88E, 0x00FFCF9A, 0x00A8AC62, 0x00B3B370, 0x00FFD7AC, 0x00A1B16F, 0x00639640, 0x001D7607,
        0x0080AA61, 0x00FFE4C5, 0x00B95728, 0x373F1100, 0x8D000000, 0xDB000000, 0xFE000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xF5000000, 0x347C0000, 0x00D47663, 0x00C5CE90, 0x00228011, 0x005C9638, 0x00FFD4A6, 0x00FFD19F, 0x00FFCD98, 0x00FFCB94, 0x00FFC88F, 0x00FFC78B, 0x00FFC488, 0x00FFC384, 0x00FFC182, 0x00FFC080, 0x00FFC07E, 0x00FFBF7E,
        0x00FFBF7C, 0x00FFBF7C, 0x00FFBF7C, 0x00FFBF7D, 0x00FFC07E, 0x00FFC080, 0x00FFC182, 0x00FFC385, 0x00FFC98C, 0x00FFC98F, 0x00FFC98F, 0x00FFCC94, 0x00FFCF9B, 0x00F6CE9A, 0x00769F49, 0x00E8CE9C, 0x00FFDCB7, 0x00FFE5C7, 0x00F1DCB7, 0x0099B778,
        0x00EDE9C9, 0x00E7A880, 0x098F2500, 0x5E0B0300, 0xB0000000, 0xEF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFE000000, 0xA1630000, 0x0296130E, 0x00F6C1A2, 0x0077AD58, 0x00409026, 0x00F3D5A7, 0x00FFD7AD, 0x00FFD3A4, 0x00FFD09F, 0x00FFCE9A, 0x00FFCC96, 0x00FFCA92, 0x00FFC98F, 0x00FFC78C, 0x00FFC78A, 0x00FFC589, 0x00FFC488,
        0x00FFC487, 0x00FFC487, 0x00FFC487, 0x00FFC488, 0x00FFC589, 0x00FFC58B, 0x00FFC88D, 0x00FFCA90, 0x00FFCB93, 0x00FFCC96, 0x00FFCE9A, 0x00FFD1A0, 0x00FFD4A6, 0x00FAD4A7, 0x00D2C890, 0x00FEDCB7, 0x00FFE0C0, 0x00FFE4C8, 0x00FFEAD4, 0x00FFF8EC,
        0x00FFE0C3, 0x00B55021, 0x29541500, 0x7F000000, 0xD0000000, 0xFB000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xF0000000, 0x47720000, 0x00BF5646, 0x00F5E5BA, 0x004B9E37, 0x0093B569, 0x00FFE0BF, 0x00FFD9B1, 0x00FFD6AC, 0x00FFD5A7, 0x00FFD3A3, 0x00FFD09F, 0x00FFCF9C, 0x00FFCD99, 0x00FFCC97, 0x00FFCB96, 0x00FFCB94,
        0x00FFCB94, 0x00FFCB94, 0x00FFCB94, 0x00FFCB94, 0x00FFCB96, 0x00FFCD97, 0x00FFCD99, 0x00FFCF9C, 0x00FFD09F, 0x00FFD3A3, 0x00FFD4A7, 0x00FFD7AD, 0x00FFDAB2, 0x00FFDDB8, 0x00FFE3C3, 0x00FFE4C7, 0x00FFE7CE, 0x00FFEBD6, 0x00FFF1E3, 0x00FFF8E3,
        0x00D4895E, 0x0F801D00, 0x5E050100, 0xA7000000, 0xE9000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFC000000, 0xCB3B0100, 0x15850502, 0x00E2967D, 0x00DBE2AF, 0x004AA137, 0x00E0D7AA, 0x00FFE1C2, 0x00FFDDB8, 0x00FFDAB3, 0x00FFD9B0, 0x00FFD7AB, 0x00FFD5A8, 0x00FFD4A5, 0x00FFD2A3, 0x00FFD1A2, 0x00FFD1A0,
        0x00FFD1A0, 0x00FFD19F, 0x00FFD1A0, 0x00FFD1A0, 0x00FFD2A2, 0x00FFD2A3, 0x00FFD4A6, 0x00FFD5A8, 0x00FFD6AC, 0x00FFD9B0, 0x00FFDAB4, 0x00FFDDB9, 0x00FFE0BF, 0x00FFE2C4, 0x00FFE6CC, 0x00FFE9D3, 0x00FFEDDB, 0x00FFF2E6, 0x00FFFDF0, 0x00EEBE98,
        0x03A1340B, 0x402A0B00, 0x89000000, 0xD3000000, 0xFA000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF6000000, 0x8B5D0000, 0x0295140E, 0x00EBAD90, 0x00DDE6BC, 0x00F2E6CA, 0x00FFE7CE, 0x00FFE4C7, 0x00FFE1C2, 0x00FFDFBD, 0x00FFDDBA, 0x00FFDCB6, 0x00FFDAB3, 0x00FFD9B1, 0x00FFD8AF, 0x00FFD8AE,
        0x00FFD7AD, 0x00FFD7AD, 0x00FFD8AD, 0x00FFD8AE, 0x00FFD8B0, 0x00FFD9B1, 0x00FFDAB4, 0x00FFDCB6, 0x00FFDDBA, 0x00FFDFBE, 0x00FFE1C2, 0x00FFE4C8, 0x00FFE7CE, 0x00FFE9D4, 0x00FFEDDB, 0x00FFF0E1, 0x00FFF5ED, 0x00FFFFF7, 0x00F4CFAD, 0x00B0471C,
        0x28531300, 0x71000000, 0xBB000000, 0xF1000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xEC000000, 0x65630000, 0x009A1B12, 0x00F4C0A3, 0x00FFF9E6, 0x00FFEFE0, 0x00FFEBD7, 0x00FFE8D2, 0x00FFE7CE, 0x00FFE4CA, 0x00FFE3C5, 0x00FFE1C2, 0x00FFE1C0, 0x00FFE0BE, 0x00FFDFBD,
        0x00FFDEBC, 0x00FFDEBC, 0x00FFDEBC, 0x00FFDFBD, 0x00FFDFBE, 0x00FFE1C0, 0x00FFE1C3, 0x00FFE4C5, 0x00FFE5CA, 0x00FFE7CE, 0x00FFE9D2, 0x00FFECD7, 0x00FFEEDD, 0x00FFF1E3, 0x00FFF5EA, 0x00FFFAF4, 0x00FFFFFD, 0x00F9DCBE, 0x00B44E22, 0x1B681600,
        0x63010000, 0xA6000000, 0xE4000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xE2000000, 0x5E600000, 0x009C1D13, 0x00E8B292, 0x00FFFDEA, 0x00FFF6EC, 0x00FFF0E2, 0x00FFEFDD, 0x00FFECD9, 0x00FFEAD6, 0x00FFE9D4, 0x00FFE8D1, 0x00FFE7CF, 0x00FFE7CD,
        0x00FFE6CD, 0x00FFE6CD, 0x00FFE6CD, 0x00FFE7CE, 0x00FFE7CF, 0x00FFE8D1, 0x00FFE9D3, 0x00FFEBD6, 0x00FFECDA, 0x00FFEEDE, 0x00FFF1E2, 0x00FFF4E8, 0x00FFF6ED, 0x00FFF9F4, 0x00FFFEFE, 0x00FFFFFF, 0x00F4D1B1, 0x00B44D23, 0x1C661600, 0x5E040100,
        0x9A000000, 0xDA000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xDF000000, 0x59610000, 0x00981810, 0x00DE9D7E, 0x00FFF8E1, 0x00FFFFF9, 0x00FFF8F0, 0x00FFF5EA, 0x00FFF3E9, 0x00FFF3E7, 0x00FFF0E3, 0x00FFEFE1, 0x00FFEFE1,
        0x00FFEFDE, 0x00FFEEDC, 0x00FFEEDC, 0x00FFEFE0, 0x00FFEFE0, 0x00FFF0E0, 0x00FFF1E2, 0x00FFF2E5, 0x00FFF4E9, 0x00FFF6ED, 0x00FFF8F2, 0x00FFFBF8, 0x00FFFFFE, 0x00FFFFFF, 0x00FFFFF4, 0x00EDBE9B, 0x00AF441C, 0x1B671500, 0x5E030100, 0x98000000,
        0xD6000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xDE000000, 0x6B540200, 0x0D870A02, 0x00C0634C, 0x00F5D5B9, 0x00FFFFF5, 0x00F9FEF6, 0x00E9F5E6, 0x009FD994, 0x00CAE6BC, 0x00F9F6ED, 0x00F0F1DF,
        0x00FBF5E8, 0x00FFF9F7, 0x00FFFAF9, 0x00FBF5EA, 0x00FFF7F0, 0x00FFFCFD, 0x00FFFDFE, 0x00FFFDFC, 0x00FFFDFD, 0x00FFFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFC, 0x00FBE5CE, 0x00D2865F, 0x039E2E0B, 0x27531200, 0x63000000, 0x9A000000, 0xD7000000,
        0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA000000, 0xE0000000, 0x982E0200, 0x286E0000, 0x00A02514, 0x00D68C71, 0x00E1D8AD, 0x0095DE86, 0x003FC13C, 0x0043C343, 0x004EC54C, 0x0037BB33,
        0x0056C551, 0x009FDF9B, 0x00A6E2A3, 0x0055C450, 0x0082D67F, 0x00A0E09E, 0x00B0E5AF, 0x00DBF5DD, 0x00E4FAE9, 0x00FBFFFE, 0x00FFFFFD, 0x00FDEAD3, 0x00E1A47F, 0x00B24923, 0x107B1500, 0x3E2D0B00, 0x71000000, 0xA5000000, 0xDA000000, 0xF8000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xE9000000, 0xC4000000, 0x5D510400, 0x167A0300, 0x00A83020, 0x00D5876D, 0x00D7C294, 0x0099CF77, 0x006DD05C, 0x004FCF4A,
        0x0038C535, 0x0031C12E, 0x002DBF2A, 0x002DBF2B, 0x0031C12E, 0x0031C22D, 0x003CC938, 0x0060CF54, 0x008BCE6E, 0x00E2CFA6, 0x00DC9B78, 0x00B44D28, 0x0A881900, 0x28531000, 0x5E060100, 0x89000000, 0xBA000000, 0xE4000000, 0xFA000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF5000000, 0xD9000000, 0xAC090100, 0x623E0400, 0x276A0500, 0x0598190B, 0x00B73F2F, 0x00C4674D, 0x00CE8B69,
        0x00D3AC7F, 0x00CDC189, 0x00C9CA8A, 0x00C5CA88, 0x00C5C383, 0x00C4AD74, 0x00C79264, 0x00BE7149, 0x00B84E2F, 0x02A02B0D, 0x15731300, 0x363F0B00, 0x5E0D0300, 0x7F000000, 0xA7000000, 0xD3000000, 0xF0000000, 0xFD000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xED000000, 0xD5000000, 0xB6000000, 0x81240400, 0x4E440500, 0x305B0500, 0x1E6F0A00,
        0x13811303, 0x0C8F1C0B, 0x0995200E, 0x0996210E, 0x0B93210C, 0x10861A06, 0x18741200, 0x24600C00, 0x36480B00, 0x4F240700, 0x71000000, 0x8D000000, 0xB0000000, 0xCF000000, 0xE9000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xF2000000, 0xDF000000, 0xC8000000, 0xB1000000, 0x9F000000,
        0x92000000, 0x87000000, 0x80000000, 0x7D000000, 0x7D000000, 0x80000000, 0x86000000, 0x8F000000, 0x9C000000, 0xAD000000, 0xC3000000, 0xDB000000, 0xEF000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF8000000, 0xF0000000, 0xE7000000,
        0xE0000000, 0xDB000000, 0xD8000000, 0xD6000000, 0xD6000000, 0xD7000000, 0xDA000000, 0xDF000000, 0xE6000000, 0xEE000000, 0xF7000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000
};

GUI_CONST_STORAGE GUI_BITMAP _bmBrowserRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acBrowserRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmClock
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acClockRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF59D1D00, 0xC9981900, 0x939A2500, 0x62A13108,
        0x38AA3E10, 0x1DB44F20, 0x0FBC5B29, 0x0DBD5F2A, 0x1BB85821, 0x32B14D13, 0x56A84508, 0x849D3D00, 0xB58E3400, 0xE96C2A00, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD8920F00, 0x6A971600, 0x24A93B1C, 0x03CB7E60, 0x00E2AA8C, 0x00F1CAAB,
        0x00FADEC2, 0x00FCE3C8, 0x00FDE5CB, 0x00FEE5CB, 0x00FDE3C9, 0x00FBDFC3, 0x00F4D1AF, 0x00E9B790, 0x02D99667, 0x17BB6223, 0x52A44300, 0xB4813300, 0xF3000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCD8A0600, 0x62951606, 0x0EB95A43, 0x00DFA286, 0x00F9D9BC, 0x00FFF5DE, 0x00FFF9E7, 0x00FFF6E5,
        0x00FFF6E4, 0x00FFF7E4, 0x00FFF7E4, 0x00FFF7E5, 0x00FFF7E4, 0x00FFF6E4, 0x00FFF6E5, 0x00FFF7E6, 0x00FFF4E0, 0x00FCE0C2, 0x00EAB78C, 0x06D1854C, 0x40A74D0A, 0xAB7F3300, 0xF2000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF98D0100, 0x8E8A0200, 0x0DAB3E30, 0x00E4AD92, 0x00FFEACC, 0x00FFF3DC, 0x00FFF6E4, 0x00FFF6E0, 0x00FDDFC4, 0x00F5CBAB,
        0x00ECB99C, 0x00E3A88E, 0x00E2A48B, 0x00E2A48B, 0x00E2A88D, 0x00EBB79B, 0x00F4C9A9, 0x00FCDCC0, 0x00FFF4DE, 0x00FFF6E5, 0x00FFF1DD, 0x00FFE9D0, 0x00F3C79F, 0x03CC7939, 0x5B953F00, 0xD6361600, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE3830000, 0x438E0B09, 0x00CF816C, 0x00FDDBB8, 0x00FFF0D9, 0x00FFF2DD, 0x00FFE4C3, 0x00F0BB98, 0x00CE7D65, 0x00BF7366, 0x00C08480,
        0x00C49391, 0x00CCA3A2, 0x00C19D9D, 0x00BE9C9B, 0x00CDA5A4, 0x00C49593, 0x00C08682, 0x00BF7569, 0x00CB7762, 0x00EDB492, 0x00FFE0BF, 0x00FFF1DB, 0x00FFEEDC, 0x00FFDFBF, 0x00E7AB79, 0x20AE520E, 0xAE5F2700, 0xF1000000, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCF8B0000, 0x28991F1C, 0x00E09F84, 0x00FFE9C8, 0x00FFECD4, 0x00FFE3C1, 0x00EAA884, 0x00CA7B67, 0x00BF817C, 0x00C9A2A4, 0x00DCCDCF, 0x00E7E2E3,
        0x00EBE6E7, 0x00F0EBEC, 0x00C7C3C4, 0x00BEBBBB, 0x00F2ECED, 0x00EAE6E6, 0x00E8E4E5, 0x00DDD1D2, 0x00CCA8AA, 0x00C08380, 0x00C87968, 0x00E69F7D, 0x00FFDFBC, 0x00FFEBD5, 0x00FFE5CC, 0x00F2BF92, 0x10BB6522, 0x8B6E2C00, 0xEB000000, 0xFC000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD18F0000, 0x1B9F2019, 0x00EEB695, 0x00FFE7C7, 0x00FFE9CF, 0x00FBC598, 0x00D37D60, 0x00B16967, 0x00CEB8BA, 0x00E8E3E4, 0x00EEEAEA, 0x00EFE9E9, 0x00F0EAEA,
        0x00F1EBEB, 0x00F6F0F0, 0x00C9C4C4, 0x00BEBABA, 0x00F7F1F1, 0x00F1EBEB, 0x00F0EAEA, 0x00EFEAEA, 0x00EEE9E9, 0x00EAE5E6, 0x00D4C2C5, 0x00B17270, 0x00CD755B, 0x00F8BE91, 0x00FFE7CB, 0x00FFE3CA, 0x00F9CBA1, 0x09C36C27, 0x89622700, 0xE8000000,
        0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE5940200, 0x25A4221A, 0x00F0B38F, 0x00FFE3C4, 0x00FFE0BC, 0x00F2AB79, 0x00BC6354, 0x00CCA6A8, 0x00D1CDCE, 0x00B1AFAF, 0x00F1EBEB, 0x00F1ECEC, 0x00F1ECEC, 0x00F3EDED,
        0x00F3EEEE, 0x00F7F2F2, 0x00DAD7D7, 0x00D4D1D1, 0x00F8F3F3, 0x00F3EFEF, 0x00F3EDED, 0x00F2EDED, 0x00F1EBEB, 0x00F4EDED, 0x00B1AEAE, 0x00C3C1C1, 0x00D3B5B8, 0x00BA6458, 0x00EA9C6E, 0x00FFDFB6, 0x00FFE1C5, 0x00F8C598, 0x09C16924, 0x984B1E00,
        0xEB000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9A10900, 0x429F110A, 0x00E69A78, 0x00FFDEB8, 0x00FFDAB2, 0x00EC9C6B, 0x00BC6A5D, 0x00D3BCBF, 0x00EBE8E8, 0x00F1EAEA, 0x00B8B4B4, 0x00E2DDDD, 0x00F5F1F1, 0x00F5F0F0, 0x00F6F2F2,
        0x00F6F3F3, 0x00F7F3F3, 0x00F8F5F5, 0x00F8F5F5, 0x00F7F3F3, 0x00F6F3F3, 0x00F6F2F2, 0x00F5F1F1, 0x00F5F0F0, 0x00EBE6E6, 0x00BBB8B8, 0x00EEE8E8, 0x00EDE9EA, 0x00D8C8CA, 0x00BD7168, 0x00E58E5F, 0x00FFD7AD, 0x00FFDBBB, 0x00F5B989, 0x19A9520F,
        0xAF321500, 0xF0000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0x8D9E0700, 0x00D87A5C, 0x00FFD7AE, 0x00FFD8B2, 0x00F39E63, 0x00BD685B, 0x00D7C8CC, 0x00EDE8E8, 0x00F0E9E9, 0x00F2ECEC, 0x00F4EFEF, 0x00F3F0F0, 0x00F7F3F3, 0x00F8F4F4, 0x00F8F6F6,
        0x00FAF7F7, 0x00FAF8F8, 0x00FBF9F9, 0x00FBF9F9, 0x00FAF8F8, 0x00FAF8F8, 0x00F9F6F6, 0x00F8F5F5, 0x00F7F3F3, 0x00F4F0F0, 0x00F5F0F0, 0x00F3EEEE, 0x00F1EAEA, 0x00EDEAEA, 0x00DCD2D5, 0x00B96B64, 0x00EE945C, 0x00FFD4A8, 0x00FFD4B1, 0x00E7A067,
        0x3A883A03, 0xCD000000, 0xF7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xD2A50900, 0x0BBC3C25, 0x00FEC295, 0x00FFD2A9, 0x00FBA968, 0x00BD5E4B, 0x00D3BDC1, 0x00EDE8E8, 0x00F0EAEA, 0x00F2ECEC, 0x00F4EFEF, 0x00F7F3F3, 0x00F8F5F5, 0x00F9F7F7, 0x00FAF9F9, 0x00FCFAFA,
        0x00FDFCFC, 0x00FDFDFD, 0x00FEFDFD, 0x00FEFDFD, 0x00FDFDFD, 0x00FDFCFC, 0x00FCFBFB, 0x00FBF9F9, 0x00F9F7F7, 0x00F8F5F5, 0x00F6F2F2, 0x00F4F0F0, 0x00F2EDED, 0x00F0EAEA, 0x00EDE9E9, 0x00DBCFD1, 0x00B95F51, 0x00F49A5C, 0x00FFD2A8, 0x00FFCA9F,
        0x02D17D3A, 0x75532200, 0xE0000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0x60AD1708, 0x00EB986F, 0x00FFD1A8, 0x00FFBA7C, 0x00D46A40, 0x00CCA8AB, 0x00EFECEC, 0x00F1EAEA, 0x00F2ECEC, 0x00F5F0F0, 0x00F6F3F3, 0x00F9F5F5, 0x00FBF8F8, 0x00FCFBFB, 0x00FDFDFD, 0x00FFFEFE,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00FCFBFB, 0x00FBF8F8, 0x00F9F6F6, 0x00F7F3F3, 0x00F5F0F0, 0x00F2EDED, 0x00F0EAEA, 0x00EDE9E9, 0x00D1B4B9, 0x00CC6847, 0x00FFB16E, 0x00FFCDA5,
        0x00F3AD77, 0x1AA14A08, 0xB5070300, 0xF0000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xDAAD0E00, 0x0CCB5032, 0x00FFC393, 0x00FFC18A, 0x00EC7F3E, 0x00B16865, 0x00B3ACAD, 0x00CDC2C2, 0x00EEE9E9, 0x00F8F4F4, 0x00F7F3F3, 0x00F8F6F6, 0x00FBF9F9, 0x00FEFCFC, 0x00FFFEFE, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFCFC, 0x00FCFAFA, 0x00F9F7F7, 0x00F7F4F4, 0x00F4F0F0, 0x00F2EDED, 0x00F3EDED, 0x00D7D6D8, 0x00B27A78, 0x00E67739, 0x00FFC089,
        0x00FFC79B, 0x00D98543, 0x6B481D00, 0xD8000000, 0xFC000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x6AB21301, 0x00E88559, 0x00FFC08F, 0x00FFA358, 0x00CD6643, 0x00C5B2B7, 0x00918080, 0x00A18C8C, 0x00B3A5A5, 0x00DBD5D5, 0x00FCFAFA, 0x00FFFEFE, 0x00FEFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00FCF9F9, 0x00F9F6F6, 0x00F7F3F3, 0x00F7F2F2, 0x00C1BDBD, 0x00ACAAAA, 0x00CFC1C5, 0x00C05D47, 0x00FC994B,
        0x00FFBC8A, 0x00F3A76F, 0x278A3D04, 0xB9000000, 0xF2000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xF7BD1700, 0x22C13115, 0x00FBAD7C, 0x00FFB87E, 0x00F27C30, 0x00C07B73, 0x00E8E5E7, 0x00DAD3D3, 0x00AFA1A1, 0x009A8484, 0x00A28F8F, 0x00C1B7B7, 0x00E7E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFCFC, 0x00FBF9F9, 0x00F8F5F5, 0x00F6F2F2, 0x00E2DDDD, 0x00EFE9E9, 0x00EDEAEB, 0x00C3908E, 0x00E9732D,
        0x00FFB06F, 0x00FEB17D, 0x04C16421, 0x832B1200, 0xE0000000, 0xFE000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xCAB71300, 0x02DD6941, 0x00FFBA83, 0x00FFA14F, 0x00CF5321, 0x00C9A6AA, 0x00EEE9EA, 0x00F5F0F0, 0x00F7F4F4, 0x00D5CFCF, 0x00A19090, 0x009D8585, 0x00A79595, 0x00CFC8C8, 0x00F5F4F4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFCFC, 0x00F9F8F8, 0x00F7F3F3, 0x00F6F2F2, 0x00F2ECEC, 0x00EEE9E9, 0x00D4BFC4, 0x00D05F31,
        0x00FF9E4C, 0x00FFB987, 0x00DB7F3E, 0x4C592100, 0xC8000000, 0xF8000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x93BA1C02, 0x00ED8453, 0x00FFAC6B, 0x00FC842A, 0x00C05D42, 0x00DCD1D5, 0x00EFE9E9, 0x00F1ECEC, 0x00F5F1F1, 0x00FDFAFA, 0x00F7F7F7, 0x00CEC7C7, 0x009B8888, 0x009C8585, 0x00B0A1A1, 0x00D3CFCF, 0x00FEFEFE, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00F8F5F5, 0x00F5F1F1, 0x00F2EDED, 0x00F0EAEA, 0x00E3DBDE, 0x00BB5F4E,
        0x00F97B23, 0x00FFAA6A, 0x00EE9C62, 0x2C793103, 0xB0000000, 0xEF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x62C12708, 0x00F6945E, 0x00FF9A4F, 0x00F66F14, 0x00C07A70, 0x00E7E5E7, 0x00F0EAEA, 0x00F3EEEE, 0x00F6F2F2, 0x00F9F6F6, 0x00FDFCFC, 0x00FFFFFF, 0x00EFEEEE, 0x00BBB0B0, 0x009D8888, 0x009D8888, 0x00BCB1B1, 0x00E1DEDE,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A8A8FE, 0x00EFEFFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F5F4F4, 0x00E4E1E1, 0x00D1CACA, 0x00E6E2E2, 0x00FCFBFB, 0x00F6F2F2, 0x00F3EEEE, 0x00F0EAEA, 0x00EBEAEB, 0x00BA7876,
        0x00E96110, 0x00FF9746, 0x00FBA96F, 0x1A92420F, 0x9C000000, 0xE6000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x38CB2F0D, 0x00FC9B5E, 0x00FF8F39, 0x00EC630E, 0x00C38F8C, 0x00EBE8E9, 0x00F1EBEB, 0x00F3EEEE, 0x00F6F3F3, 0x00FAF7F7, 0x00FDFCFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E4E1E1, 0x00A99A9A, 0x009B8484, 0x00A39191,
        0x00C4BCBC, 0x00F1F0F0, 0x00FCFAFA, 0x00FAF9FA, 0x009596FF, 0x005E5FF6, 0x00F0F0F3, 0x00F0EFEF, 0x00DBD6D6, 0x00CAC0C0, 0x00AB9C9C, 0x00A69393, 0x009C8585, 0x00B8AEAE, 0x00FEFEFE, 0x00F7F4F4, 0x00F3EFEF, 0x00F1ECEC, 0x00EDE9EA, 0x00C9A0A1,
        0x00DD5711, 0x00FF8D32, 0x00FE9D5E, 0x0FA9521C, 0x8D000000, 0xDE000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x1DD33D14, 0x00FE9654, 0x00FF8526, 0x00E3580A, 0x00CCA2A2, 0x00F0ECED, 0x00F6F0F0, 0x00F7F2F2, 0x00F7F3F3, 0x00FAF8F8, 0x00FDFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D8D4D4, 0x00A39292,
        0x00998282, 0x00A89898, 0x00B8ABAA, 0x00A5989E, 0x00564EB1, 0x00B1A8B8, 0x00C0B3B0, 0x00AA9797, 0x00A28B8B, 0x00967E7E, 0x00998686, 0x00B0A4A4, 0x00C9C2C2, 0x00E9E7E7, 0x00FCFAFA, 0x00F7F4F4, 0x00F7F3F3, 0x00F6F1F1, 0x00F2EDEE, 0x00D3BCC0,
        0x00D55113, 0x00FF8019, 0x00FF9855, 0x08B75D24, 0x83000000, 0xD9000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x0FDA441A, 0x00FE9551, 0x00FF7D16, 0x00E25306, 0x00C19F9F, 0x00C7C5C7, 0x00C9C4C4, 0x00DAD6D6, 0x00F8F5F5, 0x00FBF9F9, 0x00FEFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F6F6F6,
        0x00C3BBBB, 0x009B8B8A, 0x00B1AAAA, 0x008E8282, 0x007D6D63, 0x009B8683, 0x00A28F8F, 0x00AD9F9F, 0x00BEB5B5, 0x00D6D2D2, 0x00EFEFEF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FBF9F9, 0x00F9F6F6, 0x00DDD9D9, 0x00C8C4C4, 0x00C8C4C4, 0x00C5B3B8,
        0x00D65214, 0x00FF7506, 0x00FF9750, 0x05C06229, 0x7B000000, 0xD5000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x0CDD451B, 0x00FE944E, 0x00FF780F, 0x00E25407, 0x00BE9D9D, 0x00BDBCBD, 0x00BEBBBB, 0x00D4D0D0, 0x00F8F5F5, 0x00FBF9F9, 0x00FEFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00B2A7A1, 0x00837776, 0x00897D7D, 0x00908383, 0x00968B8B, 0x00ECECEC, 0x00FEFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FBF9F9, 0x00FAF7F7, 0x00D7D4D4, 0x00BEBABA, 0x00BEBBBB, 0x00C1B0B6,
        0x00D65215, 0x00FF7304, 0x00FF9247, 0x04C0602A, 0x77000000, 0xD4000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x1BDB4014, 0x00FE944E, 0x00FF7C16, 0x00E25C13, 0x00CDA6A6, 0x00F1EDEE, 0x00F7F1F1, 0x00F8F3F3, 0x00F7F4F4, 0x00FAF8F8, 0x00FDFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00F4F5FF, 0x007E7AE3, 0x00C0B8BE, 0x00CDC8C5, 0x009E9292, 0x00B3AAAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FBF9F9, 0x00F8F4F4, 0x00F9F4F4, 0x00F7F2F2, 0x00F4EEEE, 0x00D4BFC3,
        0x00D5551B, 0x00FF7B11, 0x00FF9347, 0x07B55524, 0x77000000, 0xD3000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x32D7360C, 0x00FD9651, 0x00FF8425, 0x00EB6D22, 0x00C59392, 0x00EBE7E8, 0x00F1EBEB, 0x00F3EFEF, 0x00F6F3F3, 0x00FAF8F8, 0x00FDFCFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F9F9FF,
        0x007979FC, 0x009999F3, 0x00F3F2F2, 0x00F0EFEE, 0x00F6F6F6, 0x00FAFAFA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00FAF8F8, 0x00F7F4F4, 0x00F4F0F0, 0x00F1ECEC, 0x00EDE9EA, 0x00CAA6AA,
        0x00DC6023, 0x00FF8320, 0x00FF9850, 0x0CA44619, 0x7A000000, 0xD5000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x56CF3006, 0x00FB8F4C, 0x00FF8C35, 0x00F57A2B, 0x00BF827E, 0x00E8E5E7, 0x00F0EAEA, 0x00F3EEEE, 0x00F6F2F2, 0x00F9F6F6, 0x00FCFBFB, 0x00FFFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x009191FE,
        0x007B7BF0, 0x00FFFFFB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFBFB, 0x00F9F7F7, 0x00F6F2F2, 0x00F3EEEE, 0x00F1EBEB, 0x00ECEAEB, 0x00BC7F7F,
        0x00E76F2C, 0x00FF8A2F, 0x00FC9B57, 0x158B320D, 0x82000000, 0xD8000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x84C52902, 0x00F68144, 0x00FF9745, 0x00FC8837, 0x00C06E60, 0x00DED2D5, 0x00EFEAEA, 0x00F2ECEC, 0x00F5F1F1, 0x00F8F5F5, 0x00FBF9F9, 0x00FEFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A3A3FF, 0x007676F2,
        0x00F9F9F9, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FBFAFA, 0x00F8F6F6, 0x00F5F1F1, 0x00F3EDED, 0x00F0EAEA, 0x00E4DCDE, 0x00BA6D68,
        0x00F7863B, 0x00FF9441, 0x00ED8C4D, 0x216E1E03, 0x8C000000, 0xDD000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xB6B52100, 0x01F16D34, 0x00FF9E52, 0x00FF9D4C, 0x00CB674A, 0x00CCACB1, 0x00EEE9EA, 0x00F1EBEB, 0x00F5F0F0, 0x00F7F3F3, 0x00F9F7F7, 0x00FCFBFB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C5C5FF, 0x006D6DF3, 0x00E8E8F6,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FDFCFC, 0x00FAF8F8, 0x00F7F3F3, 0x00F5F1F1, 0x00F2ECEC, 0x00EEE9E9, 0x00D6C1C5, 0x00CD7760,
        0x00FF9B4D, 0x00FF9D4E, 0x00D36934, 0x364B1000, 0x9A000000, 0xE5000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xEA781800, 0x15E34312, 0x00FE9A52, 0x00FF9E51, 0x00EE9564, 0x00BF898A, 0x00E9E6E6, 0x00F4EDED, 0x00EBE6E6, 0x00F5F0F0, 0x00F8F5F5, 0x00FBF8F8, 0x00FDFCFC, 0x00FFFFFF, 0x00DADAFF, 0x006464F7, 0x00E0E0F4, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00FBF9F9, 0x00F9F6F6, 0x00F6F2F2, 0x00EBE6E6, 0x00F3EDED, 0x00EDE9EA, 0x00C79C9E, 0x00E5895B,
        0x00FFA052, 0x00FF9C51, 0x02B5441A, 0x51200600, 0xAD000000, 0xEE000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFB000000, 0x51D22C01, 0x00F9813F, 0x00FFA358, 0x00FFAB6B, 0x00C97B6E, 0x00D4C2C4, 0x00B0AEAE, 0x00BBB7B7, 0x00F5F0F0, 0x00F6F3F3, 0x00F8F5F6, 0x00FFFFF9, 0x00EBEBFF, 0x007474F8, 0x00CDCDF1, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FCFAFA, 0x00F8F7F7, 0x00F7F3F3, 0x00F7F3F3, 0x00C5C2C2, 0x00ACAAAA, 0x00D5C9CA, 0x00BE706A, 0x00FBA66B,
        0x00FFA459, 0x00F18C4A, 0x17771904, 0x71000000, 0xC5000000, 0xF8000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xB6A01F00, 0x06EE5C22, 0x00FFA358, 0x00FFB06F, 0x00E79775, 0x00B1777A, 0x00C3C0C0, 0x00EEE8E8, 0x00F3EEEE, 0x00F3EFF0, 0x00FBF7F3, 0x00F3F1F7, 0x007D7CFA, 0x00B5B4F0, 0x00FFFFFE, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFDFD, 0x00FCFAFA, 0x00FAF7F7, 0x00F7F4F4, 0x00F4F1F1, 0x00F3EDED, 0x00F1EBEB, 0x00C9C8C8, 0x00B08285, 0x00DF8F75, 0x00FFB274,
        0x00FFA75C, 0x00CD5D2F, 0x3B350700, 0x90000000, 0xDD000000, 0xFE000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xF3000000, 0x3FD33205, 0x00FC8C47, 0x00FFAB67, 0x00FFC18E, 0x00CD8378, 0x00D2B4B6, 0x00EDE9E9, 0x00F0EAEA, 0x00F4EFED, 0x00F7F3F1, 0x009392F9, 0x00A3A2EA, 0x00FFFFF6, 0x00FDFCFC, 0x00FEFEFE, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FEFEFE, 0x00FDFCFC, 0x00FBF9F9, 0x00F9F7F7, 0x00F7F4F4, 0x00F5F1F1, 0x00F3EEEE, 0x00F0EAEA, 0x00EEEAEA, 0x00D8C1C2, 0x00C98079, 0x00FFBF8F, 0x00FFAE69,
        0x00F28D4A, 0x0C881A07, 0x63050100, 0xB4000000, 0xF1000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFD000000, 0xAD9D1F00, 0x02F15218, 0x00FFA155, 0x00FFB97E, 0x00F9C09C, 0x00BA6F6E, 0x00D7C3C5, 0x00EEEAEA, 0x00F9F3E9, 0x00A19EF5, 0x008E8DEB, 0x00F9F5EF, 0x00FAF8F6, 0x00F9F8F8, 0x00FBFAFA, 0x00FDFBFB,
        0x00FEFDFD, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFEFE, 0x00FEFDFD, 0x00FDFBFB, 0x00FBFAFA, 0x00FAF8F8, 0x00F8F6F6, 0x00F7F3F3, 0x00F5F0F0, 0x00F2EDED, 0x00F1EBEB, 0x00EFE9E9, 0x00DFD4D4, 0x00B87172, 0x00F0B496, 0x00FFBD86, 0x00FFAA5B,
        0x01C04B25, 0x36350300, 0x86000000, 0xD5000000, 0xFC000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xF2000000, 0x5ABE2700, 0x00F87835, 0x00FFAD65, 0x00FFCA9B, 0x00EAB49E, 0x00BD7B7B, 0x00E0D2CF, 0x00B1B0F6, 0x008B89EC, 0x00ECE7E6, 0x00FAF5F3, 0x00F5F1F2, 0x00F7F3F3, 0x00F8F5F5, 0x00F9F7F7,
        0x00FAF8F8, 0x00FBF9F9, 0x00FBF9F9, 0x00FBF9F9, 0x00FBF9F9, 0x00FAF8F8, 0x00F9F7F7, 0x00F8F6F6, 0x00F7F4F4, 0x00F6F2F2, 0x00F7F2F2, 0x00F3EEEE, 0x00F1EBEB, 0x00EEE9E9, 0x00E1D6D7, 0x00BC7F80, 0x00E5AD9C, 0x00FFCEA2, 0x00FFB26A, 0x00E1733B,
        0x19680903, 0x68000000, 0xB3000000, 0xEE000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFD000000, 0xDA390C00, 0x1EDB3406, 0x00FC8941, 0x00FFB572, 0x00FFD9B2, 0x00E7B4A4, 0x00B77477, 0x00968BD6, 0x00D9D6E3, 0x00F8F2ED, 0x00C1BDBD, 0x00E2DDDD, 0x00F6F1F1, 0x00F5F1F1, 0x00F6F2F2,
        0x00F7F4F4, 0x00F7F4F4, 0x00F9F6F6, 0x00FAF7F7, 0x00F7F4F4, 0x00F7F4F4, 0x00F6F2F2, 0x00F5F1F1, 0x00F5F1F1, 0x00EBE6E6, 0x00C4C1C1, 0x00F1EBEB, 0x00EEE9E9, 0x00DFD3D4, 0x00BC7F80, 0x00DEA59A, 0x00FFDBB7, 0x00FFBB7B, 0x00F58F48, 0x088D140A,
        0x4C180000, 0x95000000, 0xDC000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF8000000, 0xB0741700, 0x0FE3410F, 0x00FD9549, 0x00FFBC7F, 0x00FFDFC1, 0x00EDC7B8, 0x00BC726D, 0x00D2B1B0, 0x00D7D4D5, 0x00ADAAAA, 0x00EFE9E9, 0x00F2EDED, 0x00F2EDED, 0x00F3EEEE,
        0x00F3EFEF, 0x00F7F3F3, 0x00DDD9D9, 0x00D7D4D4, 0x00F8F3F3, 0x00F3F0F0, 0x00F4EEEE, 0x00F3EDED, 0x00F2EDED, 0x00F3EDED, 0x00ACAAAA, 0x00C9C8C8, 0x00D8BEBF, 0x00B87171, 0x00E5B8AD, 0x00FFE4C7, 0x00FFC287, 0x00F9994D, 0x02A52414, 0x3C260000,
        0x7E000000, 0xC7000000, 0xF5000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF2000000, 0x8D8C1C00, 0x09E74611, 0x00FD9446, 0x00FFBE80, 0x00FFE4C7, 0x00F4D6C9, 0x00CB8D8B, 0x00B37C7D, 0x00CFBCBC, 0x00ECE9E9, 0x00EEE9E9, 0x00F0EAEA, 0x00F0EBEB,
        0x00F1EBEB, 0x00F7F1F1, 0x00C9C4C4, 0x00BEBBBB, 0x00F8F2F2, 0x00F1ECEC, 0x00F0EBEB, 0x00F0EAEA, 0x00EEE9E9, 0x00EDE9E9, 0x00D5C7C7, 0x00B07F80, 0x00C88886, 0x00EFCEC3, 0x00FFE8CE, 0x00FFC489, 0x00F9994D, 0x03AF2F17, 0x303C0000, 0x71000000,
        0xB7000000, 0xED000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xEB000000, 0x887E1800, 0x0AE6420F, 0x00FC8B3E, 0x00FFB974, 0x00FFE1C1, 0x00FFF3E7, 0x00E5BCB7, 0x00BF7877, 0x00C39293, 0x00D3B9B9, 0x00E2D7D7, 0x00EBE8E8,
        0x00EDE8E8, 0x00F3EDED, 0x00C8C4C4, 0x00BEBBBB, 0x00F4EEEE, 0x00EDE8E8, 0x00EBE9E9, 0x00E4DADB, 0x00D6BDBD, 0x00C59899, 0x00BD7676, 0x00DEAEAA, 0x00FFF2E7, 0x00FFE5C9, 0x00FFC07F, 0x00F58F44, 0x03AD2712, 0x303F0000, 0x6E000000, 0xAD000000,
        0xE6000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xE8000000, 0x96641400, 0x1AD13306, 0x00F8722D, 0x00FFAB5D, 0x00FFD09D, 0x00FFF4E3, 0x00FDF1EC, 0x00E8C9C5, 0x00D09998, 0x00BA7574, 0x00BA7D7D,
        0x00C99FA0, 0x00D3B7B7, 0x00C5ACAC, 0x00C2A9A9, 0x00D4B9B9, 0x00CAA3A3, 0x00BC8282, 0x00B97474, 0x00CD9494, 0x00E4C0BF, 0x00FCEEEB, 0x00FFF6E8, 0x00FFD5A6, 0x00FFB263, 0x00E57033, 0x099A1709, 0x3B2C0100, 0x72000000, 0xAE000000, 0xE3000000,
        0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xEA000000, 0xAF400E00, 0x3BAC2300, 0x02EF5418, 0x00FB883B, 0x00FFB670, 0x00FFD7A8, 0x00FFF2DD, 0x00FFFFFE, 0x00F8EDEE, 0x00E9CDCC,
        0x00DDB4B4, 0x00D6A4A4, 0x00D6A4A3, 0x00D7A4A4, 0x00D5A4A4, 0x00DCB1B1, 0x00E7C8C8, 0x00F7EAEA, 0x00FFFFFE, 0x00FFF5E4, 0x00FFDBAF, 0x00FFBD78, 0x00F48A40, 0x00CE461C, 0x19740A01, 0x4B1C0100, 0x7F000000, 0xB7000000, 0xE6000000, 0xFB000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF0000000, 0xCD000000, 0x736E1500, 0x1CC72C02, 0x01F35A1B, 0x00FB8739, 0x00FFA759, 0x00FFC98C, 0x00FFE2BC, 0x00FFF2DB,
        0x00FFFEF2, 0x00FFFFFC, 0x00FFFFFE, 0x00FFFFFE, 0x00FFFFFC, 0x00FFFFF4, 0x00FFF4DF, 0x00FFE5C0, 0x00FFCE93, 0x00FFAB5D, 0x00F4883C, 0x00D9511F, 0x0D981403, 0x36400400, 0x68000000, 0x95000000, 0xC7000000, 0xEC000000, 0xFD000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF7000000, 0xDF000000, 0xB6070100, 0x6B601300, 0x28AA2400, 0x06E3410C, 0x00F15D1B, 0x00F87E34, 0x00FD994C,
        0x00FFA658, 0x00FFB166, 0x00FFB871, 0x00FFB972, 0x00FFB267, 0x00FEA75A, 0x00FD9B4E, 0x00F37F37, 0x00E1581E, 0x03C9350F, 0x18891301, 0x3A410600, 0x64070100, 0x86000000, 0xB3000000, 0xDC000000, 0xF5000000, 0xFE000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xF0000000, 0xD9000000, 0xB9000000, 0x83390C00, 0x4C711400, 0x2C961D00, 0x1AB12803,
        0x0FC5360A, 0x08D34110, 0x05DA4612, 0x04D94613, 0x07CE3E10, 0x0CBC320B, 0x15A22104, 0x22811300, 0x365B0B00, 0x50290500, 0x72000000, 0x91000000, 0xB4000000, 0xD5000000, 0xEE000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xF2000000, 0xE0000000, 0xC8000000, 0xB0000000, 0x9C000000,
        0x8E000000, 0x83000000, 0x7B000000, 0x77000000, 0x77000000, 0x7A000000, 0x82000000, 0x8C000000, 0x9B000000, 0xAD000000, 0xC5000000, 0xDD000000, 0xF0000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF8000000, 0xEF000000, 0xE6000000,
        0xDE000000, 0xD9000000, 0xD5000000, 0xD4000000, 0xD3000000, 0xD5000000, 0xD8000000, 0xDD000000, 0xE5000000, 0xEE000000, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmClockRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acClockRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmDate
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acDateRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF0818181, 0xB37B7B7B, 0x66797979, 0x277C7C7C, 0xA1626262, 0xEF000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xBB705D5D, 0x7C786666, 0x987F6C6C, 0xE6453F3F, 0xEF585858, 0xBB7A7A7A, 0x72797979, 0x1E787878, 0x007E7E7E, 0x008C8C8C, 0x008B8B8B, 0x24707070, 0xBE060606, 0xEF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF26F5E5E, 0x9A786767, 0xA37C6B6B, 0x906D5C5C,
        0x027D6B6B, 0x00AF9D9D, 0x009B8B8B, 0x1E7C7878, 0x1E767777, 0x017D7C7D, 0x00898989, 0x00AFAEAE, 0x00D6D6D4, 0x00F4F4F4, 0x00CACAC8, 0x017C7C7C, 0x712D2D2D, 0xD9000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEA746262, 0xDB7D6A6A, 0xEE605252, 0x466C5C5C, 0x00907D7D, 0x00998686, 0x027A6E6E,
        0x00948989, 0x00A49D9D, 0x00A49596, 0x00918A8A, 0x00A8A8A6, 0x00D2D1CE, 0x00F0EFEC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00ECEAE7, 0x00848483, 0x3B4B4B4B, 0xBE000000, 0xF5000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEA7C6B6B, 0xF56C5C5C, 0x9B6C5B5B, 0x187F6C6C, 0x08958181, 0x227B6C6C, 0x00837575, 0x00B1A6A6, 0x00A89B9B, 0x00928989,
        0x008F8B8B, 0x009C9C9A, 0x00B7B2AE, 0x00BFADAC, 0x00FEFBF7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFB, 0x009C9A99, 0x19626262, 0xA4000000, 0xE9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF96B5B5B, 0x6A715F5F, 0x0D837070, 0x25867272, 0x0C736262, 0x00AFA0A0, 0x00BCADAD, 0x00978B8C, 0x00848081, 0x0081807F, 0x009B9996, 0x00B0A09E,
        0x00E0D9D3, 0x00FFFFFA, 0x00FCFAF5, 0x00C5B9B8, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFEFE, 0x00FBFBFA, 0x00E3E3E3, 0x00D8D7D5, 0x00BEBCB7, 0x03747575, 0x75212121, 0xDA000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x94645555, 0x00867474, 0x00BDADAD, 0x00A49393, 0x00857E7E, 0x00878383, 0x007E7E7D, 0x00A59594, 0x00B8ADA9, 0x00F2EEE5, 0x00FEFBF3, 0x00D0C3C0,
        0x00E3DAD7, 0x00FFFFFF, 0x00FCFAFA, 0x00E1D7D8, 0x00FFFFFE, 0x00F9F8F7, 0x00F2F1F0, 0x00FFFEFE, 0x00E1E0DF, 0x00B1B1B1, 0x00E3E2DF, 0x00EEE9E0, 0x00838383, 0x384D4D4D, 0xC1000000, 0xF6000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF07A7A7A, 0xA4767676, 0x21706D6D, 0x00867E7F, 0x007C7A7B, 0x00908887, 0x00AA9B99, 0x00CFC8BE, 0x00EFECE1, 0x00E3DBD2, 0x00D4C5C2, 0x00FFFFFF, 0x00FFFFFF, 0x00E4DCDA,
        0x00F2EFEE, 0x00FFFFFF, 0x00FFFFFD, 0x00FEFDFD, 0x00CFCFCE, 0x00B1B1B0, 0x00D4D4D3, 0x00FEFDFD, 0x00FDFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFBEF, 0x00A3A09C, 0x12646465, 0xA1020202, 0xE8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xF9797979, 0xB0737373, 0x5A727272, 0x1D707172, 0x00767777, 0x00868685, 0x00A8A39D, 0x00CDC8BC, 0x00E7E2D5, 0x00CCB9B3, 0x00F9F2EB, 0x00FFFFFD, 0x00F6F2F0, 0x00DDD3D2, 0x00FFFFFF, 0x00FFFFFD, 0x00F7F4F3,
        0x00EAEAE9, 0x00B8B8B7, 0x00D9D9D7, 0x00FFFFFF, 0x00EEEDEB, 0x00F7F6F4, 0x00FFFFFF, 0x00FFFEFD, 0x00FFFEFD, 0x00FFFEFD, 0x00FFFEFD, 0x00FFFFF5, 0x00C4BFB7, 0x01727273, 0x672B2B2B, 0xD5000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x54757575, 0x007A7A7A, 0x008D8C8A, 0x00AEAAA4, 0x00D1CBBE, 0x00F1EADB, 0x00FFFAED, 0x00FFFFF6, 0x00FFFFFC, 0x00D9CECD, 0x00F3F0F0, 0x00FFFFFF, 0x00F7F3F2, 0x00EFEDEB, 0x00DCDCDB, 0x00F5F5F3, 0x00F9F9F8,
        0x00CACAC8, 0x00DAD9D8, 0x00F7F6F4, 0x00FFFEFC, 0x00FFFFFE, 0x00FFFFFE, 0x00FFFEFC, 0x00FFFEFC, 0x00FFFEFC, 0x00FFFEFC, 0x00FFFEFD, 0x00FFFFF8, 0x00E9E0D3, 0x007D7D7D, 0x2D515252, 0xBA000000, 0xF3000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x1C717272, 0x00918F8D, 0x00F8EDDB, 0x00FFFBEC, 0x00FFFFF3, 0x00FFFEF9, 0x00FFFEFD, 0x00FFFEFD, 0x00FFFDFD, 0x00F3EDEC, 0x00F6F5F3, 0x00FEFEFD, 0x00EDEDEC, 0x00B5B4B3, 0x00BBBAB9, 0x00F8F7F5, 0x00FFFFFE,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFEFD, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFFDFC, 0x00FFF7E7, 0x00A09C97, 0x0C646566, 0x96090909, 0xE4000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x1E6D6E6E, 0x0090908E, 0x00FCF0DE, 0x00FFFCF3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFEFD, 0x00DFDEDD, 0x00B1B0AF, 0x00CBCAC9, 0x00FDFCFA, 0x00F9F8F6, 0x00FAF8F6, 0x00FFFFFF, 0x00FFFEFC, 0x00FFFDFB,
        0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFD, 0x00FFFDF0, 0x00D1C8B9, 0x00717273, 0x602B2B2B, 0xD1000000, 0xFC000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x1E6D6D6D, 0x008D8D8E, 0x00F4E9D9, 0x00FFF9EC, 0x00F4F3F3, 0x00D9D7D6, 0x00DBDAD8, 0x00FFFEFE, 0x00E1E0DE, 0x00E5E3E2, 0x00FEFDFB, 0x00FFFDFC, 0x00FFFEFC, 0x00FFFEFC, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFEFB,
        0x00FFFFFB, 0x00FFFFFB, 0x00FFFFFB, 0x00FFFFFB, 0x00FFFFFB, 0x00FFFFFB, 0x00FFFEFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFEFD, 0x00FFFCF3, 0x00F2E5D0, 0x0081807E, 0x3149494A, 0xB9000000, 0xF3000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x266B6B6B, 0x008B8C8E, 0x00EDE6DA, 0x00FCF2DE, 0x00CACAC8, 0x00C9C8C5, 0x00EDEBE9, 0x00FFFEFC, 0x00FFFFFE, 0x00FFFFFE, 0x00FFFEFB, 0x00FFFDFA, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFEFA, 0x00FFFFFA, 0x00FFFEFA,
        0x00F4F3FB, 0x00E7E6FC, 0x00DEDDFD, 0x00DAD9FD, 0x00DDDCFD, 0x00E8E7FC, 0x00FCFBFB, 0x00FFFFFA, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDFB, 0x00FFFDF8, 0x00FFF6DF, 0x00ADA79C, 0x0A626365, 0x91121212, 0xE4000000, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x5B646464, 0x00808181, 0x00DEDAD5, 0x00FEEED6, 0x00FFFDF7, 0x00FFFFFF, 0x00FFFFFC, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFEFA, 0x00FFFFFA, 0x00EBEAFC, 0x00CECEFD,
        0x00BCBCFF, 0x00B7B7FF, 0x00B3B3FF, 0x00B1B1FF, 0x00B0B0FF, 0x00B0B0FF, 0x00C2C2FE, 0x00F4F3FA, 0x00FFFFFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFEFD, 0x00FFFCEA, 0x00DACEB8, 0x00717171, 0x56333334, 0xCE000000, 0xFA000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x965B5B5B, 0x00727273, 0x00C8C4C2, 0x00FBEBD2, 0x00FFF7E6, 0x00FFFEFC, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFFFA, 0x00FAF9FA, 0x00D6D5FD, 0x00B8B8FF, 0x00B4B4FF,
        0x00B5B5FF, 0x00B1B2FF, 0x00B0B0FF, 0x00B1B1FF, 0x00B1B1FF, 0x00AFAFFF, 0x00A9A9FF, 0x00BFBFFE, 0x00FDFBFA, 0x00FFFEFA, 0x00FFFDFA, 0x00FFFDFA, 0x00FFFDFC, 0x00FFFBF1, 0x00F7E6CB, 0x0082807D, 0x2A4A4A4A, 0xB3000000, 0xF0000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xD34A4A4A, 0x07686868, 0x00ADA9A7, 0x00F6E6D1, 0x00FFF1D8, 0x00FFFCFA, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFDF9, 0x00F8F6FA, 0x00C9C8FE, 0x00B3B3FF, 0x00B4B5FF, 0x00B2B2FF,
        0x00B8B8FF, 0x00CAC9FD, 0x00CACAFD, 0x00B2B2FF, 0x00ABABFF, 0x00ACACFF, 0x00A9A9FF, 0x00A7A7FF, 0x00E6E4FB, 0x00FFFFF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFF3D9, 0x00A9A195, 0x0C5C5E5F, 0x93080808, 0xE3000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xF8000000, 0x39606060, 0x008B8989, 0x00E9D9C9, 0x00FFECCD, 0x00FFFAF4, 0x00FFFCFB, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFFF9, 0x00D9D8FC, 0x00AEAEFF, 0x00B1B1FF, 0x00B1B1FF, 0x00D6D5FD,
        0x00F6F5FA, 0x00FFFDF9, 0x00FFFFF9, 0x00ECEAFA, 0x00AFAFFF, 0x00A6A6FF, 0x00A7A7FF, 0x00A0A0FF, 0x00D5D4FC, 0x00FFFFF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFDFC, 0x00FFFAE8, 0x00D8C8B0, 0x006D6E6F, 0x55333333, 0xCF000000, 0xFB000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFE000000, 0x84565656, 0x00727273, 0x00D0C0B6, 0x00FDE7C5, 0x00FFF6E7, 0x00FFFDFC, 0x00FFFCF8, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFDF9, 0x00F9F8F9, 0x00DBDAFC, 0x00B6B6FE, 0x00DCDAFC, 0x00FFFFF8,
        0x00FFFEF8, 0x00FFFDF8, 0x00FFFDF8, 0x00FFFFF8, 0x00D1CFFC, 0x00A0A0FF, 0x00A3A3FF, 0x009C9CFF, 0x00D4D2FC, 0x00FFFFF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFCF9, 0x00FFFDFA, 0x00FFFAF1, 0x00FCE7C5, 0x008A8680, 0x214D4E4F, 0xB1000000, 0xEF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xC84A4A4A, 0x05646666, 0x00B2A59E, 0x00F8DFC1, 0x00FFEFD3, 0x00FFFDFA, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFDF8, 0x00FFFFF8, 0x00F8F5F9, 0x00FFFDF8, 0x00FFFDF8,
        0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFFF8, 0x00D7D6FB, 0x009C9CFF, 0x009F9FFF, 0x009C9CFF, 0x00E6E4FA, 0x00FFFFF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFF2D2, 0x00B2A795, 0x075D5E5F, 0x87111111, 0xDF000000,
        0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xF3222222, 0x255D5E5F, 0x00958C88, 0x00EFD3BA, 0x00FFE8C1, 0x00FFFAF1, 0x00FFFDFA, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFEF8, 0x00FFFCF8, 0x00FFFCF8,
        0x00FFFCF8, 0x00FFFFF8, 0x00FFFFF8, 0x00F1EFF9, 0x00ADADFE, 0x009B9BFF, 0x009A9AFF, 0x00BCBBFD, 0x00FFFFF8, 0x00FFFEF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFDFB, 0x00FFF7DF, 0x00D9C7A8, 0x006A6B6B, 0x522E2E2E, 0xCB000000,
        0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFE000000, 0x62575758, 0x00777473, 0x00DEBFAB, 0x00FDE1B7, 0x00FFF4DF, 0x00FFFDFB, 0x00FFFCF7, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8, 0x00FFFCF8,
        0x00FFFDF8, 0x00F0EEF9, 0x00CBCAFC, 0x00A4A4FE, 0x009999FF, 0x009999FF, 0x009A9AFF, 0x00C5C4FC, 0x00EAE9F9, 0x00FFFDF7, 0x00FFFFF7, 0x00FFFBF8, 0x00FFFBF8, 0x00FFFCF8, 0x00FFFCF9, 0x00FFFAF1, 0x00FAE3BD, 0x0087837C, 0x214A4B4B, 0xB0000000,
        0xEF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xBB474747, 0x05626465, 0x00B69F93, 0x00FAD9B1, 0x00FFEDCC, 0x00FFFCF9, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7,
        0x00FFFDF7, 0x00BCBBFC, 0x009191FF, 0x009696FF, 0x009697FF, 0x009595FF, 0x009595FF, 0x008F8FFF, 0x009392FF, 0x00ACABFD, 0x00E6E4F9, 0x00FFFEF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFCF9, 0x00FFF1D0, 0x00BBAC94, 0x035C5E60, 0x7A1C1C1C,
        0xDD000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xF5000000, 0x2C57595A, 0x008E817B, 0x00EFC9A8, 0x00FFE5B9, 0x00FFF9F0, 0x00FFFCF9, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFBF7,
        0x00FFFFF7, 0x00D6D4FA, 0x008D8EFF, 0x009595FF, 0x009999FF, 0x009393FF, 0x008D8DFF, 0x008F8FFF, 0x008E8EFF, 0x008686FF, 0x009595FE, 0x00F2EFF8, 0x00FFFEF7, 0x00FFFBF7, 0x00FFFBF7, 0x00FFFDFB, 0x00FFF5DF, 0x00E8CFA7, 0x006B6B6A, 0x41373737,
        0xC3000000, 0xF7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFD000000, 0x69525353, 0x006F6C6B, 0x00DFB398, 0x00FDDBAB, 0x00FFF1D8, 0x00FFFCFA, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6,
        0x00FFFDF6, 0x00F6F3F7, 0x00C8C5FB, 0x00E1DEF9, 0x00F1EEF7, 0x00E3E2F8, 0x00B4B2FC, 0x008A8AFF, 0x008B8BFF, 0x008A8AFF, 0x008081FF, 0x00BFBDFB, 0x00FFFFF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFCF8, 0x00FFF9EE, 0x00FEE2B4, 0x00898278, 0x1B48494A,
        0xA7000000, 0xEB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xB2484848, 0x015E6061, 0x00BC9784, 0x00FCD0A2, 0x00FFE8BE, 0x00FFFBF6, 0x00FFFBF7, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6,
        0x00FFFBF6, 0x00FFFCF6, 0x00FFFFF6, 0x00FFFFF6, 0x00FFFEF6, 0x00FFFFF6, 0x00FFFEF5, 0x00B6B4FC, 0x008282FF, 0x008686FF, 0x008181FF, 0x009292FE, 0x00FAF6F7, 0x00FFFDF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFDF9, 0x00FFF0CC, 0x00BAA88C, 0x03585A5D,
        0x761B1B1B, 0xDA000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xEE212121, 0x22545759, 0x008F7B72, 0x00F3BE96, 0x00FFE1AE, 0x00FFF7EB, 0x00FFFCF8, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6,
        0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFFF6, 0x00E0DDF8, 0x008383FF, 0x008181FF, 0x007F7FFF, 0x008483FF, 0x00F2EFF7, 0x00FFFEF6, 0x00FFFBF6, 0x00FFFBF6, 0x00FFFDFA, 0x00FFF6E2, 0x00EED1A2, 0x006D6C69,
        0x363C3C3C, 0xC1000000, 0xF5000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0x754B4C4D, 0x00686564, 0x00D8A585, 0x00FFD69F, 0x00FFEFD4, 0x00FFFCF9, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5,
        0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFFF5, 0x00E4E0F7, 0x008080FF, 0x007E7EFF, 0x007A7AFF, 0x008888FE, 0x00F8F3F6, 0x00FFFDF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF7, 0x00FFF9F1, 0x00FFE5B2, 0x00968B79,
        0x104B4C4F, 0x9C060606, 0xE7000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC4404040, 0x0455595B, 0x00B18873, 0x00FDC793, 0x00FFE4B3, 0x00FFFAF4, 0x00FFFAF6, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5,
        0x00FFFBF5, 0x00FFFCF5, 0x00FFFBF5, 0x00FFFBF5, 0x00FFFBF5, 0x00FFFFF5, 0x00FFFEF4, 0x00B1AFFC, 0x007878FF, 0x007B7BFF, 0x007171FF, 0x00B2B0FB, 0x00FFFEF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFCF9, 0x00FFEFC7, 0x00C5AD88,
        0x0157595C, 0x65212121, 0xD4000000, 0xFC000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF3161616, 0x274E5254, 0x00897065, 0x00F5B281, 0x00FFDA9F, 0x00FFF3E0, 0x00FFFCF8, 0x00FFFAF4, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFBF4,
        0x00FBF6F5, 0x00FBF6F5, 0x00FFFDF4, 0x00FFFFF4, 0x00FFFBF5, 0x00ECEAF6, 0x00B0ADFA, 0x007777FF, 0x007979FF, 0x007272FF, 0x008080FE, 0x00EDE9F6, 0x00FFFEF4, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF5, 0x00FFFAF4, 0x00FFFCF8, 0x00FFF5E3, 0x00EFCF9B,
        0x006C6A66, 0x303A3A3A, 0xBA000000, 0xF3000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0x6F494A4B, 0x00635F5E, 0x00DA9670, 0x00FFCF90, 0x00FFEBC5, 0x00FFFCF9, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFEF3,
        0x00B5B2FA, 0x009190FD, 0x00ABAAFB, 0x00ADACFB, 0x009C9BFC, 0x008080FF, 0x007474FF, 0x007575FF, 0x006D6DFF, 0x008181FE, 0x00DFDDF6, 0x00FFFFF3, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF5, 0x00FFFBF6, 0x00FFE8B5,
        0x00A89679, 0x064D5053, 0x89131313, 0xE3000000, 0xFE000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCD363636, 0x094F5457, 0x00A77A64, 0x00FEBE82, 0x00FFE1AB, 0x00FFFAF3, 0x00FFFBF5, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFEF4, 0x00EFEBF5,
        0x008484FF, 0x007676FF, 0x007475FF, 0x007273FF, 0x007272FF, 0x007171FF, 0x006D6DFF, 0x006F6FFF, 0x009A9AFC, 0x00E8E6F6, 0x00FFFFF3, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF4, 0x00FFFAF5, 0x00FFFBF6, 0x00FFFCF9, 0x00FFFDFC, 0x00FFF0CD,
        0x00E0BF8B, 0x005E5D5C, 0x43323333, 0xC8000000, 0xF8000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7000000, 0x3C494C4D, 0x0077645B, 0x00F2A46E, 0x00FFD492, 0x00FFF0D7, 0x00FFFBF8, 0x00FFF9F3, 0x00FFFAF3, 0x00FFFAF3, 0x00FFFFF2, 0x00CCC9F8,
        0x007171FF, 0x006F70FF, 0x006E6EFF, 0x006D6DFF, 0x007272FF, 0x008281FE, 0x00A4A3FB, 0x00D2D0F7, 0x00FEFBF3, 0x00FFFFF3, 0x00FFF9F3, 0x00FFFAF3, 0x00FFFAF4, 0x00FFFAF6, 0x00FFFCF9, 0x00FFFBF6, 0x00FFF5E7, 0x00FFEDD0, 0x00FFE5B8, 0x00FFDDA0,
        0x00FAD090, 0x00777067, 0x1047494A, 0xA5000000, 0xEA000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0x85454545, 0x00595657, 0x00D5875B, 0x00FFC47F, 0x00FFE4B3, 0x00FFFAF7, 0x00FFF9F3, 0x00FFF9F3, 0x00FFF9F3, 0x00FFFBF3, 0x00F2EEF4,
        0x00CECAF7, 0x00B7B4FA, 0x00B3B1FA, 0x00BAB7F9, 0x00D5D1F7, 0x00F6F1F4, 0x00FFFEF3, 0x00FFFFF2, 0x00FFFAF3, 0x00FFF9F4, 0x00FFFAF6, 0x00FFFBF9, 0x00FFF9F3, 0x00FFF5E7, 0x00FFEDD1, 0x00FFE2B1, 0x00FFD795, 0x00FDD08A, 0x00FBC884, 0x00F9BF81,
        0x00F2B47D, 0x00786E64, 0x0C48494B, 0x89000000, 0xDD000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD2333333, 0x0A4A4F53, 0x00A16C53, 0x00FEAE6B, 0x00FFD997, 0x00FFF5E6, 0x00FFFAF6, 0x00FFF9F2, 0x00FFF9F3, 0x00FFF9F3, 0x00FFFBF2,
        0x00FFFFF2, 0x00FFFFF2, 0x00FFFFF2, 0x00FFFFF2, 0x00FFFFF2, 0x00FFFBF2, 0x00FFFAF3, 0x00FFFAF5, 0x00FFFBF9, 0x00FFFAF4, 0x00FFF4E5, 0x00FFE8C1, 0x00FFDEA7, 0x00FFD692, 0x00FDCE86, 0x00FBC37E, 0x00F8BC7B, 0x00F7B176, 0x00F5A770, 0x00F8A065,
        0x00F29758, 0x0074655C, 0x0D444748, 0x80000000, 0xD7000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF8000000, 0x4C444648, 0x00685954, 0x00ED9359, 0x00FFCD82, 0x00FFE7C0, 0x00FFFBF8, 0x00FFF9F2, 0x00FFF9F2, 0x00FFF9F2, 0x00FFF9F2,
        0x00FFF9F2, 0x00FFF9F2, 0x00FFF9F2, 0x00FFFAF3, 0x00FFFBF6, 0x00FFFCF9, 0x00FFFAF4, 0x00FFF5E5, 0x00FFE8C2, 0x00FFDEA5, 0x00FFD48E, 0x00FDCC80, 0x00FCC27B, 0x00F9BB78, 0x00F8AF72, 0x00F6A66B, 0x00F79E61, 0x00F89551, 0x00FC8F45, 0x00FF873E,
        0x00F57F41, 0x00766157, 0x0C434647, 0x80000000, 0xD8000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xA43B3C3C, 0x004D5053, 0x00C1754B, 0x00FFB96E, 0x00FFDB9D, 0x00FFF8F0, 0x00FFF9F3, 0x00FFF9F2, 0x00FFF9F2, 0x00FFF9F2,
        0x00FFF9F3, 0x00FFFBF6, 0x00FFFBF9, 0x00FFF8F0, 0x00FFF0DB, 0x00FFE7BF, 0x00FFDDA5, 0x00FFD28A, 0x00FDC87B, 0x00FCC076, 0x00FAB772, 0x00F9AC6D, 0x00F8A263, 0x00F89957, 0x00F9914A, 0x00FC893F, 0x00FF8237, 0x00F67A39, 0x00D87142, 0x00AE684C,
        0x00855F51, 0x005A5554, 0x12414141, 0x82000000, 0xD9000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE9232323, 0x1743494D, 0x008F5E47, 0x00FF9E53, 0x00FFD185, 0x00FFF1DB, 0x00FFFBF6, 0x00FFFAF3, 0x00FFFBF6, 0x00FFFCF8,
        0x00FFF9F1, 0x00FFF1DA, 0x00FFE6BD, 0x00FFD998, 0x00FFCF83, 0x00FEC878, 0x00FDBF71, 0x00FBB36C, 0x00FAAA68, 0x00F99E5E, 0x00F99451, 0x00FA8E43, 0x00FD8333, 0x00FF7D2D, 0x00F97531, 0x00DB6C3C, 0x00B06446, 0x00795B50, 0x00555353, 0x01464D50,
        0x0F3B4042, 0x242E2F2F, 0x52111111, 0x9A000000, 0xE2000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0x54414444, 0x0061514D, 0x00ED7F3E, 0x00FFC06F, 0x00FFE5B7, 0x00FFFCFB, 0x00FFF8EF, 0x00FFF0DB, 0x00FFE6BD,
        0x00FFD897, 0x00FFCE7F, 0x00FEC774, 0x00FDBD6F, 0x00FCB269, 0x00FBA862, 0x00FA9C58, 0x00FA924C, 0x00FC8A3E, 0x00FF812C, 0x00FF7926, 0x00F7712A, 0x00DC6835, 0x00B06141, 0x007C594C, 0x00545252, 0x02464B4E, 0x0E3C4043, 0x222F3030, 0x46151616,
        0x69020202, 0x83000000, 0xA4000000, 0xCE000000, 0xF3000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB8333333, 0x04444A4F, 0x00AE623B, 0x00FFA654, 0x00FFD489, 0x00FFE0AC, 0x00FFD692, 0x00FFCD7D, 0x00FEC26E,
        0x00FDB967, 0x00FCAF63, 0x00FCA25B, 0x00FB9752, 0x00FC8E43, 0x00FD8431, 0x00FF7B23, 0x00FF721F, 0x00F96923, 0x00D56230, 0x009C5C42, 0x006F544B, 0x00524F4E, 0x02444A4D, 0x11383D3E, 0x2C252627, 0x4C131313, 0x6B030303, 0x83000000, 0x9E000000,
        0xBC000000, 0xD5000000, 0xE8000000, 0xF6000000, 0xFE000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF4000000, 0x2E3D4346, 0x00765142, 0x00FD8737, 0x00FFC975, 0x00FFC26B, 0x00FEB865, 0x00FEAE60, 0x00FDA057,
        0x00FC964E, 0x00FD8B3D, 0x00FF8129, 0x00FF791C, 0x00FF6E17, 0x00FB651A, 0x00D95F29, 0x009F583D, 0x00705248, 0x004F4D4E, 0x0140494D, 0x11383B3C, 0x2C252626, 0x4C131313, 0x6D010101, 0x88000000, 0xA5000000, 0xC0000000, 0xD5000000, 0xE6000000,
        0xF4000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0x773B3C3D, 0x004B494B, 0x00D3682D, 0x00FFA451, 0x00FE9F53, 0x00FE9245, 0x00FE8733, 0x00FF7C24,
        0x00FF7216, 0x00FF6810, 0x00F06016, 0x00CA5A27, 0x00A05337, 0x00724F43, 0x004F4C4C, 0x053C454A, 0x16323637, 0x2D262627, 0x4A131313, 0x6D010101, 0x88000000, 0xA5000000, 0xC0000000, 0xD8000000, 0xEA000000, 0xF6000000, 0xFD000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC8303030, 0x053D464B, 0x008E5538, 0x00FF7D23, 0x00FF7A1D, 0x00FF6C0A, 0x00FF6306, 0x00F35A0F,
        0x00CD5421, 0x009A5034, 0x00614D47, 0x00474A4C, 0x043B4448, 0x15313638, 0x2D242425, 0x540B0B0B, 0x74000000, 0x8D000000, 0xA6000000, 0xBF000000, 0xD8000000, 0xEA000000, 0xF6000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7000000, 0x423D3F41, 0x005D4C45, 0x00F45C09, 0x00F65607, 0x00D0501A, 0x009E4D2E, 0x00634C44,
        0x00474A4B, 0x053B4448, 0x14333638, 0x2C252626, 0x520D0D0D, 0x72000000, 0x8C000000, 0xA9000000, 0xC6000000, 0xDC000000, 0xEA000000, 0xF5000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0x9F353535, 0x00494848, 0x00774A3A, 0x00634941, 0x0048494A, 0x053B4448, 0x1A2E3233,
        0x371D1E1E, 0x580B0B0B, 0x74000000, 0x8C000000, 0xA7000000, 0xC5000000, 0xDC000000, 0xEC000000, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE81C1C1C, 0x3A3F3F3F, 0x113B4144, 0x1F2D3132, 0x361F2020, 0x570B0B0B, 0x76000000,
        0x92000000, 0xB0000000, 0xC8000000, 0xDC000000, 0xEB000000, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xDB000000, 0xA3000000, 0x89000000, 0x96000000, 0xAF000000, 0xC8000000,
        0xDF000000, 0xEF000000, 0xF9000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmDateRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acDateRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmEmail
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acEmailRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCD9F3C3C, 0x729F3939, 0x389D3737, 0x6F943636, 0xEA000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC5A03E3E, 0x5B9E3939, 0x06A03837, 0x00B25556, 0x00CB6265, 0x00AF3E3F, 0x76622323, 0xE0000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFDA54343, 0xA89F3F3F, 0x3F9F3A3A, 0x03A23B3B, 0x00B85655, 0x00DCABAA, 0x00F7E6E7, 0x00FE9197, 0x00D06A65, 0x168E3030, 0xB0090303, 0xEE000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xE69E4141, 0x939E4040, 0x369C3B3B, 0x00A74343, 0x00C06766, 0x00E29794, 0x00FCCECB, 0x00FFFFFF, 0x00FAB6BA, 0x00F7A29F, 0x00F3BCB3, 0x00AE413F, 0x604B1A1A, 0xD5000000, 0xFB000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xDEA04444, 0x839E4040, 0x199E3C3C, 0x00A94747, 0x00C26B6A, 0x00EAA6A2, 0x00FFCECB, 0x00FFD8D5, 0x00FFF8F6, 0x00FCE5E7, 0x00F17B7F, 0x00FAC4B3, 0x00FFE1D9, 0x00D67B73, 0x178B2D2E, 0xAB100606, 0xEF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCD9E4444,
        0x5E9E4141, 0x109F3E3E, 0x00AD4F4E, 0x00D2817F, 0x00F1B1AD, 0x00FFD0CC, 0x00FFD9D7, 0x00FFDBD9, 0x00FFEBE9, 0x00FFFEFF, 0x00F39398, 0x00F3A099, 0x00FFD3C1, 0x00FFDCD2, 0x00F9C2BB, 0x00AF4340, 0x56591F1F, 0xD4000000, 0xFB000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCB9E4444, 0x569C4141, 0x0B9F4040,
        0x00AD5150, 0x00D58683, 0x00F5B7B3, 0x00FFD4D0, 0x00FFD8D5, 0x00FFDCD9, 0x00FFE2DF, 0x00FFE7E5, 0x00FFFFFF, 0x00F9CDD0, 0x00ED7E7F, 0x00FACFBF, 0x00FFD5C5, 0x00FFD4C5, 0x00FFE1DA, 0x00D0726C, 0x18872B2C, 0xAE000000, 0xED000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF5A34A4A, 0xA69D4545, 0x4B9C4142, 0x08A04444, 0x00B75F5E, 0x00D88C89,
        0x00F6BAB6, 0x00FFD4D0, 0x00FFD9D6, 0x00FFDDDA, 0x00FFE2E0, 0x00FFE7E5, 0x00FFEBEA, 0x00FFF6F5, 0x00FEFBFB, 0x00EF8E93, 0x00F3B0A8, 0x00FFD8CB, 0x00FFD6CA, 0x00FFD5C8, 0x00FFDFD6, 0x00F7B9B2, 0x01B04240, 0x5D521C1D, 0xD6000000, 0xFB000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED9A4848, 0xA09D4748, 0x2C9B4343, 0x00A4494A, 0x00BA6463, 0x00DE9592, 0x00FEC4C0, 0x00FFD4D1,
        0x00FFD9D6, 0x00FFDDDB, 0x00FFE3E1, 0x00FFE8E7, 0x00FFECEB, 0x00FFF0EF, 0x00FFF4F3, 0x00FFFFFF, 0x00F5B7BB, 0x00EC8B8B, 0x00FCD8CD, 0x00FFD8CD, 0x00FFD7CE, 0x00FFD8CE, 0x00FFD9D0, 0x00FFE3E0, 0x00D37671, 0x148C2C2D, 0xAD0B0404, 0xEE000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE79C4A4A, 0x789C4848, 0x209C4445, 0x00A54C4C, 0x00C77573, 0x00EBA7A4, 0x00FFC9C4, 0x00FFD6D2, 0x00FFD9D6, 0x00FFDFDC,
        0x00FFE4E2, 0x00FFE9E7, 0x00FFEDEC, 0x00FFF1F0, 0x00FFF3F3, 0x00FFF5F5, 0x00FFFEFE, 0x00FDF1F2, 0x00EA7D82, 0x00F4BFB8, 0x00FFDDD4, 0x00FFD9D1, 0x00FFD9D2, 0x00FFDAD3, 0x00FFDAD3, 0x00FFE3DF, 0x00F5B7B3, 0x00AD3E3C, 0x4E632222, 0xD1000000,
        0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC49C4C4C, 0x699A4949, 0x1B9C4546, 0x00AD5757, 0x00CD7E7C, 0x00EDAAA7, 0x00FFCDC9, 0x00FFD6D2, 0x00FFDAD8, 0x00FFDFDD, 0x00FFE4E2, 0x00FFEAE8,
        0x00FFEEEC, 0x00FFF1F0, 0x00FFF4F3, 0x00FFF7F6, 0x00FFF8F7, 0x00FFFBFB, 0x00FFFFFF, 0x00F1A9AC, 0x00EB9A99, 0x00FDDDD6, 0x00FFDCD5, 0x00FFDCD6, 0x00FFDCD7, 0x00FFDCD7, 0x00FFDDD8, 0x00FFDED9, 0x00FFE0DD, 0x00D77671, 0x0F922F2F, 0xA2130707,
        0xEA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC09C4C4C, 0x549B4949, 0x049D494A, 0x00AF5C5B, 0x00D18381, 0x00F7B6B3, 0x00FFCFCB, 0x00FFD6D3, 0x00FFDAD8, 0x00FFE0DE, 0x00FFE6E4, 0x00FFEAE8, 0x00FFEEEC, 0x00FFF2F1,
        0x00FFF4F3, 0x00FFF7F6, 0x00FFF9F9, 0x00FFFBFA, 0x00FFFCFB, 0x00FFFFFF, 0x00F9DDDE, 0x00E5787C, 0x00F7CFCB, 0x00FFE0DB, 0x00FFDDDA, 0x00FFDDDA, 0x00FFDEDB, 0x00FFDFDC, 0x00FFDEDC, 0x00FFDEDD, 0x00FFE6E5, 0x00F9BBB7, 0x00B1413F, 0x53551D1D,
        0xD0000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFBA15252, 0xB89D4E4E, 0x4D9A4949, 0x02A04C4C, 0x00B15F5E, 0x00D68B89, 0x00FABCB7, 0x00FFCFCB, 0x00FFD5D2, 0x00FFDAD7, 0x00FFE0DE, 0x00FFE5E3, 0x00FFEAE9, 0x00FFEEED, 0x00FFF2F1, 0x00FFF4F3, 0x00FFF7F6,
        0x00FFF9F9, 0x00FFFAFA, 0x00FFFBFB, 0x00FFFDFC, 0x00FFFFFF, 0x00FFFFFF, 0x00EC9A9E, 0x00EA9FA0, 0x00FFE2DE, 0x00FFDFDD, 0x00FFDFDD, 0x00FFDFDE, 0x00FFE0DF, 0x00FFE0E0, 0x00FFE0E0, 0x00FFE1E1, 0x00FFE3E3, 0x00FFE1DE, 0x00DB7873, 0x11902E2E,
        0xA2180909, 0xEC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xCD9B5050, 0x369D4F4F, 0x00A05252, 0x00B97170, 0x00E29F9C, 0x00FCBFBB, 0x00FFD0CC, 0x00FFD4D2, 0x00FFDAD7, 0x00FFE0DE, 0x00FFE6E4, 0x00FFEBEA, 0x00FFEFEE, 0x00FFF2F1, 0x00FFF5F5, 0x00FFF7F7, 0x00FFF9F9, 0x00FFFBFA,
        0x00FFFBFC, 0x00FFFDFD, 0x00FFFDFD, 0x00FFFEFE, 0x00FFFFFF, 0x00F6CED0, 0x00E38286, 0x00F8D8D7, 0x00FFE2E1, 0x00FFE1E1, 0x00FFE2E2, 0x00FFE3E2, 0x00FFE3E3, 0x00FFE4E4, 0x00FFE4E4, 0x00FFE5E5, 0x00FFE5E5, 0x00FFE9E8, 0x00FABCB6, 0x00B24240,
        0x4E5F2020, 0xCF000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x2F9F4F4F, 0x00A65C5C, 0x00D4ACAC, 0x00F9F0F0, 0x00FFFFFF, 0x00FFFDFC, 0x00FFF7F5, 0x00FFF3F2, 0x00FFF0EF, 0x00FFF0EF, 0x00FFF1EF, 0x00FFF3F2, 0x00FFF4F4, 0x00FFF8F7, 0x00FFF9F9, 0x00FFFBFA, 0x00FFFCFC, 0x00FFFDFD,
        0x00FFFDFD, 0x00FFFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FEF9F9, 0x00E58388, 0x00ECB0B2, 0x00FFE7E7, 0x00FFE4E4, 0x00FFE4E4, 0x00FFE5E5, 0x00FFE6E6, 0x00FFE7E7, 0x00FFE7E7, 0x00FFE8E8, 0x00FFE8E8, 0x00FFE8E8, 0x00FFEAEA, 0x00FFE2DD, 0x00D7716D,
        0x138D2D2D, 0xA50D0505, 0xEB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x289D5050, 0x00A35656, 0x00AC6868, 0x00B26D6D, 0x00C79090, 0x00D8B0B1, 0x00E4C8C9, 0x00F0DFE0, 0x00FAF3F3, 0x00FDFBFB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFE, 0x00FFFEFE, 0x00FFFEFE, 0x00FFFEFE,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2BFC1, 0x00E1898D, 0x00F9E1E1, 0x00FFE7E7, 0x00FFE7E7, 0x00FFE8E8, 0x00FFE9E9, 0x00FFE9E9, 0x00FFEAEA, 0x00FFEAEA, 0x00FFEBEB, 0x00FFEBEB, 0x00FFEBEB, 0x00FFECEC, 0x00FFEEEC, 0x00FCB5AE,
        0x00B54242, 0x4F5F2020, 0xD1000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x947F4242, 0x00A24E4E, 0x00D59F9E, 0x00D0A99F, 0x00BC7A6F, 0x00B46F67, 0x00B56E69, 0x00B66A68, 0x00B96869, 0x00C37A7C, 0x00D19192, 0x00DDA6A8, 0x00EAC8C9, 0x00F8E9E9, 0x00FDF8F8, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FCEDED, 0x00DF7A7E, 0x00EFC8C9, 0x00FFECEB, 0x00FFE9E9, 0x00FFEAEA, 0x00FFEBEB, 0x00FFEBEB, 0x00FFECEC, 0x00FFECEC, 0x00FFEDED, 0x00FFEDED, 0x00FFEDED, 0x00FFEEEE, 0x00FFEEEE, 0x00FFF0F0, 0x00FFE2DB,
        0x00DA7470, 0x0D952E2F, 0xA2120606, 0xEA000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xE7271414, 0x2C8F4646, 0x00D58382, 0x00FFEDE6, 0x00FDCFBC, 0x00F7C9B7, 0x00EEC0B2, 0x00E5B6AA, 0x00DDABA1, 0x00D39B94, 0x00CB8985, 0x00C37978, 0x00C47676, 0x00C87778, 0x00CF8182, 0x00D89092, 0x00E2A2A4, 0x00EAB6B8,
        0x00F2CECF, 0x00F9E6E7, 0x00FDF6F6, 0x00E89EA0, 0x00E09396, 0x00FDEBEA, 0x00FFEBEB, 0x00FFECEC, 0x00FFEDED, 0x00FFEEEE, 0x00FFEEEE, 0x00FFEFEF, 0x00FFEFEF, 0x00FFF0F0, 0x00FFF0F0, 0x00FFF0F0, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF1F2, 0x00FFF1EE,
        0x00F9B1A9, 0x00B43F40, 0x50591E1E, 0xCF000000, 0xFA000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFB000000, 0x9C6D3737, 0x00A54E4D, 0x00F3C1C0, 0x00FFE7DB, 0x00FFD2C1, 0x00FFD7C9, 0x00FFDACE, 0x00FFDBD1, 0x00FFDBD1, 0x00FEDBD3, 0x00FAD9D3, 0x00F0CDC8, 0x00E7BEBC, 0x00DFB4B3, 0x00DAA6A7, 0x00D38F91, 0x00D18184,
        0x00D27C7F, 0x00D6777B, 0x00DC797E, 0x00DA7B7F, 0x00F1D8D8, 0x00FFF0EF, 0x00FFEDED, 0x00FFEEEE, 0x00FFEFEF, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF1F1, 0x00FFF2F2, 0x00FFF2F2, 0x00FFF2F2, 0x00FFF3F3, 0x00FFF3F3, 0x00FFF3F3, 0x00FFF2F3, 0x00FFF5F6,
        0x00FFE1D8, 0x00DE706D, 0x0E942F30, 0xA01A0909, 0xEA000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xEB1F1111, 0x22924646, 0x00D08181, 0x00FFF1ED, 0x00FFD5C7, 0x00FFD4C8, 0x00FFD7CC, 0x00FFD8CF, 0x00FFDAD3, 0x00FFDCD6, 0x00FFDED9, 0x00FFE0DD, 0x00FFE3E1, 0x00FFE3E3, 0x00FFE4E4, 0x00FDE5E5, 0x00F7E0E0,
        0x00EED5D5, 0x00EACCCC, 0x00E5BEBF, 0x00EACDCE, 0x00FEEEEE, 0x00FFEFEF, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF1F1, 0x00FFF2F2, 0x00FFF3F3, 0x00FFF3F3, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF4F4, 0x00FFF4F4, 0x00FFF4F4, 0x00FFF6F6,
        0x00FFF2ED, 0x00FAAFA4, 0x00B33E3F, 0x4D5D1F1F, 0xCE000000, 0xF9000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFC000000, 0x92743B3B, 0x01AB5050, 0x00F8BFBE, 0x00FFE7DE, 0x00FFD5CA, 0x00FFD8CF, 0x00FFD9D2, 0x00FFDBD5, 0x00FFDDD8, 0x00FFDEDB, 0x00FFDFDE, 0x00FFE1E1, 0x00FFE3E4, 0x00FFE6E6, 0x00FFE8E8, 0x00FFEBEB,
        0x00FFEEEE, 0x00FFEEEE, 0x00FFEFEF, 0x00FFF0F0, 0x00FFF0F0, 0x00FFF0F0, 0x00FFF2F2, 0x00FFF3F3, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF5F5, 0x00FFF5F5, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF5F5, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF8F8,
        0x00FFFBFC, 0x00FFE3D6, 0x00DD6E6B, 0x12912D2E, 0xAA000000, 0xEC000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xEE000000, 0x2F8C4343, 0x00CC7777, 0x00FFEDEA, 0x00FFDAD1, 0x00FFDAD2, 0x00FFDBD6, 0x00FFDCD8, 0x00FFDEDB, 0x00FFE0DF, 0x00FFE1E1, 0x00FFE4E4, 0x00FFE6E6, 0x00FFE8E8, 0x00FFEAEA, 0x00FFECEC,
        0x00FFEDED, 0x00FFEEEE, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF2F2, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF6F6, 0x00FFF7F7, 0x00FFF7F7, 0x00FFF7F7, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF9F9, 0x00FFFAFA, 0x00FFF6F2,
        0x00FFE0D8, 0x00F9AFA6, 0x00D76665, 0x079E3434, 0x89000000, 0xDD000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFB000000, 0x92773D3D, 0x00A84E4E, 0x00F6C5C7, 0x00FFECE7, 0x00FFDAD3, 0x00FFDDD8, 0x00FFDEDC, 0x00FFE0DF, 0x00FFE1E1, 0x00FFE4E4, 0x00FFE7E7, 0x00FFE9E9, 0x00FFEAEA, 0x00FFECEC, 0x00FFEEEE,
        0x00FFEFEF, 0x00FFF0F0, 0x00FFF2F2, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF6F6, 0x00FFF7F7, 0x00FFF7F7, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF8F8, 0x00FFF9F9, 0x00FFFAFA, 0x00FFFBFB, 0x00FFF8F7, 0x00FFDFDC, 0x00F6ACA7,
        0x00D96A6A, 0x00B33E3F, 0x0A933030, 0x324F1B1B, 0x89000000, 0xDB000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xEA261313, 0x2C8D4444, 0x00D17576, 0x00FFE9E8, 0x00FFDFDB, 0x00FFDEDB, 0x00FFDFDE, 0x00FFE1E1, 0x00FFE4E4, 0x00FFE7E7, 0x00FFE9E9, 0x00FFEBEB, 0x00FFECEC, 0x00FFEDED, 0x00FFF0F0,
        0x00FFF1F1, 0x00FFF1F1, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF6F6, 0x00FFF7F7, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF8F8, 0x00FFF9F9, 0x00FFF9F9, 0x00FFFBFB, 0x00FFFCFC, 0x00FFF6F6, 0x00FFDADC, 0x00F19A9C, 0x00CC5E5F, 0x00AE3C3E,
        0x0E872C2D, 0x32481818, 0x5C0F0505, 0x81000000, 0xB6000000, 0xEC000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0x9B6F3838, 0x00A84C4D, 0x00F3BCBD, 0x00FFEFED, 0x00FFDEDC, 0x00FFE1E1, 0x00FFE4E4, 0x00FFE7E7, 0x00FFE9E9, 0x00FFEBEB, 0x00FFEDED, 0x00FFEEEE, 0x00FFF0F0, 0x00FFF1F1,
        0x00FFF2F2, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF6F6, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF8F8, 0x00FFF9F9, 0x00FFF9F9, 0x00FFFAFA, 0x00FFFCFC, 0x00FFFEFE, 0x00FFF7F8, 0x00FFDADB, 0x00ED9699, 0x00C7595B, 0x00AA393A, 0x0F852D2D, 0x34431717,
        0x60090303, 0x85000000, 0xAC000000, 0xCF000000, 0xEB000000, 0xFB000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEB201010, 0x27914544, 0x00CF7A7C, 0x00FFF1F1, 0x00FFE3E3, 0x00FFE3E3, 0x00FFE7E7, 0x00FFE9E9, 0x00FFEBEB, 0x00FFEDED, 0x00FFEFEF, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF3F3,
        0x00FFF4F4, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF8F8, 0x00FFF9F9, 0x00FFF9F9, 0x00FFFAFA, 0x00FFFDFD, 0x00FFFDFD, 0x00FFF5F5, 0x00FDCCCD, 0x00E48F90, 0x00C35758, 0x03A13839, 0x19732828, 0x3B3D1616, 0x65050202, 0x87000000,
        0xAF000000, 0xD3000000, 0xEC000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0x92763B3B, 0x01AB4E4F, 0x00F7BBBD, 0x00FFF3F2, 0x00FFE4E4, 0x00FFE8E8, 0x00FFEBEB, 0x00FFEDED, 0x00FFEFEF, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF3F3, 0x00FFF4F4,
        0x00FFF5F5, 0x00FFF6F6, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF9F9, 0x00FFF9F9, 0x00FFFBFB, 0x00FFFCFC, 0x00FFFEFE, 0x00FFEFEF, 0x00FAC5C7, 0x00E28688, 0x00BA4B4C, 0x059A3535, 0x1C6E2828, 0x48250F0F, 0x70000000, 0x92000000, 0xB5000000, 0xD5000000,
        0xEE000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEE000000, 0x2F8D4242, 0x00CC6E70, 0x00FFEEEE, 0x00FFEBEB, 0x00FFE9E9, 0x00FFEDED, 0x00FFEEEE, 0x00FFF0F0, 0x00FFF1F1, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF5F5,
        0x00FFF6F6, 0x00FFF7F7, 0x00FFF8F8, 0x00FFF9F9, 0x00FFFBFB, 0x00FFFEFE, 0x00FFFEFE, 0x00FFECEC, 0x00F8B3B5, 0x00D37071, 0x00B14747, 0x07943636, 0x29572121, 0x501C0C0C, 0x75000000, 0x98000000, 0xBF000000, 0xDD000000, 0xF1000000, 0xFB000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0x95763B3B, 0x00A84A4C, 0x00F0ACAF, 0x00FFF6F6, 0x00FFEBEB, 0x00FFEEEE, 0x00FFF0F0, 0x00FFF2F2, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF7F7,
        0x00FFF8F8, 0x00FFF9F9, 0x00FFFAFA, 0x00FFFDFD, 0x00FFFCFC, 0x00FFE0E2, 0x00EEA7A9, 0x00CE6C6D, 0x00AF4344, 0x11823030, 0x2E50201F, 0x56170909, 0x7C000000, 0xA2000000, 0xC5000000, 0xE1000000, 0xF4000000, 0xFE000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEB201010, 0x2C8E4443, 0x00C4686A, 0x00FFECED, 0x00FFF1F0, 0x00FFEFEF, 0x00FFF1F1, 0x00FFF3F3, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF8F8,
        0x00FFFAFA, 0x00FFFEFE, 0x00FFFBFB, 0x00FFDBDD, 0x00ECA0A3, 0x00C75D5F, 0x00A64041, 0x127F3131, 0x37401A1A, 0x64020101, 0x86000000, 0xAA000000, 0xCB000000, 0xE6000000, 0xF7000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0x9A723938, 0x00A64C4C, 0x00F4B7BA, 0x00FFFAFA, 0x00FFF0F0, 0x00FFF2F2, 0x00FFF4F4, 0x00FFF5F5, 0x00FFF6F6, 0x00FFF9F9, 0x00FFFEFD,
        0x00FFF8F9, 0x00FED8D9, 0x00E89799, 0x00C2585A, 0x02A14040, 0x147A3131, 0x393C1919, 0x67000000, 0x89000000, 0xB2000000, 0xD5000000, 0xEC000000, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED120909, 0x368B4242, 0x00CC696C, 0x00FFE8EA, 0x00FFF5F5, 0x00FFF3F3, 0x00FFF5F5, 0x00FFF9F9, 0x00FFFEFE, 0x00FFF8F8, 0x00FDCACD,
        0x00DD8386, 0x00B75354, 0x049B3E3F, 0x1F652B2B, 0x452B1313, 0x6D000000, 0x8D000000, 0xB4000000, 0xD7000000, 0xEF000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xAA673333, 0x02A44748, 0x00EEADB0, 0x00FFFDFD, 0x00FFF7F7, 0x00FFFDFD, 0x00FFEDEE, 0x00F5BCBF, 0x00D67E81, 0x00B55051,
        0x0A8E3A3A, 0x265C2828, 0x4C231010, 0x74000000, 0x98000000, 0xBD000000, 0xDB000000, 0xF0000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF2000000, 0x328D4342, 0x00C96D70, 0x00FFF0F2, 0x00FFECED, 0x00F1B3B5, 0x00D16D70, 0x00AB494A, 0x0C873939, 0x2A522525,
        0x5A0E0606, 0x7D000000, 0xA1000000, 0xC4000000, 0xE1000000, 0xF4000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0xA86B3434, 0x00A85050, 0x00DC7D81, 0x00C3686A, 0x00A74949, 0x14793636, 0x383F1D1C, 0x60090404, 0x82000000,
        0xA7000000, 0xCC000000, 0xE6000000, 0xF6000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF2000000, 0x5C7E3E3E, 0x158D4242, 0x1F6E3333, 0x3F371A1A, 0x69000000, 0x8C000000, 0xB1000000, 0xD2000000,
        0xEA000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0xE2000000, 0xAD000000, 0x8E000000, 0x98000000, 0xB8000000, 0xD9000000, 0xEF000000, 0xFB000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9000000, 0xE9000000, 0xDF000000, 0xE4000000, 0xF2000000, 0xFC000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmEmailRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acEmailRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmPassword
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acPasswordRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE91121A0, 0x7F1118A2, 0x2A0F2AAE, 0x4A0F1BA4, 0xD8060B43, 0xF6000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB1327A5, 0xA8131D9D, 0x231226A8, 0x000B6ACD, 0x0002C3F6, 0x000879D7, 0x350D1292, 0xC9030520, 0xF5000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC314229B, 0x3F1423A0, 0x000F4FBB, 0x0005AAEA, 0x0000E1FF, 0x0000DEFF, 0x0000DDFF, 0x00086ED4, 0x310C1090, 0xC8030523, 0xF5000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD7152999, 0x50152298, 0x03114DB5, 0x0006A4E5, 0x0000D9FE, 0x0000DEFF, 0x0000D0FF, 0x0000CFFF, 0x0000D9FF, 0x0000DEFF, 0x00067CDB, 0x240B109A, 0xBB04073C, 0xF3000000, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xEC172E93, 0x72182696, 0x0E143AA5, 0x000A89D5, 0x0001D3FB, 0x0000E1FF, 0x0000D2FF, 0x0000CAFF, 0x0000CDFF, 0x0000D4FF, 0x0000D9FF, 0x0000DDFF, 0x0000E0FF, 0x000680DE, 0x270A119A, 0xC2020320, 0xF4000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xBD182F93, 0x2118329B, 0x000E75C7, 0x0003C3F3, 0x0000E3FF, 0x0000D4FF, 0x0000C7FF, 0x0000C7FF, 0x0000CDFF, 0x0000D4FF, 0x0000D9FF, 0x0000DFFF, 0x0000DCFF, 0x0000DCFF, 0x0000E7FF, 0x00057BDD, 0x29090B99, 0xC4020325, 0xF5000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xE0183193, 0x24193298, 0x00089FDE, 0x0000E3FF, 0x0000D7FF, 0x0000C8FF, 0x0000C1FF, 0x0000C5FF, 0x0000CDFF, 0x0000D4FF, 0x0000D9FF, 0x0002D9FE, 0x0004A5DF, 0x000872C9, 0x00067CCE, 0x0001B0E8, 0x0000D9FE, 0x000577DE, 0x27090F9F, 0xBA030541,
        0xF3000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0x4F1B2B90, 0x000D85CC, 0x0000E2FF, 0x0000CCFF, 0x0000BBFF, 0x0000BEFF, 0x0000C7FF, 0x0000CEFF, 0x0000D4FF, 0x0000D9FF, 0x0006E0FF, 0x0022A6DF, 0x060D22A0, 0x1D0A0F72, 0x160C128B, 0x000A3CBA, 0x0001A8E9, 0x0000DCFF, 0x000480E4, 0x20080DA5,
        0xBD020330, 0xF2000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x931B2F8E,
        0x00135EB1, 0x0001D7FC, 0x0000D3FF, 0x0000B9FF, 0x0000BFFF, 0x0000C7FF, 0x0000CEFF, 0x0000D4FF, 0x0000DAFF, 0x0000DCFF, 0x0023E0FF, 0x003B61C0, 0x2F05094C, 0x69000000, 0x88000000, 0x2C0B0F8D, 0x00076BD3, 0x0000D0FF, 0x0000CDFF, 0x000379E5,
        0x2907099D, 0xBF02032C, 0xF4000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDC1B348E, 0x1519439D,
        0x0005C0EE, 0x0000DDFF, 0x0000C1FF, 0x0000BEFF, 0x0000C7FF, 0x0000CEFF, 0x0000D4FF, 0x0000DAFF, 0x0000E1FF, 0x0000DFFF, 0x0037E1FF, 0x013D58BE, 0x47030732, 0x9C000000, 0xD2000000, 0x7A0A0C84, 0x000957CE, 0x0001D9FF, 0x000DCAFF, 0x0000BFFF,
        0x000368E2, 0x290609A0, 0xBF020335, 0xF4000000, 0xFE000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA1C3992, 0x461C3590, 0x0008A6DF,
        0x0000E1FF, 0x0000C7FF, 0x0000BEFF, 0x0000C8FF, 0x0000CEFF, 0x0000D3FF, 0x0000DAFF, 0x0000E0FF, 0x0000E7FF, 0x0000E6FF, 0x001DE4FF, 0x0071A4E0, 0x1D061086, 0xA7010210, 0xE802041E, 0x490D0EA9, 0x000682DE, 0x0008DEFF, 0x0057F6FF, 0x002AD3FF,
        0x0000B4FF, 0x000272E9, 0x1C050BAE, 0xB202034B, 0xF2000000, 0xFE000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x8A1D3188, 0x001273BA, 0x0000DFFF,
        0x0000CFFF, 0x0000BFFF, 0x0000C7FF, 0x0000CEFF, 0x0000D4FF, 0x0000DAFF, 0x0000E0FF, 0x0000E7FF, 0x0000EDFF, 0x0000F2FF, 0x0000E0FF, 0x0045DFFE, 0x00678CD8, 0x131C26A7, 0x1C1521AE, 0x000A61D3, 0x0000C9FA, 0x0025D9FF, 0x0076FFFF, 0x0080FDFF,
        0x002DC8FF, 0x0000AEFF, 0x00026FEA, 0x2D040BA0, 0xCA000000, 0xF5000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC51D3789, 0x0B1757A4, 0x0002CEF6, 0x0000D7FF,
        0x0000C2FF, 0x0000C5FF, 0x0000CEFF, 0x0000D4FF, 0x0000DAFF, 0x0000DFFF, 0x0000E6FF, 0x0000ECFF, 0x0000F2FF, 0x0000FAFF, 0x0001F9FF, 0x0002DDFF, 0x002CDAFF, 0x003CBEF6, 0x0021B1F5, 0x0003C8FE, 0x0014D1FF, 0x0067F6FF, 0x0082FFFF, 0x009CFFFF,
        0x005BD9FF, 0x0000A7FF, 0x000086F3, 0x15030EA6, 0x9D000000, 0xE5000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x5C1D368A, 0x000B9ED6, 0x0000E5FF, 0x0000CBFF,
        0x0000C4FF, 0x0000CEFF, 0x0000D4FF, 0x0000DAFF, 0x0000E0FF, 0x0000E6FF, 0x0000ECFF, 0x0000F3FF, 0x0000F9FF, 0x0002FEFF, 0x000FFFFF, 0x001CFCFF, 0x0017E9FF, 0x0013DDFF, 0x001ADBFF, 0x0036E1FF, 0x0069F8FF, 0x0084FFFF, 0x0096FFFF, 0x0090F5FF,
        0x0013AFFF, 0x000097FC, 0x00042ED0, 0x37020250, 0x93000000, 0xE1000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xA0193079, 0x01155CAE, 0x0002CAF9, 0x0000DBFF,
        0x0000D0FF, 0x0000D4FF, 0x0000DAFF, 0x0000E1FF, 0x0000E7FF, 0x0000ECFF, 0x0000F3FF, 0x0000F9FF, 0x0001FEFF, 0x000FFFFF, 0x0020FFFF, 0x0030FFFF, 0x0042FFFF, 0x0052FFFF, 0x0063FFFF, 0x0075FFFF, 0x0083FFFF, 0x0092FFFF, 0x00A8FFFF, 0x0044C7FF,
        0x0000A0FF, 0x000353DD, 0x1B040381, 0x68000000, 0xB2000000, 0xEF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF0000000, 0x5B17286F, 0x001164BB, 0x0000D5FF,
        0x0000DAFF, 0x0000D9FF, 0x0000E0FF, 0x0000E7FF, 0x0000EDFF, 0x0000F2FF, 0x0000F9FF, 0x0001FEFF, 0x000EFFFF, 0x001FFFFF, 0x002FFFFF, 0x003FFFFF, 0x004FFFFF, 0x0060FFFF, 0x0070FFFF, 0x0080FFFF, 0x0090FFFF, 0x00ACFFFF, 0x007BE2FF, 0x0000A2FF,
        0x00007CF0, 0x090512AF, 0x4D01021F, 0x95000000, 0xDC000000, 0xFB000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xDD03050D, 0x2B182C7F, 0x000A7BD3,
        0x0000D8FF, 0x0000D8FF, 0x0000E3FF, 0x0000EDFF, 0x0000F7FF, 0x0000FDFF, 0x0001FEFF, 0x000FFFFF, 0x001EFFFF, 0x002FFFFF, 0x0040FFFF, 0x004FFFFF, 0x005FFFFF, 0x0070FFFF, 0x0080FFFF, 0x008FFFFF, 0x00A6FFFF, 0x00A5F7FF, 0x001CAEFF, 0x000096FD,
        0x000531CD, 0x3203034F, 0x7C000000, 0xC7000000, 0xF5000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED122359, 0x331B2F86, 0x000E80C9,
        0x0000D7FF, 0x0000DAFF, 0x0000EBFF, 0x0000F8FF, 0x0000C2DC, 0x0001D8E6, 0x000FFFFF, 0x001FFFFF, 0x002FFFFF, 0x0040FFFF, 0x0051FFFF, 0x0060FFFF, 0x0070FFFF, 0x0081FFFF, 0x0091FFFF, 0x00A2FFFF, 0x00B9FFFF, 0x004AC2FF, 0x00009BFF, 0x00025EE0,
        0x1D05077B, 0x68000000, 0xAE000000, 0xEA000000, 0xFE000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED1B3987, 0x461E318A, 0x001178BE, 0x0000DAFF,
        0x0000DCFF, 0x0000EBFF, 0x0000F8FD, 0x0000BEDD, 0x00008AE1, 0x0007B4E5, 0x001FFFFE, 0x002FFFFF, 0x003FFFFF, 0x0050FFFF, 0x0060FFFF, 0x0070FFFF, 0x0080FFFF, 0x0090FFFF, 0x00A4FFFF, 0x00C1FFFF, 0x007EDEFF, 0x00009CFF, 0x000081F1, 0x070718AE,
        0x4E01021D, 0x97000000, 0xDD000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE21D3B88, 0x3C1E368A, 0x001080C2, 0x0001D8FE, 0x0000DEFF,
        0x0000ECFF, 0x0000F6FD, 0x0000BBDE, 0x000093E8, 0x0007C4FF, 0x001CFAFF, 0x0030FFFF, 0x0040FFFF, 0x0050FFFF, 0x0060FFFF, 0x0071FFFF, 0x0081FFFF, 0x0090FFFF, 0x00A9FFFF, 0x00AEFBFF, 0x005FD0FF, 0x000BA1FF, 0x000091FB, 0x000637CC, 0x2D040657,
        0x7A000000, 0xC6000000, 0xF6000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEC1D3D84, 0x3D1E3B8A, 0x000F8AC8, 0x0000DCFF, 0x0000DDFF, 0x0000EEFF,
        0x0000F5FC, 0x0000B6D9, 0x000097EA, 0x0008CCFF, 0x001FFEFF, 0x0030FFFF, 0x0041FFFF, 0x0052FFFF, 0x0061FFFF, 0x0071FFFF, 0x0082FFFF, 0x0093FFFF, 0x00ACFFFF, 0x0096F0FF, 0x002CB4FF, 0x000099FF, 0x000088F5, 0x000640CC, 0x19080B81, 0x65000000,
        0xAB000000, 0xE9000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE81F3F84, 0x3D203884, 0x001088C4, 0x0000DDFF, 0x0000DCFF, 0x0000EDFF, 0x0000F3FA,
        0x0000B2D8, 0x000097ED, 0x0008CDFF, 0x001FFFFF, 0x0030FFFF, 0x003FFFFF, 0x0053FFFF, 0x0062FEFF, 0x0072FEFF, 0x0085FFFF, 0x0095FFFF, 0x00AAFFFF, 0x0078E2FF, 0x000FA6FF, 0x00009AFF, 0x000179EB, 0x01082DBF, 0x1F070972, 0x5C000108, 0x99000000,
        0xDA000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE5204182, 0x36213C84, 0x001283BE, 0x0000D9FF, 0x0000DCFF, 0x0000EDFF, 0x0000F3FA, 0x0000AED7,
        0x00009CEF, 0x0007CCFF, 0x001EFCFF, 0x0031FFFF, 0x003FFFFF, 0x0052FFFF, 0x005EFCFF, 0x0029D2FF, 0x0028CBFF, 0x0081F6FF, 0x009DFCFF, 0x004FCDFF, 0x0001A1FF, 0x000094FB, 0x000460D9, 0x050A1DAB, 0x3005084D, 0x68000000, 0x9D000000, 0xD6000000,
        0xF7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE4214381, 0x2B224085, 0x001092CA, 0x0000DBFF, 0x0000DDFF, 0x0000EFFF, 0x0000EEF7, 0x0000ABD6, 0x0000A4F4,
        0x0009D6FF, 0x0020FFFF, 0x0030FEFF, 0x003EFCFF, 0x0058FFFF, 0x0055F7FF, 0x0016C5FF, 0x0000AFFF, 0x0000ABFF, 0x0013B3FF, 0x001DB3FF, 0x0000A0FF, 0x000088F1, 0x00093DC2, 0x160A1281, 0x4B02041B, 0x79000000, 0xAD000000, 0xDE000000, 0xF8000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE422447F, 0x36224181, 0x000F96CB, 0x0000DEFF, 0x0000DCFF, 0x0000EFFF, 0x0000ECF5, 0x0000A8D5, 0x0000A4F3, 0x000ADAFF,
        0x0020FFFF, 0x0032FFFF, 0x0017DBFF, 0x000ECAFF, 0x0036E2FF, 0x0013C3FF, 0x0000B1FF, 0x00047EE1, 0x000569D5, 0x00009CFB, 0x0000A0FF, 0x000372E1, 0x010C2CB3, 0x27080D5B, 0x5F000104, 0x90000000, 0xC4000000, 0xE9000000, 0xFB000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE023477D, 0x3024417D, 0x00128CC0, 0x0000DCFF, 0x0000DDFF, 0x0000EFFF, 0x0000E8F3, 0x0000A7D5, 0x0000A8F4, 0x0008D7FF, 0x0021FFFF,
        0x0030FEFF, 0x0015D9FF, 0x0000C0FF, 0x0000BAFF, 0x0000B8FF, 0x0000B3FF, 0x000670D7, 0x0612279C, 0x0A121D92, 0x00094EC3, 0x000759CE, 0x080E1F9C, 0x38060A3C, 0x6D000000, 0xA3000000, 0xD7000000, 0xF5000000, 0xFE000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD625487C, 0x2D234980, 0x001194C7, 0x0000DBFF, 0x0000DEFF, 0x0000F0FF, 0x0000E5F1, 0x0000AADA, 0x0000B0F6, 0x000DE1FF, 0x0023FFFF, 0x0029F8FF,
        0x000FD3FF, 0x0000C2FF, 0x00048EE3, 0x000966CA, 0x000098EF, 0x000966CE, 0x10112083, 0x46040822, 0x68010209, 0x3A0B1468, 0x290C1573, 0x5303061E, 0x7E000000, 0xB3000000, 0xE3000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDE25497B, 0x2E244B7E, 0x000F9ECD, 0x0000DEFF, 0x0000DDFF, 0x0000F2FF, 0x0000E1EF, 0x0000A5D6, 0x0000B2F7, 0x0003D1FF, 0x0010EBFF, 0x0017E8FF, 0x0009CEFF,
        0x0000C1FF, 0x00048AE0, 0x07133395, 0x1213217B, 0x011335A6, 0x17101F78, 0x5501030C, 0x88000000, 0xB9000000, 0xC4000000, 0xAC000000, 0xA7000000, 0xC8000000, 0xEC000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD9254A7B, 0x2826487A, 0x00119AC8, 0x0000DEFF, 0x0000DCFF, 0x0000F0FF, 0x0000DDED, 0x0000A4D7, 0x0000B7F9, 0x0005D6FF, 0x0001D0FF, 0x0000C8FF, 0x0000C2FF, 0x0000BEFF,
        0x00077ED7, 0x06153092, 0x3A081031, 0x6A000000, 0x65060D2B, 0x7601030A, 0x96000000, 0xCD000000, 0xF2000000, 0xF7000000, 0xEC000000, 0xEA000000, 0xF5000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xD9254A7B, 0x28254A7C, 0x001296C3, 0x0000DCFF, 0x0000DDFF, 0x0000F1FF, 0x0000DEEF, 0x0000A6DA, 0x0000BAFB, 0x000CE6FF, 0x0011ECFF, 0x0000C9FF, 0x00068CDB, 0x000A67C4, 0x000C5EBD,
        0x0A152E86, 0x41070D25, 0x79000000, 0xB1000000, 0xCD000000, 0xCE000000, 0xDD000000, 0xF5000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xD725497C, 0x20264C7F, 0x000EA5D2, 0x0000DDFF, 0x0000DEFF, 0x0000F2FF, 0x0000D6E8, 0x0000A8DB, 0x0000C0FC, 0x000DEBFF, 0x0025FFFF, 0x0014E2FF, 0x0000BBFC, 0x02174C9F, 0x21121E56, 0x330E1845,
        0x59040816, 0x85000000, 0xBC000000, 0xEA000000, 0xFB000000, 0xFB000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xD325497C, 0x23264A7D, 0x000EA5D1, 0x0000DDFF, 0x0000DDFF, 0x0000F2FF, 0x0000D3E7, 0x0000A9DC, 0x0000C5FD, 0x000DECFF, 0x001EFFFF, 0x0021F0FF, 0x000AD2FF, 0x0002ACF1, 0x0E17367E, 0x5C010103, 0x8C000000,
        0xB0000000, 0xCF000000, 0xEF000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xDA244B7C, 0x23254D7F, 0x00109BC8, 0x0000DDFF, 0x0000DDFF, 0x0000F3FF, 0x0000D1E6, 0x0000AADD, 0x0000C7FE, 0x0002D4FF, 0x0007DFFF, 0x0004D4FF, 0x0000CAFF, 0x0000C5FF, 0x000897E2, 0x1A152B6A, 0x7C000000, 0xC7000000,
        0xEF000000, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x98254677, 0x001C6F9F, 0x0000D9FE, 0x0000DFFF, 0x0000F0FF, 0x0000CDE4, 0x0000AFE2, 0x0002D0FF, 0x0003D9FF, 0x0000CEFF, 0x0000B6F8, 0x00019EE9, 0x000588D7, 0x000B70C3, 0x001450A6, 0x2C101F4F, 0x92000000, 0xE0000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x85244271, 0x00187EAD, 0x0000DBFF, 0x0000ECFF, 0x0000C9E1, 0x0000AEE1, 0x0001D2FF, 0x000DEFFF, 0x0004DAFF, 0x0001B9F4, 0x001457A5, 0x071A3C83, 0x13182F6B, 0x23142456, 0x3E0C1534, 0x72010204, 0xB3000000, 0xEC000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x75254070, 0x00148BB8, 0x0000E2FF, 0x0000BEDF, 0x0000B4E2, 0x0000D4FF, 0x0010F4FF, 0x0013EFFF, 0x0000CFFF, 0x000C8DCF, 0x1E172752, 0x57010205, 0x74000000, 0x8B000000, 0x9E000000, 0xBC000000, 0xE1000000, 0xFA000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x63244273, 0x001198C5, 0x0000BEE9, 0x0000B6E5, 0x0000D7FF, 0x000CEFFF, 0x001DFEFF, 0x0009D6FF, 0x0000BAFA, 0x03185597, 0x49080F1C, 0x8F000000, 0xC5000000, 0xDD000000, 0xE6000000, 0xF2000000, 0xFB000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x4F234A7A, 0x000BABD5, 0x0000CDF4, 0x0000DEFF, 0x0000D9FF, 0x0002D8FF, 0x0003D2FF, 0x0000CEFF, 0x000B92D5, 0x1719305F, 0x70000000, 0xC0000000, 0xF5000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x3A234E7F, 0x0006A0DD, 0x0000B4F1, 0x0000ACEC, 0x0000A8EC, 0x0000A5EC, 0x0000A2ED, 0x00009FED, 0x01185EA0, 0x3D0C162A, 0x92000000, 0xDD000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x62214776, 0x0E1C4D89, 0x0B194883, 0x09194882, 0x09194882, 0x09194882, 0x09184882, 0x071A4886, 0x1C18315B, 0x6D000101, 0xB9000000, 0xF2000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xE9000000, 0xB1000000, 0x86000000, 0x7C000000, 0x7D000000, 0x7D000000, 0x7D000000, 0x7C000000, 0x81000000, 0xA6000000, 0xDF000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xF9000000, 0xE7000000, 0xD9000000, 0xD6000000, 0xD6000000, 0xD6000000, 0xD6000000, 0xD6000000, 0xD8000000, 0xE6000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmPasswordRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acPasswordRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmRead
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acReadRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD3F1A52, 0xC8BE5000, 0x9EBE5608, 0x7CC56015, 0x5AD06C23, 0x3CD77835, 0x26E28645, 0x19E98F51, 0x0DEC9357, 0x11EC9254, 0x1FE48848,
        0x3BD4722B, 0x68BD580E, 0xB2933D00, 0xCB973E00, 0x8FC35707, 0x63CE6416, 0x4DD06A1E, 0x4FCF6A1E, 0x5FC56014, 0x78BC590D, 0x93B14D03, 0xB1A34200, 0xD78D3700, 0xF4542100, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xEE000074, 0x9E000072, 0x70000073, 0x565C2A44, 0x06FAB07D, 0x00FFDEC7, 0x00FFEDE0, 0x00FFFBF7, 0x00FFFFFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFEFB, 0x00FFECD9, 0x04B37C55, 0x06A9734B, 0x00FFE2C7, 0x00FFF6EE, 0x00FFFCF7, 0x00FFFBF5, 0x00FFF8F1, 0x00FFECDF, 0x00FFD9BE, 0x01FDC49B, 0x07F0A974, 0x23D98A4E, 0x4FC76E2A, 0x7FB25008, 0xCB863400, 0xF33A1802, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFA00007A, 0x3D00007A, 0x000000B9, 0x003A158C, 0x00F69B59, 0x00FFF9ED, 0x00FFFFFF, 0x00FEFFFF, 0x00FDFFFF, 0x00FCFEFE, 0x00FAFCFD, 0x00FAFBFC, 0x00FAFBFC, 0x00FAFCFD, 0x00FCFEFE,
        0x00FDFFFF, 0x00FFFFFF, 0x00A3A4A4, 0x00969797, 0x00FFFFFF, 0x00FDFFFF, 0x00FCFEFF, 0x00FCFEFF, 0x00FDFFFF, 0x00FEFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFD8B7, 0x03FBA156, 0x32B25924, 0x9505005C, 0xBA000062,
        0xF2000025, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xB2000072, 0x00000098, 0x000000FF, 0x004E1E9D, 0x00FFB776, 0x00FFFFFA, 0x00F9FAFC, 0x00F8F8F8, 0x00F8F8F8, 0x00F9F9F9, 0x00F8F8F8, 0x00F7F7F7, 0x00F7F7F7, 0x00F8F8F8, 0x00F7F7F7,
        0x00F6F6F6, 0x00FFFFFF, 0x00A1A1A1, 0x00929292, 0x00FFFFFF, 0x00F5F5F5, 0x00F5F5F5, 0x00F5F5F5, 0x00F5F5F5, 0x00F4F4F4, 0x00F4F4F4, 0x00F4F4F4, 0x00F5F6F6, 0x00F8F9FA, 0x00FCFFFF, 0x00FFF7EB, 0x00FFF2CE, 0x00C87D64, 0x000400E0, 0x000000CA,
        0x5F000064, 0xE1000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0x79000071, 0x000000AE, 0x000000FF, 0x005F288E, 0x00FFC17E, 0x00FFFFFB, 0x00FAFBFC, 0x00F9F9F8, 0x00F7F7F7, 0x00F7F7F6, 0x00F7F6F6, 0x00F6F6F6, 0x00F6F6F6, 0x00F6F6F5, 0x00F6F6F5,
        0x00F7F7F7, 0x00FFFFFF, 0x00A1A1A1, 0x00929292, 0x00FFFFFF, 0x00F6F6F6, 0x00F6F6F6, 0x00F6F6F6, 0x00F6F6F5, 0x00F5F5F6, 0x00F5F5F5, 0x00F6F6F6, 0x00F5F5F5, 0x00F4F4F4, 0x00F4F5F7, 0x00FAF0E4, 0x00FFECC7, 0x00D5865F, 0x000700EA, 0x000000F7,
        0x0D000082, 0xA2000010, 0xE7000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0x5F00006F, 0x000000BA, 0x000000FF, 0x00713583, 0x00FFCE8C, 0x00FDFEFB, 0x00F9F9F8, 0x00F7F6F4, 0x00F6F5F3, 0x00F6F5F3, 0x00F7F5F3, 0x00F7F5F3, 0x00F7F5F3, 0x00F6F4F2, 0x00F5F3F1,
        0x00F6F4F2, 0x00FFFFFF, 0x009F9F9E, 0x00969695, 0x00FFFFFF, 0x00F9F7F7, 0x00FAF9F8, 0x00FAF9F9, 0x00FBFAF9, 0x00FAFAF9, 0x00F9F9F9, 0x00F7F7F7, 0x00F6F6F6, 0x00F5F5F5, 0x00F4F6F7, 0x00FAF1E7, 0x00FFEDCC, 0x00DF8F5D, 0x000D00DE, 0x000000F8,
        0x0300008E, 0x72000017, 0xD4000000, 0xFD000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0x4D000070, 0x000000C7, 0x000000FF, 0x00874374, 0x00FFD49C, 0x00FCFCFA, 0x00F9F7F4, 0x00F8F5F3, 0x00F7F4F1, 0x00F5F3F0, 0x00F5F3F0, 0x00F6F3F0, 0x00F5F2EF, 0x00F5F2EF, 0x00F6F2EF,
        0x00F6F3F0, 0x00FFFFFD, 0x009D9B9A, 0x00A2A1A0, 0x00FCFAF7, 0x00D7D5D4, 0x00C3C2C1, 0x00BCBBBA, 0x00BEBDBC, 0x00C9C7C5, 0x00DCDBDA, 0x00F2F0EF, 0x00FDFCFB, 0x00F7F6F5, 0x00F4F4F5, 0x00FAF3EA, 0x00FFEFCF, 0x00E5965F, 0x001803CD, 0x000000FD,
        0x02000095, 0x5E000020, 0xC8000000, 0xFA000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFE00007A, 0x3A000073, 0x000000D1, 0x000000FB, 0x009D5161, 0x00FFDBB0, 0x00FAF8F5, 0x00F9F5F0, 0x00F8F4EF, 0x00F9F4F0, 0x00F9F4F0, 0x00F9F4EF, 0x00F9F4EF, 0x00F8F4EF, 0x00F8F4EF, 0x00F7F3EE,
        0x00F6F2ED, 0x00FFFFFC, 0x00A1A09C, 0x00696867, 0x00969695, 0x009EA0A1, 0x00ADB1B3, 0x00AFB6B8, 0x00ADB4B6, 0x00A9ADAF, 0x0097999A, 0x00878787, 0x00ACABA9, 0x00F3F1EE, 0x00FDFDFC, 0x00FAF4EC, 0x00FFF0D4, 0x00EC9D63, 0x002709B7, 0x000000FF,
        0x0100009E, 0x52000027, 0xC1000000, 0xF8000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xF900007A, 0x29000079, 0x000000D9, 0x000000F1, 0x00B35F53, 0x00FEE0BD, 0x00F9F6F2, 0x00F9F3ED, 0x00F9F3ED, 0x00F9F4ED, 0x00F9F4ED, 0x00F9F4ED, 0x00F9F4ED, 0x00F9F3EC, 0x00F8F2EC, 0x00F8F2EC,
        0x00FEF8F1, 0x00C9C4BE, 0x00888786, 0x00A7ACAE, 0x00CED1D3, 0x00DACDC9, 0x00DAB6AA, 0x00D9A291, 0x00D49C8A, 0x00C9A297, 0x00BFB1AD, 0x00B6B9BB, 0x00919798, 0x00777776, 0x00C7C5C4, 0x00FFFEF8, 0x00FFF0D7, 0x00F2A466, 0x00330FA7, 0x000000FF,
        0x000000A5, 0x48000030, 0xBA000000, 0xF5000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xF200006F, 0x17000080, 0x000000E1, 0x000600E6, 0x00CA6F45, 0x00F9E0C5, 0x00F9F5F0, 0x00F9F2EB, 0x00F9F2EA, 0x00F9F2EA, 0x00F9F2EA, 0x00F9F2EA, 0x00F9F2E9, 0x00F8F1E9, 0x00F9F2EA, 0x00FCF5EC,
        0x00B1ADA8, 0x008D8E8E, 0x00CDD1D3, 0x00EACEC2, 0x00ECAC91, 0x00F3AC8B, 0x00F8B698, 0x00FBC1A7, 0x00FBC0A8, 0x00F6B096, 0x00E6997D, 0x00D0876D, 0x00BA9D93, 0x00A3AAAD, 0x00757676, 0x00B6B2AE, 0x00FFF2DA, 0x00F6AA67, 0x00411599, 0x000000FF,
        0x000000AD, 0x3F000037, 0xB4000000, 0xF2000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xE700006E, 0x0B000086, 0x000000E9, 0x001204D7, 0x00D77B47, 0x00F5E0CB, 0x00FAF5ED, 0x00F9F1E7, 0x00F9F0E7, 0x00F9F0E7, 0x00F8F0E6, 0x00F9F0E6, 0x00F9F0E6, 0x00F8EFE5, 0x00FFF7EC, 0x00C3BDB5,
        0x008F9090, 0x00DFE3E5, 0x00F0C2AD, 0x00FAAA7F, 0x00FFD5B1, 0x00FFEBD1, 0x00FFF1DB, 0x00FFF2E1, 0x00FFF5E5, 0x00FFF8E9, 0x00FFF4E3, 0x00FFDEC3, 0x00EB956F, 0x00B2816F, 0x00AAAFB1, 0x00606061, 0x00BEB1A0, 0x00FFB770, 0x004E1C89, 0x000000FF,
        0x000000B7, 0x3800003F, 0xAE000000, 0xEF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xD3000070, 0x0600008C, 0x000000F1, 0x001F0BC8, 0x00DD844D, 0x00F3E0CE, 0x00FCF5EB, 0x00FAF1E5, 0x00F9EFE4, 0x00F9EFE4, 0x00F9EFE4, 0x00FAEFE4, 0x00F9EFE3, 0x00FEF3E8, 0x00E2D9CF, 0x00838281,
        0x00D8DCDF, 0x00F4C9B3, 0x00FDB686, 0x00FFDDB6, 0x00FFE0BE, 0x00FFE2C3, 0x00FFE5CA, 0x00FFE8D0, 0x00FFE9D3, 0x00FFE9D3, 0x00FFE9D2, 0x00FFEBD3, 0x00FFE9CD, 0x00F6A77E, 0x00AF7D6A, 0x009FA4A8, 0x00606161, 0x00E79F57, 0x00602880, 0x000000FF,
        0x000000BF, 0x30000047, 0xA8000000, 0xEC000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xBD00006F, 0x02000093, 0x000000F7, 0x002E12B6, 0x00E48D56, 0x00F0E1D2, 0x00FCF4E9, 0x00FAEEE1, 0x00F8EDE0, 0x00F7ECE0, 0x00F6EBDF, 0x00F5EADD, 0x00F5EADD, 0x00FAEFE1, 0x009E9993, 0x00B6BBBE,
        0x00F5DACB, 0x00FEB683, 0x00FFD3A4, 0x00FFD7AD, 0x00FFD9B4, 0x00FFDDBA, 0x00FFE1C0, 0x00FFE2C4, 0x00FFE4C9, 0x00FFE4C9, 0x00FFE3C8, 0x00FFE1C3, 0x00FFE1C0, 0x00FFE2BE, 0x00EEA579, 0x00A28B81, 0x007E8991, 0x00955C1F, 0x00733376, 0x000000FF,
        0x000000CA, 0x28000050, 0xA1000000, 0xE9000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xA800006C, 0x0000009D, 0x000000FA, 0x003D17A6, 0x00EB965C, 0x00EEE1D5, 0x00FCF3E6, 0x00FAEDDF, 0x00F8ECDE, 0x00F7EBDC, 0x00F6EADC, 0x00F6EADB, 0x00FAEDDE, 0x00DBCFC2, 0x00858585, 0x00E4E3E4,
        0x00FDC097, 0x00FFC78F, 0x00FFD09D, 0x00FFD2A5, 0x00FFD6AB, 0x00FFDAB2, 0x00FFDCB7, 0x00FFDDBB, 0x00FFDFBD, 0x00FFDFBE, 0x00FFDEBC, 0x00FFDDBA, 0x00FFDBB5, 0x00FFDAB2, 0x00FFD3A4, 0x00C08262, 0x00969CA0, 0x00785830, 0x006B2A62, 0x000000FF,
        0x000000D4, 0x1E00005B, 0x9B000000, 0xE5000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x9400006D, 0x000000A8, 0x000000FD, 0x004D1F97, 0x00EF9E62, 0x00EDE4DA, 0x00FBEFE1, 0x00F7EADA, 0x00F7E9D9, 0x00F7E9D9, 0x00F7E8D9, 0x00F7E9D9, 0x00FFF1E0, 0x00B6ADA2, 0x00A3A6A9, 0x00F2E3D9,
        0x00FFC898, 0x00FFCF9B, 0x00FFCB96, 0x00FFCE9B, 0x00FFD2A2, 0x00FFD4A9, 0x00FFD7AD, 0x00FFD8B0, 0x00FFDAB2, 0x00FFDAB3, 0x00FFD9B2, 0x00FFD8AF, 0x00FFD6AB, 0x00FFD4A7, 0x00FFD5A5, 0x00F1A673, 0x009E8B83, 0x00726E5D, 0x00441B5A, 0x000000FC,
        0x000000DA, 0x15000067, 0x92000000, 0xE0000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x7F00006E, 0x000000B3, 0x000000FF, 0x005E288A, 0x00F0A366, 0x00EDE6DF, 0x00F9EBDA, 0x00F8E9D7, 0x00F8E9D7, 0x00F9E9D8, 0x00F9EAD8, 0x00F9E9D7, 0x00FFF0DD, 0x00A7A098, 0x00BDC2C7, 0x00FADFCA,
        0x00FFD3A6, 0x00FFD5A6, 0x00FFCD9A, 0x00FFCB95, 0x00FFCC99, 0x00FFCF9E, 0x00FFD1A2, 0x00FFD3A5, 0x00FFD5A7, 0x00FFD5A7, 0x00FFD4A7, 0x00FFD3A5, 0x00FFD1A1, 0x00FFCE9D, 0x00FFCE99, 0x00FFC18A, 0x00A5816E, 0x00737971, 0x00241358, 0x000501F6,
        0x000000DD, 0x0D000074, 0x85000007, 0xDC000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x6900006E, 0x000000BE, 0x000000FF, 0x006C3180, 0x00F0A96D, 0x00F0E9E3, 0x00F8E9D6, 0x00F7E7D4, 0x00F8E8D5, 0x00F9E8D5, 0x00F9E8D5, 0x00FAE9D6, 0x00FBE9D6, 0x00A39D95, 0x00C8CCD0, 0x00FDDFC5,
        0x00FFDAB2, 0x00FFDBB2, 0x00FFD4A7, 0x00FFCF9D, 0x00FFCB97, 0x00FFCB95, 0x00FFCC98, 0x00FFCE9B, 0x00FFCF9D, 0x00FFCF9D, 0x00FFCE9C, 0x00FFCD9A, 0x00FFCC97, 0x00FFCB95, 0x00FFCD99, 0x00FFD29E, 0x00AA836A, 0x00767D76, 0x001D1353, 0x000400F1,
        0x000000E1, 0x06000082, 0x7900000C, 0xD5000000, 0xFE000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x4B000072, 0x000000C8, 0x000000FC, 0x007C3C77, 0x00ECAE7A, 0x00F5EFE7, 0x00FBE9D3, 0x00F9E7D2, 0x00F9E7D2, 0x00F9E7D2, 0x00F9E7D2, 0x00F9E7D2, 0x00F9E6D1, 0x00A29B94, 0x00C7CBCE, 0x00FCE0C5,
        0x00FFE0BE, 0x00FFE1BF, 0x00FFDBB4, 0x00FFD7AC, 0x00FFD3A5, 0x00FFCF9E, 0x00FFCD99, 0x00FFCC97, 0x00FFCB96, 0x00FFCB96, 0x00FFCB97, 0x00FFCC97, 0x00FFCE9A, 0x00FFD1A0, 0x00FFD5A7, 0x00FFDCAE, 0x00AC886E, 0x00747A75, 0x00231548, 0x000501ED,
        0x000000E5, 0x0300008C, 0x6A000015, 0xCE000000, 0xFD000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFC00007A, 0x31000078, 0x000000CF, 0x000000F4, 0x008C466C, 0x00E6B288, 0x00F8F0E7, 0x00FBE6CF, 0x00F9E5CF, 0x00F8E4CE, 0x00F7E3CD, 0x00F7E3CD, 0x00F7E3CD, 0x00FBE7CF, 0x00A39B91, 0x00BBBFC3, 0x00FAE4CC,
        0x00FFE4C4, 0x00FFE7CB, 0x00FFE3C2, 0x00FFDFBA, 0x00FFDBB3, 0x00FFD8AE, 0x00FFD6A9, 0x00FFD4A5, 0x00FFD2A3, 0x00FFD2A3, 0x00FFD2A4, 0x00FFD4A6, 0x00FFD6AA, 0x00FFD9B0, 0x00FFDDB7, 0x00FFDEB7, 0x00A48771, 0x006A716E, 0x00331939, 0x000C04E8,
        0x000000E9, 0x02000094, 0x5D00001F, 0xC8000000, 0xFB000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xF200006F, 0x1600007E, 0x000000D5, 0x000000ED, 0x009B4F63, 0x00E1B897, 0x00F8EDE0, 0x00FAE4CB, 0x00F7E3CB, 0x00F6E2CA, 0x00F6E2CA, 0x00F6E2CA, 0x00F7E2CA, 0x00FFEAD0, 0x00ADA294, 0x00A6A9AC, 0x00EEE0D1,
        0x00FFE6C7, 0x00FFEAD1, 0x00FFE8CC, 0x00FFE6C9, 0x00FFE3C2, 0x00FFE0BD, 0x00FFDEB9, 0x00FFDCB7, 0x00FFDBB4, 0x00FFDBB4, 0x00FFDBB5, 0x00FFDDB7, 0x00FFDFBA, 0x00FFE1BF, 0x00FFE7C9, 0x00FAD4AC, 0x00998779, 0x0063645E, 0x00612825, 0x000E06E5,
        0x000000ED, 0x0100009B, 0x53000027, 0xC1000000, 0xF8000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xE3000070, 0x0A000083, 0x000000DA, 0x000000E5, 0x00A85759, 0x00DDBCA5, 0x00F9EBDB, 0x00FAE2C8, 0x00F8E2C8, 0x00F7E1C8, 0x00F6E0C5, 0x00F5E0C5, 0x00F6E0C7, 0x00FCE6CB, 0x00C8B6A2, 0x00878889, 0x00DAD6D1,
        0x00FFE3C3, 0x00FFECD5, 0x00FFEAD1, 0x00FFE8CE, 0x00FFE8CD, 0x00FFE7CA, 0x00FFE5C7, 0x00FFE4C4, 0x00FFE3C3, 0x00FFE3C3, 0x00FFE3C3, 0x00FFE4C5, 0x00FFE6C9, 0x00FFE7CB, 0x00FFEDD1, 0x00DEB792, 0x00878482, 0x0061574A, 0x00A4471F, 0x000B04DF,
        0x000000EF, 0x000000A2, 0x48000030, 0xBA000000, 0xF5000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xCD000071, 0x05000087, 0x000000E0, 0x000300DF, 0x00B46152, 0x00DBBFAC, 0x00F9E7D3, 0x00F9E1C4, 0x00F8E1C5, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E1C4, 0x00F8E0C4, 0x00FAE1C5, 0x00F3DCBF, 0x0089847D, 0x00B3B6B9,
        0x00F1DDC8, 0x00FFEACE, 0x00FFEDD8, 0x00FFEBD3, 0x00FFEAD1, 0x00FFE9D0, 0x00FFE9CF, 0x00FFE9CE, 0x00FFE8CE, 0x00FFE8CE, 0x00FFE8CE, 0x00FFE9CE, 0x00FFE9CF, 0x00FFEDD5, 0x00FFE5C3, 0x009B8876, 0x0072787C, 0x00655547, 0x00CF6933, 0x000B02D9,
        0x000000F0, 0x000000A9, 0x3F000037, 0xB4000000, 0xF2000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xB900006E, 0x0100008C, 0x000000E5, 0x000901DA, 0x00BF6A4B, 0x00D9C0B2, 0x00FAE6CE, 0x00F9DFC2, 0x00F8E0C3, 0x00F9E1C3, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E1C4, 0x00FFE7CA, 0x00C1B19D, 0x00818384,
        0x00CDCCCB, 0x00F8E1C7, 0x00FFEFD7, 0x00FFEFDB, 0x00FFEDD7, 0x00FFECD6, 0x00FFEBD5, 0x00FFEBD3, 0x00FFEBD3, 0x00FFEBD3, 0x00FFEBD3, 0x00FFEBD4, 0x00FFEED8, 0x00FFF2D6, 0x00CEB091, 0x00868685, 0x0047494B, 0x00907768, 0x00D77539, 0x001104CE,
        0x000000EF, 0x000000B1, 0x3800003F, 0xAE000000, 0xEF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xA300006D, 0x00000093, 0x000000E9, 0x001206CF, 0x00C87047, 0x00D8C2B7, 0x00FCE7CE, 0x00F9E1C3, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E1C3, 0x00F9E1C4, 0x00F9E0C4, 0x00FAE1C4, 0x00F9E1C4, 0x009D9387,
        0x0094979A, 0x00CCC8C2, 0x00F4DFC9, 0x00FFF0D9, 0x00FFF2E0, 0x00FFEFDD, 0x00FFEEDB, 0x00FFEED9, 0x00FFEED9, 0x00FFEEDA, 0x00FFEEDB, 0x00FFF2DF, 0x00FFEDD3, 0x00CEB398, 0x008A8581, 0x00606366, 0x006F6967, 0x00C2A598, 0x00D17136, 0x001E0ABC,
        0x000000F0, 0x000000BB, 0x30000047, 0xA8000000, 0xEC000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x8F00006E, 0x0000009B, 0x000000EC, 0x00270FB7, 0x00CB7648, 0x00D8C4BB, 0x00FFEACF, 0x00F9E1C3, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E1C4, 0x00F9E0C3, 0x00F8E0C3, 0x00F8E0C3, 0x00F8E0C3, 0x00FCE3C7, 0x00F2DBBE,
        0x0088827A, 0x008E9194, 0x00BBBAB8, 0x00E4D3C0, 0x00FCE8D1, 0x00FFF3DD, 0x00FFF4E2, 0x00FFF4E2, 0x00FFF4E2, 0x00FFF5E1, 0x00FFF2DB, 0x00F4DBC1, 0x00B5A38F, 0x00858584, 0x00737577, 0x00716A5E, 0x00E7DACD, 0x00BAA198, 0x00D17537, 0x002C10AB,
        0x000000F0, 0x000000C2, 0x2A00004F, 0xA2000000, 0xE9000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x7B00006F, 0x000000A4, 0x000000EE, 0x003A17A1, 0x00CE7B4B, 0x00DACAC1, 0x00FFEACE, 0x00F9E1C2, 0x00F9E0C2, 0x00F9E0C2, 0x00F9E0C2, 0x00FAE0C2, 0x00FAE0C1, 0x00FAE0C1, 0x00F9E0C1, 0x00F8DFC1, 0x00FCE3C4,
        0x00EDD7BA, 0x0091897F, 0x0089898A, 0x00A1A3A5, 0x00B9B4B0, 0x00D5C7B6, 0x00E2D1BD, 0x00E9D7C2, 0x00E6D4BF, 0x00DAC7B2, 0x00BEAF9E, 0x00928E89, 0x008B8D8E, 0x00B5B6B7, 0x009E9F9E, 0x00736A76, 0x00D9CAD8, 0x00B69F97, 0x00D1783B, 0x0039149B,
        0x000000F0, 0x000000CD, 0x23000057, 0x9D000000, 0xE6000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x60000070, 0x000000AD, 0x000000F1, 0x004F208A, 0x00D18554, 0x00DDD6D3, 0x00FFF0D9, 0x00FBEBD6, 0x00FBECD9, 0x00F9ECDB, 0x00F8EBDD, 0x00F6EBDF, 0x00F6ECE1, 0x00F6ECE1, 0x00F7EBE0, 0x00F8EBDE, 0x00F9EBDB,
        0x00FEEDD8, 0x00FFF2D9, 0x00868078, 0x006C6F70, 0x007F8285, 0x008A8F93, 0x00939698, 0x00949696, 0x00909192, 0x00878A8D, 0x00767C7F, 0x00626668, 0x00696B68, 0x00B2B2AE, 0x00DFDED9, 0x00878EC1, 0x003945D7, 0x006F60A5, 0x00DA833B, 0x00481A8B,
        0x000000F1, 0x000000D2, 0x1C000060, 0x97000000, 0xE3000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x45000073, 0x000000B4, 0x000000F3, 0x00662C72, 0x00EB8E3D, 0x00DFB08D, 0x00EBB992, 0x00E3AF89, 0x00DAA986, 0x00D3A381, 0x00CF9D7B, 0x00CB9877, 0x00C99775, 0x00C99775, 0x00C49778, 0x00C7997A, 0x00CD9F80,
        0x00D5A989, 0x00EFC39E, 0x00947359, 0x00AC8D74, 0x00A98569, 0x0094785F, 0x00877460, 0x007E6F5C, 0x00796A58, 0x00796755, 0x00806856, 0x00A1846F, 0x006F606B, 0x00777692, 0x00C9C2BD, 0x00EAEBE2, 0x0087DAF9, 0x002440D0, 0x00C76D3C, 0x005A2479,
        0x000000F0, 0x000000D3, 0x17000069, 0x91000000, 0xE0000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x2C000076, 0x000000B9, 0x000000EF, 0x0047208F, 0x009B4A4D, 0x00904153, 0x008E3F50, 0x008C3E4F, 0x008B3E4F, 0x008B3E50, 0x008B3E50, 0x008B3E50, 0x008A3D4F, 0x008A3D4F, 0x00853B51, 0x007F3857, 0x007D3555,
        0x007C3454, 0x007B3353, 0x00874056, 0x008C4253, 0x00883B52, 0x008E3F4E, 0x008C3E49, 0x008F3E42, 0x00934241, 0x009A4842, 0x00A75143, 0x00B1563A, 0x00652B64, 0x006682D6, 0x00B8D6CA, 0x0099DCE6, 0x0060F3FF, 0x00359FF5, 0x005E276C, 0x005A2577,
        0x000000F0, 0x000000D6, 0x11000073, 0x8B000000, 0xDD000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x26000077, 0x000707C1, 0x000202E5, 0x000000E0, 0x000000DF, 0x000000DF, 0x000000DF, 0x000000E1, 0x000000E2, 0x000001E3, 0x000405E5, 0x000809E6, 0x000E0FE8, 0x001617EA, 0x001E1FED, 0x002427F0, 0x002A2CF2,
        0x002F31F2, 0x003C3EF2, 0x005355F3, 0x005354F1, 0x004041F1, 0x003A3BEE, 0x003636EC, 0x002E2CE8, 0x001F1DE3, 0x001311E0, 0x000B09DB, 0x000301D9, 0x000000CB, 0x002548CF, 0x004DC5FF, 0x002CB6FF, 0x003BC8FF, 0x005DF4FF, 0x002661D7, 0x000000C8,
        0x000000E4, 0x000A0AD8, 0x0C01017F, 0x86000000, 0xDA000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x4A00006F, 0x001414B9, 0x002424F2, 0x001F20ED, 0x002122EE, 0x002526EE, 0x002B2CEF, 0x003031EF, 0x003636EE, 0x003B3CEB, 0x004142E9, 0x004647E7, 0x004848E3, 0x004546DF, 0x004344DB, 0x003F40D7, 0x003838D2,
        0x003232CC, 0x001F1FBD, 0x000506A3, 0x000A0AA5, 0x002727BE, 0x003A3BCD, 0x004C4CD8, 0x005B5CE1, 0x00696AE7, 0x006F70EC, 0x006A6BF0, 0x006566F3, 0x006363F5, 0x003431CE, 0x000045E1, 0x000F88FF, 0x002BABFF, 0x004BDEFF, 0x0057DEF9, 0x000B1EC7,
        0x00201CE0, 0x003535E1, 0x0A000085, 0x80000000, 0xD8000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xB8000052, 0x1102027A, 0x000E0E9D, 0x001313A5, 0x001313A7, 0x001111A5, 0x000D0D9E, 0x000B0B97, 0x00090993, 0x0106068F, 0x0204048B, 0x03010186, 0x03000081, 0x07000079, 0x0C00006E, 0x12000064, 0x1800005C,
        0x20000052, 0x2700004C, 0x3A000035, 0x42000030, 0x36000041, 0x2B00004D, 0x1F000058, 0x15000067, 0x0B000078, 0x04020287, 0x020A0A90, 0x0111119A, 0x001C1CA5, 0x001A17A8, 0x00000DAB, 0x00086CF7, 0x001792FF, 0x0034B8FF, 0x0056F0FF, 0x0044ADE8,
        0x003F3CCA, 0x002524D1, 0x0F000074, 0x80000000, 0xD7000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xF3000000, 0xB2000027, 0x5800003E, 0x3A000041, 0x34000040, 0x35000040, 0x3800003B, 0x3F000034, 0x4500002E, 0x4B000027, 0x52000021, 0x5B000019, 0x64000011, 0x6C00000A, 0x74000006, 0x7F000001, 0x87000000,
        0x8F000000, 0x96000000, 0xA0000000, 0xAB000000, 0xAF000000, 0xA7000000, 0x9D000000, 0x92000001, 0x84000008, 0x73000011, 0x6100001E, 0x53000027, 0x48000030, 0x3E000038, 0x2E000050, 0x030230C5, 0x000C81FF, 0x00229FFF, 0x0040CCFF, 0x005DF5FF,
        0x002050C7, 0x06000083, 0x3900002F, 0x8F000000, 0xDF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFD000000, 0xEE000000, 0xCF000000, 0xB4000000, 0xA9000000, 0xA7000000, 0xA7000000, 0xAB000000, 0xB0000000, 0xB4000000, 0xB9000000, 0xBF000000, 0xC5000000, 0xCA000000, 0xD0000000, 0xD6000000, 0xDA000000,
        0xDF000000, 0xE3000000, 0xE8000000, 0xED000000, 0xEF000000, 0xEB000000, 0xE6000000, 0xE1000000, 0xDB000000, 0xD4000000, 0xCB000000, 0xC2000000, 0xBB000000, 0xB3000000, 0xAB000000, 0x3C000076, 0x00044FDF, 0x00128CFF, 0x002CADFF, 0x004BDDFF,
        0x0056D9F6, 0x0B0D1D96, 0x5E000014, 0xB0000000, 0xEE000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFE000000, 0xF9000000, 0xF1000000, 0xEC000000, 0xEB000000, 0xEC000000, 0xED000000, 0xEF000000, 0xF2000000, 0xF4000000, 0xF7000000, 0xF9000000, 0xFB000000, 0xFD000000, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xFB000000, 0xF8000000, 0xF5000000, 0xF2000000, 0xED000000, 0xC9000025, 0x1C000D94, 0x00086BF3, 0x001995FF, 0x0035B9FF,
        0x0059F3FF, 0x0044A6E3, 0x35010068, 0xBC000000, 0xF3000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7000000, 0x9000005C, 0x020232BD, 0x000C83FF, 0x0022A0FF,
        0x0043D4FF, 0x0055DFF9, 0x20081179, 0xA7000000, 0xEA000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED000000, 0x4F00016E, 0x000551DC, 0x00138FFF,
        0x002DADFF, 0x001B51BD, 0x37000040, 0x9B000000, 0xE5000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xC800003B, 0x2D000B7B, 0x05062EAD,
        0x0E051886, 0x3000003D, 0x71000000, 0xB6000000, 0xEF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF6000000, 0xD1000000, 0x9A000000,
        0x7E000000, 0x8A000000, 0xB2000000, 0xE2000000, 0xFB000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF5000000, 0xE2000000,
        0xD7000000, 0xDD000000, 0xED000000, 0xFB000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmReadRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acReadRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmRemote
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acRemoteRad[] = {
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD50B0B0B, 0x9F0A0A0A, 0x6E0A0A0A, 0x3C0C0C0C,
        0x1B0D0D0D, 0x0C0D0D0D, 0x080E0E0E, 0x080E0E0E, 0x0B0D0D0D, 0x1A0D0D0D, 0x390B0B0B, 0x660A0A0A, 0x95090909, 0xCA070707, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE30B0B0B, 0x950B0B0B, 0x380D0D0D, 0x080F0F0F, 0x14111111, 0x28111111, 0x3E101010,
        0x4F0E0E0E, 0x5C0C0C0C, 0x600B0B0B, 0x600B0B0B, 0x5B0C0C0C, 0x4A0D0D0D, 0x380F0F0F, 0x23101010, 0x11101010, 0x060F0F0F, 0x310C0C0C, 0x84090909, 0xD4070707, 0xF8000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE30D0D0D, 0x770D0D0D, 0x120F0F0F, 0x13121212, 0x3D111111, 0x6F0B0B0B, 0x8A020202, 0x95000000, 0x9B000000,
        0xA1000000, 0xA7000000, 0xAD000000, 0xB0000000, 0xB0000000, 0xAD000000, 0xA7000000, 0xA1000000, 0x92030303, 0x690B0B0B, 0x32101010, 0x0F111111, 0x0E0F0F0F, 0x650B0B0B, 0xCF060606, 0xF7000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF90E0E0E, 0x7D0E0E0E, 0x11101010, 0x25131313, 0x6F0D0D0D, 0x8E030303, 0x95000000, 0xA1000000, 0xB6000000, 0xCB000000, 0xDA000000,
        0xE1000000, 0xE8000000, 0xEC000000, 0xEF000000, 0xEF000000, 0xEC000000, 0xE8000000, 0xE1000000, 0xDA000000, 0xCB000000, 0xB6000000, 0x97030303, 0x610C0C0C, 0x1C121212, 0x0E101010, 0x660B0B0B, 0xE1020202, 0xF9000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD80E0E0E, 0x31101010, 0x0A141414, 0x5F101010, 0x8F020202, 0x9B000000, 0xB3000000, 0xCC000000, 0xE0000000, 0xF0000000, 0xFB000000, 0xFF000000,
        0xF5373737, 0xED343434, 0xE0313131, 0xDF2F2F2F, 0xEA2C2C2C, 0xF22A2A2A, 0xFE000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xF0000000, 0xE0000000, 0xCB000000, 0xAB030303, 0x56101010, 0x06131313, 0x230E0E0E, 0xB4070707, 0xF2000000, 0xFE000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB50F0F0F, 0x12121212, 0x34141414, 0x82080808, 0x91000000, 0xAB000000, 0xD0000000, 0xEC000000, 0xFA000000, 0xF2343434, 0xAB353535, 0x5D353535, 0x32363636,
        0x1D393939, 0x10393939, 0x0D3A3A3A, 0x0D3A3A3A, 0x0F393939, 0x1B373737, 0x2E353535, 0x55323232, 0x9B2C2C2C, 0xE5191919, 0xFB000000, 0xFE000000, 0xFA000000, 0xEC000000, 0xD0000000, 0x8F090909, 0x26131313, 0x0D111111, 0x8D090909, 0xEA000000,
        0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x93101010, 0x07141414, 0x4E141414, 0x91010101, 0x9B000000, 0xC1000000, 0xE6000000, 0xF9000000, 0xCA363636, 0x67363636, 0x1E3A3A3A, 0x193B3B3B, 0x34393939, 0x4F303030,
        0x61262626, 0x6C1F1F1F, 0x731A1A1A, 0x721A1A1A, 0x6A1E1E1E, 0x5E262626, 0x482F2F2F, 0x2C373737, 0x143A3A3A, 0x18393939, 0x58313131, 0xB6272727, 0xF4000000, 0xFD000000, 0xF9000000, 0xE5000000, 0xBB020202, 0x42131313, 0x05131313, 0x6C0C0C0C,
        0xE3000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x93121212, 0x00151515, 0x62131313, 0x92000000, 0xA5000000, 0xD2000000, 0xF2000000, 0xE1343434, 0x60393939, 0x043D3D3D, 0x273D3D3D, 0x67292929, 0x910A0A0A, 0x9A000000, 0x9F000000,
        0xA8000000, 0xB1000000, 0xB7000000, 0xBB000000, 0xBB000000, 0xB7000000, 0xB1000000, 0xA7000000, 0x940A0A0A, 0x5F272727, 0x20393939, 0x033C3C3C, 0x4B313131, 0xC71B1B1B, 0xF5000000, 0xFC000000, 0xF2000000, 0xD2000000, 0x5C121212, 0x00151515,
        0x660C0C0C, 0xE0000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB7121212, 0x04161616, 0x61131313, 0x8F000000, 0xAA000000, 0xDB000000, 0xF8000000, 0xB8383838, 0x1E3D3D3D, 0x1E3F3F3F, 0x6B272727, 0x90010101, 0x9A000000, 0xB3000000, 0xCB000000, 0xDC000000,
        0xE6000000, 0xEC000000, 0xF2000000, 0xF4000000, 0xF4000000, 0xF2000000, 0xEC000000, 0xE6000000, 0xDC000000, 0xCB000000, 0xB2010101, 0x65252525, 0x163C3C3C, 0x14393939, 0x93242424, 0xEC000000, 0xFC000000, 0xF8000000, 0xDB000000, 0x5F131313,
        0x03151515, 0x80090909, 0xE3000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDB121212, 0x0C151515, 0x4D171717, 0x90000000, 0xAA000000, 0xDE000000, 0xFA000000, 0x913B3B3B, 0x043F3F3F, 0x463A3A3A, 0x8F070707, 0x95000000, 0xB2000000, 0xD4000000, 0xEC000000, 0xF9000000, 0xF9545454,
        0xE6606060, 0xC95E5E5E, 0xB85A5A5A, 0xB6585858, 0xC3545454, 0xDE4B4B4B, 0xF6383838, 0xFE000000, 0xFE000000, 0xF9000000, 0xEC000000, 0xD4000000, 0xAA0A0A0A, 0x39353535, 0x023E3E3E, 0x6B2B2B2B, 0xE3000000, 0xFB000000, 0xFA000000, 0xDE000000,
        0x48161616, 0x09141414, 0x9F060606, 0xEB000000, 0xFE000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFA131313, 0x2D151515, 0x31191919, 0x8F020202, 0xA4000000, 0xDB000000, 0xFA000000, 0x683D3D3D, 0x03424242, 0x6D2D2D2D, 0x92000000, 0xA2000000, 0xCB000000, 0xEC000000, 0xFB000000, 0xC6616161, 0x62626262, 0x25656565,
        0x0E676767, 0x0D686868, 0x0D676767, 0x0D676767, 0x0C666666, 0x0C666666, 0x20626262, 0x545A5A5A, 0xB1494949, 0xF3000000, 0xFD000000, 0xFB000000, 0xEC000000, 0xCB000000, 0x662A2A2A, 0x02414141, 0x49313131, 0xDC000000, 0xF8000000, 0xFA000000,
        0xD4050505, 0x29181818, 0x1C131313, 0xBE010101, 0xF2000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0x7D141414, 0x051B1B1B, 0x84090909, 0x99000000, 0xD1000000, 0xF8000000, 0x903D3D3D, 0x00444444, 0x74272727, 0x90000000, 0xAE000000, 0xDB000000, 0xF7000000, 0xD3616161, 0x44656565, 0x086A6A6A, 0x21666666, 0x564F4F4F,
        0x6F333333, 0x7F1F1F1F, 0x89131313, 0x8A131313, 0x801F1F1F, 0x6E323232, 0x4F494949, 0x1D636363, 0x05686868, 0x325D5D5D, 0xB53C3C3C, 0xF2000000, 0xFD000000, 0xF7000000, 0xDB000000, 0x79282828, 0x00434343, 0x602A2A2A, 0xDB000000, 0xFB000000,
        0xF8000000, 0xB30F0F0F, 0x031A1A1A, 0x4D0E0E0E, 0xD2000000, 0xF9000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xE8141414, 0x08191919, 0x5F151515, 0x8F000000, 0xC1000000, 0xF2000000, 0xBB3D3D3D, 0x00434343, 0x6D2F2F2F, 0x8F000000, 0xB1000000, 0xE3000000, 0xFB000000, 0x8E666666, 0x0B6A6A6A, 0x31676767, 0x74373737, 0x8F040404, 0x97000000,
        0xA8000000, 0xB9000000, 0xC4000000, 0xCA000000, 0xCA000000, 0xC4000000, 0xB9000000, 0xA7040404, 0x70333333, 0x265F5F5F, 0x07676767, 0x6B4F4F4F, 0xE6000000, 0xFA000000, 0xFB000000, 0xE3000000, 0x6F2F2F2F, 0x00424242, 0x80202020, 0xE3000000,
        0xFC000000, 0xF2000000, 0x6A171717, 0x05171717, 0x9B040404, 0xE6000000, 0xFE000000,
  0xFF000000, 0xFF000000, 0x76161616, 0x211C1C1C, 0x8C030303, 0xAA000000, 0xE6000000, 0xE43C3C3C, 0x18424242, 0x453E3E3E, 0x91000000, 0xAD000000, 0xE3000000, 0xF04E4E4E, 0x5D676767, 0x0B6D6D6D, 0x60515151, 0x92020202, 0x9A000000, 0xB6000000, 0xD4000000,
        0xE6000000, 0xF2000000, 0xF8000000, 0xFB000000, 0xFB000000, 0xF8000000, 0xF2000000, 0xE7000000, 0xD4000000, 0xB6010101, 0x584A4A4A, 0x086A6A6A, 0x41585858, 0xD1191919, 0xF7000000, 0xFB000000, 0xE3000000, 0x413C3C3C, 0x0F3F3F3F, 0xA6121212,
        0xEC000000, 0xFD000000, 0xDB0A0A0A, 0x1B1A1A1A, 0x48101010, 0xC8000000, 0xF8000000,
  0xFF000000, 0xE5161616, 0x0D1B1B1B, 0x70131313, 0x99000000, 0xD0000000, 0xF9000000, 0x5D414141, 0x16474747, 0x8F060606, 0xA1000000, 0xDB000000, 0xFB000000, 0x5B696969, 0x0C707070, 0x7E343434, 0x92000000, 0xAC000000, 0xD4000000, 0xEF000000, 0xFC000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xF0000000, 0xD5000000, 0x86363636, 0x086D6D6D, 0x3B585858, 0xD4000000, 0xF7000000, 0xFB000000, 0xD30F0F0F, 0x12454545, 0x38333333,
        0xC9000000, 0xF5000000, 0xF9000000, 0x88171717, 0x09191919, 0x9A060606, 0xE5000000,
  0xFF000000, 0x95171717, 0x0F1E1E1E, 0x8E040404, 0xB2000000, 0xEC000000, 0xCD404040, 0x00474747, 0x6C2C2C2C, 0x93000000, 0xCB000000, 0xF7000000, 0x8F6A6A6A, 0x07717171, 0x7D353535, 0x92000000, 0xB8000000, 0xE4000000, 0xFA000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA000000, 0xE4000000, 0x8F3B3B3B, 0x056E6E6E, 0x5B4A4A4A, 0xDA000000, 0xFA000000, 0xF7000000, 0x88353535, 0x00464646,
        0x83191919, 0xDF000000, 0xFD000000, 0xE00E0E0E, 0x0D1C1C1C, 0x5B0F0F0F, 0xCA000000,
  0xFE191919, 0x351A1A1A, 0x3C1D1D1D, 0x94000000, 0xCB000000, 0xFA000000, 0x65434343, 0x23484848, 0x8F000000, 0xB1000000, 0xEC000000, 0xD6696969, 0x02717171, 0x60545454, 0x90000000, 0xB7000000, 0xE9000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0xE9000000, 0x68565656, 0x01707070, 0x91282828, 0xE5000000, 0xFD000000, 0xEC000000, 0x21464646,
        0x3B333333, 0xBF000000, 0xF4000000, 0xFA000000, 0x441D1D1D, 0x23171717, 0xAE000000,
  0xD7191919, 0x041E1E1E, 0x6E131313, 0xA0000000, 0xE0000000, 0xF43F3F3F, 0x19484848, 0x68313131, 0x99000000, 0xD4000000, 0xFB000000, 0x426E6E6E, 0x2D6F6F6F, 0x8F040404, 0xAA000000, 0xE3000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFC00005C, 0xF1000053, 0xF1000053, 0xFB000045, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0xE10C0C0C, 0x2A696969, 0x25616161, 0xC0000000, 0xF2000000, 0xFB000000, 0x843D3D3D,
        0x11454545, 0x9F070707, 0xE3000000, 0xFE000000, 0xA01A1A1A, 0x031D1D1D, 0x82080808,
  0x9E1A1A1A, 0x12202020, 0x8B040404, 0xB6000000, 0xF0000000, 0xAC444444, 0x164A4A4A, 0x93090909, 0xB2000000, 0xEC000000, 0xC86D6D6D, 0x00767676, 0x76383838, 0x98000000, 0xD3000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF900005D,
        0x8B000057, 0x36000067, 0x13000082, 0x1200007E, 0x30000061, 0x7A00004C, 0xE5000015, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA000000, 0xA0505050, 0x00747474, 0x7B2E2E2E, 0xDA000000, 0xFD000000, 0xE11F1F1F,
        0x13484848, 0x6A252525, 0xCF000000, 0xFB000000, 0xE4111111, 0x101F1F1F, 0x5E0F0F0F,
  0x6D1C1C1C, 0x27212121, 0x95000000, 0xCC000000, 0xFB000000, 0x5B464646, 0x33474747, 0x99000000, 0xCB000000, 0xFA000000, 0x5F6F6F6F, 0x1F747474, 0x8D040404, 0xB6000000, 0xF0000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA00005D, 0x5301005E,
        0x00010BAE, 0x000012EA, 0x00020BFC, 0x000000F5, 0x000000D7, 0x00000099, 0x3A000052, 0xD300000B, 0xF7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xED141414, 0x1F717171, 0x37595959, 0xBC000000, 0xF3000000, 0xFA000000,
        0x39484848, 0x3A393939, 0xB8000000, 0xF0000000, 0xFB000000, 0x2D212121, 0x46141414,
  0x391E1E1E, 0x3E1F1F1F, 0x9B000000, 0xDA000000, 0xFE4A4A4A, 0x30494949, 0x4F3E3E3E, 0x9E000000, 0xDC000000, 0xF9626262, 0x23747474, 0x58585858, 0x96000000, 0xD4000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x71000059, 0x00010EB8,
        0x002F4BFF, 0x005871FF, 0x00152FFB, 0x000005FE, 0x000000F1, 0x000000DD, 0x00000091, 0x44000045, 0xD5000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0x74676767, 0x166E6E6E, 0xA0060606, 0xE3000000, 0xFE000000,
        0x69484848, 0x21434343, 0xA4010101, 0xE3000000, 0xFF000000, 0x4F222222, 0x31191919,
  0x19202020, 0x4E1C1C1C, 0xA0000000, 0xE1000000, 0xF54B4B4B, 0x1B4C4C4C, 0x61323232, 0xA7000000, 0xE6000000, 0xE5717171, 0x0D797979, 0x713A3A3A, 0xA8000000, 0xE7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDD000056, 0x09000087, 0x000114FC,
        0x005C76FC, 0x00ADBAFC, 0x002744F9, 0x000008FD, 0x000000F1, 0x000000DB, 0x000000AD, 0x04000064, 0x8C00001A, 0xE3000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xAC636363, 0x0B777777, 0x89181818, 0xD5000000, 0xFE000000,
        0x8F464646, 0x15494949, 0x96070707, 0xDA000000, 0xFF000000, 0x6F222222, 0x241D1D1D,
  0x0C232323, 0x5C181818, 0xA6000000, 0xE8000000, 0xEE474747, 0x104F4F4F, 0x6C292929, 0xB1000000, 0xED000000, 0xC9707070, 0x0D7C7C7C, 0x80232323, 0xB9000000, 0xF2000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x93000054, 0x000000B8, 0x00020CFF,
        0x001631FA, 0x002744F9, 0x000B20FB, 0x000004FC, 0x000000EC, 0x000000D7, 0x000000A5, 0x00000069, 0x45000034, 0xC6000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD0616161, 0x0C797979, 0x772C2C2C, 0xCA000000, 0xFB000000,
        0xA9474747, 0x0D4C4C4C, 0x8E0B0B0B, 0xD3000000, 0xFE000000, 0x8B232323, 0x211F1F1F,
  0x07242424, 0x60171717, 0xAC000000, 0xEC000000, 0xE0454545, 0x0D505050, 0x73242424, 0xB7000000, 0xF2000000, 0xB66E6E6E, 0x0D7D7D7D, 0x89151515, 0xC4000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x76000056, 0x000000BA, 0x000101FD,
        0x000008FD, 0x00000BFD, 0x000105FD, 0x000000F2, 0x000000E2, 0x000000CA, 0x00000091, 0x00000060, 0x2F00003A, 0xAD000000, 0xEE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE5636363, 0x0D7C7C7C, 0x6E363636, 0xC1000000, 0xF6000000,
        0xB9484848, 0x0B4E4E4E, 0x86121212, 0xCD000000, 0xFC000000, 0x96242424, 0x20212121,
  0x07252525, 0x5F181818, 0xB0000000, 0xEF000000, 0xDF444444, 0x0D525252, 0x72252525, 0xBB000000, 0xF4000000, 0xB56D6D6D, 0x0D7E7E7E, 0x8A161616, 0xCA000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x9600004D, 0x000000A1, 0x000000ED,
        0x000000EE, 0x000000F0, 0x000000EC, 0x000000E1, 0x000000D0, 0x000000AA, 0x00000073, 0x0000005D, 0x3A00002C, 0xA5000000, 0xEA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE76D6D6D, 0x0D7D7D7D, 0x6F373737, 0xBC000000, 0xF4000000,
        0xBB4C4C4C, 0x0C505050, 0x85121212, 0xC9000000, 0xFB000000, 0x97252525, 0x21222222,
  0x0B262626, 0x5A1A1A1A, 0xB0000000, 0xEF000000, 0xEB404040, 0x0F535353, 0x6A2B2B2B, 0xBB000000, 0xF4000000, 0xC4696969, 0x0C808080, 0x81262626, 0xCA000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xDC000037, 0x0F00006E, 0x000000C9,
        0x000000DA, 0x000000DA, 0x000000D6, 0x000000C7, 0x000000A9, 0x0000007D, 0x0000005F, 0x06000058, 0x5A00000C, 0xB1000000, 0xF0000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD87A7A7A, 0x0D7E7E7E, 0x782E2E2E, 0xBC000000, 0xF4000000,
        0xAE4F4F4F, 0x0E505050, 0x8C0B0B0B, 0xC9000000, 0xFB000000, 0x8F272727, 0x23232323,
  0x18252525, 0x4A1F1F1F, 0xAC000000, 0xEC000000, 0xF23F3F3F, 0x1A525252, 0x5E353535, 0xB7000000, 0xF2000000, 0xE0616161, 0x0B808080, 0x6E3D3D3D, 0xC4000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9000000, 0x6C000049, 0x00000082,
        0x000000AE, 0x000000B0, 0x000000A8, 0x00000092, 0x00000073, 0x00000060, 0x0000005E, 0x27000034, 0x7A000000, 0xCB000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xBA7C7C7C, 0x0C7F7F7F, 0x86191919, 0xC1000000, 0xF6000000,
        0x97525252, 0x17505050, 0x90070707, 0xCD000000, 0xFC000000, 0x75292929, 0x28222222,
  0x37242424, 0x38252525, 0xA7000000, 0xE8000000, 0xFD292929, 0x2D505050, 0x49434343, 0xB1000000, 0xED000000, 0xF6474747, 0x1E7D7D7D, 0x4F5D5D5D, 0xB9000000, 0xF2000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xE5000008, 0x4D000049,
        0x03000069, 0x00000072, 0x0000006D, 0x00000063, 0x0000005D, 0x0200005C, 0x2100003B, 0x64000001, 0xA8000000, 0xE7000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x847F7F7F, 0x1B7B7B7B, 0x92060606, 0xCA000000, 0xFB000000,
        0x71545454, 0x254C4C4C, 0x97010101, 0xD3000000, 0xFE000000, 0x542A2A2A, 0x35202020,
  0x65222222, 0x23282828, 0xA0000000, 0xE1000000, 0xFF000000, 0x534D4D4D, 0x2B505050, 0xA7000000, 0xE6000000, 0xFE000000, 0x54767676, 0x187F7F7F, 0xA4060606, 0xE6000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFA000000, 0xDB000000,
        0x63000039, 0x00000064, 0x0000006A, 0x00000061, 0x0000005F, 0x2C00002B, 0x68000000, 0xA0000000, 0xDB000000, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC8B8B8B, 0x28858585, 0x416B6B6B, 0x98000000, 0xD5000000, 0xFE000000,
        0x40575757, 0x40454545, 0x9C000000, 0xDA000000, 0xFF000000, 0x312A2A2A, 0x4B1C1C1C,
  0x94202020, 0x0F2C2C2C, 0x93060606, 0xDA000000, 0xFF000000, 0x9C444444, 0x11575757, 0x970B0B0B, 0xDC000000, 0xFE000000, 0xB65C5C5C, 0x01888888, 0x6F404040, 0xD3000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9000000,
        0x95000047, 0x0000006E, 0x00000078, 0x0000006A, 0x00000061, 0x3B000026, 0x98000000, 0xDA000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xBE898989, 0x00878787, 0x76333333, 0xA3000000, 0xE3000000, 0xF2575757,
        0x17595959, 0x6C2D2D2D, 0xA5000000, 0xE3000000, 0xF32F2F2F, 0x152B2B2B, 0x62151515,
  0xCA1C1C1C, 0x042C2C2C, 0x6A181818, 0xCB000000, 0xFB000000, 0xE7232323, 0x14575757, 0x5F3A3A3A, 0xCB000000, 0xFA000000, 0xF3000000, 0x34797979, 0x217D7D7D, 0xB00F0F0F, 0xEF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0x81000057, 0x00000079, 0x00000082, 0x00000075, 0x00000067, 0x33000035, 0xAD000000, 0xEE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF88F8F8F, 0x398A8A8A, 0x2F787878, 0x8F000000, 0xBC000000, 0xF3000000, 0x9C5C5C5C,
        0x16565656, 0x93080808, 0xB8000000, 0xF0000000, 0xB52F2F2F, 0x052A2A2A, 0x7E0C0C0C,
  0xF7050505, 0x2F272727, 0x31292929, 0xB6000000, 0xF0000000, 0xFB000000, 0x564E4E4E, 0x1C585858, 0xB2000000, 0xEC000000, 0xFD000000, 0xBA4B4B4B, 0x038A8A8A, 0x60565656, 0xD4000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0x5900005C, 0x0000008B, 0x0000008B, 0x0000007F, 0x00000071, 0x22000044, 0xA4000000, 0xE9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x9E8B8B8B, 0x03888888, 0x802B2B2B, 0x9D000000, 0xDA000000, 0xFD000000, 0x2B5D5D5D,
        0x4A474747, 0x9A000000, 0xCF000000, 0xFB000000, 0x53303030, 0x29262626, 0x91000000,
  0xFE000000, 0x84222222, 0x0B303030, 0x96070707, 0xE0000000, 0xFE000000, 0xB83B3B3B, 0x005E5E5E, 0x65373737, 0xD4000000, 0xFB000000, 0xF3000000, 0x7D5F5F5F, 0x81323232, 0xC4000000, 0xF4000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC00005E,
        0x2E000063, 0x0000009A, 0x00000094, 0x00000088, 0x0000007C, 0x11000053, 0x92000003, 0xE1000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC7747474, 0x7C616161, 0x9E000000, 0xC1000000, 0xF2000000, 0xA75F5F5F, 0x005C5C5C,
        0x7B1F1F1F, 0xA5000000, 0xE3000000, 0xF12F2F2F, 0x112F2F2F, 0x60191919, 0x9A000000,
  0xFF000000, 0xD6181818, 0x0A2D2D2D, 0x611E1E1E, 0xCB000000, 0xFA000000, 0xF4000000, 0x494E4E4E, 0x105E5E5E, 0xAA0B0B0B, 0xEC000000, 0xFD000000, 0xE8000000, 0xCC000000, 0xDA000000, 0xF7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEA000058,
        0x0D00006F, 0x000000A6, 0x0000009D, 0x00000092, 0x00000086, 0x05000062, 0x7900000E, 0xD7000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7000000, 0xDA000000, 0xCB000000, 0xE7000000, 0xF4525252, 0x1A606060, 0x434B4B4B,
        0x8F000000, 0xBF000000, 0xF4000000, 0xA6323232, 0x0B2C2C2C, 0x85090909, 0xAE000000,
  0xFF000000, 0xF8000000, 0x64242424, 0x19303030, 0xAA070707, 0xEC000000, 0xFD000000, 0xC92A2A2A, 0x105A5A5A, 0x38515151, 0xCB000000, 0xF7000000, 0xFB000000, 0xF5000000, 0xF7000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xCB000055,
        0x0400007D, 0x000000B3, 0x000000A7, 0x0000009C, 0x00000092, 0x0100006B, 0x5E00001C, 0xCB000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xF7000000, 0xF5000000, 0xFB000000, 0x5D636363, 0x155B5B5B, 0x85131313,
        0xA1000000, 0xDF000000, 0xF32C2C2C, 0x27323232, 0x55222222, 0x96000000, 0xC9000000,
  0xFF000000, 0xFE000000, 0xD2141414, 0x06303030, 0x55252525, 0xCF000000, 0xF9000000, 0xF5000000, 0x95393939, 0x00626262, 0x61434343, 0xDB000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xA9000052,
        0x0000008B, 0x000000C1, 0x000000B1, 0x000000A6, 0x0000009E, 0x00000076, 0x48000027, 0xBF000000, 0xF6000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x98666666, 0x00606060, 0x772C2C2C, 0x94000000,
        0xC9000000, 0xF5000000, 0x8B343434, 0x07303030, 0x8C080808, 0xA9000000, 0xE4000000,
  0xFF000000, 0xFF000000, 0xF8000000, 0x66252525, 0x03353535, 0x91131313, 0xE5000000, 0xFD000000, 0xEC000000, 0x65464646, 0x36515151, 0xB8020202, 0xEF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x8B000054,
        0x000B0BA4, 0x000303CD, 0x000000B9, 0x000000AF, 0x000000A8, 0x00000083, 0x38000031, 0xB2000000, 0xF1000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE666666, 0x575F5F5F, 0x65454545, 0x94000000, 0xB8000000,
        0xEC000000, 0xDB363636, 0x05343434, 0x55212121, 0x92000000, 0xC8000000, 0xF7000000,
  0xFF000000, 0xFF000000, 0xFE000000, 0xE1080808, 0x202D2D2D, 0x23313131, 0xBA070707, 0xF2000000, 0xFC000000, 0xE3000000, 0xB20E0E0E, 0xC0000000, 0xEB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x6D000059,
        0x002929CA, 0x001616DB, 0x000000C2, 0x000000B9, 0x000000B1, 0x00000095, 0x2B00003D, 0xA7000000, 0xEB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE1222222, 0xBF000000, 0xBA000000, 0xE3000000,
        0xF5282828, 0x3D363636, 0x232D2D2D, 0x8C020202, 0xA9000000, 0xE6000000, 0xFE000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xF9000000, 0xB7151515, 0x09323232, 0x402E2E2E, 0xD1000000, 0xF8000000, 0xFA000000, 0xEC000000, 0xE9000000, 0xF7000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x4E000062,
        0x004141EA, 0x003636EC, 0x000000CD, 0x000000C3, 0x000000BA, 0x000000A9, 0x1D00004B, 0x9C000000, 0xE6000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7000000, 0xE9000000, 0xEC000000, 0xFA000000,
        0x69393939, 0x0B323232, 0x840D0D0D, 0x98000000, 0xD2000000, 0xF9000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF2000000, 0x8E1D1D1D, 0x03363636, 0x5B2A2A2A, 0xD8030303, 0xFA000000, 0xFF000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC00005A, 0x33000075,
        0x005555F8, 0x004646F5, 0x000000D7, 0x000000CD, 0x000000C3, 0x000000B6, 0x1200005B, 0x8F000003, 0xE0000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xFD3B3B3B, 0x8B3A3A3A,
        0x04353535, 0x77181818, 0x93000000, 0xC2000000, 0xF2000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xEB000000, 0x6A242424, 0x01383838, 0x871B1B1B, 0xE3000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF4000050, 0x1903038B,
        0x006969FF, 0x004A4AFC, 0x000000E2, 0x000000D6, 0x000000CD, 0x000000C2, 0x09000070, 0x80000007, 0xD9000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC63A3A3A, 0x01373737,
        0x64222222, 0x90000000, 0xB6000000, 0xEA000000, 0xFE000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xE3000000, 0x7C1D1D1D, 0x970A0A0A, 0xD3000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD6000051, 0x060C0CA0,
        0x007C7CFF, 0x004C4CFF, 0x000202ED, 0x000000E0, 0x000000D7, 0x000000D1, 0x02000085, 0x66000016, 0xD0000000, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE7282828, 0x95242424,
        0xA2000000, 0xB0000000, 0xE3000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xE3000000, 0xD1000000, 0xE4000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xA4000050, 0x001C1CB4,
        0x008181FF, 0x004949FF, 0x000909F8, 0x000000EB, 0x000000E1, 0x000000DD, 0x00000099, 0x48000029, 0xC2000000, 0xF8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0xE4000000,
        0xD1000000, 0xE3000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xF8000000, 0xFB000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x77000056, 0x002D2DC7,
        0x008181FF, 0x004545FF, 0x000C0CFE, 0x000000F5, 0x000000EA, 0x000000E6, 0x000000AC, 0x30000039, 0xB0000000, 0xEF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0xFB000000,
        0xF8000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x5800005B, 0x003E3ED8,
        0x008181FF, 0x003E3EFF, 0x000A0AFF, 0x000000FD, 0x000000F4, 0x000000EE, 0x000000C0, 0x22000044, 0xA1000000, 0xE8000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE00005C, 0x3A000061, 0x005050ED,
        0x008181FF, 0x003838FF, 0x000606FF, 0x000000FF, 0x000000FE, 0x000000F9, 0x000000DA, 0x15000051, 0x94000001, 0xE2000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF600005A, 0x1B000075, 0x006666FF,
        0x007E7EFF, 0x003333FF, 0x000404FF, 0x000000FF, 0x000000FF, 0x000000FF, 0x000000F2, 0x0A000069, 0x83000007, 0xDB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE2000059, 0x1600006C, 0x072B2B99,
        0x042B2B96, 0x04101091, 0x04010190, 0x04000090, 0x04000090, 0x04000090, 0x0400008F, 0x08000065, 0x6F000012, 0xD3000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000
};

GUI_CONST_STORAGE GUI_BITMAP _bmRemoteRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acRemoteRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmSystem
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acSystemRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFFA08A8A, 0xFFA28B8B, 0xFFA48C8C, 0xFFA48D8D, 0xFFA48C8C, 0xFFAC9393, 0xFF927E7E, 0xFF383030, 0xFF948080, 0x5AA28B8B, 0x42A28B8B, 0x7C9C8585, 0xB19B8484, 0xDE9C8686, 0xFF9E8989, 0xFF938080, 0xFF928080, 0xFF938181, 0xFF938181, 0xFF928080,
        0xFF0A0909, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF060505, 0xFF000000,
        0xFFA08A8A, 0xFFA28B8B, 0xFFA48C8C, 0xB3A18989, 0x3AA78E8E, 0x7FA78F8F, 0xFF937F7F, 0xFF352D2D, 0xBD2C2525, 0x02B59F9F, 0x00E1D4D4, 0x00D3C3C3, 0x00C1AFAF, 0x13AD9898, 0xA09C8787, 0xFF938080, 0xFF928080, 0xFF938181, 0xFF938181, 0xFF928080,
        0xFF7C6F6F, 0xFF010101, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF040303, 0xFF292323, 0xFF837171, 0xFF000000,
        0xFFA68F8F, 0xDFA08989, 0x5AA18A8A, 0x00BBA6A6, 0x00E3D5D5, 0x00C2AFAF, 0x5EA18989, 0xD6453B3B, 0x46615151, 0x00D7C4C4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D3C2C2, 0x6B988383, 0xFF938080, 0xFF928080, 0xFF938181, 0xFF938181, 0xFF928080,
        0xFF8E7F7F, 0xFF736767, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF1C1818, 0xFF9F8989, 0xFF9F8B8B, 0xFF000000,
        0xD4786767, 0x16AA9393, 0x00D1BFBF, 0x00F5EDED, 0x00FDF8F8, 0x00F7EFEF, 0x00C9B7B7, 0x04AF9B9B, 0x00C4B1B1, 0x00EEE4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D8C9C9, 0x52937E7E, 0xFF938080, 0xFF928080, 0xF0907E7E, 0xC9907E7E, 0xFF928080,
        0xFF8B7C7C, 0xFF8C7E7E, 0xFF383131, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF958282, 0xFF998686, 0xFD000000,
        0x50605353, 0x00C7AFAF, 0x00DDCDCD, 0x00F8F3F3, 0x00F8F1F1, 0x00FAF5F5, 0x00F9F3F3, 0x00F9F2F2, 0x00FEFBFB, 0x00FFFCFC, 0x00FFFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00EBE1E1, 0x07AD9999, 0x688F7C7C, 0x708F7C7C, 0x1DA18E8E, 0x00A79494, 0x8D8E7C7C,
        0xFF8B7C7C, 0xFF887A7A, 0xFF988686, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF948181, 0xFF998686, 0xD40D0B0B,
        0x07A49090, 0x00CCB4B4, 0x00C9B3B3, 0x00EAE0E0, 0x00F7F1F1, 0x00F7F1F1, 0x00FAF4F4, 0x00FBF7F7, 0x00FCF9F9, 0x00FEFBFB, 0x00FFFCFC, 0x00FFFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00F9F5F5, 0x00CFBFBF, 0x00D4C5C5, 0x00F6F0F0, 0x00F1E8E8, 0x03A29090,
        0xB8877878, 0xFF887A7A, 0xFF8C7D7D, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF0D0B0B, 0xFF1E1A1A, 0xFF000000, 0xFF998585, 0xFF998686, 0xCA100D0D,
        0x2E7C6C6C, 0x00C5AEAE, 0x00CAB3B3, 0x00E5D9D9, 0x00F5EFEF, 0x00F6EFEF, 0x00F8F1F1, 0x00FBF5F5, 0x00FAF5F5, 0x00F8F3F3, 0x00FAF6F6, 0x00FFFCFC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E6DBDB,
        0x15988686, 0xD4867777, 0xFF897A7A, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF0A0909, 0xFF756464, 0xFF756464, 0xFF3B3232, 0xFF000000, 0xFF615555, 0xFF443B3B, 0xFF000000, 0xFB786868, 0x51978484, 0x1A9B8888,
        0x0D9A8787, 0x00B59F9F, 0x00DBCACA, 0x00F2EBEB, 0x00F2EBEB, 0x00F4EEEE, 0x00F6F0F0, 0x00E6DBDB, 0x00D6C8C8, 0x00D2C2C2, 0x00D5C5C5, 0x00DED1D1, 0x00F1E9E9, 0x00FFFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E9E9,
        0x00AA9797, 0x96827474, 0xFF7C7171, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF010101, 0xFF453B3B, 0xFF968080, 0xFFAD9494, 0xFFAD9494, 0xFFB39999, 0xFF655858, 0xFF000000, 0xFF000000, 0xFF000000, 0xA12B2626, 0x00AF9A9A, 0x00DFCFCF,
        0x00E9DCDC, 0x00E5D9D9, 0x00ECE2E2, 0x00F0E7E7, 0x00F1E8E8, 0x00F3ECEC, 0x00DDCECE, 0x00C8B2B2, 0x00C8B2B2, 0x00CBB6B6, 0x00CFBBBB, 0x00D1C0C0, 0x00D3C3C3, 0x00E6DCDC, 0x00FEFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EEE5E5, 0x00B4A0A0,
        0x4C857777, 0xF7827575, 0xFF776E6E, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF1A1616, 0xFF897575, 0xFFAC9595, 0xFFA88F8F, 0xFFA58D8D, 0xFFA58D8D, 0xFFAA9191, 0xFFAE9696, 0xFF675959, 0xFF000000, 0xFB000000, 0x35746666, 0x00C2ADAD, 0x00E0D3D3,
        0x00ECE2E2, 0x00ECE2E2, 0x00EDE4E4, 0x00EEE5E5, 0x00F1E9E9, 0x00E6DADA, 0x00B19C9C, 0x118C7C7C, 0x30756767, 0x2A786A6A, 0x07998888, 0x00BEAAAA, 0x00D3C2C2, 0x00D6C8C8, 0x00ECE3E3, 0x00FFFEFE, 0x00FFFFFF, 0x00FFFFFF, 0x00DCCFCF, 0x1A897979,
        0xF57D7171, 0xFF807373, 0xFF786E6E, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFA08B8B, 0xFFA79090, 0xFFA28C8C, 0xFFA48C8C, 0xFFA58D8D, 0xFFA58D8D, 0xFFAA9191, 0xFFA48E8E, 0xFFA08A8A, 0xFF000000, 0xB41C1818, 0x079B8989, 0x00C3AFAF, 0x00DED0D0,
        0x00E9DEDE, 0x00EAE0E0, 0x00EBE0E0, 0x00EDE3E3, 0x00F1E8E8, 0x00D7C7C7, 0x2F7E6E6E, 0xD6030303, 0xEB000000, 0xDD000000, 0xB3131111, 0x4C564D4D, 0x00AA9797, 0x00D4C2C2, 0x00DFD3D3, 0x00FBF6F6, 0x00FFFEFE, 0x00FFFFFF, 0x00F3ECEC, 0x0E918080,
        0xBB736969, 0xFF786D6D, 0xFF786E6E, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFFA18C8C, 0xFFA28B8B, 0xFFA28C8C, 0xFFA48C8C, 0xB8A38B8B, 0x82A28A8A, 0xF7AA9191, 0xFFA48E8E, 0xFF988383, 0xFB000000, 0x36605353, 0x00A89595, 0x00C7B1B1, 0x00CCB7B7,
        0x00D3C0C0, 0x00E1D3D3, 0x00E9DFDF, 0x00EBE1E1, 0x00F0E7E7, 0x00CFBDBD, 0x53857676, 0xFF504949, 0xFF000000, 0xFF000000, 0xF8000000, 0xDD000000, 0x59474040, 0x00B3A0A0, 0x00DDCFCF, 0x00F5EEEE, 0x00FFFDFD, 0x00FFFEFE, 0x00FEFBFB, 0x00D7CACA,
        0x00A39494, 0x38827575, 0x9D786E6E, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF2E2828, 0xFF000000, 0xFFA08B8B, 0xFFA28B8B, 0xE6A18B8B, 0x66A08888, 0x00B29B9B, 0x00BBA6A6, 0x40A28B8B, 0xE5A48D8D, 0xFF988383, 0xBB1C1818, 0x00857373, 0x00A59191, 0x00CDB5B5, 0x00CEB7B7,
        0x00CAB4B4, 0x00D2BDBD, 0x00E8DCDC, 0x00E9DEDE, 0x00EDE3E3, 0x00DCCDCD, 0x208C7D7D, 0xF7837878, 0xFF595050, 0xFF2C2929, 0xFF000000, 0xF9000000, 0xC9040404, 0x1C786B6B, 0x00D3C1C1, 0x00F5EFEF, 0x00FEFBFB, 0x00FEFBFB, 0x00FFFDFD, 0x00FFFFFF,
        0x00FFFCFC, 0x00D7C9C9, 0x13807373, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF120F0F, 0xFF5C4F4F, 0xFF594D4D, 0xFF000000, 0xFFA48E8E, 0x9D9F8989, 0x1BA58F8F, 0x00C3B0B0, 0x00EDE2E2, 0x00F4ECEC, 0x00C7B3B3, 0x28A18B8B, 0x8F968080, 0x31776565, 0x00968585, 0x009A8F8F, 0x009C8B8B, 0x00B29D9D,
        0x00C9B3B3, 0x00CEB9B9, 0x00E2D4D4, 0x00E8DDDD, 0x00E9DFDF, 0x00EBE0E0, 0x00B3A1A1, 0x72786C6C, 0xFF7E7272, 0xFF7F7575, 0xFF5E5757, 0xFF201D1D, 0xF5000000, 0x43504848, 0x00CBB8B8, 0x00FBF5F5, 0x00FCF8F8, 0x00FCF9F9, 0x00FFFCFC, 0x00FFFEFE,
        0x00FFFFFF, 0x00DBCFCF, 0x3B796D6D, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF332C2C, 0xFFA48F8F, 0xFF000000, 0xFF000000, 0xA4837171, 0x00AF9898, 0x00D6C4C4, 0x00F7F0F0, 0x00FAF5F5, 0x00FBF6F6, 0x00F8F0F0, 0x00CFBDBD, 0x00BFACAC, 0x00D5C4C4, 0x00D5CCCC, 0x00D6D5D5, 0x00BCB9B9, 0x00978E8E,
        0x00AF9A9A, 0x00D3BFBF, 0x00E2D4D4, 0x00E7DBDB, 0x00E8DCDC, 0x00EBE0E0, 0x00E6D9D9, 0x00A99898, 0x57776C6C, 0xC5716868, 0xED766D6D, 0xDF756B6B, 0x81453F3F, 0x098E8080, 0x00E6DADA, 0x00FBF5F5, 0x00FAF5F5, 0x00FCF8F8, 0x00F9F4F4, 0x00F9F4F4,
        0x00FCFAFA, 0x00C8B8B8, 0x5D6F6666, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF958282, 0xFF000000, 0xF3050505, 0x26806F6F, 0x00C8AFAF, 0x00D4C0C0, 0x00F4EDED, 0x00F7F1F1, 0x00F8F2F2, 0x00FAF5F5, 0x00FAF4F4, 0x00FCF6F6, 0x00FFFDFD, 0x00FFFFFF, 0x00FFFFFF, 0x00F0EFEF, 0x00979090,
        0x00B29F9F, 0x00E2D4D4, 0x00E4D7D7, 0x00E5D8D8, 0x00E6DBDB, 0x00E7DCDC, 0x00EBE0E0, 0x00EADDDD, 0x00C8B8B8, 0x00A09292, 0x148F8282, 0x0A948686, 0x00BBABAB, 0x00E9DDDD, 0x00F8F2F2, 0x00F7F1F1, 0x00F9F3F3, 0x00F8F2F2, 0x00DFD1D1, 0x00DCCBCB,
        0x00DECDCD, 0x00A89696, 0x83696363, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF968383, 0xFF000000, 0xA3312A2A, 0x00B8A1A1, 0x00CAB3B3, 0x00C7B0B0, 0x00E2D5D5, 0x00F6F1F1, 0x00F7F0F0, 0x00F8F2F2, 0x00FAF4F4, 0x00FBF6F6, 0x00FCF8F8, 0x00FDFAFA, 0x00FFFFFF, 0x00C4C0C0, 0x00948383,
        0x00C9B6B6, 0x00DFD1D1, 0x00E3D5D5, 0x00E4D7D7, 0x00E6DADA, 0x00E7DBDB, 0x00E8DCDC, 0x00EADFDF, 0x00EFE5E5, 0x00EFE3E3, 0x00EBDFDF, 0x00EEE3E3, 0x00F5ECEC, 0x00F5EEEE, 0x00F3EDED, 0x00F6EFEF, 0x00F8F1F1, 0x00E0D3D3, 0x00D5C1C1, 0x00DCC9C9,
        0x00C7B1B1, 0x187A6F6F, 0xE0666161, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF968383, 0xFF000000, 0xB8141212, 0x0D9F8B8B, 0x00CAB2B2, 0x00C7B0B0, 0x00D7C5C5, 0x00F4EDED, 0x00F5EFEF, 0x00F7F0F0, 0x00F8F2F2, 0x00FAF4F4, 0x00FCF8F8, 0x00FFFCFC, 0x00F2F0F0, 0x00908787, 0x00B5A0A0,
        0x00C3B1B1, 0x00CBB9B9, 0x00DFD1D1, 0x00DECECE, 0x00D4C3C3, 0x00DBCCCC, 0x00E7DBDB, 0x00E8DDDD, 0x00E9DEDE, 0x00EBE1E1, 0x00EEE5E5, 0x00EFE6E6, 0x00F0E7E7, 0x00F1E9E9, 0x00F2EBEB, 0x00F4EDED, 0x00F8F2F2, 0x00D3C2C2, 0x00908080, 0x05887B7B,
        0x08807575, 0xAC646060, 0xFF666161, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xE3968383, 0x72615555, 0x5F594D4D, 0x17847272, 0x00B49D9D, 0x00CEB8B8, 0x00EBE1E1, 0x00F3ECEC, 0x00F3EDED, 0x00F5EFEF, 0x00F8F2F2, 0x00F5EEEE, 0x00E9DEDE, 0x00E3D7D7, 0x00C2B7B7, 0x00897F7F, 0x00A08E8E,
        0x00C5B2B2, 0x00C4B3B3, 0x00D2BFBF, 0x00D4BFBF, 0x00CAB7B7, 0x00C8B5B5, 0x00D1C1C1, 0x00E4D7D7, 0x00E8DDDD, 0x00E9DEDE, 0x00EAE0E0, 0x00EDE3E3, 0x00EEE4E4, 0x00EDE3E3, 0x00F1E9E9, 0x00F3EBEB, 0x00F5EFEF, 0x00F3EAEA, 0x00988B8B, 0x712E2D2D,
        0xD80E0E0E, 0xFF656161, 0xFF666161, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0x59756666, 0x00C0A9A9, 0x00D1BDBD, 0x00C9B7B7, 0x00CAB7B7, 0x00E0D3D3, 0x00F2E9E9, 0x00F1E8E8, 0x00F2EBEB, 0x00F5EFEF, 0x00EAE1E1, 0x00D2C0C0, 0x00CAB7B7, 0x00CDBABA, 0x00C3B2B2, 0x00A19595, 0x00807777,
        0x00A99797, 0x00CEB8B8, 0x00C7B1B1, 0x00AE9A9A, 0x00BFABAB, 0x00CCBABA, 0x00CAB9B9, 0x00E0D1D1, 0x00E7DBDB, 0x00E8DCDC, 0x00E9DDDD, 0x00E0D1D1, 0x00DCCDCD, 0x00D3C2C2, 0x00DED2D2, 0x00F2E9E9, 0x00F3EBEB, 0x00F0E6E6, 0x00BAA8A8, 0x4F625E5E,
        0xFF000000, 0xFF656161, 0xFF666161, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xCC171515, 0x039A8787, 0x00CAB4B4, 0x00EADFDF, 0x00EEE4E4, 0x00EFE5E5, 0x00EEE5E5, 0x00EFE6E6, 0x00F0E7E7, 0x00F2EAEA, 0x00EBE1E1, 0x00CBB6B6, 0x00C1ABAB, 0x00BEA9A9, 0x00BEA9A9, 0x00C8B2B2, 0x00BEACAC, 0x009C9191,
        0x00817777, 0x00928181, 0x00857B7B, 0x008B8686, 0x00877C7C, 0x00AD9A9A, 0x00D2BEBE, 0x00DED0D0, 0x00E6DADA, 0x00E7DCDC, 0x00E5D9D9, 0x00DAC8C8, 0x00D7C3C3, 0x00CCBCBC, 0x00C9BABA, 0x00DBCFCF, 0x00E6D9D9, 0x00D5C0C0, 0x00887A7A, 0xAD5C5B5B,
        0xFF000000, 0xFF656161, 0xFF666262, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0x67514747, 0x00BAA4A4, 0x00CBB8B8, 0x00E9DEDE, 0x00E9DFDF, 0x00EAE0E0, 0x00ECE2E2, 0x00EEE4E4, 0x00EFE6E6, 0x00F2E9E9, 0x00D4C2C2, 0x009D8989, 0x29736666, 0x52554B4B, 0x54544B4B, 0x316D6060, 0x029B8989, 0x00A69494,
        0x00998D8D, 0x009E9494, 0x00CABFBF, 0x00E0DEDE, 0x00ACADAD, 0x00857979, 0x00D2BDBD, 0x00D8C7C7, 0x00DACACA, 0x00E1D2D2, 0x00D0BDBD, 0x00A59494, 0x00998A8A, 0x00BEABAB, 0x00CEC0C0, 0x00D7C7C7, 0x00D4BFBF, 0x009F8E8E, 0x645C5A5A, 0xFF5B5A5A,
        0xFF000000, 0xFF6A6666, 0xFF4D4C4C, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xF5000000, 0x3A6B5F5F, 0x00BEA9A9, 0x00CCB9B9, 0x00E6DADA, 0x00E9DFDF, 0x00EADFDF, 0x00EAE0E0, 0x00ECE2E2, 0x00EEE5E5, 0x00EFE5E5, 0x00C0ADAD, 0x4B6A5E5E, 0xEA000000, 0xF5000000, 0xEE000000, 0xDA000000, 0xA31D1A1A, 0x2E6A5F5F,
        0x00AC9898, 0x00D8C7C7, 0x00E0D4D4, 0x00F5EFEF, 0x00C9C9C9, 0x00827878, 0x00D1BBBB, 0x00DBCACA, 0x00D9C9C9, 0x00D8C5C5, 0x00A18F8F, 0x63303030, 0x881C1D1D, 0x0C726969, 0x00BDA9A9, 0x00BDAAAA, 0x047A7070, 0x75323232, 0xF7595858, 0xFF5B5A5A,
        0xFF000000, 0xFF242424, 0xFF0A0A0A, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xE0030303, 0x247C6E6E, 0x00C1ACAC, 0x00C8B3B3, 0x00CFBABA, 0x00D8C7C7, 0x00E3D6D6, 0x00E9DFDF, 0x00EAE0E0, 0x00EDE3E3, 0x00EDE3E3, 0x00B4A2A2, 0x7B807373, 0xFF262323, 0xFF000000, 0xFF000000, 0xFD000000, 0xF1000000, 0xC9060606,
        0x33655B5B, 0x00B5A1A1, 0x00DBCDCD, 0x00EEE6E6, 0x00D3D0D0, 0x007B7575, 0x008F7F7F, 0x00A49292, 0x00BAA7A7, 0x00B5A0A0, 0x016E6666, 0x724F4949, 0xDA0E0C0C, 0xA6101111, 0x1C5D5858, 0x384F4B4B, 0xC30F1010, 0xFF040404, 0xFF5A5959, 0xFF5C5B5B,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xC3110F0F, 0x0D8F7E7E, 0x00CAB3B3, 0x00CDB7B7, 0x00CBB5B5, 0x00C9B3B3, 0x00D3BEBE, 0x00E8DDDD, 0x00E9DEDE, 0x00EBE0E0, 0x00EEE4E4, 0x00C2AFAF, 0x48807373, 0xFF857979, 0xFF464040, 0xFF1A1818, 0xFF000000, 0xFF000000, 0xF3000000,
        0xAD121111, 0x0A887979, 0x00CEBBBB, 0x00EFE6E6, 0x00E3E0E0, 0x00BCBABA, 0x00A09F9F, 0x008D8B8B, 0x00807C7C, 0x007B7676, 0x00B8B0B0, 0x00AB9999, 0x7C605959, 0xF8000000, 0xD7010202, 0xF9000000, 0xFF000000, 0xFF090909, 0xFF5A5959, 0xFF5C5B5B,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xB9131111, 0x13847575, 0x00B5A1A1, 0x00C3ADAD, 0x00CBB4B4, 0x00CCB6B6, 0x00CFBABA, 0x00E3D6D6, 0x00E8DEDE, 0x00E9DEDE, 0x00ECE2E2, 0x00DDCECE, 0x05918181, 0xBF7A6F6F, 0xFF837777, 0xFF7B7070, 0xFF4D4747, 0xFF221F1F, 0xFF000000,
        0xE8000000, 0x3E564D4D, 0x00BDAAAA, 0x00F1E9E9, 0x00FDF9F9, 0x00FBF7F7, 0x00F7F4F4, 0x00EFEDED, 0x00E1E0E0, 0x00EFF0F0, 0x00FFFAFA, 0x00A39292, 0x97716969, 0xFF060606, 0xFF000000, 0xFF000000, 0xFF010101, 0xFF090909, 0xFF605F5F, 0xFF111111,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xDB000000, 0xB2131111, 0x68433C3C, 0x22766A6A, 0x01A08E8E, 0x00C7B0B0, 0x00CEB8B8, 0x00DDCDCD, 0x00E8DCDC, 0x00E8DCDC, 0x00E9DFDF, 0x00ECE1E1, 0x00C5B3B3, 0x19807373, 0xC0766C6C, 0xFF786E6E, 0xFF7E7373, 0xFF7B7171, 0xFF554E4E,
        0xE1131313, 0x285F5555, 0x00C1AEAE, 0x00F9F2F2, 0x00FBF6F6, 0x00FCF8F8, 0x00FFFBFB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E9E9, 0x00918282, 0xBB6C6565, 0xFF060606, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF080808, 0xFF282828, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFE000000, 0xF7000000, 0xEC000000, 0xD2000000, 0x2C665C5C, 0x00BDA7A7, 0x00D7C4C4, 0x00E3D6D6, 0x00E6D9D9, 0x00E6DBDB, 0x00E8DCDC, 0x00EADFDF, 0x00EBE0E0, 0x00C7B5B5, 0x048B7D7D, 0x59746969, 0xA2716767, 0xB4706767, 0x8A756B6B,
        0x25776C6C, 0x00B09F9F, 0x00EDE3E3, 0x00F9F3F3, 0x00F9F3F3, 0x00FAF5F5, 0x00FBF6F6, 0x00F0E8E8, 0x00EFE6E6, 0x00F3ECEC, 0x00E1D2D2, 0x03857878, 0xD8696464, 0xFF020303, 0xFF000000, 0xFF000000, 0xFF090909, 0xFF010101, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xBB232020, 0x0B877878, 0x00CAB6B6, 0x00E4D6D6, 0x00E4D6D6, 0x00E4D7D7, 0x00E5D9D9, 0x00E7DBDB, 0x00E8DDDD, 0x00EADFDF, 0x00EDE2E2, 0x00E0D1D1, 0x00BFAFAF, 0x00A69696, 0x009F9090, 0x00AD9E9E,
        0x00D3C2C2, 0x00F1E8E8, 0x00F6F1F1, 0x00F6F0F0, 0x00F8F1F1, 0x00FBF5F5, 0x00F0E8E8, 0x00D9C7C7, 0x00D9C7C7, 0x00D9C8C8, 0x00BBA5A5, 0x18766D6D, 0xF2696363, 0xFF171616, 0xFF131212, 0xFF383535, 0xFF131212, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFC010101, 0x3B615959, 0x00B7A2A2, 0x00CBB9B9, 0x00DFD1D1, 0x00E3D4D4, 0x00E3D5D5, 0x00E5D8D8, 0x00E6DADA, 0x00E7DCDC, 0x00E8DCDC, 0x00E9DEDE, 0x00ECE2E2, 0x00F0E6E6, 0x00F0E5E5, 0x00F0E5E5, 0x00F3E9E9,
        0x00F4EDED, 0x00F3ECEC, 0x00F3EDED, 0x00F4EEEE, 0x00F7F1F1, 0x00F3EBEB, 0x00D8C7C7, 0x00D7C3C3, 0x00DAC8C8, 0x00D0BABA, 0x008D7E7E, 0x93656060, 0xFF6A6363, 0xFF696363, 0xFF706A6A, 0xFF4A4646, 0xFF010101, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xAA221F1F, 0x00998989, 0x00C4B0B0, 0x00C1AFAF, 0x00CCBBBB, 0x00E0D3D3, 0x00E0D2D2, 0x00DBCBCB, 0x00D6C5C5, 0x00E2D5D5, 0x00E8DCDC, 0x00E8DDDD, 0x00E9DEDE, 0x00EAE0E0, 0x00EDE2E2, 0x00EEE5E5, 0x00EFE6E6,
        0x00F0E7E7, 0x00F1E8E8, 0x00F2EAEA, 0x00F3EDED, 0x00F6F0F0, 0x00F2EAEA, 0x00B9A5A5, 0x00908181, 0x00988989, 0x00958686, 0x486E6868, 0xFF676161, 0xFF6A6363, 0xFF706B6B, 0xFF5E5959, 0xFF080808, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFD000000, 0x96211F1F, 0x00918282, 0x00BFAAAA, 0x00C4B2B2, 0x00C2B0B0, 0x00D1BFBF, 0x00D5C2C2, 0x00CEBABA, 0x00C7B3B3, 0x00CDBABA, 0x00DACCCC, 0x00E6D9D9, 0x00E8DDDD, 0x00E9DEDE, 0x00EADFDF, 0x00ECE2E2, 0x00EEE5E5,
        0x00EFE7E7, 0x00F1E8E8, 0x00F2E9E9, 0x00F2EAEA, 0x00F3EDED, 0x00F8F2F2, 0x00DCCECE, 0x01776D6D, 0x662F2E2E, 0x96232222, 0xEB252424, 0xFF676161, 0xFF6B6363, 0xFF282626, 0xFF111010, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFC000000, 0xDF000000, 0x66373333, 0x009B8B8B, 0x00C2ADAD, 0x00C9B7B7, 0x00D3BEBE, 0x00CFB9B9, 0x00C9B4B4, 0x00CBB9B9, 0x00C9B7B7, 0x00CAB8B8, 0x00E1D3D3, 0x00E7DBDB, 0x00E8DCDC, 0x00E9DEDE, 0x00E9DEDE, 0x00E6D9D9,
        0x00E4D7D7, 0x00DDCFCF, 0x00E7DCDC, 0x00F2EAEA, 0x00F3EBEB, 0x00F5EFEF, 0x00F3EAEA, 0x00AC9B9B, 0x5B4C4949, 0xFF000000, 0xFF000000, 0xFF676161, 0xFF696363, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xF8000000, 0xD6000000, 0x434A4646, 0x00A69595, 0x00C5AFAF, 0x00B5A1A1, 0x00938484, 0x00988989, 0x00BEAAAA, 0x00C9B7B7, 0x00D0BEBE, 0x00E0D1D1, 0x00E6DADA, 0x00E7DBDB, 0x00E8DEDE, 0x00E3D5D5, 0x00D7C5C5,
        0x00D8C7C7, 0x00CBBBBB, 0x00CCBDBD, 0x00E4D9D9, 0x00F1E8E8, 0x00EADFDF, 0x00DAC8C8, 0x009F8C8C, 0x56636060, 0xFF000000, 0xFF000000, 0xFF686262, 0xFF525050, 0xFF040404, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF4000000, 0xC4060606, 0x285F5858, 0x087B7070, 0x444C4747, 0x8F201F1F, 0x6E2F2D2D, 0x0F726969, 0x00B19D9D, 0x00D5C2C2, 0x00DCCECE, 0x00E3D6D6, 0x00E5D9D9, 0x00E7DBDB, 0x00DBCBCB, 0x00D5C1C1,
        0x00CEB9B9, 0x00CBBCBC, 0x00CABBBB, 0x00CCBEBE, 0x00DECFCF, 0x00DECDCD, 0x00B8A3A3, 0x14696464, 0xE25B5A5A, 0xFF000000, 0xFF000000, 0xFF625F5F, 0xFF1A1919, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEB000000, 0xC3060606, 0xC80A0A0A, 0xFA000000, 0xFF000000, 0xEE000000, 0x6A2D2C2C, 0x00A29292, 0x00D7C3C3, 0x00D6C4C4, 0x00D7C7C7, 0x00DBCBCB, 0x00DCCBCB, 0x00AD9999, 0x008A7D7D,
        0x00867979, 0x00B7A3A3, 0x00CDBDBD, 0x00D4C5C5, 0x00D8C3C3, 0x00B8A3A3, 0x007B7171, 0xA3585757, 0xFF5C5B5B, 0xFF000000, 0xFF222121, 0xFF343434, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x7F212121, 0x009C8C8C, 0x00D9C5C5, 0x00D9C9C9, 0x00D8C8C8, 0x00D9C9C9, 0x00C3AEAE, 0x01736A6A, 0x891A1B1B,
        0x821F2020, 0x086D6666, 0x00B39F9F, 0x00C7B1B1, 0x00978888, 0x1E555252, 0xA71D1D1D, 0xFF5C5959, 0xFF5C5B5B, 0xFF000000, 0xFF1A1919, 0xFF020202, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x931C1C1C, 0x00867B7B, 0x00AE9B9B, 0x00BBA8A8, 0x00CAB4B4, 0x00D1BBBB, 0x00968585, 0x6B5A5959, 0xFF121212,
        0xE8000000, 0x9C131313, 0x15605B5B, 0x01716A6A, 0x682E2F2F, 0xE4040505, 0xFF000000, 0xFF5C5959, 0xFF605F5F, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0xCB040404, 0x7D242323, 0x54383737, 0x304E4A4A, 0x15655E5E, 0x007D7272, 0x24666262, 0xEF5D5D5D, 0xFF1A1A1A,
        0xFD000000, 0xE8000000, 0xB7080909, 0xC00D0E0E, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF5C5959, 0xFF474646, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF7000000, 0xF4000000, 0xEB000000, 0xDB000000, 0xC2040505, 0xA3101111, 0xDA1B1B1B, 0xFF5E5E5E, 0xFF191919,
        0xFF000000, 0xFF000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF5C5959, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmSystemRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acSystemRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmWrite
*
* Purpose:
*   Icon bitmap with alpha channel
*/
static GUI_CONST_STORAGE unsigned long _acWriteRad[] = {
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xF500007C, 0x4D000073, 0x0B01016D, 0x0C070672, 0x0C00006F, 0x0C01006D, 0x0C02006C, 0x0D03006B, 0x1703006B, 0x1903006A, 0x1904006A, 0x1904006A, 0x1905006A, 0x1906006A, 0x18060069, 0x1C070068, 0x25060069, 0x25070069, 0x25080068, 0x25090068,
        0x250A0068, 0x250B0068, 0x260B0066, 0x310A0064, 0x32090065, 0x320A0064, 0x310C0064, 0x3A0C0062, 0x83060059, 0xE6000019, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0x9E000078, 0x0000006F, 0x0000005C, 0x004A4684, 0x00685CBD, 0x005249C3, 0x00554DBC, 0x005E56B7, 0x00655DBA, 0x006B63BD, 0x007067BD, 0x007169BE, 0x00746BC0, 0x00776EC0, 0x00786FBA, 0x007970BA, 0x007B72BC, 0x007D75BD, 0x007E76BE, 0x007B72BF,
        0x007C74C0, 0x008076C0, 0x007E74BA, 0x007F75BB, 0x007F77BC, 0x008179BE, 0x00857CBF, 0x008277BC, 0x0048248C, 0x300E005C, 0xBB000030, 0xF3000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0x4D000076, 0x00010274, 0x0003027B, 0x003E1441, 0x00685280, 0x008D7AD9, 0x007466F2, 0x00665BEF, 0x006D61EE, 0x007367EF, 0x007A6EEF, 0x008479F0, 0x008F84F1, 0x009D93F2, 0x00A79DF3, 0x00ADA4F4, 0x00B1A8F4, 0x00B3ABF6, 0x00B6AEF7, 0x00BAB3F8,
        0x00BCB6F8, 0x00C0BAF9, 0x00C4BEFA, 0x00C7C2FB, 0x00CAC4FC, 0x00CBC7FD, 0x00CFCBFD, 0x00EEEBFE, 0x00E0E1F7, 0x00806EBB, 0x0925026D, 0x6706004E, 0xE300000B, 0xFA000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0x1200007B, 0x00000275, 0x000D087E, 0x00962516, 0x00996760, 0x00544A7A, 0x007160B2, 0x007868EA, 0x005B50EE, 0x005B51EA, 0x005F55EA, 0x005F55EB, 0x006258EC, 0x00665DED, 0x006F66ED, 0x007A71EE, 0x008A81EF, 0x00968FF0, 0x009D96F1, 0x00A29CF1,
        0x00A59EF2, 0x00A7A1F2, 0x00AAA4F4, 0x00ADA8F5, 0x00AFAAF5, 0x00B3AEF5, 0x00B3AEF6, 0x00C5C0F6, 0x00EBE9FB, 0x00E9EDFF, 0x00A69FDC, 0x00431D8B, 0x31110058, 0xBC00002E, 0xF4000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xEB000074, 0xCE000072, 0xF500003D, 0xFD000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0x2A000077, 0x00010276, 0x000A0D84, 0x007D1A1D, 0x00D9865B, 0x00C2AD94, 0x00765F70, 0x00635295, 0x007A6AD4, 0x00685CEF, 0x00564DEC, 0x005D53EC, 0x005E55ED, 0x005F56ED, 0x005F57EE, 0x006058EF, 0x00635CF0, 0x006B63F1, 0x00766FF1, 0x00837DF1,
        0x00908AF2, 0x009B95F3, 0x00A09AF4, 0x00A39EF5, 0x00A5A0F5, 0x00A9A4F6, 0x00AAA6F7, 0x00B3AFF7, 0x00D7D4F9, 0x00D1D1FF, 0x00E0E2FF, 0x00C4C8F6, 0x00755AB5, 0x0F25046E, 0x77060047, 0xE5000010, 0xFB000000, 0xFF000000, 0xFF000000, 0xAE000076,
        0x2302037A, 0x0002057E, 0x3500006D, 0xB200004B, 0xF2000000, 0xFE000000, 0xFF000000,
  0xFFFFFFFF, 0x6A00006C, 0x00000075, 0x000B138C, 0x005C1843, 0x00CF6637, 0x00D8C0A4, 0x00DEB88C, 0x00A1847B, 0x005C4978, 0x007563B8, 0x007365ED, 0x00554CF0, 0x00564EED, 0x005A52ED, 0x005C54EF, 0x005D56EF, 0x005E57F1, 0x005F58F1, 0x005F59F1, 0x00615BF2,
        0x006660F3, 0x00736EF4, 0x00837EF5, 0x00908CF5, 0x009995F6, 0x009D99F7, 0x009F9CF8, 0x00A29FF9, 0x00C1BEF9, 0x00BDBDFE, 0x00BFBFFF, 0x00CCCCFF, 0x00D4D8FF, 0x009991DF, 0x0045178A, 0x35140057, 0xBE01002E, 0xF2000012, 0x7E000073, 0x0215107B,
        0x005770B4, 0x00207FED, 0x000527A3, 0x1200006E, 0xA8000030, 0xED000000, 0xFE000000,
  0xFFFFFFFF, 0xA0000065, 0x00000077, 0x000A0E80, 0x00301577, 0x00C24A1F, 0x00D5B9A0, 0x00D5B390, 0x00DEBB93, 0x00CFAD8D, 0x00755C78, 0x006A579D, 0x007969DF, 0x005C53F2, 0x005149F0, 0x00564FEF, 0x005851F1, 0x005953F1, 0x005B54F1, 0x005C56F2, 0x005E58F3,
        0x005F59F4, 0x005F5AF5, 0x00615CF5, 0x006863F6, 0x007672F7, 0x008682F8, 0x00908DF9, 0x009491F9, 0x00ACA9F9, 0x00B0AFFD, 0x00A5A5FF, 0x00AEAEFF, 0x00B6B6FF, 0x00C5C8FF, 0x00AEAFF6, 0x006C48B3, 0x1624026A, 0x35000067, 0x00012393, 0x009FA0B6,
        0x00C7D2DB, 0x0034A6FF, 0x001788FD, 0x000528A7, 0x25000061, 0xC100000A, 0xF3000000,
  0xFFFFFFFF, 0xD8000052, 0x0B000079, 0x00040672, 0x001D1891, 0x00B3341D, 0x00D9A27F, 0x00D4B99A, 0x00D6B491, 0x00DCBA97, 0x00DFBD98, 0x00AC8F88, 0x00644D84, 0x007663BE, 0x006F62EE, 0x004E47F4, 0x005049F1, 0x00544DF1, 0x00554FF3, 0x005750F3, 0x005953F4,
        0x005A55F5, 0x005B57F5, 0x005D59F6, 0x005E5AF7, 0x005E5AF7, 0x00615EF8, 0x006966F9, 0x007471F9, 0x008F8DFA, 0x009F9EFC, 0x008A8AFF, 0x009191FF, 0x009999FF, 0x00A4A4FF, 0x00B5B6FF, 0x007672D1, 0x000B0680, 0x000059B9, 0x001FC5F9, 0x00D1E7E9,
        0x00FBEDE8, 0x0072BDF6, 0x001E9AFF, 0x00157CF4, 0x0001108C, 0x54000049, 0xD6000000,
  0xFFFFFFFF, 0xF9000000, 0x3D000071, 0x00010172, 0x00171E97, 0x009B2C37, 0x00F18750, 0x00D3BFA6, 0x00D7B593, 0x00D8B797, 0x00DABA9A, 0x00E4C49F, 0x00CCAD96, 0x00876C85, 0x006E58A0, 0x007767DC, 0x005F55F5, 0x004C46F3, 0x00514AF2, 0x00514CF4, 0x00524DF5,
        0x00544EF5, 0x005551F6, 0x005753F7, 0x005956F8, 0x005A57F9, 0x005B58F9, 0x005C59F9, 0x005C5AFA, 0x006563FB, 0x007876FC, 0x005353FF, 0x006868FF, 0x007C7CFF, 0x007C7BF6, 0x003D32AA, 0x00011589, 0x000384D3, 0x0043E0FF, 0x009AEEFF, 0x00C8EDF8,
        0x00EDDBD6, 0x00B8BDCB, 0x0042A7F5, 0x001E9BFF, 0x000A44BE, 0x19000062, 0xAD000000,
  0xFFFFFFFF, 0xFE000000, 0x7E000068, 0x00000074, 0x00172193, 0x00752D61, 0x00FF7A37, 0x00DEC9B0, 0x00D5B595, 0x00D8B899, 0x00DABB9B, 0x00DBBD9E, 0x00E1C3A3, 0x00E8CBA5, 0x00B09391, 0x006D538A, 0x007B66BF, 0x006C61F1, 0x004F4AF5, 0x004E49F5, 0x004E4AF7,
        0x004E4AF7, 0x004E4BF8, 0x004F4CFA, 0x00504DFB, 0x005250FB, 0x005352FB, 0x005454FD, 0x005655FE, 0x005757FE, 0x006161FF, 0x002224FF, 0x001F20FF, 0x003733E6, 0x00160D8E, 0x00003299, 0x000EB6F0, 0x006AEBFF, 0x00B8F1FF, 0x00A8EEFF, 0x0056DEFF,
        0x0085C0D2, 0x00D8AEA7, 0x00A5A8B7, 0x004AA9F4, 0x001C6EDE, 0x06000075, 0x8C000000,
  0xFFFFFFFF, 0xFF000000, 0xB3000060, 0x01000079, 0x000A0F7D, 0x00502D83, 0x00F86223, 0x00F9D7B9, 0x00D8BC9D, 0x00D8B999, 0x00DBBC9D, 0x00DDBFA0, 0x00DEC1A3, 0x00E0C3A5, 0x00E9CEAB, 0x00D8BAA4, 0x00876A8B, 0x00785FAA, 0x007667DF, 0x00675DE9, 0x00675DE8,
        0x00695FEB, 0x00695FEB, 0x006A5FED, 0x006A5FEE, 0x00675EEF, 0x00665FF2, 0x00665FF2, 0x006761F2, 0x006761F3, 0x007871F4, 0x005E4FF1, 0x00140ACB, 0x00000083, 0x00005AB1, 0x0028D6FE, 0x008BEEFF, 0x00B9F1FF, 0x0091E9FF, 0x0040D9FF, 0x0000CDFF,
        0x0008CCFC, 0x0080BACC, 0x00C0ABA3, 0x009D8D96, 0x00252F8F, 0x10000064, 0x7F000000,
  0xFFFFFFFF, 0xFF000000, 0xE500004B, 0x14000077, 0x00060873, 0x002D2799, 0x00D14726, 0x00FFBC8E, 0x00EFD7B7, 0x00D8B99A, 0x00DBBD9F, 0x00D8BB9E, 0x00DBBFA2, 0x00E2C7A9, 0x00DFC4A9, 0x00E7CDAF, 0x00E2C8A9, 0x00B6999C, 0x007C5F90, 0x00725591, 0x00795A96,
        0x006F4E90, 0x00795697, 0x007D5899, 0x007F589A, 0x00835CA0, 0x007A5799, 0x008760A3, 0x00865CA4, 0x009063AA, 0x008151A1, 0x0037187D, 0x00011881, 0x000496D8, 0x0051E7FF, 0x00ADF2FF, 0x00B2F0FF, 0x006FE4FF, 0x0018D3FF, 0x0000CEFF, 0x0000CEFF,
        0x0000D3FF, 0x0005DCFF, 0x00378FC2, 0x00221579, 0x19000053, 0x5300000E, 0x98000000,
  0xFFFFFFFF, 0xFF000000, 0xFB000000, 0x3B000071, 0x00000073, 0x001B229E, 0x00943751, 0x00FF9F61, 0x00FFE8CA, 0x00E7C7A4, 0x00DCBFA1, 0x00C3A993, 0x00AC9484, 0x00AE9686, 0x00BAA290, 0x00B7A08F, 0x00AD9888, 0x00B29D8A, 0x009B8678, 0x00A48F81, 0x00C7AF9D,
        0x00B29B8D, 0x0089756D, 0x0098837A, 0x00A28D83, 0x00AB968A, 0x008D7A70, 0x0088756C, 0x00BAA391, 0x00D3B5BA, 0x00321A74, 0x00003698, 0x0011BDF0, 0x0073F0FF, 0x00BAF4FF, 0x00A0EEFF, 0x0052DFFF, 0x000BD2FF, 0x0000CFFF, 0x0000D1FF, 0x0000DAFF,
        0x0006DBFF, 0x001274BE, 0x0204067B, 0x2500003F, 0x5F000006, 0x94000000, 0xCE000000,
  0xFFFFFFFF, 0xFF000000, 0xFE000000, 0x6E00006B, 0x00000074, 0x001D2495, 0x00632F7E, 0x00FF8344, 0x00FFE5C8, 0x00FCDBB4, 0x00E4C7A7, 0x00D5BAA0, 0x00CAB09A, 0x00AB9485, 0x00C1A996, 0x00CFB7A1, 0x00C8B09D, 0x00C8B29E, 0x00CAB5A1, 0x00D9C3AD, 0x00E3CEB7,
        0x00D6C2AE, 0x00C7B4A2, 0x00C4B3A1, 0x00D2BFAD, 0x00D9C8B3, 0x00D4C2AD, 0x00C8B7A3, 0x00705F81, 0x00141878, 0x000063B6, 0x0037DCFC, 0x0094F4FF, 0x00B6F2FF, 0x0087EAFF, 0x002EDBFF, 0x0001D2FF, 0x0000D2FF, 0x0000D5FF, 0x0001E0FF, 0x000FB9EA,
        0x001347A2, 0x0D030063, 0x3C000025, 0x6F000000, 0xA3000000, 0xD7000000, 0xF5000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xB700005C, 0x03000077, 0x00191D85, 0x004F3AA4, 0x00EE6A36, 0x00FFDDBE, 0x00FFE2BE, 0x00F7D7B4, 0x00E0C4A8, 0x00E0C7AB, 0x00CBB19C, 0x00D3BBA4, 0x00EAD2B7, 0x00EAD3B8, 0x00EBD5BC, 0x00ECD7BE, 0x00EDD7BF, 0x00ECD7C1,
        0x00EFDBC4, 0x00F4DFCA, 0x00F1DDC9, 0x00F1DECA, 0x00FFEED5, 0x00EAD9CB, 0x00624E88, 0x00001A7E, 0x00069EDB, 0x0057F0FF, 0x00B2F5FF, 0x00B2F2FF, 0x0068E6FF, 0x0013D8FF, 0x0000D4FF, 0x0000D6FF, 0x0000DAFF, 0x0004E1FF, 0x00159ED7, 0x000E1783,
        0x19000051, 0x5300000D, 0x83000000, 0xB8000000, 0xE3000000, 0xF9000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xEB000036, 0x1B000074, 0x000D0F76, 0x004442B5, 0x00BC534A, 0x00FFCDA1, 0x00FFE7CA, 0x00FFDFBA, 0x00F2D4B4, 0x00DDC3AA, 0x00DEC5AD, 0x00E0C8AF, 0x00DEC7AF, 0x00DEC7B0, 0x00E4CEB6, 0x00E8D3BC, 0x00E7D2BC, 0x00EBD7C2,
        0x00EDD9C3, 0x00EEDAC7, 0x00F3E0CB, 0x00FEECD3, 0x00C8B4B5, 0x00291F6C, 0x0000338C, 0x001BC5EF, 0x0080F6FF, 0x00B9F5FF, 0x0099EFFF, 0x004CE3FF, 0x0007D6FF, 0x0000D4FF, 0x0000D4FF, 0x0000D9FF, 0x000AD0FA, 0x00186EB9, 0x03080573, 0x2B000038,
        0x64000000, 0x95000000, 0xCB000000, 0xEF000000, 0xFD000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFC000000, 0x4B00006E, 0x0000006E, 0x002830A5, 0x00833F77, 0x00FFB47B, 0x00FFECD2, 0x00FFDEBA, 0x00FFE3C0, 0x00D8BDA6, 0x00C8B09D, 0x00B29C8D, 0x00A49083, 0x00B19E8F, 0x008E7C73, 0x00B7A495, 0x00AE9C8F, 0x00EBD8C3,
        0x00EDDAC7, 0x00F7E4CD, 0x00F7E4CF, 0x00826E8B, 0x00080E5F, 0x00007DBE, 0x0042E7FF, 0x00A3F7FF, 0x00B6F4FF, 0x007EE8FF, 0x0022D6FF, 0x0000CDFF, 0x0000CBFF, 0x0000CAFF, 0x0001D2FF, 0x0015B0E9, 0x00133494, 0x0F02005C, 0x4200001B, 0x73000000,
        0xA8000000, 0xDA000000, 0xF6000000, 0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0x84000066, 0x00000072, 0x00262B92, 0x005D399F, 0x00FA8E56, 0x00FFE8CD, 0x00FFE0BE, 0x00FFE1C0, 0x00FBDEBE, 0x00E1C9B0, 0x00CBB4A1, 0x00CAB4A2, 0x00CCB7A5, 0x00C9B5A3, 0x00C8B4A4, 0x00D2BFAD, 0x00ECD9C5,
        0x00F5E3CD, 0x00D6C4BB, 0x004E406D, 0x00002473, 0x0011A2D8, 0x006FF2FF, 0x00B4F3FF, 0x00A4ECFF, 0x0056DAFF, 0x0011C9FF, 0x0000C1FF, 0x0000C0FF, 0x0000C1FF, 0x0008C2FE, 0x001879C5, 0x010F167C, 0x20000043, 0x58000008, 0x87000000, 0xBC000000,
        0xE6000000, 0xFB000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xCD00004F, 0x09000075, 0x001A1C7F, 0x00584DBE, 0x00E2744E, 0x00FFDCBC, 0x00FFE5C7, 0x00FFE1C0, 0x00FFE4C4, 0x00FAE0C3, 0x00EBD4BB, 0x00E9D4BC, 0x00EBD6BF, 0x00ECD8C2, 0x00ECD9C4, 0x00EDDBC7, 0x00F1E0CB,
        0x00DDCCBF, 0x00221A4F, 0x0000438C, 0x002ED2FC, 0x0094F1FF, 0x00BBEFFF, 0x008DE3FF, 0x0034CBFF, 0x0000B9FF, 0x0000B7FF, 0x0000B4FF, 0x0000B9FF, 0x000EAEF5, 0x001C52A8, 0x07050060, 0x35000029, 0x6A000000, 0x9C000000, 0xD0000000, 0xF1000000,
        0xFE000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xF5000023, 0x2B000070, 0x00090A74, 0x004C4BBA, 0x00BF6563, 0x00FFCEA1, 0x00FFECD3, 0x00FFE1C0, 0x00FFE3C4, 0x00FFE5C8, 0x00E7CFB6, 0x00E0CAB4, 0x00E4CEB9, 0x00F0DBC5, 0x00F4DFCA, 0x00F5E0CB, 0x00FFECD5,
        0x006F6375, 0x00071C6E, 0x0055CEFF, 0x00ACEFFF, 0x00B2E9FF, 0x006ED5FF, 0x001CBAFF, 0x0000AEFF, 0x0000ADFF, 0x0000ACFF, 0x0004B1FF, 0x001495E6, 0x00122B88, 0x0A000055, 0x46000013, 0x79000000, 0xB0000000, 0xDF000000, 0xF8000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0x5C00006B, 0x00000071, 0x003036A8, 0x00995985, 0x00FFB77B, 0x00FFECD6, 0x00FFE1C2, 0x00FFE5C8, 0x00F4D9BE, 0x00C9B09F, 0x00C4AD9D, 0x00A28E83, 0x00BBA798, 0x00F4DFC8, 0x00FFF8DE, 0x00BEB3AD,
        0x0017153B, 0x007F9CDA, 0x009AD0FF, 0x009FE3FF, 0x004BC2FF, 0x0006A8FF, 0x0000A3FF, 0x0000A2FF, 0x0000A2FF, 0x000AA6FF, 0x001861B6, 0x000B0B68, 0x00605792, 0x10131361, 0x6D000002, 0xB6000000, 0xEA000000, 0xFC000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x8D000065, 0x00000074, 0x00252A9A, 0x006547A9, 0x00FB995F, 0x00FFE7CE, 0x00FFE4C7, 0x00FFE4C8, 0x00FEE3C8, 0x00F7DEC4, 0x00E8D1BA, 0x00EBD5BE, 0x00DFC9B5, 0x00FBE6D0, 0x00FEEDD7, 0x003B354B,
        0x0059637F, 0x00DFF3FF, 0x0070AAFE, 0x0025AEFF, 0x00009CFF, 0x000099FF, 0x000097FF, 0x00009CFF, 0x001396F5, 0x00133D95, 0x00170F61, 0x008C8197, 0x00E1D9CE, 0x044D4C94, 0x6E000013, 0xD1000000, 0xFC000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xC3000059, 0x05000074, 0x00131588, 0x004943CA, 0x00D87E66, 0x00FFDDB9, 0x00FFE8CD, 0x00FFE4C8, 0x00FFE6CB, 0x00FFE8CD, 0x00FFEBD1, 0x00FFECD4, 0x00FFEED7, 0x00FFF9E0, 0x009C9190, 0x00232747,
        0x00D0E3EC, 0x00E4F9FB, 0x009AC0F8, 0x001385FF, 0x000091FF, 0x000093FF, 0x00069BFF, 0x001878D3, 0x00101E70, 0x003C306B, 0x00B1A7AA, 0x00D7CCBB, 0x00DCD4C4, 0x008786B2, 0x4E00002B, 0xC6000000, 0xF9000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF000002F, 0x2300006F, 0x000B0C7F, 0x004446CE, 0x00AB687E, 0x00FFD3A3, 0x00FFEDD7, 0x00FFE4C8, 0x00FFE6CC, 0x00FEE6CD, 0x00FEE8D0, 0x00FCE6CF, 0x00FFEED7, 0x00F1E0CD, 0x0028232E, 0x006F7A7C,
        0x00E3FFFF, 0x00C5E8F5, 0x00C0E7F4, 0x0089B9FC, 0x002792FF, 0x00078FFA, 0x001D5FAE, 0x00110D52, 0x00645A7D, 0x00D1C7BB, 0x00D3CABC, 0x00CBC0B7, 0x00CFC7B9, 0x00AEACC1, 0x2F050544, 0xB2000000, 0xF0000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFD000000, 0x62000066, 0x00000077, 0x004E53C8, 0x008F629F, 0x00FFC58D, 0x00FFEFDB, 0x00FFE5C9, 0x00FFE9CF, 0x00EAD3BD, 0x00D9C2B0, 0x00D9C4B2, 0x00C3B0A1, 0x00655C5D, 0x000A0A00, 0x004A4D1C,
        0x00ACCCCE, 0x00AFDCF1, 0x008EB8D2, 0x006589A8, 0x002B4288, 0x0004105B, 0x000F0941, 0x003E384A, 0x00948C81, 0x00D4CBC0, 0x00CBC2B9, 0x00D2CAC0, 0x00E6DCD1, 0x00E9E4E8, 0x11171767, 0x98000005, 0xE5000000, 0xFF000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0x9E00005F, 0x00000074, 0x003235AE, 0x005E4FC5, 0x00F1A87E, 0x00FFF0DB, 0x00FFE6CD, 0x00FFE7CE, 0x00FCE5CD, 0x00F7E1CB, 0x00F4DECA, 0x00B0A094, 0x000B0A09, 0x001D1E00, 0x00141300,
        0x00161D25, 0x001E294B, 0x000E1137, 0x00292649, 0x005D556A, 0x00867D88, 0x00A89F9D, 0x00AAA298, 0x00B0A9A2, 0x00DCD4CB, 0x00F0E8DF, 0x00FCF4EA, 0x00FFF9EF, 0x00FFFEF7, 0x024C4C92, 0x6C00001B, 0xD5000000, 0xFD000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xD5000050, 0x0A000071, 0x00131397, 0x004B49DC, 0x00C48583, 0x00FFE8C5, 0x00FFEAD4, 0x00FFE7CE, 0x00FFE9D1, 0x00FFEED7, 0x00F8E5D0, 0x004F4842, 0x00000000, 0x001F1D1C, 0x003E3A44,
        0x005D5660, 0x00847B7E, 0x00AEA49F, 0x00CABFB5, 0x00D6CCBE, 0x00D5CDBF, 0x00DAD2C8, 0x00EAE2D9, 0x00F9F1E8, 0x00FFF8EF, 0x00FFFAF1, 0x00FFF8F0, 0x00FFF8EF, 0x00FFFFFA, 0x008686B7, 0x4400002F, 0xC1000000, 0xF7000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xF9000000, 0x3B000069, 0x00050687, 0x004F51DC, 0x00A2729A, 0x00FFDBA8, 0x00FFEEDA, 0x00FFE7CE, 0x00FFE9D2, 0x00FEE9D3, 0x00D4C2B0, 0x0091867B, 0x00A69A8E, 0x00C2B4A9, 0x00D0C2B4,
        0x00D8CCBC, 0x00D5CABC, 0x00D0C5B9, 0x00D0C4BB, 0x00DAD1C7, 0x00EEE5DB, 0x00FEF5EB, 0x00FFFAF1, 0x00FFFAF2, 0x00FFF8F1, 0x00FFF8F2, 0x00FFF8F2, 0x00FFF8F1, 0x00FFFFF5, 0x00D1D0E4, 0x2405054B, 0xAB000000, 0xED000000, 0xFF000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0x78000063, 0x00000079, 0x005055D2, 0x008D6FB6, 0x00FFCE97, 0x00FFEFDC, 0x00FFE7CE, 0x00FFE9D2, 0x00FEE9D4, 0x00F9E6D1, 0x00FCE9D6, 0x00FBE9D7, 0x00F5E5D4, 0x00F2E4D4,
        0x00F0E3D5, 0x00EEE2D4, 0x00EDE1D6, 0x00F5EAE0, 0x00FFF6EC, 0x00FFF9EF, 0x00FFF8EF, 0x00FFF8F0, 0x00FFF8F2, 0x00FFF9F3, 0x00FFFAF4, 0x00FFFAF4, 0x00FFF9F3, 0x00FFFCF4, 0x00F9F6F7, 0x09292976, 0x8A00000D, 0xE0000000, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xB100005A, 0x01000075, 0x001F21B7, 0x007568D2, 0x00F3BA89, 0x00FFEED8, 0x00FFEFDE, 0x00FFEBD5, 0x00FFECD8, 0x00FFEFDC, 0x00FFF1DF, 0x00FFF2E2, 0x00FFF3E4, 0x00FFF4E6,
        0x00FFF5E8, 0x00FFF6EA, 0x00FFF8ED, 0x00FFF8EF, 0x00FFF8EF, 0x00FFF9F2, 0x00FFFAF3, 0x00FFFAF5, 0x00FFFBF6, 0x00FFFCF8, 0x00FFFCF9, 0x00FFFDF9, 0x00FFFDF9, 0x00FFFDF8, 0x00FFFFFF, 0x005D5DA0, 0x5C000022, 0xCE000000, 0xFB000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xE300004A, 0x1100006E, 0x001212A3, 0x00504FE5, 0x00BB8E93, 0x00FFDD97, 0x00FFECCB, 0x00FFF3D8, 0x00FFF2D8, 0x00FFF2D8, 0x00FFF3D9, 0x00FFF3D9, 0x00FFF2DA, 0x00FFF6E0,
        0x00FFF7E1, 0x00FFF6E2, 0x00FFF6E3, 0x00FFF6E3, 0x00FFF6E4, 0x00FFF6E4, 0x00FFF8E7, 0x00FFFBED, 0x00FFFBEE, 0x00FFFBEF, 0x00FFFBF0, 0x00FFFCF0, 0x00FFFCF0, 0x00FFFFEF, 0x00E1DBF1, 0x006867C3, 0x3A000035, 0xB9000000, 0xF4000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFB000000, 0x38000069, 0x0002028A, 0x004648E8, 0x003129D4, 0x00AA829A, 0x00CE9D93, 0x00D1A398, 0x00D3A395, 0x00D3A194, 0x00D3A093, 0x00D39E92, 0x00D39E90, 0x00D39D90,
        0x00D49D8E, 0x00DCA28B, 0x00DCA18A, 0x00DC9F88, 0x00DC9E87, 0x00DC9D85, 0x00DC9C84, 0x00DB9B84, 0x00E09D81, 0x00E69F7E, 0x00E69E7D, 0x00E69D7B, 0x00EA9E78, 0x00D48F81, 0x003321DD, 0x005E60F4, 0x20131352, 0xA5000000, 0xEA000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFE000000, 0x6A000064, 0x00000078, 0x004849D5, 0x001E1FFB, 0x000000EC, 0x000503E2, 0x000604E2, 0x000705E4, 0x000704E5, 0x000704E5, 0x000704E5, 0x000604E4, 0x000603E3,
        0x000704E3, 0x000804E4, 0x000703E4, 0x000703E5, 0x000603E5, 0x000602E4, 0x000501E2, 0x000501E1, 0x000602E1, 0x000601E1, 0x000500E1, 0x000400E1, 0x000400E0, 0x000100EB, 0x000000FF, 0x005F5FFC, 0x1329296B, 0x93000002, 0xE1000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xAF00005A, 0x01000074, 0x001111A1, 0x002222D2, 0x000A0BD5, 0x000F10D8, 0x001212DA, 0x001313DD, 0x001516E0, 0x001919E2, 0x001C1DE3, 0x002122E7, 0x002626EA,
        0x00292AEB, 0x002A2BEE, 0x002A2BF0, 0x002D2EF2, 0x002F31F3, 0x003334F5, 0x003B3CF9, 0x003E3FFA, 0x004243FB, 0x003E40FD, 0x003F40FE, 0x004243FE, 0x004446FF, 0x00494AFF, 0x005555FF, 0x006363E7, 0x1C08084E, 0x8C000000, 0xDD000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEC00002A, 0x2100006E, 0x00000082, 0x0001018B, 0x00030390, 0x00030394, 0x00030398, 0x0005059C, 0x000505A0, 0x000505A4, 0x000606A8, 0x000707AD, 0x000909B1,
        0x000909B5, 0x000A0AB9, 0x000C0CBD, 0x000C0CC2, 0x000D0DC7, 0x000E0ECC, 0x001010D0, 0x001010D3, 0x001212D3, 0x001313D3, 0x001313D4, 0x001414D5, 0x001515D5, 0x001717D6, 0x001818DA, 0x0007079E, 0x3100002E, 0x95000000, 0xE2000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0x9F00004D, 0x23000065, 0x0E00006B, 0x0D000069, 0x0D00006A, 0x0B00006D, 0x09000070, 0x09000070, 0x09000072, 0x09000072, 0x09000074, 0x09000075,
        0x09000075, 0x08000079, 0x0700007B, 0x0700007F, 0x0700007F, 0x07000080, 0x07000081, 0x07000081, 0x06000080, 0x04000083, 0x04000085, 0x04000087, 0x04000086, 0x04000087, 0x04000082, 0x1A000048, 0x6100000A, 0xB0000000, 0xED000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xEF000000, 0xC2000000, 0x94000000, 0x82000000, 0x80000000, 0x80000000, 0x7F000000, 0x7D000000, 0x7D000000, 0x7D000000, 0x7D000000, 0x7D000000,
        0x7D000000, 0x7C000000, 0x7A000000, 0x7A000000, 0x7A000000, 0x7A000000, 0x7A000000, 0x7A000000, 0x7A000000, 0x78000000, 0x77000000, 0x76000000, 0x76000000, 0x76000000, 0x76000000, 0x7D000000, 0xA1000000, 0xD7000000, 0xF9000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFC000000, 0xF0000000, 0xE0000000, 0xD9000000, 0xD8000000, 0xD8000000, 0xD7000000, 0xD6000000, 0xD6000000, 0xD6000000, 0xD6000000, 0xD6000000,
        0xD6000000, 0xD6000000, 0xD5000000, 0xD4000000, 0xD4000000, 0xD4000000, 0xD4000000, 0xD4000000, 0xD4000000, 0xD4000000, 0xD3000000, 0xD2000000, 0xD2000000, 0xD2000000, 0xD2000000, 0xD6000000, 0xE4000000, 0xF6000000, 0xFE000000, 0xFF000000,
        0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
  0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF,
        0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};

GUI_CONST_STORAGE GUI_BITMAP _bmWriteRad = {
  48, /* XSize */
  48, /* YSize */
  192, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)_acWriteRad,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       _bmRectRed_60x60
*/
static GUI_CONST_STORAGE unsigned char _acRectRed_60x60[] = {
  /* RLE: 003 Pixels @ 000,000*/ 3, 0xFF,
  /* ABS: 007 Pixels @ 003,000*/ 0, 7, 0xC3, 0x81, 0x99, 0x99, 0xC0, 0xCC, 0xE1,
  /* RLE: 008 Pixels @ 010,000*/ 8, 0xFF,
  /* ABS: 008 Pixels @ 018,000*/ 0, 8, 0xF0, 0xB4, 0x78, 0x4B, 0x33, 0x33, 0x33, 0x4B,
  /* RLE: 004 Pixels @ 026,000*/ 4, 0x66,
  /* ABS: 003 Pixels @ 030,000*/ 0, 3, 0x93, 0x99, 0xC9,
  /* RLE: 011 Pixels @ 033,000*/ 11, 0xFF,
  /* ABS: 013 Pixels @ 044,000*/ 0, 13, 0xF0, 0xB7, 0x8D, 0x66, 0x66, 0x3F, 0x33, 0x24, 0x03, 0x33, 0x33, 0x54, 0xA8,
  /* RLE: 005 Pixels @ 057,000*/ 5, 0xFF,
  /* RLE: 001 Pixels @ 002,001*/ 1, 0x69,
  /* RLE: 009 Pixels @ 003,001*/ 9, 0x00,
  /* ABS: 006 Pixels @ 012,001*/ 0, 6, 0x33, 0x33, 0x51, 0x63, 0x33, 0x12,
  /* RLE: 015 Pixels @ 018,001*/ 15, 0x00,
  /* ABS: 011 Pixels @ 033,001*/ 0, 11, 0x03, 0x33, 0x66, 0x8A, 0xAE, 0xD5, 0xFF, 0xCC, 0xA5, 0x6F, 0x30,
  /* RLE: 013 Pixels @ 044,001*/ 13, 0x00,
  /* RLE: 001 Pixels @ 057,001*/ 1, 0x72,
  /* RLE: 004 Pixels @ 058,001*/ 4, 0xFF,
  /* RLE: 001 Pixels @ 002,002*/ 1, 0x12,
  /* RLE: 055 Pixels @ 003,002*/ 55, 0x00,
  /* ABS: 004 Pixels @ 058,002*/ 0, 4, 0xC3, 0xFF, 0xFF, 0xF3,
  /* RLE: 004 Pixels @ 002,003*/ 4, 0x00,
  /* ABS: 006 Pixels @ 006,003*/ 0, 6, 0x42, 0x6F, 0x75, 0x66, 0x36, 0x2A,
  /* RLE: 013 Pixels @ 012,003*/ 13, 0x00,
  /* ABS: 015 Pixels @ 025,003*/ 0, 15, 0x12, 0x33, 0x66, 0x90, 0xBA, 0xE4, 0xFF, 0xFF, 0xD5, 0xCC, 0xC9, 0x99, 0x6F, 0x45, 0x18,
  /* RLE: 007 Pixels @ 040,003*/ 7, 0x00,
  /* RLE: 001 Pixels @ 047,003*/ 1, 0x2D,
  /* RLE: 004 Pixels @ 048,003*/ 4, 0x33,
  /* RLE: 001 Pixels @ 052,003*/ 1, 0x30,
  /* RLE: 005 Pixels @ 053,003*/ 5, 0x00,
  /* ABS: 008 Pixels @ 058,003*/ 0, 8, 0x75, 0xFF, 0xFF, 0xDE, 0x00, 0x00, 0x48, 0xCF,
  /* RLE: 009 Pixels @ 006,004*/ 9, 0xFF,
  /* RLE: 001 Pixels @ 015,004*/ 1, 0xED,
  /* RLE: 004 Pixels @ 016,004*/ 4, 0xCC,
  /* ABS: 005 Pixels @ 020,004*/ 0, 5, 0xBA, 0x99, 0xB4, 0xCC, 0xFC,
  /* RLE: 016 Pixels @ 025,004*/ 16, 0xFF,
  /* ABS: 003 Pixels @ 041,004*/ 0, 3, 0xF0, 0xCC, 0xF9,
  /* RLE: 010 Pixels @ 044,004*/ 10, 0xFF,
  /* ABS: 011 Pixels @ 054,004*/ 0, 11, 0xB4, 0x06, 0x00, 0x00, 0x66, 0xFF, 0xFF, 0xC9, 0x00, 0x12, 0xF9,
  /* RLE: 050 Pixels @ 005,005*/ 50, 0xFF,
  /* ABS: 009 Pixels @ 055,005*/ 0, 9, 0x54, 0x00, 0x00, 0x78, 0xFF, 0xFF, 0xC6, 0x00, 0x48,
  /* RLE: 051 Pixels @ 004,006*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,006*/ 0, 9, 0x5A, 0x00, 0x00, 0x9F, 0xFF, 0xFF, 0xDB, 0x00, 0x5A,
  /* RLE: 051 Pixels @ 004,007*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,007*/ 0, 9, 0x3F, 0x00, 0x00, 0xCF, 0xFF, 0xFF, 0xF6, 0x00, 0x60,
  /* RLE: 051 Pixels @ 004,008*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,008*/ 0, 9, 0x27, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0x0C, 0x5A,
  /* RLE: 051 Pixels @ 004,009*/ 51, 0xFF,
  /* ABS: 003 Pixels @ 055,009*/ 0, 3, 0x0F, 0x00, 0x00,
  /* RLE: 004 Pixels @ 058,009*/ 4, 0xFF,
  /* ABS: 002 Pixels @ 002,010*/ 0, 2, 0x24, 0x4B,
  /* RLE: 051 Pixels @ 004,010*/ 51, 0xFF,
  /* ABS: 003 Pixels @ 055,010*/ 0, 3, 0x12, 0x00, 0x00,
  /* RLE: 004 Pixels @ 058,010*/ 4, 0xFF,
  /* ABS: 002 Pixels @ 002,011*/ 0, 2, 0x21, 0x3F,
  /* RLE: 051 Pixels @ 004,011*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,011*/ 0, 9, 0x33, 0x00, 0x00, 0xF6, 0xFF, 0xFF, 0xFF, 0x1B, 0x3C,
  /* RLE: 051 Pixels @ 004,012*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,012*/ 0, 9, 0x7B, 0x00, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0x0F, 0x30,
  /* RLE: 051 Pixels @ 004,013*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,013*/ 0, 9, 0xD2, 0x00, 0x00, 0xE1, 0xFF, 0xFF, 0xFF, 0x06, 0x2D,
  /* RLE: 052 Pixels @ 004,014*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,014*/ 0, 8, 0x18, 0x00, 0xD8, 0xFF, 0xFF, 0xF9, 0x00, 0x27,
  /* RLE: 052 Pixels @ 004,015*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,015*/ 0, 8, 0x4E, 0x00, 0xCF, 0xFF, 0xFF, 0xE1, 0x00, 0x1E,
  /* RLE: 052 Pixels @ 004,016*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,016*/ 0, 8, 0x72, 0x00, 0xC3, 0xFF, 0xFF, 0xCC, 0x00, 0x0C,
  /* RLE: 052 Pixels @ 004,017*/ 52, 0xFF,
  /* ABS: 009 Pixels @ 056,017*/ 0, 9, 0x78, 0x00, 0xB4, 0xFF, 0xFF, 0xB4, 0x00, 0x00, 0xF9,
  /* RLE: 051 Pixels @ 005,018*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 056,018*/ 0, 9, 0x72, 0x00, 0xAE, 0xFF, 0xFF, 0x9C, 0x00, 0x00, 0xE1,
  /* RLE: 051 Pixels @ 005,019*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 056,019*/ 0, 9, 0x54, 0x00, 0xA5, 0xFF, 0xFF, 0x7E, 0x00, 0x00, 0xC6,
  /* RLE: 051 Pixels @ 005,020*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 056,020*/ 0, 9, 0x2D, 0x00, 0xA2, 0xFF, 0xFF, 0x63, 0x00, 0x00, 0xA8,
  /* RLE: 050 Pixels @ 005,021*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,021*/ 0, 10, 0xF9, 0x06, 0x00, 0x93, 0xFF, 0xFF, 0x45, 0x00, 0x00, 0x84,
  /* RLE: 050 Pixels @ 005,022*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,022*/ 0, 10, 0xD2, 0x00, 0x00, 0x81, 0xFF, 0xFF, 0x27, 0x00, 0x00, 0x5D,
  /* RLE: 050 Pixels @ 005,023*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,023*/ 0, 10, 0xA8, 0x00, 0x00, 0x6C, 0xFF, 0xFF, 0x09, 0x00, 0x00, 0x42,
  /* RLE: 050 Pixels @ 005,024*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,024*/ 0, 10, 0x7B, 0x00, 0x00, 0x4E, 0xFF, 0xEA, 0x00, 0x00, 0x00, 0x2D,
  /* RLE: 050 Pixels @ 005,025*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,025*/ 0, 10, 0x57, 0x00, 0x00, 0x2A, 0xFF, 0xCC, 0x00, 0x00, 0x00, 0x21,
  /* RLE: 050 Pixels @ 005,026*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,026*/ 0, 10, 0x3F, 0x00, 0x00, 0x0C, 0xFF, 0xB1, 0x00, 0x00, 0x00, 0x33,
  /* RLE: 050 Pixels @ 005,027*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,027*/ 0, 10, 0x30, 0x00, 0x00, 0x00, 0xDB, 0x99, 0x00, 0x00, 0x00, 0x57,
  /* RLE: 050 Pixels @ 005,028*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,028*/ 0, 10, 0x30, 0x00, 0x00, 0x00, 0xAE, 0x84, 0x00, 0x00, 0x00, 0xA5,
  /* RLE: 050 Pixels @ 005,029*/ 50, 0xFF,
  /* ABS: 010 Pixels @ 055,029*/ 0, 10, 0x3F, 0x00, 0x00, 0x00, 0x7E, 0x6F, 0x00, 0x00, 0x12, 0xF6,
  /* RLE: 050 Pixels @ 005,030*/ 50, 0xFF,
  /* ABS: 009 Pixels @ 055,030*/ 0, 9, 0x4B, 0x00, 0x00, 0x00, 0x57, 0x5D, 0x00, 0x00, 0x63,
  /* RLE: 051 Pixels @ 004,031*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,031*/ 0, 9, 0x66, 0x00, 0x00, 0x00, 0x33, 0x4E, 0x00, 0x00, 0xB1,
  /* RLE: 051 Pixels @ 004,032*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,032*/ 0, 9, 0x93, 0x00, 0x00, 0x00, 0x27, 0x3F, 0x00, 0x00, 0xDB,
  /* RLE: 051 Pixels @ 004,033*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,033*/ 0, 9, 0xB7, 0x00, 0x00, 0x00, 0x2D, 0x33, 0x00, 0x00, 0xE7,
  /* RLE: 051 Pixels @ 004,034*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,034*/ 0, 9, 0xE4, 0x00, 0x00, 0x00, 0x3C, 0x2D, 0x00, 0x00, 0xE1,
  /* RLE: 052 Pixels @ 004,035*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,035*/ 0, 8, 0x06, 0x00, 0x00, 0x5D, 0x2A, 0x00, 0x00, 0xCC,
  /* RLE: 052 Pixels @ 004,036*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,036*/ 0, 8, 0x1B, 0x00, 0x00, 0x8A, 0x1E, 0x00, 0x00, 0xB7,
  /* RLE: 052 Pixels @ 004,037*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,037*/ 0, 8, 0x18, 0x00, 0x00, 0xB7, 0x1E, 0x00, 0x00, 0xA8,
  /* RLE: 052 Pixels @ 004,038*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,038*/ 0, 8, 0x0C, 0x00, 0x00, 0xE7, 0x1E, 0x00, 0x00, 0x93,
  /* RLE: 051 Pixels @ 004,039*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,039*/ 0, 9, 0xF9, 0x00, 0x00, 0x18, 0xFF, 0x0F, 0x00, 0x00, 0x96,
  /* RLE: 051 Pixels @ 004,040*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,040*/ 0, 9, 0xDE, 0x00, 0x00, 0x3C, 0xFF, 0x0F, 0x00, 0x00, 0xAB,
  /* RLE: 051 Pixels @ 004,041*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,041*/ 0, 9, 0xC3, 0x00, 0x00, 0x5A, 0xFF, 0x0F, 0x00, 0x00, 0xC3,
  /* RLE: 051 Pixels @ 004,042*/ 51, 0xFF,
  /* ABS: 009 Pixels @ 055,042*/ 0, 9, 0xAB, 0x00, 0x00, 0x6F, 0xFF, 0x15, 0x00, 0x00, 0xE4,
  /* RLE: 051 Pixels @ 004,043*/ 51, 0xFF,
  /* ABS: 008 Pixels @ 055,043*/ 0, 8, 0x96, 0x00, 0x00, 0x60, 0xFF, 0x1E, 0x00, 0x0C,
  /* RLE: 052 Pixels @ 003,044*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 055,044*/ 0, 8, 0x8A, 0x00, 0x00, 0x45, 0xFF, 0x33, 0x00, 0x2D,
  /* RLE: 052 Pixels @ 003,045*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 055,045*/ 0, 8, 0x90, 0x00, 0x00, 0x18, 0xFF, 0x4B, 0x00, 0x48,
  /* RLE: 052 Pixels @ 003,046*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 055,046*/ 0, 8, 0xA2, 0x00, 0x00, 0x00, 0xE7, 0x69, 0x00, 0x57,
  /* RLE: 052 Pixels @ 003,047*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 055,047*/ 0, 8, 0xCC, 0x00, 0x00, 0x00, 0xB4, 0x84, 0x00, 0x51,
  /* RLE: 052 Pixels @ 003,048*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 055,048*/ 0, 8, 0xFC, 0x09, 0x00, 0x00, 0x87, 0xA2, 0x00, 0x3F,
  /* RLE: 053 Pixels @ 003,049*/ 53, 0xFF,
  /* ABS: 007 Pixels @ 056,049*/ 0, 7, 0x42, 0x00, 0x00, 0x72, 0xBD, 0x00, 0x18,
  /* RLE: 053 Pixels @ 003,050*/ 53, 0xFF,
  /* ABS: 008 Pixels @ 056,050*/ 0, 8, 0x7B, 0x00, 0x00, 0x69, 0xD2, 0x00, 0x00, 0xDB,
  /* RLE: 052 Pixels @ 004,051*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,051*/ 0, 8, 0xAB, 0x00, 0x00, 0x84, 0xD2, 0x00, 0x00, 0x9F,
  /* RLE: 052 Pixels @ 004,052*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,052*/ 0, 8, 0xC0, 0x00, 0x00, 0xAB, 0xD2, 0x00, 0x00, 0x6C,
  /* RLE: 052 Pixels @ 004,053*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,053*/ 0, 8, 0xBA, 0x00, 0x00, 0xC0, 0xE4, 0x00, 0x00, 0x5D,
  /* RLE: 052 Pixels @ 004,054*/ 52, 0xFF,
  /* ABS: 008 Pixels @ 056,054*/ 0, 8, 0x6F, 0x00, 0x00, 0xC6, 0xFC, 0x15, 0x00, 0x51,
  /* RLE: 042 Pixels @ 004,055*/ 42, 0xFF,
  /* ABS: 021 Pixels @ 046,055*/ 0, 21, 0xCF, 0x99, 0x99, 0x99, 0xB4, 0xCC, 0xCC, 0xFF, 0xD2, 0x99, 0x06, 0x00, 0x00, 0xD8, 0xFF, 0x81, 0x00, 0x06, 0x72, 0xC0, 0xFC,
  /* RLE: 022 Pixels @ 007,056*/ 22, 0xFF,
  /* ABS: 009 Pixels @ 029,056*/ 0, 9, 0xDE, 0xAB, 0x84, 0x66, 0x66, 0x66, 0x93, 0x99, 0xA8,
  /* RLE: 004 Pixels @ 038,056*/ 4, 0xCC,
  /* ABS: 004 Pixels @ 042,056*/ 0, 4, 0x99, 0x72, 0x3C, 0x06,
  /* RLE: 012 Pixels @ 046,056*/ 12, 0x00,
  /* ABS: 005 Pixels @ 058,056*/ 0, 5, 0x15, 0xFC, 0xFF, 0xFC, 0x57,
  /* RLE: 005 Pixels @ 003,057*/ 5, 0x00,
  /* ABS: 011 Pixels @ 008,057*/ 0, 11, 0x0C, 0x33, 0x33, 0x4B, 0x66, 0x66, 0x84, 0x99, 0x99, 0x99, 0xAE,
  /* RLE: 005 Pixels @ 019,057*/ 5, 0xCC,
  /* ABS: 005 Pixels @ 024,057*/ 0, 5, 0xA2, 0x93, 0x66, 0x45, 0x12,
  /* RLE: 028 Pixels @ 029,057*/ 28, 0x00,
  /* ABS: 002 Pixels @ 057,057*/ 0, 2, 0x09, 0xB1,
  /* RLE: 004 Pixels @ 059,057*/ 4, 0xFF,
  /* ABS: 002 Pixels @ 003,058*/ 0, 2, 0xA2, 0x2A,
  /* RLE: 033 Pixels @ 005,058*/ 33, 0x00,
  /* ABS: 008 Pixels @ 038,058*/ 0, 8, 0x15, 0x33, 0x33, 0x60, 0x7E, 0x9C, 0xCC, 0xF0,
  /* RLE: 004 Pixels @ 046,058*/ 4, 0xFF,
  /* ABS: 008 Pixels @ 050,058*/ 0, 8, 0xD2, 0xCC, 0xBA, 0x99, 0x99, 0x7E, 0x99, 0xE4,
  /* RLE: 007 Pixels @ 058,058*/ 7, 0xFF,
  /* ABS: 004 Pixels @ 005,059*/ 0, 4, 0xE4, 0xAB, 0x6F, 0x63,
  /* RLE: 004 Pixels @ 009,059*/ 4, 0x33,
  /* ABS: 024 Pixels @ 013,059*/ 0, 24, 0x5D, 0x66, 0x66, 0x90, 0x99, 0xC0, 0xD2, 0xF9, 0xCC, 0xA2, 0x81, 0x66, 0x51, 0x33, 0x21, 0x0C, 0x33, 0x3C, 0x66, 0x6F, 0x99, 0xA8, 0xCC, 0xDE,
  /* RLE: 023 Pixels @ 037,059*/ 23, 0xFF,


  0};  /* 968 bytes for 3600 pixels */

static GUI_CONST_STORAGE GUI_BITMAP _bmRectRed_60x60 = {
  60, /* XSize */
  60, /* YSize */
  60, /* BytesPerLine */
  GUI_COMPRESS_RLE8, /* BitsPerPixel */
  (unsigned char *)_acRectRed_60x60,  /* Pointer to picture data */
  NULL,  /* Pointer to palette */
  GUI_DRAW_RLEALPHA
};

/*********************************************************************
*
*       _aBitmapItem
*/
static const BITMAP_ITEM _aBitmapItem[] = {
  { &_bmBrowserRad,  "Use the browser to explore the www" },
  { &_bmClockRad,    "Adjust current time and date"       },
  { &_bmDateRad,     "Use the diary"                      },
  { &_bmEmailRad,    "Read an email"                      },
  { &_bmSystemRad,   "Change system settings"             },
  { &_bmReadRad,     "Read a document"                    },
  { &_bmWriteRad,    "Write an email"                     },
  { &_bmPasswordRad, "Determine the system password"      },
  { &_bmRemoteRad,   "Select network"                     },
};

/*********************************************************************
*
*       Private routines
*
**********************************************************************
*/
/*********************************************************************
*
*       _cbDraw
*
*  Function description:
*    Callback routine of radial menu
*/
static void _cbDraw(WM_MESSAGE * pMsg) {
  ITEM_INFO   ItemInfo;
  WM_HWIN     hWin;
  PARA      * pPara;
  int         a1000;
  int         Swap;
  int         i;
  I32         SinHQ;
  I32         CosHQ;
  I32         a;

  hWin = pMsg->hWin;
  switch (pMsg->MsgId) {
  case WM_PAINT:
    WM_GetUserData(hWin, &pPara, sizeof(pPara));
    //
    // One time initialization of parameter structure
    //
    if (pPara->xSizeItem == 0) {
      pPara->xSizeWindow = WM_GetWindowSizeX(hWin);
      pPara->ySizeWindow = WM_GetWindowSizeY(hWin);
      pPara->xSizeItem   = pPara->pBitmapItem[0].pBitmap->XSize;
      pPara->ySizeItem   = pPara->pBitmapItem[0].pBitmap->YSize;
      pPara->rx          = (pPara->xSizeWindow - pPara->xSizeItem) / 2;
      pPara->ry          = (pPara->ySizeWindow - pPara->ySizeItem) / 2;
      pPara->mx          = pPara->xSizeWindow / 2;
      pPara->my          = pPara->ry + pPara->ySizeItem / 2;
    }
    //
    // Calculate current positions of items
    //
    a1000 = (pPara->Pos * 3600) / pPara->NumItems;
    for (i = 0; i < pPara->NumItems; i++) {
      a                         = 90000 + a1000 + (i * 360000) / pPara->NumItems;
      SinHQ                     = GUI__SinHQ(a);
      CosHQ                     = GUI__CosHQ(a);
      pPara->pItemInfo[i].Index = i;
      pPara->pItemInfo[i].xPos  = pPara->mx -  ((CosHQ * pPara->rx) >> 16);
      pPara->pItemInfo[i].yPos  = pPara->my + (((SinHQ * pPara->ry) >> 16) * pPara->ry) / pPara->rx;
    }
    //
    // Bubble sort items to be able to draw background items first
    //
    do {
      Swap = 0;
      for (i = 0; i < (pPara->NumItems - 1); i++) {
        if (pPara->pItemInfo[i].yPos > pPara->pItemInfo[i + 1].yPos) {
          ItemInfo                = pPara->pItemInfo[i];
          pPara->pItemInfo[i]     = pPara->pItemInfo[i + 1];
          pPara->pItemInfo[i + 1] = ItemInfo;
          Swap                    = 1;
        }
      }
    } while (Swap);
    //
    // Draw items
    //
    for (i = 0; i < pPara->NumItems; i++) {
      GUI_DrawBitmap((pPara->pBitmapItem + pPara->pItemInfo[i].Index)->pBitmap, pPara->pItemInfo[i].xPos - pPara->xSizeItem / 2, pPara->pItemInfo[i].yPos - pPara->ySizeItem / 2);
    }
    //
    // Draw item text only after final move
    //
    if (pPara->FinalMove) {
      GUI_SetColor(GUI_WHITE);
      GUI_SetTextMode(GUI_TM_TRANS);
      GUI_SetFont(GUI_FONT_13_ASCII);
      GUI_DispStringHCenterAt((pPara->pBitmapItem + pPara->pItemInfo[pPara->NumItems - 1].Index)->pExplanation, 150, 160);
    }
    //
    // Draw frame surround the current item
    //
    GUI_SetColor(GUI_RED);
    GUI_DrawBitmap(&_bmRectRed_60x60, 120, 95);
    break;
  }
}

/*********************************************************************
*
*       _cbMotion
*
*  Function description:
*    Callback routine of transparent motion window which is responsible
*    for processing the motion messages.
*/
static void _cbMotion(WM_MESSAGE * pMsg) {
  WM_MOTION_INFO * pInfo;
  WM_HWIN          hWin;
  PARA           * pPara;

  hWin = pMsg->hWin;
  switch (pMsg->MsgId) {
  case WM_MOTION:
    WM_GetUserData(hWin, &pPara, sizeof(pPara));
    pInfo = (WM_MOTION_INFO *)pMsg->Data.p;
    switch (pInfo->Cmd) {
    case WM_MOTION_INIT:
      pInfo->Flags = WM_CF_MOTION_X | WM_MOTION_MANAGE_BY_WINDOW;
      pInfo->SnapX = 100;
      break;
    case WM_MOTION_MOVE:
      pPara->FinalMove  = pInfo->FinalMove;
      pPara->Pos       += pInfo->dx;
      if (pPara->Pos > pPara->NumItems * 100) {
        pPara->Pos -= pPara->NumItems * 100;
      }
      if (pPara->Pos < 0) {
        pPara->Pos += pPara->NumItems * 100;
      }
      WM_Invalidate(WM_GetParent(hWin));
      break;
    case WM_MOTION_GETPOS:
      pInfo->xPos = pPara->Pos;
      break;
    }
    break;
  }
}

/*********************************************************************
*
*       _Delay
*
*  Function description:
*    Delay function which returns 1 immediately if automatic animation is off
*/
static int _Delay(int ms) {
  int TimeNow, TimeEnd;

  TimeNow = GUI_GetTime();
  TimeEnd = TimeNow + ms;
  do {
    GUI_Delay(10);
    TimeNow = GUI_GetTime();
  } while (TimeNow < TimeEnd);
  return GUIDEMO_CheckCancel();
}

/*********************************************************************
*
*       _RadialMenu
*
*  Function description:
*    Creates and executes a radial menu with motion support.
*/
static void _RadialMenu(void) {
  static ITEM_INFO   aItemInfo[GUI_COUNTOF(_aBitmapItem)];
  static PARA        Para;
  WM_HWIN            hMotion;
  WM_HWIN            hDraw;
  PARA             * pPara;
  int                xSizeWindow;
  int                ySizeWindow;
  int                i;

  //
  // Initialize parameter structure for items to be shown
  //
  pPara              = &Para;
  pPara->NumItems    = GUI_COUNTOF(_aBitmapItem);
  pPara->pBitmapItem = _aBitmapItem;
  pPara->pItemInfo   = aItemInfo;
  pPara->Pos         = 0;
  //
  // Create radial menu window
  //
  xSizeWindow        = LCD_GetXSize() / 16 * 15;
  ySizeWindow        = LCD_GetYSize() /  4 *  3;
  hDraw              = WM_CreateWindowAsChild(10, 20, xSizeWindow, ySizeWindow, WM_HBKWIN, WM_CF_SHOW | WM_CF_HASTRANS, _cbDraw, sizeof(pPara));
  //
  // Create transparent window which receives the motion messages
  //
  hMotion            = WM_CreateWindowAsChild(0, ySizeWindow / 2, xSizeWindow, ySizeWindow / 2, hDraw, WM_CF_SHOW | WM_CF_HASTRANS, _cbMotion, sizeof(pPara));
  //
  // Add pointer to parameter structure to windows
  //
  WM_SetUserData(hDraw,   &pPara, sizeof(pPara));
  WM_SetUserData(hMotion, &pPara, sizeof(pPara));
  //
  // Animation loop
  //
  if (GUIDEMO_CheckCancel()) {
    return;
  }
  //
  // Turn left (motion)
  //
  WM_MOTION_SetMotion(hMotion, GUI_COORD_X, 200, 200);
  if (_Delay(2000)) {
    WM_DeleteWindow(hMotion);
    WM_DeleteWindow(hDraw);
    return;
  }
  WM_MOTION_SetMotion(hMotion, GUI_COORD_X, 800, 800);
  if (_Delay(2000)) {
    WM_DeleteWindow(hMotion);
    WM_DeleteWindow(hDraw);
    return;
  }
  WM_MOTION_SetMotion(hMotion, GUI_COORD_X, 2000, 2000);
  if (_Delay(2000)) {
    WM_DeleteWindow(hMotion);
    WM_DeleteWindow(hDraw);
    return;
  }
  //
  // Turn right (motion)
  //
  WM_MOTION_SetMotion(hMotion, GUI_COORD_X, -200, 200);
  if (_Delay(2000)) {
    WM_DeleteWindow(hMotion);
    WM_DeleteWindow(hDraw);
    return;
  }
  WM_MOTION_SetMotion(hMotion, GUI_COORD_X, -800, 800);
  if (_Delay(2000)) {
    WM_DeleteWindow(hMotion);
    WM_DeleteWindow(hDraw);
    return;
  }
  WM_MOTION_SetMotion(hMotion, GUI_COORD_X, -2000, 2000);
  if (_Delay(2000)) {
    WM_DeleteWindow(hMotion);
    WM_DeleteWindow(hDraw);
    return;
  }
  //
  // Turn left (movement)
  //
  for (i = 0; i < 5; i++) {
    WM_MOTION_SetMovement(hMotion, GUI_COORD_X, 500, 100);
    if (_Delay(500)) {
      WM_DeleteWindow(hMotion);
      WM_DeleteWindow(hDraw);
      return;
    }
  }
  WM_DeleteWindow(hMotion);
  WM_DeleteWindow(hDraw);
}

/*********************************************************************
*
*       Public routines
*
**********************************************************************
*/
/*********************************************************************
*
*       MainTask
*/
void GUIDEMO_RadialMenu(void) {
  GUIDEMO_HideInfoWin();
  GUIDEMO_ShowIntro("Radial Menu",
                    "Selecting an icon from a radial menu.\n"
                    "Changing the selection is done\n"
                    "using STemWin motion support.");
  WM_MOTION_Enable(1);
  _RadialMenu();
  WM_MOTION_Enable(0);
}

#endif  // (SHOW_GUIDEMO_RADIALMENU)

/*************************** End of file ****************************/
