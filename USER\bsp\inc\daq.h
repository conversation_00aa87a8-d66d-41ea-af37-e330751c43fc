#ifndef _DAQ_H
#define _DAQ_H
#include "bsp.h"

#define OVERVIEW_DATABUF_LEN	4096*40
#define ONEWAVE_LEN				4096
#define DATA_LENGTH 			4096//ADC_POINTS_NUM


#define FS_MHZ_LS		50  //ADC低速采样
#define FS_MHZ_HS_CALC	500 //计算频率
#define FS_MHZ_HS_ADC	250 //ADC采样频率

#define DAQ_CH_NUM		1

enum WAVE_RECV { RECV_IDLE = 0, RECV_ING, RECV_DONE };//空闲、接收(采集)中、接收(采集)完成


//数据采集
typedef struct
{
	int16_t		*waveSrc_buf;		// 波形原始数据数组指针
	uint8_t  	dataValid;			// 数据有效
//	uint16_t	fs_MHz;
//	uint32_t	waveBgn_ns;			//
//	uint32_t	waveLen_ns;			//
//	uint32_t	waveBgn_pt;			//
//	uint32_t	waveLen_pt;			//
	int16_t		waveMax_val;
	int32_t		waveMax_idx;
}DAQ_DATA_T;

//数据采集
typedef struct
{
	uint8_t				isRunning;		//数据采集运行中（设备测量中）
//	uint8_t				testData_en;	//开启测试数据
	enum WAVE_RECV	 	waveRecvStage;  //0:发射就绪(空闲); 1:发射中; 2:一帧数据完成;
	uint16_t			fs_MHz;			//采样频率, 计算使用(若有插值则为插值后的)
	uint16_t			avg;
	float				gainVal;		//
	uint16_t			filter_LP_KHz;
	uint16_t			filter_HP_KHz;
	uint32_t			waveBgn_ns;			//
	uint32_t			waveLen_ns;			//
	uint32_t			waveBgn_pt;			//
	uint32_t			waveLen_pt;			//
//	uint8_t				ppkeep_en;
//	int32_t				ppkeep_bgn_pt;
//	int32_t				ppkeep_end_pt;
	DAQ_DATA_T			data;			//
}DAQ_T;
extern DAQ_T g_Daq[DAQ_CH_NUM];

uint8_t DAQ_SetAvg(uint8_t ch, uint16_t avg);
uint8_t DAQ_SetFs(uint8_t ch, uint16_t fs_MHz);
uint8_t DAQ_SetGain(uint8_t ch, float val);
uint8_t DAQ_SetWaveBgnTime(uint8_t ch, uint32_t time_ns);
uint8_t DAQ_SetWaveLenTime(uint8_t ch, uint32_t time_ns);
uint8_t DAQ_SetWaveBgnPt(uint8_t ch, uint32_t pt);
uint8_t DAQ_SetWaveLenPt(uint8_t ch, uint32_t pt);

#endif
