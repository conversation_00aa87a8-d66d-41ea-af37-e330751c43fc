/**
************************************************************************************************************************
* Copyright (C), 2018-2022, 苏州博昇科技有限公司, www.phaserise.com
* @file    : fpga.c
* @project : 
* @brief   : 
* <AUTHOR> PR Team
* @date    : 2021/06/01
* @version : V1.0 
* @history :
*   2023/01/04: 
*     1) 修改 SendCmd2FPGA_Type() 函数，默认通道为通道1;
*
************************************************************************************************************************
* @note
* 
************************************************************************************************************************
* @attention
* 
************************************************************************************************************************
*/
#include "includes.h"
#include "fpga.h"
//说明：STM32和FPGA之间的通讯程序。
//实现重复次数
//u16 W25QXX_ReadID(void);

static uint32_t fpgaVersionCode = 0;
/**
  * @brief  STM32与FPGA连接IO初始化(发射触发、数据RD、ADC控制)
  * PB10-发射触发; PB11-FPGA数据Ready标志;
  * @param  None
  * @retval None
  */
void FPGA_IO_Init(void)//采用IO口PB0实现重复次数
{
	GPIO_InitTypeDef GPIO_InitStruct;
	__HAL_RCC_GPIOF_CLK_ENABLE();

	//Emit-Trig PF0(FMC-A0)
	GPIO_InitStruct.Pin = GPIO_PIN_0;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLDOWN;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);
	EMIT_Trig(0);

	//DAQData-Ready PF6(AF-D1)
//	GPIO_InitStruct.Pin=GPIO_PIN_11; //PB11
//	GPIO_InitStruct.Mode=GPIO_MODE_INPUT; 
//	GPIO_InitStruct.Pull=GPIO_PULLDOWN;          //上拉
//	GPIO_InitStruct.Speed=GPIO_SPEED_HIGH;     //高速
//	HAL_GPIO_Init(GPIOB,&GPIO_InitStruct);     //初始化GPIOI.7

	FPGA_FMC_DataRxTx_Init();
	//QSPI初始化(ADC等数据通讯)
//	QSPI_Init();
	fpgaVersionCode = FPGA_ReadVersionReg();
}

static void FPGA_SendPPKeepCmd(uint8_t en, int32_t bgn,  int32_t end) {
	uint8_t buf[15];
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_PPKEEP;
	buf[3] = 0; //保留
	buf[4] = en;
	buf[5] = 0x00;//((bgn&0xff000000)>>24);
	buf[6] = ((bgn&0x00ff0000)>>16);
	buf[7] = ((bgn&0x0000ff00)>>8);
	buf[8] = ( bgn&0x000000ff);
	buf[9] = 0x00;//((end&0xff000000)>>24);
	buf[10] = ((end&0x00ff0000)>>16);
	buf[11] = ((end&0x0000ff00)>>8);
	buf[12] = ( end&0x000000ff);
	FpgaUart_SendDatas(buf, 13);
}

static void SendCmd2FPGA_Type(uint8_t type)
{
	static uint8_t buf[15]={0x0D, 0x0A, 0x01, 1,3,50,64,0,0,10,0x20,0x00};
	uint8_t tmp;
	CHANNEL_T *chn;
	
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	
	chn = &g_CHN[0]; //默认参数来自于通道1
	
	if ((type == FPGA_CMD_EMAT) || (type == FPGA_CMD_CH1) || (type == FPGA_CMD_PPKEEP))
		chn = &g_CHN[0];
//	else if(type == FPGA_CMD_CH1)
//		chn = &g_CHN[0];
	
	if(type == FPGA_CMD_EMAT) { //
		buf[2]  = type;
		buf[3]  = g_Dev.emit_en;
		buf[4]  = g_Dev.trig_mode;
		buf[5]  = (((g_Dev.encTrigPul_num)&0xff00)>>8);
		buf[6] 	= ( (g_Dev.encTrigPul_num)&0x00ff);
		buf[9]  = (((chn->daq->fs_MHz)&0xff00)>>8);
		buf[10] = ( (chn->daq->fs_MHz)&0x00ff);
		buf[11] = (((chn->daq->avg)	 &0xff00)>>8);
		buf[12] = ( (chn->daq->avg)	 &0x00ff);
	}
	else if((type == FPGA_CMD_CH1) || (type == FPGA_CMD_CH2) || (type == FPGA_CMD_CH3) || (type == FPGA_CMD_CH4)) { // CH1/CH2/CH3/CH4 参数
		buf[2]  = type;
		// | b4:HV1_en | b3:HV2_en | b2:pul_dir | b1:chirp_en | b0:ch_en |
		buf[3]  = ((chn->emat->HV2_en & 0x01) << 4) | ((chn->emat->HV1_en & 0x01) << 3) | ((chn->emat->emitPulDir & 0x01) << 2) | (chn->emat->chirp_en << 1) | ((chn->enable & 0x01) << 0);
		//脉冲数参数组合, b7表示是否+0.5脉冲, b6~b0表示整发射脉冲数;
		buf[4]  = (chn->emat->emitHalfPul_en) ? (chn->emat->emitPulNum | 0x80) : (chn->emat->emitPulNum & 0x7f);	//周期数
		//刹车参数组合, b7表示刹车使能, b6表示刹车是否+0.5脉冲, b5表示刹车是否延迟半个周期启动, b3~b0刹车整脉冲数;
		tmp = (chn->emat->brakePulNum & 0x0f) | ((chn->emat->brakeDlyHalfPul_en & 0x01) << 5) | ((chn->emat->brakeHalfPul_en & 0x01) << 6) 
		| (((chn->emat->brakePulNum != 0) || (chn->emat->brakeHalfPul_en != 0)) ? 0x80 : 0x00);
		buf[5]  = tmp;
		buf[6]  = 0x00;
		buf[7]  = (((chn->daq->waveBgn_pt)&0x00ff0000)>>16);
		buf[8]  = (((chn->daq->waveBgn_pt)&0x0000ff00)>>8);
		buf[9]  = ( (chn->daq->waveBgn_pt)&0x000000ff);
		buf[10] = (((chn->daq->waveLen_pt)&0x0000ff00)>>8);
		buf[11] = ( (chn->daq->waveLen_pt)&0x000000ff);
		buf[12] = 0x00;
	}
	else if(type == FPGA_CMD_PPKEEP) {
		FPGA_SendPPKeepCmd(chn->emat->ppkeep_en, chn->emat->ppkeep_bgn_pt, chn->emat->ppkeep_end_pt);
	}
	else if(type == FPGA_CMD_EMIT_ENCODE) {
		buf[2]  = type;
		buf[3]  = 0; //保留
		buf[4]  = (uint8_t)(chn->emat->emitEncode >>  0 & 0x000000ff);
		buf[5]  = (uint8_t)(chn->emat->emitEncode >>  8 & 0x000000ff);
		buf[6] 	= (uint8_t)(chn->emat->emitEncode >> 16 & 0x000000ff);
		buf[7]  = (uint8_t)(chn->emat->emitEncode >> 24 & 0x000000ff);
	}
	

	FpgaUart_SendDatas(buf, 13);
	
	if(type == FPGA_CMD_EMAT) {
		delay_us(20000); // 若涉及采样率的改变，则延迟 50ms, 待其同步生效
	}
}

void SendCmd2FPGA(void) {
	uint8_t emiten = g_Dev.emit_en;
	
	//停止
	g_Dev.emit_en = 0;
	SendCmd2FPGA_Type(FPGA_CMD_EMAT);
	delay_us(10);
	
	SendCmd2FPGA_Type(FPGA_CMD_EMIT_ENCODE);
	delay_us(10);
	
	SendCmd2FPGA_Type(FPGA_CMD_CH1);
	delay_us(10);
	SendCmd2FPGA_Type(FPGA_CMD_CH2);
	delay_us(10);
	SendCmd2FPGA_Type(FPGA_CMD_CH3);
	delay_us(10);
	SendCmd2FPGA_Type(FPGA_CMD_CH4);
	delay_us(10);
	
	SendCmd2FPGA_Type(FPGA_CMD_PPKEEP);
	delay_us(10);
	
	//继续上次状态
	g_Dev.emit_en = emiten;
	SendCmd2FPGA_Type(FPGA_CMD_EMAT);
	delay_us(10);
}


void FPGA_ClearEncoderData(void) {
	uint8_t buf[15];
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_CLEAR_ENCODER;
	buf[6] = 0x00;
	buf[7] = 0x01; //clear encoder-data
	FpgaUart_SendDatas(buf, 13);
}
void FPGA_SetAdcTestData(uint8_t enable) {
	uint8_t buf[15];
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_CLEAR_ENCODER;
	buf[6] = enable ? 0x01 : 0x00;
	buf[7] = 0x00;
	FpgaUart_SendDatas(buf, 13);
}

void SendCmd2FPGA_PPKeep() {
	SendCmd2FPGA_Type(FPGA_CMD_PPKEEP);
}

static void Fmc_ReadOutPrepareData(void) {
	uint16_t es2b;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
}

//FPGA 数据发送为 双字节/小端模式 LSB,MSB
static uint16_t extrabuf[FPGA_DATA_EXTRA_NUM/2];
u16 ReadData_fromFPGA(uint8_t chn) {
	uint32_t 	i, rxlen;
	uint8_t		cmd;
	CHANNEL_T	*pchn;
//	uint16_t	extrabuf[FPGA_DATA_EXTRA_NUM/2];
	uint16_t *pwavebuf;

	if(chn == 1) {
		cmd = FPGA_CMD_READ_DAQ_DATA_CH1;
		pchn = &g_CHN[0];
	}
	
	FMC_CS(0);
	memset(extrabuf, 00, sizeof(extrabuf));
	//read extra data
	rxlen = sizeof(extrabuf) / 2;
	pwavebuf = (uint16_t *)extrabuf;
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = cmd;
	delay_us(1); //此处延时不可删除！！！
	Fmc_ReadOutPrepareData();
	for(i = 0; i < rxlen; i++) {
		pwavebuf[i] = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	}
	//encoder data
//	pchn->encData1 = (extrabuf[0] << 16) | (extrabuf[1]);
//	pchn->encData2 = (extrabuf[2] << 16) | (extrabuf[3]);
	
	g_Dev.dataAcqCnt_fpga 	= (extrabuf[0] << 16) | (extrabuf[1]);
//	g_Dev.dataAcqCnt_adc 	= extrabuf[2];
//	g_Dev.dataAcqCnt_emit 	= extrabuf[3];
	pchn->encData1 = (extrabuf[2] << 16) | (extrabuf[3]);
	
	//read wave data
	rxlen = pchn->daq->waveLen_pt;
	pwavebuf = (uint16_t *)pchn->daq->data.waveSrc_buf;
//	for(i = 0; i < rxlen; i++) {
//		pwavebuf[i] = *(volatile uint8_t *)FPGA_DATA_BASE;
//	}
	memcpy(pwavebuf, (uint32_t *)FMC_FPGA_BASE_ADDR, rxlen * 2);
	
	pwavebuf[rxlen] = 0x0000;
	pwavebuf[rxlen + 1] = 0xF5F5;

	FMC_CS(1);
	
	return 0;
}


uint16_t FPGA_Read_DataStateReg(void) {
	uint16_t content2b; //uint8_t msb, lsb;
	
	FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = FPGA_CMD_READ_STATE_REG;
	delay_us(1); 
	Fmc_ReadOutPrepareData();
//	lsb = *(volatile uint8_t *)FMC_FPGA_BASE_ADDR;
//	msb = *(volatile uint8_t *)FMC_FPGA_BASE_ADDR;
	content2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	FMC_CS(1);
	
	return content2b; //return (((uint16_t)msb << 8) | lsb);
}

//FPGA版本号,指令码0xB0，接收数据为0xB0xx, 低8bits为版本号
uint16_t FPGA_ReadVersionReg() {
	uint16_t content2b; //	uint8_t msb, lsb;
	
	FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = FPGA_CMD_READ_VERSION_REG;
	delay_us(1); 
	Fmc_ReadOutPrepareData();
//	lsb = *(volatile uint8_t *)FMC_FPGA_BASE_ADDR;
//	msb = *(volatile uint8_t *)FMC_FPGA_BASE_ADDR;
	content2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	FMC_CS(1);
	
	return content2b; //(((uint16_t)msb << 8) | lsb);
}

void FPGA_ClearDaqData(void) {
	FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = FPGA_CMD_CLEAR_DAQ_TXDATA;
	FMC_CS(1);
}


/***********************END OF FILE********************************************/
