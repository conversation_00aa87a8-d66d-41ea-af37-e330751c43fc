#ifndef _USART_H
#define _USART_H
#include "bsp.h"

// #define USART3_RXBUF_LEN  			50  	//定义最大接收字节数 50
//#define EN_USART2_RX 			1		//使能（1）/禁止（0）串口1接收

// #define RS485DE(n)		(n?HAL_GPIO_WritePin(GPIOH,GPIO_PIN_5,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOH,GPIO_PIN_5,GP<PERSON>_PIN_RESET))
// //#define RS485RE(n)		(n?HAL_GPIO_WritePin(GPIOH,GPIO_PIN_3,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOH,GPIO_PIN_3,GPIO_PIN_RESET))
// #define RS485RXEN		{RS485DE(0);}//{RS485RE(0);RS485DE(0);}
// #define RS485TXEN		{RS485DE(1);}//{RS485RE(1);RS485DE(1);}
	  	
//extern u8  USART_RX_BUF[USART_REC_LEN]; //接收缓冲,最大USART_REC_LEN个字节.末字节为换行符 
//extern u16 USART_RX_STA;         		//接收状态标记	
//extern UART_HandleTypeDef UART1_Handler; //UART句柄

// #define RXBUFFER_SIZE   1 //缓存大小
//extern u8 aRxBuffer[RXBUFFER_SIZE];//HAL库USART接收Buffer


void Uart1_Init(u32 bound);
void Uart7_Init(u32 bound);


void UART1_SendData(uint8_t *pData, uint32_t length);
void Send2BytesData2PC(int16_t *pData, uint32_t length);
void RxOneFrameDone(void);
void ModuleBoxMain(void);

void FpgaUart_SendDatas(uint8_t *dataBuf, uint32_t len);
void RS485_SendDatas(uint8_t *dataBuf, uint32_t len);

void Modbus_Uart1RxDone();

#endif
