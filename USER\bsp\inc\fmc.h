/**
  ******************************************************************************
  * Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com
  * @file 	 : fmc.h
  * @project ：PREMAT_M4
  * <AUTHOR> YL
  * @date    ：2021/11/11
  * @version ：v1.0
  * @brief
  * @history :
  *
  * 
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#ifndef __FMC_H
#define __FMC_H		
#include "sys.h"	 
#include "stdlib.h" 

//LCD MPU保护参数
#define LCD_REGION_NUMBER		MPU_REGION_NUMBER0		//LCD使用region0
#define LCD_ADDRESS_START		(0X60000000)			//LCD区的首地址
#define LCD_REGION_SIZE			MPU_REGION_SIZE_256MB   //LCD区大小

//使用NOR/SRAM的 Bank1.sector4,地址位HADDR[27,26]=11 A18作为数据命令区分线 
//注意设置时STM32内部会右移一位对其! 			    
#define LCD_BASE        ((u32)(0x60000000 | 0x0007FFFE))
#define LCD             ((LCD_TypeDef *) LCD_BASE)
//////////////////////////////////////////////////////////////////////////////////
#define FPGA_DATA_BASE        ((u32)(0x60000000))
#define FMC_FPGA_BASE_ADDR    FPGA_DATA_BASE

#define FMC_CS(n)		(n?HAL_GPIO_WritePin(GPIOC,GPIO_PIN_7,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOC,GPIO_PIN_7,GPIO_PIN_RESET))
	    															  
void FPGA_FMC_DataRxTx_Init(void);
//uint16_t FPGA_DATA_ReadStateReg();//初始化


#endif
	 
	 



