# TI Smart Lithium Battery UDP Communication Implementation

## Overview

This implementation adds a second UDP communication channel on port 8090 specifically for transmitting TI smart lithium battery data while maintaining the existing UDP communication on port 8089.

## Network Configuration

- **STM32 Local IP**: ************
- **Host Computer IP**: *************
- **Existing UDP Channel**: Port 8089 (unchanged)
- **New Battery UDP Channel**: Port 8090

## Implementation Details

### 1. Dual UDP Socket Architecture

The implementation creates two independent UDP connections:

- **Primary UDP (Port 8089)**: Handles existing application data
- **Battery UDP (Port 8090)**: Dedicated to battery data transmission

### 2. Battery Data Structure

```c
typedef struct {
    uint16_t voltage_mv;        // Battery voltage in millivolts
    int16_t current_ma;         // Battery current in milliamps
    int16_t temperature_c;      // Battery temperature in Celsius * 10
    uint8_t state_of_charge;    // State of charge percentage (0-100)
    uint8_t state_of_health;    // State of health percentage (0-100)
    uint16_t cycle_count;       // Number of charge/discharge cycles
    uint8_t status_flags;       // Battery status flags
    uint8_t protection_flags;   // Protection status flags
    uint32_t timestamp;         // Timestamp of the data
    uint8_t reserved[3];        // Reserved for future use
} __attribute__((packed)) TI_Battery_Data_t;
```

### 3. Key Files Modified/Added

#### New Files:
- `USER/bsp/inc/ti_battery.h` - Battery module header
- `USER/bsp/src/ti_battery.c` - Battery module implementation

#### Modified Files:
- `LWIP/lwip_app/inc/netconn_udp.h` - Added battery UDP definitions
- `LWIP/lwip_app/src/netconn_udp.c` - Added battery UDP socket management
- `LWIP/lwip_app/inc/lwipopts.h` - Updated lwIP configuration
- `USER/bsp/bsp.h` - Added battery module include
- `USER/main.c` - Added battery UDP initialization
- `USER/app/app.c` - Added battery task integration

### 4. Battery Status Flags

#### Status Flags:
- `BATTERY_STATUS_CHARGING` (0x01)
- `BATTERY_STATUS_DISCHARGING` (0x02)
- `BATTERY_STATUS_FULL` (0x04)
- `BATTERY_STATUS_EMPTY` (0x08)
- `BATTERY_STATUS_ERROR` (0x80)

#### Protection Flags:
- `BATTERY_PROT_OVERVOLTAGE` (0x01)
- `BATTERY_PROT_UNDERVOLTAGE` (0x02)
- `BATTERY_PROT_OVERCURRENT` (0x04)
- `BATTERY_PROT_OVERTEMP` (0x08)
- `BATTERY_PROT_UNDERTEMP` (0x10)

## Usage

### 1. Initialization

The battery module is automatically initialized in the main application:

```c
// In main.c
while(udp_battery_init()) {
    delay_ms(500);
}

// In app.c
Battery_Init();
```

### 2. Data Transmission

Battery data is automatically transmitted every 1000ms (configurable via `BATTERY_UPDATE_PERIOD`):

```c
// Called in main application loop
Battery_Task();
```

### 3. Manual Data Transmission

You can also manually send battery data:

```c
TI_Battery_Data_t battery_data;
Battery_ReadData(&battery_data);
udp_send_battery_data(&battery_data);
```

### 4. Simulation Mode

For testing without actual battery hardware, simulation mode is enabled by default:

```c
Battery_EnableSimulation(1);  // Enable simulation
Battery_EnableSimulation(0);  // Disable simulation (use real hardware)
```

## Host Computer Integration

### Receiving Battery Data

On the host computer (*************), create a UDP socket listening on port 8090:

```python
import socket
import struct

# Create UDP socket
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
sock.bind(('*************', 8090))

while True:
    data, addr = sock.recvfrom(1024)
    if len(data) == 20:  # Size of TI_Battery_Data_t
        # Unpack battery data
        voltage, current, temp, soc, soh, cycles, status, protection, timestamp = struct.unpack('<HhhBBHBBL3x', data)
        print(f"Battery: {voltage}mV, {current}mA, {temp/10}°C, SOC:{soc}%, SOH:{soh}%")
```

## Testing and Validation

### 1. Network Connectivity Test

Verify both UDP channels are working:

```bash
# Test existing channel (port 8089)
nc -u ************ 8089

# Test battery channel (port 8090)
nc -u ************ 8090
```

### 2. Battery Data Monitoring

Use the provided simulation mode to generate test data and verify transmission.

### 3. Performance Verification

- Both UDP channels operate independently
- No interference between channels
- Battery data transmitted every 1 second
- Existing application functionality unchanged

## Configuration Options

### Battery Update Period

Modify `BATTERY_UPDATE_PERIOD` in `ti_battery.h`:

```c
#define BATTERY_UPDATE_PERIOD   1000    // Battery data update period in ms
```

### lwIP Configuration

The implementation uses these lwIP settings:

```c
#define MEMP_NUM_UDP_PCB        6       // UDP PCB count (sufficient for dual channels)
#define MEMP_NUM_NETBUF         16      // Increased netbuf pool
#define DEFAULT_UDP_RECVMBOX_SIZE 2000  // UDP receive mailbox size
```

## Future Enhancements

### 1. Real Hardware Integration

Replace simulation functions with actual I2C communication:

```c
// Implement in ti_battery.c
Battery_Status_t Battery_ReadRegister(uint8_t reg, uint16_t *value) {
    // Add I2C communication code here
    return BATTERY_STATUS_OK;
}
```

### 2. Error Handling

Add robust error handling for communication failures and battery faults.

### 3. Data Logging

Implement local data logging for battery history and diagnostics.

### 4. Configuration Commands

Add UDP commands to configure battery monitoring parameters remotely.

## Troubleshooting

### Common Issues

1. **UDP Connection Failed**: Check network configuration and firewall settings
2. **No Battery Data**: Verify simulation mode is enabled or hardware is connected
3. **Data Corruption**: Check network packet size and endianness
4. **Performance Issues**: Monitor lwIP memory usage and adjust buffer sizes

### Debug Information

Enable debug output by modifying `LWIP_DEBUG` in `lwipopts.h` for detailed network diagnostics.

## Conclusion

This implementation successfully adds a dedicated UDP channel for TI smart lithium battery data transmission while preserving all existing functionality. The modular design allows for easy integration of real battery hardware and future enhancements.
