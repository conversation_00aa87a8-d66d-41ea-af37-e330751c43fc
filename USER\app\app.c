/**
  *********************************************************************************************************
  * Copyright (C), 2018-2022, 苏州博昇科技有限公司, www.phaserise.com
  * @file 	 : app.c
  * @project ：ACUT_GCV1
  * <AUTHOR> YL
  * @date    ：2021/11/24
  * @version ：v1.0
  * @brief
  * @history :
  * 2023/01/04: 
  *  1) 扫频结束时，增加 g_Dev.sys_state = STATE_IDLE;
  *
  * 
  *********************************************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#include "app.h"
#include "includes.h"

static uint8_t DAQ_DataReceive(uint8_t isContinous);
static uint8_t DAQ_DataSend();

CHANNEL_T g_CHN[CHN_NUM];


/**
  * @brief  
  * @param  None
  * @retval None
  */
static uint8_t RxPC_Data_Handler(void)
{
//	if(g_Com.rxPC_numInCache != 0) { //多缓冲机制
	if(g_Com.rxPC_rdCache_id != g_Com.rxPC_wrCache_id) {
//		LED1(0);
		
		RxDataFromUpper_Exe(g_Com.rxPC_cache[g_Com.rxPC_rdCache_id], g_Com.rxPC_cache_src[g_Com.rxPC_rdCache_id], RXBUF_LEN);
		Comm_rdCacheId_Inc();
//		g_Com.rxPC_rdCache_id = (g_Com.rxPC_rdCache_id < (CACHE_DEPTH-1)) ? (g_Com.rxPC_rdCache_id + 1) : 0;
//		g_Com.rxPC_numInCache--;
//		delay_ms(30); LED1(1);
		return 0;
	}
	return 1;
}

static void DevPara_Init() {
	g_Dev.modbus_addr		= 1;
	g_Dev.runMode			= RUN_MODE_COMMON;
	g_Dev.emit_en 			= 0;
	g_Dev.trig_mode 		= TRIG_MODE_INT;
	g_Dev.repeat_times 		= 10;
	g_Dev.encTrigPul_num 	= 100;
	g_Dev.waveRecvStage		= RECV_IDLE;
	g_Dev.ematParasChanged	= 0;
	g_Com.devCommPort		= COM_NET;//COM_RS422_1;
	g_Dev.dataAcqCnt		= 0;
	g_Dev.sys_state			= STATE_IDLE;

}

static void CommPara_Init() {
	int i, j;
	
	g_Com.rxPC_rdCache_id 	= 0;
	g_Com.rxPC_wrCache_id 	= 0;
	
	for(i = 0; i < CACHE_DEPTH; i++) {
		g_Com.rxPC_cache_src[i] = 0;
		for(j = 0; j < RXBUF_LEN; j++) {
			g_Com.rxPC_cache[i][j] = 0;
		}
	}
}

int16_t chnbuf1[DATA_BUF_LEN];
//int16_t chnbuf2[DATA_BUF_LEN];
//int16_t chnbuf3[DATA_BUF_LEN];
//int16_t chnbuf4[DATA_BUF_LEN];
static void Channel_Para_Init() {
	int i, j;
	
	//CH1
	g_CHN[0].emat 						= &g_Emat[0];
	g_CHN[0].daq 						= &g_Daq[0];
	g_CHN[0].enable 					= 1;
	g_CHN[0].emat->emitPulNum 			= 3;
	g_CHN[0].emat->emitHalfPul_en 		= 0;
	g_CHN[0].emat->emitFreq_MHz	 		= 4.3;
	g_CHN[0].emat->chirp_en 			= 1;
	g_CHN[0].emat->emitDly_ns 			= 0;
	g_CHN[0].emat->emitPulDir 			= 0;
	g_CHN[0].emat->brakeComp_KHz 		= g_CHN[0].emat->emitFreq_MHz * 1000;
	g_CHN[0].emat->brakeDlyHalfPul_en 	= 0;         
	g_CHN[0].emat->brakeFreq_KHz 		= g_CHN[0].emat->emitFreq_MHz * 1000;
	g_CHN[0].emat->brakeHalfPul_en 		= 0;
	g_CHN[0].emat->brakePulNum 			= 0;
	g_CHN[0].emat->emitVoltage 			= 0;
	g_CHN[0].emat->emitVoltage_HVM2		= 0;
	g_CHN[0].emat->HV1_en 				= 1;
	g_CHN[0].emat->HV2_en				= 1;
	g_CHN[0].emat->sweepFreq_bgnFreq	= 1000000;
	g_CHN[0].emat->sweepFreq_endFreq	= 5000000;
	g_CHN[0].emat->sweepFreq_stepFreq	= 1000;//100000;
	
	g_CHN[0].emat->chirp_en 			= 0;
	g_CHN[0].emat->chirpFreq_bgnFreq	= 3000000;
	g_CHN[0].emat->chirpFreq_endFreq	= 5000000;
	g_CHN[0].emat->emitEncode			= 0x00000000;//全0表示不反向
	
	g_CHN[0].emat->ppkeep_en 			= 1;
	g_CHN[0].emat->ppkeep_bgn_pt		= 10;
	g_CHN[0].emat->ppkeep_end_pt		= 100;
		 
	g_CHN[0].daq->avg 					= 1;
	g_CHN[0].daq->fs_MHz 				= 50;
	g_CHN[0].daq->waveRecvStage 		= RECV_IDLE;
	g_CHN[0].daq->waveBgn_ns 			= 0;
	g_CHN[0].daq->waveLen_ns 			= 2000;
	g_CHN[0].daq->waveBgn_pt 			= g_CHN[0].daq->waveBgn_ns * g_CHN[0].daq->fs_MHz / 1000.0;
	g_CHN[0].daq->waveLen_pt 			= g_CHN[0].daq->waveLen_ns * g_CHN[0].daq->fs_MHz / 1000.0;

	
	g_CHN[0].daq->filter_HP_KHz			= 40;
	g_CHN[0].daq->filter_LP_KHz			= 2000;
	g_CHN[0].daq->gainVal				= 20.0;
	
	g_CHN[0].daq->data.waveSrc_buf		= chnbuf1;//(int16_t *)mymalloc(SRAMIN, 2 * DATA_BUF_LEN);
}

/**
  * @brief  
  * @param  None
  * @retval None
  */
/*
	指令接收、解析、开启发射采集任务、开启数据传输任务
*/
//int16_t tstbuf_cmd[20];
//int16_t tstbuf1[512];
//int16_t tstbuf2[512];
//uint8_t tstSend[1200];
//uint8_t tst_cnt_prea;
void App_MainTask(void) {
	CommPara_Init();
	Channel_Para_Init();
	DevPara_Init();
	EEPROM_LoadData();

	// Initialize battery module
	Battery_Init();
	
	AFE_SetInnerPreampValid();
	AFE_SetFilter_LP(g_Daq[0].filter_LP_KHz);
	AFE_SetFilter_HP(g_Daq[0].filter_HP_KHz);
	DAQ_SetGain(1, g_Daq[0].gainVal);
	Emat_SetEmitVol(1, g_Emat[0].emitVoltage);
	
	SendCmd2FPGA();
	DDS_SetPara();
	
	//test
//	g_Dev.emit_en 			= 0;
//	g_Dev.trig_mode 		= TRIG_MODE_INT;
//	g_Dev.repeat_times 		= 2;
//	g_CHN[0].enable			= 1;
//	
	g_Daq[0].fs_MHz = 5;//50;
	DAQ_SetWaveBgnPt(1, 0);
	DAQ_SetWaveLenPt(1, 500);
	DAQ_SetGain(1, 0);
	
//	AFE_SetInnerPreampValid();
//	AFE_SetFilter_LP(2000);
//	AFE_SetFilter_HP(80);
//	FPGA_SetAdcTestData(0);

	g_CHN[0].emat->chirp_en = 1;
	g_CHN[0].emat->emitFreq_MHz = 2; //100/1000.0;
//	g_CHN[0].emat->chirpFreq_endFreq = 100000;
	g_CHN[0].emat->emitPulNum = 8;
	g_CHN[0].emat->emitHalfPul_en = 0;
	g_Dev.emit_en = 0;


	//外触发测试
//	g_Dev.emit_en = 1;
//	g_Dev.runMode = 0; //（0）普通、（1）扫频、（2）测厚、（3）调频
//	g_Dev.trig_mode = 0; //（0）内触发、（1）外触发、（2）编码器触发
//	g_Dev.repeat_times = 20; 
//	g_CHN[0].emat->emitEncode = 0x00000000;
//	g_CHN[0].emat->chirp_en	= (g_Dev.runMode == RUN_MODE_COMMON) || (g_Dev.runMode == RUN_MODE_SWEEP_FRQ) || (g_Dev.runMode == RUN_MODE_CHIRP);
//	g_Dev.sys_state = STATE_RUN_PREPARE;
//	delay_ms(500);
//	g_Dev.emit_en = 0;
//	SendCmd2FPGA();
//	g_Dev.emit_en = 1;
//	SendCmd2FPGA();
//	g_Dev.emit_en = 0;
//	g_Dev.ematParasChanged 	= 1;
	
//	delay_ms(500);
	
//	Gain_SetGain(1, 100.0);
	while(1) {
		RxPC_Data_Handler();

		// Handle battery data collection and transmission
		Battery_Task();
		
//		if(g_Dev.guard.devWell == 0) {
//			LED1(1); LED2(1);
//			delay_ms(100);
//			LED1(0); LED2(0);
//			delay_ms(100);
//			LED1(1); LED2(1);
//			delay_ms(100);
//			LED1(0); LED2(0);
//			delay_ms(100);
//			LED1(1); LED2(1);
//			delay_ms(500);
//			LED1(0); LED2(0);
//			delay_ms(1000);
//			continue;
//		}
		
		if(g_Dev.sys_state == STATE_IDLE) {
			delay_ms(1);
		}
		else if(g_Dev.sys_state == STATE_RUN_PREPARE) {
			g_Dev.emit_en			= 1; //不可少
			g_Dev.dataAcqCnt 		= 0; //不可少
			g_Dev.ematParasChanged	= 1;
			//清除FPGA缓存的数据
			DAQ_DataReceive(1);
			g_Dev.emit_en			= 1; //不可少
			g_Dev.dataAcqCnt 		= 0; //不可少
			g_Dev.waveRecvStage 	= RECV_IDLE;
			
			g_Dev.sys_state 		= STATE_RUN_ING;
//			tst_cnt_prea++;
		}
		else if(g_Dev.sys_state == STATE_RUN_ING) {
			if((g_Dev.runMode == RUN_MODE_COMMON) || (g_Dev.runMode == RUN_MODE_CHIRP)) {
				//ADC数据读取
				DAQ_DataReceive(1); //连续采集
				DAQ_DataSend();
			}
			else if(g_Dev.runMode == RUN_MODE_SWEEP_FRQ) {
				if(DAQ_DataReceive(0) != 0)
					continue;
				//接收完成一组数据
				DAQ_DataSend();
				
				//判断是否到达
				if(g_Emat[0].sweepFreq_bgnFreq > g_Emat[0].sweepFreq_endFreq ) {
					//频率减小
					if(g_Emat[0].sweepFreq_currFreq <= g_Emat[0].sweepFreq_endFreq) {
						//结束
						g_Dev.emit_en = 0;
						g_Dev.ematParasChanged = 1;
						g_Dev.sys_state = STATE_IDLE;
					}
					else {
						//变频
						g_Emat[0].sweepFreq_currFreq = g_Emat[0].sweepFreq_currFreq - g_Emat[0].sweepFreq_stepFreq;
	//					DDS_SetParaDetail(g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].emitPulNum);
						DDS_SetParaChirp(g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].emitPulNum, g_Emat[0].emitHalfPul_en);
						g_Dev.emit_en = 1;
						SendCmd2FPGA();
						Timer_setEmitRate((g_Dev.trig_mode == TRIG_MODE_INT) ? g_Dev.repeat_times : 0);
					}
				}
				else {
					//频率增加
					if(g_Emat[0].sweepFreq_currFreq >= g_Emat[0].sweepFreq_endFreq) {
						//结束
						g_Dev.emit_en = 0;
						g_Dev.ematParasChanged = 1;
						g_Dev.sys_state = STATE_IDLE;
					}
					else {
						//变频
						g_Emat[0].sweepFreq_currFreq = g_Emat[0].sweepFreq_currFreq + g_Emat[0].sweepFreq_stepFreq;
	//					DDS_SetParaDetail(g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].emitPulNum);
						DDS_SetParaChirp(g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].emitPulNum, g_Emat[0].emitHalfPul_en);
						g_Dev.emit_en = 1;
						SendCmd2FPGA();
						Timer_setEmitRate((g_Dev.trig_mode == TRIG_MODE_INT) ? g_Dev.repeat_times : 0);
					}
				}
			}
			else if(g_Dev.runMode == RUN_MODE_THICKNESS) {
				
			}
		}
		else if(g_Dev.sys_state == STATE_STOP_PREPARE) {
			g_Dev.emit_en			= 0;
			g_Dev.dataAcqCnt 		= 0;
			g_Dev.ematParasChanged	= 1;
			g_Dev.sys_state 		= STATE_IDLE;
		}
		
		//启动/停止
		if(g_Dev.ematParasChanged) {
			//为避免FPGA过于劳累, 修改参数过程中, 先关闭激励, 配置完后不需要恢复, 流程中会重新启动激励
			if(g_Dev.trig_mode == TRIG_MODE_INT) {
				Timer_setEmitRate(0);
				delay_ms(50);
			}
			
			if(g_Dev.emit_en == 0) {
				g_Dev.dataAcqCnt = 0;
				//stop run
				Timer_setEmitRate(0);
				SendCmd2FPGA();
			}
			else {
				//start run
				if(g_Dev.runMode == RUN_MODE_COMMON) {
//					DDS_SetPara();
					DDS_SetParaChirp(g_Emat[0].emitFreq_MHz, g_Emat[0].emitFreq_MHz, g_Emat[0].emitPulNum, g_Emat[0].emitHalfPul_en);
				}
				else if(g_Dev.runMode == RUN_MODE_CHIRP) {
					DDS_SetParaChirp(g_Emat[0].chirpFreq_bgnFreq/1000000.0, g_Emat[0].chirpFreq_endFreq/1000000.0, g_Emat[0].emitPulNum, g_Emat[0].emitHalfPul_en);
				}
				else if(g_Dev.runMode == RUN_MODE_SWEEP_FRQ) {
					g_Emat[0].sweepFreq_currFreq = g_Emat[0].sweepFreq_bgnFreq;
//					DDS_SetParaDetail(g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].emitPulNum);
					DDS_SetParaChirp(g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].sweepFreq_currFreq/1000000.0, g_Emat[0].emitPulNum, g_Emat[0].emitHalfPul_en);
				}
				delay_ms(10);
				DAQ_SetGain(1, (g_CHN[0].daq->gainVal)); //设置CH1增益
				SendCmd2FPGA();
				delay_ms(10);
				
				Timer_setEmitRate((g_Dev.trig_mode == TRIG_MODE_INT) ? g_Dev.repeat_times : 0);
			}
			g_Dev.ematParasChanged = 0;
			
		}
		
	}
}

static uint16_t qspiReadBuf[5];
uint16_t fpga_data_state;
static uint8_t DAQ_DataReceive(uint8_t isContinous) {
	uint32_t i;
	//读取状态寄存器
	fpga_data_state = FPGA_Read_DataStateReg();
	if(fpga_data_state == 0xffff)
		return 1;
	if(!g_Dev.emit_en) {
		//清除
		return 2;
	}
	
	if( (!g_CHN[0].enable) || ((fpga_data_state & 0x0003) == 0x0003) ) {
		g_Dev.dataAcqCnt++;
		
		if(isContinous == 0) {
			//非连续模式, 停止发射及FPGA相关部分
			g_Dev.emit_en = 0;
			Timer_setEmitRate(0);
			SendCmd2FPGA();
		}
	
		//读取通道1
		if((fpga_data_state & 0x0003) == 0x0003) {
			g_CHN[0].daq->data.dataValid = 1;
			ReadData_fromFPGA(1);
		}
		else {
			g_CHN[0].daq->data.dataValid = 0;
		}
		
		g_Dev.waveRecvStage = RECV_DONE; //接收一帧数据完成
		return 0; //rx done
	}
	
	return 3;
}

static uint8_t DAQ_DataSend() {
	uint32_t i;
	uint32_t freq;
	uint32_t cnt;
	
	if(g_Dev.waveRecvStage == RECV_DONE) {
		uint8_t extrabuf[TXWAVE_EXTRA_DATA_LEN];
		
		memset(extrabuf, 0xff, sizeof(extrabuf));
		for(i = 1; i < sizeof(extrabuf); i++) {
			extrabuf[i] = i;
		}
		
		freq = (g_Dev.runMode == RUN_MODE_SWEEP_FRQ) ? g_Emat[0].sweepFreq_currFreq : g_Emat[0].emitFreq_MHz * 1000000;

		if(g_Dev.trig_mode == TRIG_MODE_INT)
			cnt = g_Dev.dataAcqCnt;
		else
			cnt = g_Dev.dataAcqCnt_fpga;
		
		for(i = 0; i < CHN_NUM; i++) {
			if(g_CHN[i].daq->data.dataValid) {
				extrabuf[0]  = (((cnt)&0xff000000)>>24);
				extrabuf[1]  = (((cnt)&0x00ff0000)>>16);
				extrabuf[2]  = (((cnt)&0x0000ff00)>>8);
				extrabuf[3]  = ( (cnt)&0x000000ff);
				extrabuf[4]  = (((g_CHN[i].encData1)&0xff000000)>>24);
				extrabuf[5]  = (((g_CHN[i].encData1)&0x00ff0000)>>16);
				extrabuf[6]  = (((g_CHN[i].encData1)&0x0000ff00)>>8);
				extrabuf[7]  = ( (g_CHN[i].encData1)&0x000000ff);
				extrabuf[8]  = (((g_CHN[i].encData2)&0xff000000)>>24);
				extrabuf[9]  = (((g_CHN[i].encData2)&0x00ff0000)>>16);
				extrabuf[10] = (((g_CHN[i].encData2)&0x0000ff00)>>8);
				extrabuf[11] = ( (g_CHN[i].encData2)&0x000000ff);
				extrabuf[12] = (freq&0xff000000)>>24;
				extrabuf[13] = (freq&0x00ff0000)>>16;
				extrabuf[14] = (freq&0x0000ff00)>>8;
				extrabuf[15] = (freq&0x000000ff);
				Comm_SendWaveData(g_Com.devCommPort, extrabuf, TXWAVE_EXTRA_DATA_LEN, g_CHN[i].daq->data.waveSrc_buf, g_CHN[i].daq->waveLen_pt);
			}
		}
		
		g_Dev.waveRecvStage = RECV_IDLE;
		return 0; //tx done
	}
	return 1; //dont need tx data
}



////EMAT发射任务
//#define 	EMIT_TASK_PRIO 						4//设置任务优先级
//#define 	EMIT_STK_SIZE						128*2//任务堆栈大小
//OS_TCB 		EmitTaskTCB;
//CPU_STK 	EMIT_TASK_STK[EMIT_STK_SIZE];
//void 		emit_task(void *p_arg);
////// OS-Timer
////OS_TMR 		tmr1;		//定时器1
////void 		tmr1_callback(void *p_tmr, void *p_arg); 	//定时器1回调函数


////uint32_t tmr1_cnt_ms;
//////定时器1的回调函数
////void tmr1_callback(void *p_tmr, void *p_arg)
////{
////	tmr1_cnt_ms = tmr1_cnt_ms + 10;
////	LED1_Toggle;
////}

////void OS_StartSoftTmr1(void)
////{
////	OS_ERR err;
////	OSTmrStart(&tmr1,&err);	//开启定时器1
////	tmr1_cnt_ms = 0;
////}

////void OS_StopSoftTmr1(void)
////{
////	OS_ERR err;
////	OSTmrStop(&tmr1,OS_OPT_TMR_NONE,0,&err);	//关闭定时器1
////}

////uint32_t OS_GetTimeSoftTmr1(void) {
////	return tmr1_cnt_ms;
////}


//void Start_OneEmitDaq_Cycle(void) {
//	OS_ERR err;
//	OSTaskSemPost(&EmitTaskTCB,OS_OPT_POST_NONE,&err);//使用系统内建信号量向任务emit发送信号量,启动新一轮发射
//}
//static uint16_t test_emit_cnt = 0;
////int32_t ZeroOffset_ns;
////EMIT任务
//static uint8_t random_dly_us[11] = {3, 21, 15, 0, 33, 30, 15, 24, 27, 6, 21 };
//static int16_t rubbish_buf[10];

//static uint16_t emitCnt;
////int tst_emitpp_cnt = 0;
//static void emit_task_thread(void *p_arg)
//{
//	OS_ERR err;
//	static uint16_t offset_time=0;
//	while(1)
//	{
//		OSTaskSemPend(0,OS_OPT_PEND_BLOCKING,0,&err);		//请求任务内建的信号量
//		OSTaskSemSet(&EmitTaskTCB, 0, &err); //清除多余的信号量
//		
////		emitCnt = 0;
////		g_Dev.waveRecvStage = RECV_ING; //数据接收中

////		while(g_Dev.emit_en) {
////			EMIT_Trig(1); delay_us(10); EMIT_Trig(0);
////			OSTimeDlyHMSM(0, 0, 0, 2, OS_OPT_TIME_PERIODIC, &err);//延时 ms
////			delay_us(random_dly_us[offset_time]); offset_time++; if(offset_time == 11) offset_time = 0;
////			emitCnt++;
////			//读取状态寄存器
////			QSPI_Send_CMD(0x30,0x00,0x00,QSPI_INSTRUCTION_4_LINES,QSPI_ADDRESS_NONE,QSPI_ADDRESS_24_BITS,QSPI_DATA_4_LINES);
////			QSPI_Receive((u8*)qspiReadBuf, (1)*2);
////			
////			if(((!g_CHN1.enable) || ((qspiReadBuf[0] & 0xC000) == 0xC000)) && ((!g_CHN2.enable) || ((qspiReadBuf[0] & 0x00C0) == 0x00C0))) {
////				//读取通道1
////				if(((qspiReadBuf[0] & 0xC000) == 0xC000) && (emitCnt == g_CHN1.daq->avg)) {
////					g_CHN1.daq->data.dataValid = 1;
////					ReadData_fromFPGA(1, g_CHN1.daq->data.waveSrc_buf, g_CHN1.daq->waveLen_pt);
////				}
////				else {
////					g_CHN1.daq->data.dataValid = 0;
////				}
////				//读取通道2
////				if(((qspiReadBuf[0] & 0x00C0) == 0x00C0) && (emitCnt == g_CHN2.daq->avg)) {
////					g_CHN2.daq->data.dataValid = 1;
////					ReadData_fromFPGA(2, g_CHN2.daq->data.waveSrc_buf, g_CHN2.daq->waveLen_pt);
////				}
////				else {
////					g_CHN2.daq->data.dataValid = 0;
////				}
////				
////				emitCnt 				= 0;
////				g_Dev.waveRecvStage 	= RECV_DONE; //接收一帧数据完成
////				break;
////			}
////		}
//	}
//}



//void emit_task_create(void) {
//	OS_ERR err;
//	CPU_SR_ALLOC();
//	
//	OS_CRITICAL_ENTER();//进入临界区
//		//EMIT发射任务
//	OSTaskCreate((OS_TCB*     			)&EmitTaskTCB,		
//				 (CPU_CHAR*   			)"Emit task", 		
//                 (OS_TASK_PTR 			)emit_task_thread, 			
//                 (void*       			)0,					
//                 (OS_PRIO	  			)EMIT_TASK_PRIO,     
//                 (CPU_STK*    			)&EMIT_TASK_STK[0],	
//                 (CPU_STK_SIZE			)EMIT_STK_SIZE/10,	
//                 (CPU_STK_SIZE			)EMIT_STK_SIZE,		
//                 (OS_MSG_QTY  			)0,					
//                 (OS_TICK	  			)0,  					
//                 (void*       			)0,					
//                 (OS_OPT      			)OS_OPT_TASK_STK_CHK|OS_OPT_TASK_STK_CLR,
//                 (OS_ERR*     			)&err);
////	//创建定时器1
////	OSTmrCreate((OS_TMR	*)&tmr1,		//定时器1
////                (CPU_CHAR*				)"tmr1",			//定时器名字
////				(OS_TICK	 			)0,					//dly: 0*10=0ms
////				(OS_TICK	 			)1,          		//period: 1*10=10ms
////                (OS_OPT		 			)OS_OPT_TMR_PERIODIC, //周期模式
////                (OS_TMR_CALLBACK_PTR	)tmr1_callback,//定时器1回调函数
////                (void*					)0,			//参数为0
////                (OS_ERR*				)&err);		//返回的错误码
//	OS_CRITICAL_EXIT();	//退出临界区
//	
//}





/*************************** End of file ****************************/
