/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : GUIDEMO_Bitmap.c
Purpose     : Draws bitmaps with and without compression
----------------------------------------------------------------------
*/

/**
  ******************************************************************************
  * @file    GUIDEMO_Bitmap.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Draws bitmaps with and without compression
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


#include "GUIDEMO.h"
#include <math.h>

#if (SHOW_GUIDEMO_BITMAP)

#if GUI_WINSUPPORT
  #include "WM.h"
#endif

#define PI4 12.5663706f

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/

/*********************************************************************
*
*       GUIDEMO_bm4bpp
*
**********************************************************************
*/
static GUI_CONST_STORAGE GUI_COLOR ColorsNature[] = {
     0x000000,0x111111,0x222222,0x333333
    ,0x444444,0x555555,0x666666,0x777777
    ,0x888888,0x999999,0xAAAAAA,0xBBBBBB
    ,0xCCCCCC,0xDDDDDD,0xEEEEEE,0xFFFFFF
};

static GUI_CONST_STORAGE GUI_LOGPALETTE _PalNature = {
  16,	/* number of entries */
  0, 	/* No transparency */
  &ColorsNature[0]
};

static GUI_CONST_STORAGE unsigned char _acNature[] = {
  0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x33, 0x33, 0x33, 0x44, 0x44, 0x44, 0x44, 0x44, 0x45, 0x55, 0x55, 0x54, 0x44, 0x43, 0x33, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x43, 0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 0x44, 0x43, 0x34, 0x55, 0x55, 0x55, 0x44, 0x44, 0x44, 0x43, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x33, 0x33, 0x44, 0x44, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x44, 0x54, 0x45, 0x54, 0x54, 0x44, 0x44, 0x43, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x33, 0x33, 0x33, 0x43, 0x33, 0x33, 0x33, 0x33, 0x33, 0x22, 0x22, 0x33, 0x32, 0x45, 0x44, 0x44, 0x33, 0x34, 0x44, 0x44, 0x44, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x43, 0x33, 0x33, 0x33, 0x43, 0x33, 0x33, 0x33, 0x33, 0x32, 0x21, 0x11, 0x23, 0x32, 0x35, 0x54, 0x32, 0x33, 0x33, 0x34, 0x44, 0x33, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x21, 0x12, 0x10, 0x12, 0x32, 0x34, 0x44, 0x31, 0x34, 0x23, 0x34, 0x34, 0x43, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x11, 0x22, 0x10, 0x02, 0x34, 0x44, 0x43, 0x33, 0x43, 0x33, 0x33, 0x44, 0x33, 0x33, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x43, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x10, 0x11, 0x10, 0x01, 0x33, 0x54, 0x55, 0x33, 0x32, 0x34, 0x44, 0x43, 0x33, 0x22, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x43, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x21, 0x12, 0x20, 0x01, 0x12, 0x33, 0x44, 0x44, 0x44, 0x44, 0x44, 0x34, 0x33, 0x33, 0x22, 0x33, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x11, 0x47, 0x52, 0x12, 0x23, 0x34, 0x44, 0x55, 0x55, 0x55, 0x54, 0x43, 0x33, 0x34, 0x32, 0x33, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x22, 0x14, 0x66, 0x54, 0x43, 0x33, 0x43, 0x45, 0x57, 0x67, 0x87, 0x55, 0x44, 0x32, 0x23, 0x43, 0x33, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x22, 0x36, 0x66, 0x43, 0x56, 0x55, 0x54, 0x57, 0x88, 0x9A, 0xBA, 0x86, 0x65, 0x43, 0x23, 0x44, 0x33, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x23, 0x44, 0x34, 0x45, 0x46, 0x78, 0x78, 0x9A, 0xAB, 0xCD, 0xDC, 0xBA, 0x98, 0x76, 0x43, 0x43, 0x23, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x22, 0x33, 0x45, 0x54, 0x45, 0x66, 0x8A, 0xBB, 0xCD, 0xCD, 0xDD, 0xED, 0xDD, 0xCC, 0xBA, 0x84, 0x44, 0x23, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x22, 0x22, 0x33, 0x33, 0x23, 0x34, 0x45, 0x65, 0x45, 0x78, 0xAA, 0xBB, 0xCD, 0xDD, 0xDE, 0xEE, 0xDD, 0xDD, 0xDC, 0xA7, 0x44, 0x32, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x32, 0x22, 0x22, 0x33, 0x33, 0x23, 0x45, 0x45, 0x43, 0x36, 0x89, 0xAA, 0xBC, 0xCC, 0xDE, 0xEE, 0xEE, 0xEE, 0xDD, 0xDC, 0xCA, 0x55, 0x43, 0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x32, 0x22, 0x22, 0x33, 0x33, 0x33, 0x44, 0x45, 0x44, 0x56, 0x88, 0x9A, 0xAB, 0xBD, 0xDE, 0xEE, 0xEE, 0xEE, 0xEE, 0xDD, 0xCA, 0x75, 0x63, 0x23, 0x33, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x22, 0x22, 0x22, 0x23, 0x33, 0x33, 0x32, 0x35, 0x44, 0x57, 0x78, 0x99, 0x9A, 0xBC, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE, 0xDC, 0xCB, 0x95, 0x55, 0x23, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x22, 0x22, 0x22, 0x23, 0x22, 0x12, 0x34, 0x54, 0x44, 0x67, 0x78, 0x89, 0x9A, 0xAC, 0xDE, 0xEE, 0xEE, 0xEE, 0xEE, 0xDD, 0xCB, 0x97, 0x45, 0x42, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x32, 0x22, 0x22, 0x22, 0x22, 0x33, 0x35, 0x54, 0x33, 0x77, 0x68, 0x89, 0x9A, 0xBC, 0xDE, 0xEE, 0xFF, 0xEE, 0xED, 0xDC, 0xBB, 0xA7, 0x45, 0x53, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x22, 0x22, 0x22, 0x21, 0x02, 0x31, 0x33, 0x43, 0x34, 0x66, 0x67, 0x89, 0xAA, 0xBC, 0xDE, 0xEE, 0xFF, 0xEE, 0xED, 0xDC, 0xCB, 0xA8, 0x45, 0x54, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x22, 0x22, 0x22, 0x21, 0x02, 0x22, 0x32, 0x45, 0x65, 0x76, 0x67, 0x89, 0x9A, 0xBB, 0xDE, 0xFE, 0xEF, 0xFE, 0xED, 0xDC, 0xCB, 0xA9, 0x54, 0x54, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x32, 0x22, 0x22, 0x22, 0x20, 0x01, 0x22, 0x12, 0x46, 0x86, 0x76, 0x77, 0x89, 0x9B, 0xCD, 0xEE, 0xFF, 0xFF, 0xFE, 0xFE, 0xED, 0xCB, 0xA9, 0x73, 0x34, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x21, 0x10, 0x11, 0x12, 0x35, 0x65, 0x66, 0x67, 0x88, 0xAA, 0xAA, 0xBC, 0xEF, 0xFF, 0xFE, 0xFE, 0xEE, 0xDB, 0xB9, 0x64, 0x33, 0x22, 0x12, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x00, 0x02, 0x54, 0x44, 0x55, 0x66, 0x89, 0x76, 0x56, 0x79, 0xCE, 0xFE, 0xFE, 0xEE, 0xDD, 0xDC, 0xB9, 0x63, 0x41, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x00, 0x00, 0x11, 0x32, 0x33, 0x45, 0x66, 0x87, 0x76, 0x65, 0x67, 0xAD, 0xEE, 0xED, 0xDC, 0xAA, 0xAB, 0xBA, 0x63, 0x32, 0x01, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x00, 0x00, 0x01, 0x21, 0x43, 0x46, 0x65, 0x77, 0x99, 0x98, 0x66, 0x8B, 0xCE, 0xED, 0xC9, 0x65, 0x57, 0xAA, 0x73, 0x32, 0x11, 0x12, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x00, 0x01, 0x43, 0x22, 0x46, 0x65, 0x58, 0x98, 0x99, 0x76, 0x69, 0xCD, 0xDC, 0x97, 0x55, 0x68, 0x9A, 0x83, 0x42, 0x11, 0x12, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x00, 0x00, 0x23, 0x23, 0x57, 0x75, 0x67, 0x78, 0xAA, 0x86, 0x67, 0xBE, 0xDA, 0x87, 0x89, 0x79, 0x9A, 0x83, 0x31, 0x00, 0x12, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x00, 0x00, 0x12, 0x34, 0x67, 0x76, 0x77, 0x55, 0x56, 0x46, 0x66, 0xAE, 0xC9, 0x78, 0x9A, 0x98, 0x99, 0x94, 0x31, 0x00, 0x12, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x00, 0x01, 0x12, 0x44, 0x68, 0x78, 0x75, 0x38, 0x74, 0xA9, 0x86, 0xBE, 0xCA, 0x89, 0x85, 0x36, 0x9A, 0x94, 0x20, 0x00, 0x12, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x00, 0x00, 0x12, 0x44, 0x67, 0x88, 0x76, 0x59, 0xBB, 0xCD, 0xA8, 0xBD, 0xCC, 0xCD, 0xB5, 0x66, 0x9A, 0x94, 0x00, 0x11, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x00, 0x01, 0x01, 0x23, 0x57, 0x89, 0x88, 0x78, 0xAA, 0xBD, 0xA9, 0xAD, 0xCD, 0xCC, 0xCA, 0x97, 0x9B, 0x93, 0x00, 0x11, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x00, 0x13, 0x10, 0x13, 0x47, 0x8A, 0xBA, 0x9A, 0xBD, 0xED, 0xB9, 0xAD, 0xCC, 0xDB, 0xAA, 0x9A, 0xBB, 0x82, 0x00, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x00, 0x15, 0x20, 0x13, 0x46, 0x8A, 0xBB, 0xBC, 0xDE, 0xEC, 0xA9, 0xAC, 0xDC, 0xDD, 0xCB, 0xBC, 0xCC, 0x81, 0x00, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x11, 0x00, 0x17, 0x41, 0x13, 0x35, 0x8A, 0xBC, 0xCD, 0xEE, 0xEC, 0xAA, 0xAC, 0xDC, 0xCD, 0xDD, 0xCD, 0xCB, 0x72, 0x10, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x22, 0x11, 0x00, 0x17, 0x62, 0x23, 0x34, 0x7A, 0xBC, 0xDE, 0xEE, 0xEC, 0xA9, 0xAC, 0xDC, 0xCE, 0xED, 0xDD, 0xDA, 0x62, 0x00, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x21, 0x11, 0x00, 0x06, 0x63, 0x23, 0x34, 0x69, 0xAC, 0xDE, 0xEE, 0xEC, 0x96, 0xAC, 0xDD, 0xCD, 0xEE, 0xDD, 0xCA, 0x41, 0x01, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x21, 0x11, 0x00, 0x04, 0x54, 0x23, 0x34, 0x69, 0xAB, 0xCD, 0xDE, 0xDB, 0x86, 0xAD, 0xED, 0xCD, 0xEE, 0xDD, 0xC9, 0x31, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x21, 0x11, 0x00, 0x02, 0x65, 0x23, 0x34, 0x68, 0xAB, 0xBD, 0xDE, 0xDA, 0x97, 0x8C, 0xFE, 0xCD, 0xEE, 0xDD, 0xC9, 0x31, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x22, 0x11, 0x11, 0x10, 0x01, 0x66, 0x22, 0x34, 0x68, 0x9A, 0xBD, 0xDD, 0xC8, 0x76, 0x5A, 0xDC, 0xBD, 0xED, 0xDD, 0xC8, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x21, 0x11, 0x11, 0x10, 0x01, 0x56, 0x22, 0x34, 0x78, 0x9A, 0xBC, 0xDD, 0xD7, 0x42, 0x37, 0xA9, 0x8D, 0xDD, 0xDD, 0xB7, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x21, 0x11, 0x11, 0x11, 0x00, 0x35, 0x22, 0x34, 0x78, 0x9A, 0xBC, 0xDD, 0xDA, 0x42, 0x14, 0x55, 0x9D, 0xDD, 0xDC, 0xB7, 0x43, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x11, 0x11, 0x11, 0x11, 0x00, 0x01, 0x01, 0x24, 0x68, 0x9A, 0xBC, 0xDD, 0xCA, 0x53, 0x34, 0x56, 0xBD, 0xDD, 0xDC, 0xB7, 0x43, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x22, 0x11, 0x11, 0x11, 0x11, 0x10, 0x00, 0x01, 0x23, 0x68, 0x9A, 0xBC, 0xB9, 0x96, 0x34, 0x54, 0x54, 0x8B, 0xCD, 0xDC, 0xA5, 0x33, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x21, 0x11, 0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x24, 0x57, 0x9A, 0xBB, 0x86, 0x53, 0x23, 0x43, 0x43, 0x47, 0x8B, 0xCC, 0x95, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x21, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x24, 0x56, 0x89, 0xAA, 0x43, 0x33, 0x22, 0x44, 0x43, 0x34, 0x48, 0xBB, 0x74, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x21, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x13, 0x66, 0x78, 0xA9, 0x32, 0x22, 0x12, 0x46, 0x42, 0x23, 0x47, 0xBA, 0x53, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x02, 0x56, 0x68, 0x98, 0x32, 0x33, 0x46, 0x67, 0x52, 0x32, 0x37, 0xA7, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x11, 0x01, 0x11, 0x11, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x01, 0x45, 0x66, 0x88, 0x45, 0x66, 0x79, 0xAA, 0x97, 0x64, 0x38, 0x95, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x11, 0x11, 0x22, 0x22, 0x22, 0x21, 0x00, 0x00, 0x00, 0x01, 0x00, 0x35, 0x55, 0x77, 0x67, 0x78, 0x76, 0x77, 0x9A, 0xA8, 0x7A, 0x83, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x22, 0x33, 0x33, 0x33, 0x21, 0x00, 0x01, 0x10, 0x13, 0x00, 0x14, 0x54, 0x56, 0x67, 0x89, 0x8A, 0xA9, 0xAB, 0xBA, 0x99, 0x63, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x22, 0x23, 0x33, 0x44, 0x43, 0x31, 0x11, 0x11, 0x20, 0x24, 0x41, 0x12, 0x44, 0x46, 0x67, 0x88, 0xBE, 0xEE, 0xED, 0xCB, 0xA8, 0x52, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x44, 0x44, 0x43, 0x21, 0x11, 0x22, 0x31, 0x25, 0x54, 0x22, 0x33, 0x46, 0x56, 0x79, 0xCE, 0xFF, 0xFF, 0xEE, 0xDB, 0x83, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x33, 0x34, 0x44, 0x44, 0x43, 0x22, 0x12, 0x23, 0x31, 0x25, 0x66, 0x43, 0x22, 0x34, 0x55, 0x7B, 0xCC, 0xDE, 0xEE, 0xEE, 0xED, 0xB6, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x44, 0x43, 0x32, 0x22, 0x23, 0x41, 0x05, 0x77, 0x64, 0x22, 0x23, 0x35, 0x9B, 0xBA, 0xAB, 0xCD, 0xDD, 0xDD, 0xCA, 0x42, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x44, 0x43, 0x32, 0x22, 0x33, 0x42, 0x03, 0x77, 0x76, 0x42, 0x22, 0x26, 0xAA, 0xAA, 0xCD, 0xDE, 0xEE, 0xEE, 0xDC, 0x94, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x44, 0x33, 0x32, 0x22, 0x33, 0x42, 0x01, 0x68, 0x78, 0x83, 0x21, 0x16, 0x9A, 0xCC, 0xDE, 0xEE, 0xEE, 0xEF, 0xEE, 0xC9, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x44, 0x33, 0x32, 0x22, 0x33, 0x43, 0x10, 0x37, 0x8A, 0xB7, 0x11, 0x15, 0x7C, 0xDD, 0xDE, 0xED, 0xCD, 0xEF, 0xFE, 0xEC, 0x83, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x43, 0x33, 0x22, 0x32, 0x23, 0x46, 0x51, 0x15, 0x8C, 0xDA, 0x40, 0x13, 0x6B, 0xCB, 0xBC, 0xCB, 0xCD, 0xEE, 0xEE, 0xEE, 0xC7, 0x32, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x33, 0x33, 0x22, 0x32, 0x23, 0x48, 0xA3, 0x02, 0x7E, 0xEC, 0x71, 0x22, 0x5A, 0x99, 0x9B, 0xCD, 0xDD, 0xEE, 0xEE, 0xEE, 0xEC, 0x63, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x44, 0x33, 0x32, 0x23, 0x32, 0x23, 0x48, 0xE8, 0x11, 0x7E, 0xEC, 0x93, 0x12, 0x69, 0x9A, 0xBC, 0xDD, 0xDD, 0xEF, 0xEE, 0xEE, 0xEE, 0xC5, 0x32, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x44, 0x43, 0x22, 0x32, 0x22, 0x22, 0x23, 0x47, 0xFC, 0x51, 0x9F, 0xEC, 0xA6, 0x11, 0x58, 0xBB, 0xCD, 0xDD, 0xCD, 0xDE, 0xEE, 0xEE, 0xEE, 0xEB, 0x43, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x43, 0x33, 0x21, 0x23, 0x22, 0x22, 0x23, 0x47, 0xFE, 0xA4, 0xAE, 0xDC, 0x97, 0x11, 0x37, 0xBB, 0xCC, 0xBB, 0xCD, 0xEE, 0xEE, 0xEF, 0xFE, 0xED, 0x93, 0x22, 0x22, 0x22, 0x22, 0x20,
  0x44, 0x43, 0x33, 0x30, 0x22, 0x22, 0x23, 0x22, 0x46, 0xFF, 0xE9, 0x79, 0x88, 0x64, 0x10, 0x25, 0x88, 0xAB, 0xCC, 0xDE, 0xEF, 0xFE, 0xEE, 0xEF, 0xEE, 0xD8, 0x32, 0x22, 0x22, 0x22, 0x20,
  0x43, 0x33, 0x33, 0x31, 0x12, 0x22, 0x22, 0x23, 0x45, 0xEF, 0xFE, 0x72, 0x11, 0x00, 0x00, 0x13, 0x7A, 0xBB, 0xCC, 0xDE, 0xEF, 0xFE, 0xEE, 0xEE, 0xFE, 0xEC, 0x83, 0x22, 0x22, 0x22, 0x20,
  0x43, 0x33, 0x23, 0x31, 0x12, 0x22, 0x23, 0x33, 0x45, 0xEF, 0xFF, 0xC4, 0x11, 0x00, 0x00, 0x02, 0x6A, 0xAA, 0xBC, 0xCD, 0xDE, 0xEE, 0xEE, 0xEE, 0xEF, 0xED, 0xC8, 0x32, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x32, 0x32, 0x22, 0x23, 0x33, 0x45, 0xDF, 0xFF, 0xE7, 0x10, 0x00, 0x00, 0x01, 0x38, 0x99, 0x99, 0xAB, 0xCD, 0xDE, 0xEE, 0xEE, 0xEF, 0xEE, 0xDC, 0x73, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x33, 0x23, 0x32, 0x22, 0x33, 0x33, 0x45, 0xCF, 0xFF, 0xF9, 0x20, 0x00, 0x00, 0x00, 0x25, 0x75, 0x42, 0x47, 0xAB, 0xCD, 0xDE, 0xEE, 0xEF, 0xFE, 0xED, 0xB6, 0x22, 0x22, 0x20,
  0x33, 0x33, 0x22, 0x33, 0x32, 0x23, 0x33, 0x34, 0x44, 0xBF, 0xFF, 0xFA, 0x41, 0x00, 0x00, 0x01, 0x34, 0x74, 0x10, 0x03, 0x79, 0xBC, 0xDD, 0xEE, 0xEE, 0xFF, 0xEE, 0xCA, 0x42, 0x22, 0x20,
  0x33, 0x33, 0x23, 0x33, 0x33, 0x33, 0x33, 0x44, 0x44, 0xAF, 0xFF, 0xFC, 0x51, 0x00, 0x00, 0x03, 0x23, 0x77, 0x52, 0x00, 0x15, 0x8B, 0xCD, 0xEE, 0xEE, 0xEF, 0xFE, 0xDC, 0x83, 0x33, 0x30,
  0x33, 0x32, 0x23, 0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 0x9F, 0xFF, 0xFD, 0x83, 0x00, 0x00, 0x03, 0x10, 0x47, 0x75, 0x10, 0x01, 0x47, 0xBC, 0xDD, 0xEE, 0xEF, 0xFF, 0xED, 0xB5, 0x33, 0x20,
  0x22, 0x32, 0x13, 0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 0x8F, 0xFF, 0xFE, 0xB4, 0x10, 0x00, 0x12, 0x00, 0x01, 0x22, 0x00, 0x00, 0x14, 0x7A, 0xCD, 0xDE, 0xEE, 0xEF, 0xFE, 0xD8, 0x33, 0x30,
  0x22, 0x22, 0x02, 0x33, 0x33, 0x33, 0x33, 0x34, 0x44, 0x7F, 0xFF, 0xFF, 0xC5, 0x11, 0x00, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x47, 0xAB, 0xCD, 0xEE, 0xEF, 0xFF, 0xEB, 0x33, 0x30,
  0x22, 0x22, 0x02, 0x33, 0x32, 0x32, 0x33, 0x34, 0x44, 0x6E, 0xFF, 0xFF, 0xE6, 0x22, 0x00, 0x31, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x68, 0xAB, 0xDD, 0xEE, 0xFF, 0xED, 0x33, 0x30,
  0x22, 0x22, 0x11, 0x33, 0x32, 0x22, 0x33, 0x33, 0x44, 0x5E, 0xFF, 0xFF, 0xE6, 0x32, 0x10, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x35, 0x89, 0xBC, 0xDE, 0xEF, 0xFD, 0x34, 0x40,
  0x22, 0x22, 0x10, 0x33, 0x33, 0x22, 0x23, 0x34, 0x44, 0x5E, 0xFF, 0xFF, 0xC4, 0x32, 0x12, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x22, 0x47, 0xAC, 0xDE, 0xEF, 0xEC, 0x24, 0x40,
  0x11, 0x22, 0x20, 0x23, 0x33, 0x22, 0x23, 0x34, 0x44, 0x4D, 0xFF, 0xFD, 0x52, 0x33, 0x23, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x32, 0x13, 0x7A, 0xCD, 0xEE, 0xEA, 0x24, 0x40,
  0x11, 0x12, 0x21, 0x13, 0x33, 0x22, 0x23, 0x34, 0x44, 0x4C, 0xFF, 0xE7, 0x33, 0x33, 0x33, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x23, 0x11, 0x59, 0xCD, 0xEE, 0xE8, 0x24, 0x40,
  0x00, 0x11, 0x21, 0x02, 0x33, 0x22, 0x23, 0x33, 0x44, 0x4B, 0xFF, 0xB5, 0x43, 0x44, 0x31, 0x00, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x22, 0x12, 0x5A, 0xCD, 0xEE, 0xEB, 0x24, 0x30,
  0x00, 0x01, 0x12, 0x11, 0x22, 0x21, 0x12, 0x33, 0x34, 0x3A, 0xFE, 0x75, 0x44, 0x43, 0x31, 0x01, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x05, 0x79, 0xBD, 0xDE, 0xFB, 0x23, 0x30,
  0x00, 0x01, 0x11, 0x10, 0x22, 0x11, 0x12, 0x23, 0x34, 0x38, 0xFB, 0x55, 0x44, 0x44, 0x31, 0x02, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x99, 0xBD, 0xEF, 0xE9, 0x33, 0x30,
  0x00, 0x00, 0x10, 0x00, 0x12, 0x11, 0x11, 0x22, 0x34, 0x36, 0xD7, 0x54, 0x44, 0x44, 0x31, 0x23, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x47, 0xAD, 0xEF, 0xD6, 0x43, 0x30,
  0x00, 0x00, 0x10, 0x00, 0x01, 0x11, 0x11, 0x12, 0x33, 0x35, 0xA5, 0x44, 0x44, 0x44, 0x31, 0x23, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x7C, 0xED, 0xC4, 0x33, 0x30,
  0x10, 0x00, 0x00, 0x00, 0x01, 0x11, 0x11, 0x12, 0x23, 0x34, 0x64, 0x44, 0x44, 0x43, 0x21, 0x32, 0x21, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x5B, 0xDD, 0x82, 0x33, 0x30,
  0x20, 0x00, 0x01, 0x00, 0x00, 0x11, 0x00, 0x01, 0x23, 0x33, 0x44, 0x44, 0x44, 0x43, 0x22, 0x22, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x47, 0xDC, 0x61, 0x33, 0x30,
  0x10, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x01, 0x12, 0x23, 0x44, 0x44, 0x44, 0x43, 0x12, 0x21, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x24, 0x87, 0x41, 0x23, 0x20,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x23, 0x44, 0x44, 0x44, 0x32, 0x12, 0x21, 0x11, 0x11, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x32, 0x13, 0x10,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x22, 0x34, 0x44, 0x44, 0x31, 0x22, 0x11, 0x01, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x23, 0x02, 0x10,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x22, 0x34, 0x44, 0x43, 0x21, 0x21, 0x11, 0x00, 0x11, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x12, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x21, 0x35, 0x65, 0x33, 0x12, 0x21, 0x01, 0x11, 0x11, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x21, 0x20,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x11, 0x21, 0x25, 0x42, 0x12, 0x10, 0x01, 0x11, 0x11, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x31, 0x20
};

const GUI_BITMAP GUIDEMO_bm4bpp = {
  61,        /* XSize */
  92,        /* YSize */
  31,        /* BytesPerLine */
  4,         /* BitsPerPixel */
  _acNature,  /* Pointer to picture data (indices) */
  &_PalNature /* Pointer to palette */
};

/*********************************************************************
*
*       Bug bitmap
*
**********************************************************************
*/

static const GUI_COLOR _ColorsLadyBug[] = {
  0xFFFFFF, 0x000000, 0x0000ff, 0xFFCCCC
};

static const GUI_LOGPALETTE _PalLadyBug = {
  4,	/* number of entries */
  0, 	/* No transparency */
  &_ColorsLadyBug[0]
};

static const unsigned char _acLadyBug[] = {
  /* RLE: 056 Pixels @ 000,000*/ 56, 0x00, 
  /* RLE: 007 Pixels @ 056,000*/ 7, 0x01, 
  /* RLE: 152 Pixels @ 063,000*/ 152, 0x00, 
  /* RLE: 009 Pixels @ 055,001*/ 9, 0x01, 
  /* RLE: 150 Pixels @ 064,001*/ 150, 0x00, 
  /* RLE: 010 Pixels @ 054,002*/ 10, 0x01, 
  /* RLE: 149 Pixels @ 064,002*/ 149, 0x00, 
  /* RLE: 012 Pixels @ 053,003*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 065,003*/ 148, 0x00, 
  /* RLE: 012 Pixels @ 053,004*/ 12, 0x01, 
  /* RLE: 147 Pixels @ 065,004*/ 147, 0x00, 
  /* RLE: 013 Pixels @ 052,005*/ 13, 0x01, 
  /* RLE: 147 Pixels @ 065,005*/ 147, 0x00, 
  /* RLE: 013 Pixels @ 052,006*/ 13, 0x01, 
  /* RLE: 146 Pixels @ 065,006*/ 146, 0x00, 
  /* RLE: 014 Pixels @ 051,007*/ 14, 0x01, 
  /* RLE: 146 Pixels @ 065,007*/ 146, 0x00, 
  /* RLE: 014 Pixels @ 051,008*/ 14, 0x01, 
  /* RLE: 146 Pixels @ 065,008*/ 146, 0x00, 
  /* RLE: 014 Pixels @ 051,009*/ 14, 0x01, 
  /* RLE: 145 Pixels @ 065,009*/ 145, 0x00, 
  /* RLE: 014 Pixels @ 050,010*/ 14, 0x01, 
  /* RLE: 146 Pixels @ 064,010*/ 146, 0x00, 
  /* RLE: 014 Pixels @ 050,011*/ 14, 0x01, 
  /* RLE: 146 Pixels @ 064,011*/ 146, 0x00, 
  /* RLE: 014 Pixels @ 050,012*/ 14, 0x01, 
  /* RLE: 146 Pixels @ 064,012*/ 146, 0x00, 
  /* RLE: 013 Pixels @ 050,013*/ 13, 0x01, 
  /* RLE: 146 Pixels @ 063,013*/ 146, 0x00, 
  /* RLE: 014 Pixels @ 049,014*/ 14, 0x01, 
  /* RLE: 146 Pixels @ 063,014*/ 146, 0x00, 
  /* RLE: 013 Pixels @ 049,015*/ 13, 0x01, 
  /* RLE: 147 Pixels @ 062,015*/ 147, 0x00, 
  /* RLE: 013 Pixels @ 049,016*/ 13, 0x01, 
  /* RLE: 147 Pixels @ 062,016*/ 147, 0x00, 
  /* RLE: 012 Pixels @ 049,017*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 061,017*/ 148, 0x00, 
  /* RLE: 012 Pixels @ 049,018*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 061,018*/ 148, 0x00, 
  /* RLE: 012 Pixels @ 049,019*/ 12, 0x01, 
  /* RLE: 147 Pixels @ 061,019*/ 147, 0x00, 
  /* RLE: 012 Pixels @ 048,020*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 060,020*/ 148, 0x00, 
  /* RLE: 012 Pixels @ 048,021*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 060,021*/ 148, 0x00, 
  /* RLE: 011 Pixels @ 048,022*/ 11, 0x01, 
  /* RLE: 149 Pixels @ 059,022*/ 149, 0x00, 
  /* RLE: 011 Pixels @ 048,023*/ 11, 0x01, 
  /* RLE: 149 Pixels @ 059,023*/ 149, 0x00, 
  /* RLE: 011 Pixels @ 048,024*/ 11, 0x01, 
  /* RLE: 149 Pixels @ 059,024*/ 149, 0x00, 
  /* RLE: 010 Pixels @ 048,025*/ 10, 0x01, 
  /* RLE: 150 Pixels @ 058,025*/ 150, 0x00, 
  /* RLE: 010 Pixels @ 048,026*/ 10, 0x01, 
  /* RLE: 150 Pixels @ 058,026*/ 150, 0x00, 
  /* RLE: 010 Pixels @ 048,027*/ 10, 0x01, 
  /* RLE: 150 Pixels @ 058,027*/ 150, 0x00, 
  /* RLE: 009 Pixels @ 048,028*/ 9, 0x01, 
  /* RLE: 152 Pixels @ 057,028*/ 152, 0x00, 
  /* RLE: 008 Pixels @ 049,029*/ 8, 0x01, 
  /* RLE: 152 Pixels @ 057,029*/ 152, 0x00, 
  /* RLE: 008 Pixels @ 049,030*/ 8, 0x01, 
  /* RLE: 152 Pixels @ 057,030*/ 152, 0x00, 
  /* RLE: 008 Pixels @ 049,031*/ 8, 0x01, 
  /* RLE: 152 Pixels @ 057,031*/ 152, 0x00, 
  /* RLE: 008 Pixels @ 049,032*/ 8, 0x01, 
  /* RLE: 152 Pixels @ 057,032*/ 152, 0x00, 
  /* RLE: 008 Pixels @ 049,033*/ 8, 0x01, 
  /* RLE: 152 Pixels @ 057,033*/ 152, 0x00, 
  /* RLE: 008 Pixels @ 049,034*/ 8, 0x01, 
  /* RLE: 153 Pixels @ 057,034*/ 153, 0x00, 
  /* RLE: 007 Pixels @ 050,035*/ 7, 0x01, 
  /* RLE: 153 Pixels @ 057,035*/ 153, 0x00, 
  /* RLE: 007 Pixels @ 050,036*/ 7, 0x01, 
  /* RLE: 153 Pixels @ 057,036*/ 153, 0x00, 
  /* RLE: 007 Pixels @ 050,037*/ 7, 0x01, 
  /* RLE: 153 Pixels @ 057,037*/ 153, 0x00, 
  /* RLE: 007 Pixels @ 050,038*/ 7, 0x01, 
  /* RLE: 154 Pixels @ 057,038*/ 154, 0x00, 
  /* RLE: 006 Pixels @ 051,039*/ 6, 0x01, 
  /* RLE: 154 Pixels @ 057,039*/ 154, 0x00, 
  /* RLE: 007 Pixels @ 051,040*/ 7, 0x01, 
  /* RLE: 154 Pixels @ 058,040*/ 154, 0x00, 
  /* RLE: 006 Pixels @ 052,041*/ 6, 0x01, 
  /* RLE: 154 Pixels @ 058,041*/ 154, 0x00, 
  /* RLE: 007 Pixels @ 052,042*/ 7, 0x01, 
  /* RLE: 154 Pixels @ 059,042*/ 154, 0x00, 
  /* RLE: 006 Pixels @ 053,043*/ 6, 0x01, 
  /* RLE: 154 Pixels @ 059,043*/ 154, 0x00, 
  /* RLE: 007 Pixels @ 053,044*/ 7, 0x01, 
  /* RLE: 049 Pixels @ 060,044*/ 49, 0x00, 
  /* RLE: 010 Pixels @ 109,044*/ 10, 0x01, 
  /* RLE: 095 Pixels @ 119,044*/ 95, 0x00, 
  /* RLE: 006 Pixels @ 054,045*/ 6, 0x01, 
  /* RLE: 046 Pixels @ 060,045*/ 46, 0x00, 
  /* RLE: 014 Pixels @ 106,045*/ 14, 0x01, 
  /* RLE: 095 Pixels @ 120,045*/ 95, 0x00, 
  /* RLE: 006 Pixels @ 055,046*/ 6, 0x01, 
  /* RLE: 043 Pixels @ 061,046*/ 43, 0x00, 
  /* RLE: 017 Pixels @ 104,046*/ 17, 0x01, 
  /* RLE: 094 Pixels @ 121,046*/ 94, 0x00, 
  /* RLE: 007 Pixels @ 055,047*/ 7, 0x01, 
  /* RLE: 040 Pixels @ 062,047*/ 40, 0x00, 
  /* RLE: 020 Pixels @ 102,047*/ 20, 0x01, 
  /* RLE: 054 Pixels @ 122,047*/ 54, 0x00, 
  /* RLE: 019 Pixels @ 016,048*/ 19, 0x01, 
  /* RLE: 021 Pixels @ 035,048*/ 21, 0x00, 
  /* RLE: 006 Pixels @ 056,048*/ 6, 0x01, 
  /* RLE: 004 Pixels @ 062,048*/ 4, 0x00, 
  /* RLE: 006 Pixels @ 066,048*/ 6, 0x01, 
  /* RLE: 029 Pixels @ 072,048*/ 29, 0x00, 
  /* RLE: 022 Pixels @ 101,048*/ 22, 0x01, 
  /* RLE: 049 Pixels @ 123,048*/ 49, 0x00, 
  /* RLE: 027 Pixels @ 012,049*/ 27, 0x01, 
  /* RLE: 018 Pixels @ 039,049*/ 18, 0x00, 
  /* RLE: 006 Pixels @ 057,049*/ 6, 0x01, 
  /* RLE: 001 Pixels @ 063,049*/ 1, 0x00, 
  /* RLE: 009 Pixels @ 064,049*/ 9, 0x01, 
  /* RLE: 026 Pixels @ 073,049*/ 26, 0x00, 
  /* RLE: 024 Pixels @ 099,049*/ 24, 0x01, 
  /* RLE: 046 Pixels @ 123,049*/ 46, 0x00, 
  /* RLE: 032 Pixels @ 009,050*/ 32, 0x01, 
  /* RLE: 017 Pixels @ 041,050*/ 17, 0x00, 
  /* RLE: 016 Pixels @ 058,050*/ 16, 0x01, 
  /* RLE: 024 Pixels @ 074,050*/ 24, 0x00, 
  /* RLE: 026 Pixels @ 098,050*/ 26, 0x01, 
  /* RLE: 043 Pixels @ 124,050*/ 43, 0x00, 
  /* RLE: 036 Pixels @ 007,051*/ 36, 0x01, 
  /* RLE: 015 Pixels @ 043,051*/ 15, 0x00, 
  /* RLE: 017 Pixels @ 058,051*/ 17, 0x01, 
  /* RLE: 022 Pixels @ 075,051*/ 22, 0x00, 
  /* RLE: 027 Pixels @ 097,051*/ 27, 0x01, 
  /* RLE: 040 Pixels @ 124,051*/ 40, 0x00, 
  /* RLE: 041 Pixels @ 004,052*/ 41, 0x01, 
  /* RLE: 014 Pixels @ 045,052*/ 14, 0x00, 
  /* RLE: 016 Pixels @ 059,052*/ 16, 0x01, 
  /* RLE: 021 Pixels @ 075,052*/ 21, 0x00, 
  /* RLE: 016 Pixels @ 096,052*/ 16, 0x01, 
  /* RLE: 005 Pixels @ 112,052*/ 5, 0x00, 
  /* RLE: 012 Pixels @ 117,052*/ 12, 0x01, 
  /* RLE: 034 Pixels @ 129,052*/ 34, 0x00, 
  /* RLE: 044 Pixels @ 003,053*/ 44, 0x01, 
  /* RLE: 010 Pixels @ 047,053*/ 10, 0x00, 
  /* RLE: 018 Pixels @ 057,053*/ 18, 0x01, 
  /* RLE: 020 Pixels @ 075,053*/ 20, 0x00, 
  /* RLE: 016 Pixels @ 095,053*/ 16, 0x01, 
  /* RLE: 007 Pixels @ 111,053*/ 7, 0x00, 
  /* RLE: 013 Pixels @ 118,053*/ 13, 0x01, 
  /* RLE: 031 Pixels @ 131,053*/ 31, 0x00, 
  /* RLE: 046 Pixels @ 002,054*/ 46, 0x01, 
  /* RLE: 008 Pixels @ 048,054*/ 8, 0x00, 
  /* RLE: 020 Pixels @ 056,054*/ 20, 0x01, 
  /* RLE: 018 Pixels @ 076,054*/ 18, 0x00, 
  /* RLE: 015 Pixels @ 094,054*/ 15, 0x01, 
  /* RLE: 008 Pixels @ 109,054*/ 8, 0x00, 
  /* RLE: 016 Pixels @ 117,054*/ 16, 0x01, 
  /* RLE: 028 Pixels @ 133,054*/ 28, 0x00, 
  /* RLE: 049 Pixels @ 001,055*/ 49, 0x01, 
  /* RLE: 005 Pixels @ 050,055*/ 5, 0x00, 
  /* RLE: 021 Pixels @ 055,055*/ 21, 0x01, 
  /* RLE: 003 Pixels @ 076,055*/ 3, 0x00, 
  /* RLE: 030 Pixels @ 079,055*/ 30, 0x01, 
  /* RLE: 006 Pixels @ 109,055*/ 6, 0x00, 
  /* RLE: 018 Pixels @ 115,055*/ 18, 0x01, 
  /* RLE: 027 Pixels @ 133,055*/ 27, 0x00, 
  /* RLE: 028 Pixels @ 000,056*/ 28, 0x01, 
  /* RLE: 012 Pixels @ 028,056*/ 12, 0x00, 
  /* RLE: 011 Pixels @ 040,056*/ 11, 0x01, 
  /* RLE: 003 Pixels @ 051,056*/ 3, 0x00, 
  /* RLE: 054 Pixels @ 054,056*/ 54, 0x01, 
  /* RLE: 006 Pixels @ 108,056*/ 6, 0x00, 
  /* RLE: 020 Pixels @ 114,056*/ 20, 0x01, 
  /* RLE: 026 Pixels @ 134,056*/ 26, 0x00, 
  /* RLE: 025 Pixels @ 000,057*/ 25, 0x01, 
  /* RLE: 018 Pixels @ 025,057*/ 18, 0x00, 
  /* RLE: 009 Pixels @ 043,057*/ 9, 0x01, 
  /* ABS: 002 Pixels @ 052,057*/ 0, 2, 0x00, 
  /* RLE: 053 Pixels @ 054,057*/ 53, 0x01, 
  /* RLE: 005 Pixels @ 107,057*/ 5, 0x00, 
  /* RLE: 023 Pixels @ 112,057*/ 23, 0x01, 
  /* RLE: 025 Pixels @ 135,057*/ 25, 0x00, 
  /* RLE: 023 Pixels @ 000,058*/ 23, 0x01, 
  /* RLE: 022 Pixels @ 023,058*/ 22, 0x00, 
  /* RLE: 061 Pixels @ 045,058*/ 61, 0x01, 
  /* RLE: 005 Pixels @ 106,058*/ 5, 0x00, 
  /* RLE: 024 Pixels @ 111,058*/ 24, 0x01, 
  /* RLE: 025 Pixels @ 135,058*/ 25, 0x00, 
  /* RLE: 021 Pixels @ 000,059*/ 21, 0x01, 
  /* RLE: 026 Pixels @ 021,059*/ 26, 0x00, 
  /* RLE: 058 Pixels @ 047,059*/ 58, 0x01, 
  /* RLE: 004 Pixels @ 105,059*/ 4, 0x00, 
  /* RLE: 027 Pixels @ 109,059*/ 27, 0x01, 
  /* RLE: 024 Pixels @ 136,059*/ 24, 0x00, 
  /* RLE: 019 Pixels @ 000,060*/ 19, 0x01, 
  /* RLE: 029 Pixels @ 019,060*/ 29, 0x00, 
  /* RLE: 059 Pixels @ 048,060*/ 59, 0x01, 
  /* RLE: 001 Pixels @ 107,060*/ 1, 0x00, 
  /* RLE: 029 Pixels @ 108,060*/ 29, 0x01, 
  /* RLE: 023 Pixels @ 137,060*/ 23, 0x00, 
  /* RLE: 017 Pixels @ 000,061*/ 17, 0x01, 
  /* RLE: 033 Pixels @ 017,061*/ 33, 0x00, 
  /* RLE: 030 Pixels @ 050,061*/ 30, 0x01, 
  /* RLE: 004 Pixels @ 080,061*/ 4, 0x03, 
  /* RLE: 053 Pixels @ 084,061*/ 53, 0x01, 
  /* RLE: 023 Pixels @ 137,061*/ 23, 0x00, 
  /* RLE: 015 Pixels @ 000,062*/ 15, 0x01, 
  /* RLE: 036 Pixels @ 015,062*/ 36, 0x00, 
  /* RLE: 028 Pixels @ 051,062*/ 28, 0x01, 
  /* RLE: 005 Pixels @ 079,062*/ 5, 0x03, 
  /* RLE: 038 Pixels @ 084,062*/ 38, 0x01, 
  /* RLE: 001 Pixels @ 122,062*/ 1, 0x00, 
  /* RLE: 014 Pixels @ 123,062*/ 14, 0x01, 
  /* RLE: 024 Pixels @ 137,062*/ 24, 0x00, 
  /* RLE: 012 Pixels @ 001,063*/ 12, 0x01, 
  /* RLE: 038 Pixels @ 013,063*/ 38, 0x00, 
  /* RLE: 027 Pixels @ 051,063*/ 27, 0x01, 
  /* RLE: 006 Pixels @ 078,063*/ 6, 0x03, 
  /* RLE: 009 Pixels @ 084,063*/ 9, 0x01, 
  /* RLE: 005 Pixels @ 093,063*/ 5, 0x02, 
  /* RLE: 023 Pixels @ 098,063*/ 23, 0x01, 
  /* ABS: 002 Pixels @ 121,063*/ 0, 2, 0x00, 
  /* RLE: 015 Pixels @ 123,063*/ 15, 0x01, 
  /* RLE: 025 Pixels @ 138,063*/ 25, 0x00, 
  /* RLE: 007 Pixels @ 003,064*/ 7, 0x01, 
  /* RLE: 041 Pixels @ 010,064*/ 41, 0x00, 
  /* RLE: 027 Pixels @ 051,064*/ 27, 0x01, 
  /* RLE: 006 Pixels @ 078,064*/ 6, 0x03, 
  /* RLE: 008 Pixels @ 084,064*/ 8, 0x01, 
  /* RLE: 011 Pixels @ 092,064*/ 11, 0x02, 
  /* RLE: 017 Pixels @ 103,064*/ 17, 0x01, 
  /* RLE: 004 Pixels @ 120,064*/ 4, 0x00, 
  /* RLE: 014 Pixels @ 124,064*/ 14, 0x01, 
  /* RLE: 072 Pixels @ 138,064*/ 72, 0x00, 
  /* RLE: 028 Pixels @ 050,065*/ 28, 0x01, 
  /* RLE: 006 Pixels @ 078,065*/ 6, 0x03, 
  /* RLE: 006 Pixels @ 084,065*/ 6, 0x01, 
  /* RLE: 015 Pixels @ 090,065*/ 15, 0x02, 
  /* RLE: 014 Pixels @ 105,065*/ 14, 0x01, 
  /* RLE: 006 Pixels @ 119,065*/ 6, 0x00, 
  /* RLE: 013 Pixels @ 125,065*/ 13, 0x01, 
  /* RLE: 072 Pixels @ 138,065*/ 72, 0x00, 
  /* RLE: 028 Pixels @ 050,066*/ 28, 0x01, 
  /* RLE: 006 Pixels @ 078,066*/ 6, 0x03, 
  /* RLE: 006 Pixels @ 084,066*/ 6, 0x01, 
  /* RLE: 017 Pixels @ 090,066*/ 17, 0x02, 
  /* RLE: 012 Pixels @ 107,066*/ 12, 0x01, 
  /* RLE: 007 Pixels @ 119,066*/ 7, 0x00, 
  /* RLE: 013 Pixels @ 126,066*/ 13, 0x01, 
  /* RLE: 006 Pixels @ 139,066*/ 6, 0x00, 
  /* ABS: 002 Pixels @ 145,066*/ 0, 2, 0x11, 
  /* RLE: 063 Pixels @ 147,066*/ 63, 0x00, 
  /* RLE: 028 Pixels @ 050,067*/ 28, 0x01, 
  /* RLE: 005 Pixels @ 078,067*/ 5, 0x03, 
  /* RLE: 006 Pixels @ 083,067*/ 6, 0x01, 
  /* RLE: 011 Pixels @ 089,067*/ 11, 0x02, 
  /* ABS: 002 Pixels @ 100,067*/ 0, 2, 0x11, 
  /* RLE: 006 Pixels @ 102,067*/ 6, 0x02, 
  /* RLE: 010 Pixels @ 108,067*/ 10, 0x01, 
  /* RLE: 009 Pixels @ 118,067*/ 9, 0x00, 
  /* RLE: 013 Pixels @ 127,067*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 140,067*/ 4, 0x00, 
  /* RLE: 004 Pixels @ 144,067*/ 4, 0x01, 
  /* RLE: 062 Pixels @ 148,067*/ 62, 0x00, 
  /* RLE: 038 Pixels @ 050,068*/ 38, 0x01, 
  /* RLE: 010 Pixels @ 088,068*/ 10, 0x02, 
  /* RLE: 006 Pixels @ 098,068*/ 6, 0x01, 
  /* RLE: 006 Pixels @ 104,068*/ 6, 0x02, 
  /* RLE: 007 Pixels @ 110,068*/ 7, 0x01, 
  /* RLE: 012 Pixels @ 117,068*/ 12, 0x00, 
  /* RLE: 012 Pixels @ 129,068*/ 12, 0x01, 
  /* RLE: 003 Pixels @ 141,068*/ 3, 0x00, 
  /* RLE: 004 Pixels @ 144,068*/ 4, 0x01, 
  /* RLE: 062 Pixels @ 148,068*/ 62, 0x00, 
  /* RLE: 038 Pixels @ 050,069*/ 38, 0x01, 
  /* RLE: 009 Pixels @ 088,069*/ 9, 0x02, 
  /* RLE: 008 Pixels @ 097,069*/ 8, 0x01, 
  /* RLE: 006 Pixels @ 105,069*/ 6, 0x02, 
  /* RLE: 007 Pixels @ 111,069*/ 7, 0x01, 
  /* RLE: 013 Pixels @ 118,069*/ 13, 0x00, 
  /* RLE: 011 Pixels @ 131,069*/ 11, 0x01, 
  /* RLE: 001 Pixels @ 142,069*/ 1, 0x00, 
  /* RLE: 006 Pixels @ 143,069*/ 6, 0x01, 
  /* RLE: 061 Pixels @ 149,069*/ 61, 0x00, 
  /* RLE: 037 Pixels @ 050,070*/ 37, 0x01, 
  /* RLE: 009 Pixels @ 087,070*/ 9, 0x02, 
  /* RLE: 010 Pixels @ 096,070*/ 10, 0x01, 
  /* RLE: 006 Pixels @ 106,070*/ 6, 0x02, 
  /* RLE: 006 Pixels @ 112,070*/ 6, 0x01, 
  /* RLE: 010 Pixels @ 118,070*/ 10, 0x00, 
  /* RLE: 021 Pixels @ 128,070*/ 21, 0x01, 
  /* RLE: 061 Pixels @ 149,070*/ 61, 0x00, 
  /* RLE: 037 Pixels @ 050,071*/ 37, 0x01, 
  /* RLE: 008 Pixels @ 087,071*/ 8, 0x02, 
  /* RLE: 012 Pixels @ 095,071*/ 12, 0x01, 
  /* RLE: 006 Pixels @ 107,071*/ 6, 0x02, 
  /* RLE: 006 Pixels @ 113,071*/ 6, 0x01, 
  /* RLE: 007 Pixels @ 119,071*/ 7, 0x00, 
  /* RLE: 023 Pixels @ 126,071*/ 23, 0x01, 
  /* RLE: 061 Pixels @ 149,071*/ 61, 0x00, 
  /* RLE: 037 Pixels @ 050,072*/ 37, 0x01, 
  /* RLE: 008 Pixels @ 087,072*/ 8, 0x02, 
  /* RLE: 012 Pixels @ 095,072*/ 12, 0x01, 
  /* RLE: 007 Pixels @ 107,072*/ 7, 0x02, 
  /* RLE: 006 Pixels @ 114,072*/ 6, 0x01, 
  /* RLE: 005 Pixels @ 120,072*/ 5, 0x00, 
  /* RLE: 025 Pixels @ 125,072*/ 25, 0x01, 
  /* RLE: 060 Pixels @ 150,072*/ 60, 0x00, 
  /* RLE: 036 Pixels @ 050,073*/ 36, 0x01, 
  /* RLE: 008 Pixels @ 086,073*/ 8, 0x02, 
  /* RLE: 014 Pixels @ 094,073*/ 14, 0x01, 
  /* RLE: 007 Pixels @ 108,073*/ 7, 0x02, 
  /* RLE: 006 Pixels @ 115,073*/ 6, 0x01, 
  /* ABS: 002 Pixels @ 121,073*/ 0, 2, 0x00, 
  /* RLE: 027 Pixels @ 123,073*/ 27, 0x01, 
  /* RLE: 060 Pixels @ 150,073*/ 60, 0x00, 
  /* RLE: 037 Pixels @ 050,074*/ 37, 0x01, 
  /* RLE: 007 Pixels @ 087,074*/ 7, 0x02, 
  /* RLE: 014 Pixels @ 094,074*/ 14, 0x01, 
  /* RLE: 007 Pixels @ 108,074*/ 7, 0x02, 
  /* RLE: 006 Pixels @ 115,074*/ 6, 0x01, 
  /* RLE: 001 Pixels @ 121,074*/ 1, 0x00, 
  /* RLE: 028 Pixels @ 122,074*/ 28, 0x01, 
  /* RLE: 062 Pixels @ 150,074*/ 62, 0x00, 
  /* RLE: 013 Pixels @ 052,075*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 065,075*/ 4, 0x03, 
  /* RLE: 018 Pixels @ 069,075*/ 18, 0x01, 
  /* RLE: 008 Pixels @ 087,075*/ 8, 0x02, 
  /* RLE: 013 Pixels @ 095,075*/ 13, 0x01, 
  /* RLE: 008 Pixels @ 108,075*/ 8, 0x02, 
  /* RLE: 033 Pixels @ 116,075*/ 33, 0x01, 
  /* RLE: 068 Pixels @ 149,075*/ 68, 0x00, 
  /* RLE: 007 Pixels @ 057,076*/ 7, 0x01, 
  /* RLE: 006 Pixels @ 064,076*/ 6, 0x03, 
  /* RLE: 018 Pixels @ 070,076*/ 18, 0x01, 
  /* RLE: 007 Pixels @ 088,076*/ 7, 0x02, 
  /* RLE: 013 Pixels @ 095,076*/ 13, 0x01, 
  /* RLE: 009 Pixels @ 108,076*/ 9, 0x02, 
  /* RLE: 019 Pixels @ 117,076*/ 19, 0x01, 
  /* RLE: 003 Pixels @ 136,076*/ 3, 0x00, 
  /* RLE: 009 Pixels @ 139,076*/ 9, 0x01, 
  /* RLE: 069 Pixels @ 148,076*/ 69, 0x00, 
  /* RLE: 007 Pixels @ 057,077*/ 7, 0x01, 
  /* RLE: 006 Pixels @ 064,077*/ 6, 0x03, 
  /* RLE: 019 Pixels @ 070,077*/ 19, 0x01, 
  /* RLE: 007 Pixels @ 089,077*/ 7, 0x02, 
  /* RLE: 012 Pixels @ 096,077*/ 12, 0x01, 
  /* RLE: 009 Pixels @ 108,077*/ 9, 0x02, 
  /* RLE: 017 Pixels @ 117,077*/ 17, 0x01, 
  /* RLE: 006 Pixels @ 134,077*/ 6, 0x00, 
  /* RLE: 008 Pixels @ 140,077*/ 8, 0x01, 
  /* RLE: 069 Pixels @ 148,077*/ 69, 0x00, 
  /* RLE: 007 Pixels @ 057,078*/ 7, 0x01, 
  /* RLE: 006 Pixels @ 064,078*/ 6, 0x03, 
  /* RLE: 020 Pixels @ 070,078*/ 20, 0x01, 
  /* RLE: 007 Pixels @ 090,078*/ 7, 0x02, 
  /* RLE: 011 Pixels @ 097,078*/ 11, 0x01, 
  /* RLE: 010 Pixels @ 108,078*/ 10, 0x02, 
  /* RLE: 015 Pixels @ 118,078*/ 15, 0x01, 
  /* RLE: 008 Pixels @ 133,078*/ 8, 0x00, 
  /* RLE: 007 Pixels @ 141,078*/ 7, 0x01, 
  /* RLE: 069 Pixels @ 148,078*/ 69, 0x00, 
  /* RLE: 007 Pixels @ 057,079*/ 7, 0x01, 
  /* RLE: 005 Pixels @ 064,079*/ 5, 0x03, 
  /* RLE: 013 Pixels @ 069,079*/ 13, 0x01, 
  /* RLE: 001 Pixels @ 082,079*/ 1, 0x02, 
  /* RLE: 008 Pixels @ 083,079*/ 8, 0x01, 
  /* RLE: 007 Pixels @ 091,079*/ 7, 0x02, 
  /* RLE: 010 Pixels @ 098,079*/ 10, 0x01, 
  /* RLE: 010 Pixels @ 108,079*/ 10, 0x02, 
  /* RLE: 014 Pixels @ 118,079*/ 14, 0x01, 
  /* RLE: 010 Pixels @ 132,079*/ 10, 0x00, 
  /* RLE: 006 Pixels @ 142,079*/ 6, 0x01, 
  /* RLE: 069 Pixels @ 148,079*/ 69, 0x00, 
  /* RLE: 007 Pixels @ 057,080*/ 7, 0x01, 
  /* RLE: 005 Pixels @ 064,080*/ 5, 0x03, 
  /* RLE: 008 Pixels @ 069,080*/ 8, 0x01, 
  /* RLE: 008 Pixels @ 077,080*/ 8, 0x02, 
  /* RLE: 007 Pixels @ 085,080*/ 7, 0x01, 
  /* RLE: 008 Pixels @ 092,080*/ 8, 0x02, 
  /* RLE: 007 Pixels @ 100,080*/ 7, 0x01, 
  /* RLE: 012 Pixels @ 107,080*/ 12, 0x02, 
  /* RLE: 012 Pixels @ 119,080*/ 12, 0x01, 
  /* RLE: 011 Pixels @ 131,080*/ 11, 0x00, 
  /* RLE: 007 Pixels @ 142,080*/ 7, 0x01, 
  /* RLE: 067 Pixels @ 149,080*/ 67, 0x00, 
  /* RLE: 008 Pixels @ 056,081*/ 8, 0x01, 
  /* RLE: 004 Pixels @ 064,081*/ 4, 0x03, 
  /* RLE: 006 Pixels @ 068,081*/ 6, 0x01, 
  /* RLE: 013 Pixels @ 074,081*/ 13, 0x02, 
  /* RLE: 007 Pixels @ 087,081*/ 7, 0x01, 
  /* RLE: 025 Pixels @ 094,081*/ 25, 0x02, 
  /* RLE: 011 Pixels @ 119,081*/ 11, 0x01, 
  /* RLE: 013 Pixels @ 130,081*/ 13, 0x00, 
  /* RLE: 006 Pixels @ 143,081*/ 6, 0x01, 
  /* RLE: 067 Pixels @ 149,081*/ 67, 0x00, 
  /* RLE: 016 Pixels @ 056,082*/ 16, 0x01, 
  /* RLE: 016 Pixels @ 072,082*/ 16, 0x02, 
  /* RLE: 007 Pixels @ 088,082*/ 7, 0x01, 
  /* RLE: 016 Pixels @ 095,082*/ 16, 0x02, 
  /* RLE: 005 Pixels @ 111,082*/ 5, 0x01, 
  /* RLE: 004 Pixels @ 116,082*/ 4, 0x02, 
  /* RLE: 009 Pixels @ 120,082*/ 9, 0x01, 
  /* RLE: 014 Pixels @ 129,082*/ 14, 0x00, 
  /* RLE: 007 Pixels @ 143,082*/ 7, 0x01, 
  /* RLE: 006 Pixels @ 150,082*/ 6, 0x00, 
  /* ABS: 002 Pixels @ 156,082*/ 0, 2, 0x11, 
  /* RLE: 058 Pixels @ 158,082*/ 58, 0x00, 
  /* RLE: 014 Pixels @ 056,083*/ 14, 0x01, 
  /* RLE: 019 Pixels @ 070,083*/ 19, 0x02, 
  /* RLE: 007 Pixels @ 089,083*/ 7, 0x01, 
  /* RLE: 013 Pixels @ 096,083*/ 13, 0x02, 
  /* RLE: 008 Pixels @ 109,083*/ 8, 0x01, 
  /* RLE: 003 Pixels @ 117,083*/ 3, 0x02, 
  /* RLE: 009 Pixels @ 120,083*/ 9, 0x01, 
  /* RLE: 015 Pixels @ 129,083*/ 15, 0x00, 
  /* RLE: 007 Pixels @ 144,083*/ 7, 0x01, 
  /* RLE: 004 Pixels @ 151,083*/ 4, 0x00, 
  /* RLE: 003 Pixels @ 155,083*/ 3, 0x01, 
  /* RLE: 058 Pixels @ 158,083*/ 58, 0x00, 
  /* RLE: 013 Pixels @ 056,084*/ 13, 0x01, 
  /* RLE: 007 Pixels @ 069,084*/ 7, 0x02, 
  /* RLE: 007 Pixels @ 076,084*/ 7, 0x01, 
  /* RLE: 007 Pixels @ 083,084*/ 7, 0x02, 
  /* RLE: 007 Pixels @ 090,084*/ 7, 0x01, 
  /* RLE: 011 Pixels @ 097,084*/ 11, 0x02, 
  /* RLE: 010 Pixels @ 108,084*/ 10, 0x01, 
  /* RLE: 003 Pixels @ 118,084*/ 3, 0x02, 
  /* RLE: 007 Pixels @ 121,084*/ 7, 0x01, 
  /* RLE: 016 Pixels @ 128,084*/ 16, 0x00, 
  /* RLE: 007 Pixels @ 144,084*/ 7, 0x01, 
  /* RLE: 004 Pixels @ 151,084*/ 4, 0x00, 
  /* RLE: 004 Pixels @ 155,084*/ 4, 0x01, 
  /* RLE: 057 Pixels @ 159,084*/ 57, 0x00, 
  /* RLE: 012 Pixels @ 056,085*/ 12, 0x01, 
  /* RLE: 007 Pixels @ 068,085*/ 7, 0x02, 
  /* RLE: 009 Pixels @ 075,085*/ 9, 0x01, 
  /* RLE: 008 Pixels @ 084,085*/ 8, 0x02, 
  /* RLE: 006 Pixels @ 092,085*/ 6, 0x01, 
  /* RLE: 009 Pixels @ 098,085*/ 9, 0x02, 
  /* RLE: 011 Pixels @ 107,085*/ 11, 0x01, 
  /* RLE: 003 Pixels @ 118,085*/ 3, 0x02, 
  /* RLE: 006 Pixels @ 121,085*/ 6, 0x01, 
  /* RLE: 018 Pixels @ 127,085*/ 18, 0x00, 
  /* RLE: 008 Pixels @ 145,085*/ 8, 0x01, 
  /* RLE: 001 Pixels @ 153,085*/ 1, 0x00, 
  /* RLE: 006 Pixels @ 154,085*/ 6, 0x01, 
  /* RLE: 056 Pixels @ 000,086*/ 56, 0x00, 
  /* RLE: 012 Pixels @ 056,086*/ 12, 0x01, 
  /* RLE: 006 Pixels @ 068,086*/ 6, 0x02, 
  /* RLE: 010 Pixels @ 074,086*/ 10, 0x01, 
  /* RLE: 009 Pixels @ 084,086*/ 9, 0x02, 
  /* RLE: 006 Pixels @ 093,086*/ 6, 0x01, 
  /* RLE: 008 Pixels @ 099,086*/ 8, 0x02, 
  /* RLE: 012 Pixels @ 107,086*/ 12, 0x01, 
  /* RLE: 003 Pixels @ 119,086*/ 3, 0x02, 
  /* RLE: 005 Pixels @ 122,086*/ 5, 0x01, 
  /* RLE: 018 Pixels @ 127,086*/ 18, 0x00, 
  /* RLE: 015 Pixels @ 145,086*/ 15, 0x01, 
  /* RLE: 056 Pixels @ 000,087*/ 56, 0x00, 
  /* RLE: 011 Pixels @ 056,087*/ 11, 0x01, 
  /* RLE: 006 Pixels @ 067,087*/ 6, 0x02, 
  /* RLE: 012 Pixels @ 073,087*/ 12, 0x01, 
  /* RLE: 009 Pixels @ 085,087*/ 9, 0x02, 
  /* RLE: 007 Pixels @ 094,087*/ 7, 0x01, 
  /* RLE: 005 Pixels @ 101,087*/ 5, 0x02, 
  /* RLE: 013 Pixels @ 106,087*/ 13, 0x01, 
  /* RLE: 003 Pixels @ 119,087*/ 3, 0x02, 
  /* RLE: 005 Pixels @ 122,087*/ 5, 0x01, 
  /* RLE: 019 Pixels @ 127,087*/ 19, 0x00, 
  /* RLE: 014 Pixels @ 146,087*/ 14, 0x01, 
  /* RLE: 056 Pixels @ 000,088*/ 56, 0x00, 
  /* RLE: 011 Pixels @ 056,088*/ 11, 0x01, 
  /* RLE: 006 Pixels @ 067,088*/ 6, 0x02, 
  /* RLE: 012 Pixels @ 073,088*/ 12, 0x01, 
  /* RLE: 010 Pixels @ 085,088*/ 10, 0x02, 
  /* RLE: 007 Pixels @ 095,088*/ 7, 0x01, 
  /* RLE: 004 Pixels @ 102,088*/ 4, 0x02, 
  /* RLE: 013 Pixels @ 106,088*/ 13, 0x01, 
  /* RLE: 003 Pixels @ 119,088*/ 3, 0x02, 
  /* RLE: 006 Pixels @ 122,088*/ 6, 0x01, 
  /* RLE: 018 Pixels @ 128,088*/ 18, 0x00, 
  /* RLE: 014 Pixels @ 146,088*/ 14, 0x01, 
  /* RLE: 056 Pixels @ 000,089*/ 56, 0x00, 
  /* RLE: 010 Pixels @ 056,089*/ 10, 0x01, 
  /* RLE: 007 Pixels @ 066,089*/ 7, 0x02, 
  /* RLE: 013 Pixels @ 073,089*/ 13, 0x01, 
  /* RLE: 010 Pixels @ 086,089*/ 10, 0x02, 
  /* RLE: 007 Pixels @ 096,089*/ 7, 0x01, 
  /* RLE: 004 Pixels @ 103,089*/ 4, 0x02, 
  /* RLE: 012 Pixels @ 107,089*/ 12, 0x01, 
  /* RLE: 004 Pixels @ 119,089*/ 4, 0x02, 
  /* RLE: 005 Pixels @ 123,089*/ 5, 0x01, 
  /* RLE: 019 Pixels @ 128,089*/ 19, 0x00, 
  /* RLE: 013 Pixels @ 147,089*/ 13, 0x01, 
  /* RLE: 056 Pixels @ 000,090*/ 56, 0x00, 
  /* RLE: 010 Pixels @ 056,090*/ 10, 0x01, 
  /* RLE: 007 Pixels @ 066,090*/ 7, 0x02, 
  /* RLE: 013 Pixels @ 073,090*/ 13, 0x01, 
  /* RLE: 011 Pixels @ 086,090*/ 11, 0x02, 
  /* RLE: 007 Pixels @ 097,090*/ 7, 0x01, 
  /* RLE: 003 Pixels @ 104,090*/ 3, 0x02, 
  /* RLE: 012 Pixels @ 107,090*/ 12, 0x01, 
  /* RLE: 004 Pixels @ 119,090*/ 4, 0x02, 
  /* RLE: 005 Pixels @ 123,090*/ 5, 0x01, 
  /* RLE: 020 Pixels @ 128,090*/ 20, 0x00, 
  /* RLE: 012 Pixels @ 148,090*/ 12, 0x01, 
  /* RLE: 055 Pixels @ 000,091*/ 55, 0x00, 
  /* RLE: 010 Pixels @ 055,091*/ 10, 0x01, 
  /* RLE: 008 Pixels @ 065,091*/ 8, 0x02, 
  /* RLE: 013 Pixels @ 073,091*/ 13, 0x01, 
  /* RLE: 011 Pixels @ 086,091*/ 11, 0x02, 
  /* RLE: 007 Pixels @ 097,091*/ 7, 0x01, 
  /* RLE: 003 Pixels @ 104,091*/ 3, 0x02, 
  /* RLE: 012 Pixels @ 107,091*/ 12, 0x01, 
  /* RLE: 004 Pixels @ 119,091*/ 4, 0x02, 
  /* RLE: 005 Pixels @ 123,091*/ 5, 0x01, 
  /* RLE: 021 Pixels @ 128,091*/ 21, 0x00, 
  /* RLE: 011 Pixels @ 149,091*/ 11, 0x01, 
  /* RLE: 053 Pixels @ 000,092*/ 53, 0x00, 
  /* RLE: 012 Pixels @ 053,092*/ 12, 0x01, 
  /* RLE: 008 Pixels @ 065,092*/ 8, 0x02, 
  /* RLE: 013 Pixels @ 073,092*/ 13, 0x01, 
  /* RLE: 012 Pixels @ 086,092*/ 12, 0x02, 
  /* RLE: 007 Pixels @ 098,092*/ 7, 0x01, 
  /* RLE: 003 Pixels @ 105,092*/ 3, 0x02, 
  /* RLE: 011 Pixels @ 108,092*/ 11, 0x01, 
  /* RLE: 004 Pixels @ 119,092*/ 4, 0x02, 
  /* RLE: 005 Pixels @ 123,092*/ 5, 0x01, 
  /* RLE: 023 Pixels @ 128,092*/ 23, 0x00, 
  /* RLE: 008 Pixels @ 151,092*/ 8, 0x01, 
  /* RLE: 052 Pixels @ 159,092*/ 52, 0x00, 
  /* RLE: 014 Pixels @ 051,093*/ 14, 0x01, 
  /* RLE: 009 Pixels @ 065,093*/ 9, 0x02, 
  /* RLE: 012 Pixels @ 074,093*/ 12, 0x01, 
  /* RLE: 013 Pixels @ 086,093*/ 13, 0x02, 
  /* RLE: 007 Pixels @ 099,093*/ 7, 0x01, 
  /* RLE: 003 Pixels @ 106,093*/ 3, 0x02, 
  /* RLE: 010 Pixels @ 109,093*/ 10, 0x01, 
  /* RLE: 005 Pixels @ 119,093*/ 5, 0x02, 
  /* RLE: 004 Pixels @ 124,093*/ 4, 0x01, 
  /* RLE: 025 Pixels @ 128,093*/ 25, 0x00, 
  /* RLE: 004 Pixels @ 153,093*/ 4, 0x01, 
  /* RLE: 052 Pixels @ 157,093*/ 52, 0x00, 
  /* RLE: 015 Pixels @ 049,094*/ 15, 0x01, 
  /* RLE: 011 Pixels @ 064,094*/ 11, 0x02, 
  /* RLE: 011 Pixels @ 075,094*/ 11, 0x01, 
  /* RLE: 014 Pixels @ 086,094*/ 14, 0x02, 
  /* RLE: 006 Pixels @ 100,094*/ 6, 0x01, 
  /* RLE: 004 Pixels @ 106,094*/ 4, 0x02, 
  /* RLE: 008 Pixels @ 110,094*/ 8, 0x01, 
  /* RLE: 006 Pixels @ 118,094*/ 6, 0x02, 
  /* RLE: 005 Pixels @ 124,094*/ 5, 0x01, 
  /* RLE: 079 Pixels @ 129,094*/ 79, 0x00, 
  /* RLE: 016 Pixels @ 048,095*/ 16, 0x01, 
  /* RLE: 012 Pixels @ 064,095*/ 12, 0x02, 
  /* RLE: 010 Pixels @ 076,095*/ 10, 0x01, 
  /* RLE: 015 Pixels @ 086,095*/ 15, 0x02, 
  /* RLE: 006 Pixels @ 101,095*/ 6, 0x01, 
  /* RLE: 004 Pixels @ 107,095*/ 4, 0x02, 
  /* RLE: 007 Pixels @ 111,095*/ 7, 0x01, 
  /* RLE: 006 Pixels @ 118,095*/ 6, 0x02, 
  /* RLE: 005 Pixels @ 124,095*/ 5, 0x01, 
  /* RLE: 077 Pixels @ 129,095*/ 77, 0x00, 
  /* RLE: 018 Pixels @ 046,096*/ 18, 0x01, 
  /* RLE: 014 Pixels @ 064,096*/ 14, 0x02, 
  /* RLE: 007 Pixels @ 078,096*/ 7, 0x01, 
  /* RLE: 016 Pixels @ 085,096*/ 16, 0x02, 
  /* RLE: 007 Pixels @ 101,096*/ 7, 0x01, 
  /* RLE: 005 Pixels @ 108,096*/ 5, 0x02, 
  /* RLE: 004 Pixels @ 113,096*/ 4, 0x01, 
  /* RLE: 007 Pixels @ 117,096*/ 7, 0x02, 
  /* RLE: 005 Pixels @ 124,096*/ 5, 0x01, 
  /* RLE: 076 Pixels @ 129,096*/ 76, 0x00, 
  /* RLE: 019 Pixels @ 045,097*/ 19, 0x01, 
  /* RLE: 016 Pixels @ 064,097*/ 16, 0x02, 
  /* RLE: 004 Pixels @ 080,097*/ 4, 0x01, 
  /* RLE: 018 Pixels @ 084,097*/ 18, 0x02, 
  /* RLE: 006 Pixels @ 102,097*/ 6, 0x01, 
  /* RLE: 016 Pixels @ 108,097*/ 16, 0x02, 
  /* RLE: 005 Pixels @ 124,097*/ 5, 0x01, 
  /* RLE: 075 Pixels @ 129,097*/ 75, 0x00, 
  /* RLE: 020 Pixels @ 044,098*/ 20, 0x01, 
  /* RLE: 028 Pixels @ 064,098*/ 28, 0x02, 
  /* RLE: 005 Pixels @ 092,098*/ 5, 0x01, 
  /* RLE: 005 Pixels @ 097,098*/ 5, 0x02, 
  /* RLE: 007 Pixels @ 102,098*/ 7, 0x01, 
  /* RLE: 015 Pixels @ 109,098*/ 15, 0x02, 
  /* RLE: 005 Pixels @ 124,098*/ 5, 0x01, 
  /* RLE: 074 Pixels @ 129,098*/ 74, 0x00, 
  /* RLE: 021 Pixels @ 043,099*/ 21, 0x01, 
  /* RLE: 027 Pixels @ 064,099*/ 27, 0x02, 
  /* RLE: 008 Pixels @ 091,099*/ 8, 0x01, 
  /* RLE: 004 Pixels @ 099,099*/ 4, 0x02, 
  /* RLE: 007 Pixels @ 103,099*/ 7, 0x01, 
  /* RLE: 014 Pixels @ 110,099*/ 14, 0x02, 
  /* RLE: 005 Pixels @ 124,099*/ 5, 0x01, 
  /* RLE: 073 Pixels @ 129,099*/ 73, 0x00, 
  /* RLE: 022 Pixels @ 042,100*/ 22, 0x01, 
  /* RLE: 026 Pixels @ 064,100*/ 26, 0x02, 
  /* RLE: 009 Pixels @ 090,100*/ 9, 0x01, 
  /* RLE: 005 Pixels @ 099,100*/ 5, 0x02, 
  /* RLE: 006 Pixels @ 104,100*/ 6, 0x01, 
  /* RLE: 014 Pixels @ 110,100*/ 14, 0x02, 
  /* RLE: 005 Pixels @ 124,100*/ 5, 0x01, 
  /* RLE: 072 Pixels @ 129,100*/ 72, 0x00, 
  /* RLE: 023 Pixels @ 041,101*/ 23, 0x01, 
  /* RLE: 025 Pixels @ 064,101*/ 25, 0x02, 
  /* RLE: 011 Pixels @ 089,101*/ 11, 0x01, 
  /* RLE: 004 Pixels @ 100,101*/ 4, 0x02, 
  /* RLE: 007 Pixels @ 104,101*/ 7, 0x01, 
  /* RLE: 013 Pixels @ 111,101*/ 13, 0x02, 
  /* RLE: 005 Pixels @ 124,101*/ 5, 0x01, 
  /* RLE: 071 Pixels @ 129,101*/ 71, 0x00, 
  /* RLE: 025 Pixels @ 040,102*/ 25, 0x01, 
  /* RLE: 015 Pixels @ 065,102*/ 15, 0x02, 
  /* ABS: 002 Pixels @ 080,102*/ 0, 2, 0x11, 
  /* RLE: 006 Pixels @ 082,102*/ 6, 0x02, 
  /* RLE: 012 Pixels @ 088,102*/ 12, 0x01, 
  /* RLE: 005 Pixels @ 100,102*/ 5, 0x02, 
  /* RLE: 006 Pixels @ 105,102*/ 6, 0x01, 
  /* RLE: 013 Pixels @ 111,102*/ 13, 0x02, 
  /* RLE: 005 Pixels @ 124,102*/ 5, 0x01, 
  /* RLE: 071 Pixels @ 129,102*/ 71, 0x00, 
  /* RLE: 015 Pixels @ 040,103*/ 15, 0x01, 
  /* RLE: 005 Pixels @ 055,103*/ 5, 0x00, 
  /* RLE: 005 Pixels @ 060,103*/ 5, 0x01, 
  /* RLE: 012 Pixels @ 065,103*/ 12, 0x02, 
  /* RLE: 007 Pixels @ 077,103*/ 7, 0x01, 
  /* RLE: 004 Pixels @ 084,103*/ 4, 0x02, 
  /* RLE: 013 Pixels @ 088,103*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 101,103*/ 4, 0x02, 
  /* RLE: 007 Pixels @ 105,103*/ 7, 0x01, 
  /* RLE: 012 Pixels @ 112,103*/ 12, 0x02, 
  /* RLE: 005 Pixels @ 124,103*/ 5, 0x01, 
  /* RLE: 070 Pixels @ 129,103*/ 70, 0x00, 
  /* RLE: 013 Pixels @ 039,104*/ 13, 0x01, 
  /* RLE: 008 Pixels @ 052,104*/ 8, 0x00, 
  /* RLE: 005 Pixels @ 060,104*/ 5, 0x01, 
  /* RLE: 011 Pixels @ 065,104*/ 11, 0x02, 
  /* RLE: 009 Pixels @ 076,104*/ 9, 0x01, 
  /* RLE: 003 Pixels @ 085,104*/ 3, 0x02, 
  /* RLE: 013 Pixels @ 088,104*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 101,104*/ 4, 0x02, 
  /* RLE: 007 Pixels @ 105,104*/ 7, 0x01, 
  /* RLE: 012 Pixels @ 112,104*/ 12, 0x02, 
  /* RLE: 004 Pixels @ 124,104*/ 4, 0x01, 
  /* RLE: 071 Pixels @ 128,104*/ 71, 0x00, 
  /* RLE: 010 Pixels @ 039,105*/ 10, 0x01, 
  /* RLE: 011 Pixels @ 049,105*/ 11, 0x00, 
  /* RLE: 006 Pixels @ 060,105*/ 6, 0x01, 
  /* RLE: 009 Pixels @ 066,105*/ 9, 0x02, 
  /* RLE: 011 Pixels @ 075,105*/ 11, 0x01, 
  /* ABS: 002 Pixels @ 086,105*/ 0, 2, 0x22, 
  /* RLE: 013 Pixels @ 088,105*/ 13, 0x01, 
  /* RLE: 005 Pixels @ 101,105*/ 5, 0x02, 
  /* RLE: 007 Pixels @ 106,105*/ 7, 0x01, 
  /* RLE: 010 Pixels @ 113,105*/ 10, 0x02, 
  /* RLE: 005 Pixels @ 123,105*/ 5, 0x01, 
  /* RLE: 071 Pixels @ 128,105*/ 71, 0x00, 
  /* RLE: 009 Pixels @ 039,106*/ 9, 0x01, 
  /* RLE: 013 Pixels @ 048,106*/ 13, 0x00, 
  /* RLE: 005 Pixels @ 061,106*/ 5, 0x01, 
  /* RLE: 009 Pixels @ 066,106*/ 9, 0x02, 
  /* RLE: 011 Pixels @ 075,106*/ 11, 0x01, 
  /* ABS: 002 Pixels @ 086,106*/ 0, 2, 0x22, 
  /* RLE: 013 Pixels @ 088,106*/ 13, 0x01, 
  /* RLE: 005 Pixels @ 101,106*/ 5, 0x02, 
  /* RLE: 007 Pixels @ 106,106*/ 7, 0x01, 
  /* RLE: 010 Pixels @ 113,106*/ 10, 0x02, 
  /* RLE: 005 Pixels @ 123,106*/ 5, 0x01, 
  /* RLE: 071 Pixels @ 128,106*/ 71, 0x00, 
  /* RLE: 008 Pixels @ 039,107*/ 8, 0x01, 
  /* RLE: 014 Pixels @ 047,107*/ 14, 0x00, 
  /* RLE: 006 Pixels @ 061,107*/ 6, 0x01, 
  /* RLE: 007 Pixels @ 067,107*/ 7, 0x02, 
  /* RLE: 013 Pixels @ 074,107*/ 13, 0x01, 
  /* ABS: 002 Pixels @ 087,107*/ 0, 2, 0x22, 
  /* RLE: 012 Pixels @ 089,107*/ 12, 0x01, 
  /* RLE: 005 Pixels @ 101,107*/ 5, 0x02, 
  /* RLE: 008 Pixels @ 106,107*/ 8, 0x01, 
  /* RLE: 008 Pixels @ 114,107*/ 8, 0x02, 
  /* RLE: 006 Pixels @ 122,107*/ 6, 0x01, 
  /* RLE: 071 Pixels @ 128,107*/ 71, 0x00, 
  /* RLE: 008 Pixels @ 039,108*/ 8, 0x01, 
  /* RLE: 015 Pixels @ 047,108*/ 15, 0x00, 
  /* RLE: 006 Pixels @ 062,108*/ 6, 0x01, 
  /* RLE: 006 Pixels @ 068,108*/ 6, 0x02, 
  /* RLE: 013 Pixels @ 074,108*/ 13, 0x01, 
  /* RLE: 003 Pixels @ 087,108*/ 3, 0x02, 
  /* RLE: 011 Pixels @ 090,108*/ 11, 0x01, 
  /* RLE: 006 Pixels @ 101,108*/ 6, 0x02, 
  /* RLE: 007 Pixels @ 107,108*/ 7, 0x01, 
  /* RLE: 008 Pixels @ 114,108*/ 8, 0x02, 
  /* RLE: 006 Pixels @ 122,108*/ 6, 0x01, 
  /* RLE: 071 Pixels @ 128,108*/ 71, 0x00, 
  /* RLE: 007 Pixels @ 039,109*/ 7, 0x01, 
  /* RLE: 017 Pixels @ 046,109*/ 17, 0x00, 
  /* RLE: 005 Pixels @ 063,109*/ 5, 0x01, 
  /* RLE: 006 Pixels @ 068,109*/ 6, 0x02, 
  /* RLE: 013 Pixels @ 074,109*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 087,109*/ 4, 0x02, 
  /* RLE: 010 Pixels @ 091,109*/ 10, 0x01, 
  /* RLE: 006 Pixels @ 101,109*/ 6, 0x02, 
  /* RLE: 008 Pixels @ 107,109*/ 8, 0x01, 
  /* RLE: 006 Pixels @ 115,109*/ 6, 0x02, 
  /* RLE: 006 Pixels @ 121,109*/ 6, 0x01, 
  /* RLE: 072 Pixels @ 127,109*/ 72, 0x00, 
  /* RLE: 007 Pixels @ 039,110*/ 7, 0x01, 
  /* RLE: 015 Pixels @ 046,110*/ 15, 0x00, 
  /* RLE: 008 Pixels @ 061,110*/ 8, 0x01, 
  /* RLE: 005 Pixels @ 069,110*/ 5, 0x02, 
  /* RLE: 013 Pixels @ 074,110*/ 13, 0x01, 
  /* RLE: 005 Pixels @ 087,110*/ 5, 0x02, 
  /* RLE: 009 Pixels @ 092,110*/ 9, 0x01, 
  /* RLE: 006 Pixels @ 101,110*/ 6, 0x02, 
  /* RLE: 008 Pixels @ 107,110*/ 8, 0x01, 
  /* RLE: 005 Pixels @ 115,110*/ 5, 0x02, 
  /* RLE: 007 Pixels @ 120,110*/ 7, 0x01, 
  /* RLE: 072 Pixels @ 127,110*/ 72, 0x00, 
  /* RLE: 008 Pixels @ 039,111*/ 8, 0x01, 
  /* RLE: 012 Pixels @ 047,111*/ 12, 0x00, 
  /* RLE: 011 Pixels @ 059,111*/ 11, 0x01, 
  /* RLE: 005 Pixels @ 070,111*/ 5, 0x02, 
  /* RLE: 013 Pixels @ 075,111*/ 13, 0x01, 
  /* RLE: 006 Pixels @ 088,111*/ 6, 0x02, 
  /* RLE: 006 Pixels @ 094,111*/ 6, 0x01, 
  /* RLE: 007 Pixels @ 100,111*/ 7, 0x02, 
  /* RLE: 009 Pixels @ 107,111*/ 9, 0x01, 
  /* RLE: 003 Pixels @ 116,111*/ 3, 0x02, 
  /* RLE: 008 Pixels @ 119,111*/ 8, 0x01, 
  /* RLE: 073 Pixels @ 127,111*/ 73, 0x00, 
  /* RLE: 007 Pixels @ 040,112*/ 7, 0x01, 
  /* RLE: 011 Pixels @ 047,112*/ 11, 0x00, 
  /* RLE: 013 Pixels @ 058,112*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 071,112*/ 4, 0x02, 
  /* RLE: 013 Pixels @ 075,112*/ 13, 0x01, 
  /* RLE: 019 Pixels @ 088,112*/ 19, 0x02, 
  /* RLE: 009 Pixels @ 107,112*/ 9, 0x01, 
  /* ABS: 002 Pixels @ 116,112*/ 0, 2, 0x22, 
  /* RLE: 008 Pixels @ 118,112*/ 8, 0x01, 
  /* RLE: 074 Pixels @ 126,112*/ 74, 0x00, 
  /* RLE: 008 Pixels @ 040,113*/ 8, 0x01, 
  /* RLE: 008 Pixels @ 048,113*/ 8, 0x00, 
  /* RLE: 016 Pixels @ 056,113*/ 16, 0x01, 
  /* RLE: 004 Pixels @ 072,113*/ 4, 0x02, 
  /* RLE: 011 Pixels @ 076,113*/ 11, 0x01, 
  /* RLE: 020 Pixels @ 087,113*/ 20, 0x02, 
  /* RLE: 019 Pixels @ 107,113*/ 19, 0x01, 
  /* RLE: 075 Pixels @ 126,113*/ 75, 0x00, 
  /* RLE: 008 Pixels @ 041,114*/ 8, 0x01, 
  /* RLE: 006 Pixels @ 049,114*/ 6, 0x00, 
  /* RLE: 018 Pixels @ 055,114*/ 18, 0x01, 
  /* RLE: 005 Pixels @ 073,114*/ 5, 0x02, 
  /* RLE: 009 Pixels @ 078,114*/ 9, 0x01, 
  /* RLE: 020 Pixels @ 087,114*/ 20, 0x02, 
  /* RLE: 018 Pixels @ 107,114*/ 18, 0x01, 
  /* RLE: 077 Pixels @ 125,114*/ 77, 0x00, 
  /* RLE: 007 Pixels @ 042,115*/ 7, 0x01, 
  /* RLE: 005 Pixels @ 049,115*/ 5, 0x00, 
  /* RLE: 020 Pixels @ 054,115*/ 20, 0x01, 
  /* RLE: 005 Pixels @ 074,115*/ 5, 0x02, 
  /* RLE: 008 Pixels @ 079,115*/ 8, 0x01, 
  /* RLE: 021 Pixels @ 087,115*/ 21, 0x02, 
  /* RLE: 017 Pixels @ 108,115*/ 17, 0x01, 
  /* RLE: 077 Pixels @ 125,115*/ 77, 0x00, 
  /* RLE: 008 Pixels @ 042,116*/ 8, 0x01, 
  /* RLE: 003 Pixels @ 050,116*/ 3, 0x00, 
  /* RLE: 022 Pixels @ 053,116*/ 22, 0x01, 
  /* RLE: 007 Pixels @ 075,116*/ 7, 0x02, 
  /* RLE: 003 Pixels @ 082,116*/ 3, 0x01, 
  /* RLE: 023 Pixels @ 085,116*/ 23, 0x02, 
  /* RLE: 016 Pixels @ 108,116*/ 16, 0x01, 
  /* RLE: 079 Pixels @ 124,116*/ 79, 0x00, 
  /* RLE: 008 Pixels @ 043,117*/ 8, 0x01, 
  /* RLE: 001 Pixels @ 051,117*/ 1, 0x00, 
  /* RLE: 024 Pixels @ 052,117*/ 24, 0x01, 
  /* RLE: 032 Pixels @ 076,117*/ 32, 0x02, 
  /* RLE: 016 Pixels @ 108,117*/ 16, 0x01, 
  /* RLE: 080 Pixels @ 124,117*/ 80, 0x00, 
  /* RLE: 035 Pixels @ 044,118*/ 35, 0x01, 
  /* RLE: 028 Pixels @ 079,118*/ 28, 0x02, 
  /* RLE: 016 Pixels @ 107,118*/ 16, 0x01, 
  /* RLE: 082 Pixels @ 123,118*/ 82, 0x00, 
  /* RLE: 025 Pixels @ 045,119*/ 25, 0x01, 
  /* RLE: 001 Pixels @ 070,119*/ 1, 0x00, 
  /* RLE: 008 Pixels @ 071,119*/ 8, 0x01, 
  /* RLE: 028 Pixels @ 079,119*/ 28, 0x02, 
  /* RLE: 006 Pixels @ 107,119*/ 6, 0x01, 
  /* RLE: 001 Pixels @ 113,119*/ 1, 0x00, 
  /* RLE: 008 Pixels @ 114,119*/ 8, 0x01, 
  /* RLE: 084 Pixels @ 122,119*/ 84, 0x00, 
  /* RLE: 019 Pixels @ 046,120*/ 19, 0x01, 
  /* RLE: 007 Pixels @ 065,120*/ 7, 0x00, 
  /* RLE: 009 Pixels @ 072,120*/ 9, 0x01, 
  /* RLE: 026 Pixels @ 081,120*/ 26, 0x02, 
  /* RLE: 005 Pixels @ 107,120*/ 5, 0x01, 
  /* RLE: 004 Pixels @ 112,120*/ 4, 0x00, 
  /* RLE: 005 Pixels @ 116,120*/ 5, 0x01, 
  /* RLE: 086 Pixels @ 121,120*/ 86, 0x00, 
  /* RLE: 015 Pixels @ 047,121*/ 15, 0x01, 
  /* RLE: 011 Pixels @ 062,121*/ 11, 0x00, 
  /* RLE: 010 Pixels @ 073,121*/ 10, 0x01, 
  /* RLE: 024 Pixels @ 083,121*/ 24, 0x02, 
  /* RLE: 005 Pixels @ 107,121*/ 5, 0x01, 
  /* RLE: 005 Pixels @ 112,121*/ 5, 0x00, 
  /* RLE: 004 Pixels @ 117,121*/ 4, 0x01, 
  /* RLE: 086 Pixels @ 121,121*/ 86, 0x00, 
  /* RLE: 013 Pixels @ 047,122*/ 13, 0x01, 
  /* RLE: 013 Pixels @ 060,122*/ 13, 0x00, 
  /* RLE: 012 Pixels @ 073,122*/ 12, 0x01, 
  /* RLE: 021 Pixels @ 085,122*/ 21, 0x02, 
  /* RLE: 006 Pixels @ 106,122*/ 6, 0x01, 
  /* RLE: 096 Pixels @ 112,122*/ 96, 0x00, 
  /* RLE: 010 Pixels @ 048,123*/ 10, 0x01, 
  /* RLE: 014 Pixels @ 058,123*/ 14, 0x00, 
  /* RLE: 014 Pixels @ 072,123*/ 14, 0x01, 
  /* RLE: 020 Pixels @ 086,123*/ 20, 0x02, 
  /* RLE: 007 Pixels @ 106,123*/ 7, 0x01, 
  /* RLE: 095 Pixels @ 113,123*/ 95, 0x00, 
  /* RLE: 009 Pixels @ 048,124*/ 9, 0x01, 
  /* RLE: 014 Pixels @ 057,124*/ 14, 0x00, 
  /* RLE: 019 Pixels @ 071,124*/ 19, 0x01, 
  /* RLE: 014 Pixels @ 090,124*/ 14, 0x02, 
  /* RLE: 009 Pixels @ 104,124*/ 9, 0x01, 
  /* RLE: 096 Pixels @ 113,124*/ 96, 0x00, 
  /* RLE: 008 Pixels @ 049,125*/ 8, 0x01, 
  /* RLE: 012 Pixels @ 057,125*/ 12, 0x00, 
  /* RLE: 025 Pixels @ 069,125*/ 25, 0x01, 
  /* RLE: 007 Pixels @ 094,125*/ 7, 0x02, 
  /* RLE: 012 Pixels @ 101,125*/ 12, 0x01, 
  /* RLE: 095 Pixels @ 113,125*/ 95, 0x00, 
  /* RLE: 008 Pixels @ 048,126*/ 8, 0x01, 
  /* RLE: 012 Pixels @ 056,126*/ 12, 0x00, 
  /* RLE: 045 Pixels @ 068,126*/ 45, 0x01, 
  /* RLE: 093 Pixels @ 113,126*/ 93, 0x00, 
  /* RLE: 010 Pixels @ 046,127*/ 10, 0x01, 
  /* RLE: 012 Pixels @ 056,127*/ 12, 0x00, 
  /* RLE: 044 Pixels @ 068,127*/ 44, 0x01, 
  /* RLE: 092 Pixels @ 112,127*/ 92, 0x00, 
  /* RLE: 013 Pixels @ 044,128*/ 13, 0x01, 
  /* RLE: 010 Pixels @ 057,128*/ 10, 0x00, 
  /* RLE: 044 Pixels @ 067,128*/ 44, 0x01, 
  /* RLE: 093 Pixels @ 111,128*/ 93, 0x00, 
  /* RLE: 013 Pixels @ 044,129*/ 13, 0x01, 
  /* RLE: 009 Pixels @ 057,129*/ 9, 0x00, 
  /* RLE: 018 Pixels @ 066,129*/ 18, 0x01, 
  /* RLE: 004 Pixels @ 084,129*/ 4, 0x00, 
  /* RLE: 020 Pixels @ 088,129*/ 20, 0x01, 
  /* RLE: 097 Pixels @ 108,129*/ 97, 0x00, 
  /* RLE: 013 Pixels @ 045,130*/ 13, 0x01, 
  /* RLE: 008 Pixels @ 058,130*/ 8, 0x00, 
  /* RLE: 013 Pixels @ 066,130*/ 13, 0x01, 
  /* RLE: 014 Pixels @ 079,130*/ 14, 0x00, 
  /* RLE: 010 Pixels @ 093,130*/ 10, 0x01, 
  /* RLE: 102 Pixels @ 103,130*/ 102, 0x00, 
  /* RLE: 013 Pixels @ 045,131*/ 13, 0x01, 
  /* RLE: 007 Pixels @ 058,131*/ 7, 0x00, 
  /* RLE: 011 Pixels @ 065,131*/ 11, 0x01, 
  /* RLE: 130 Pixels @ 076,131*/ 130, 0x00, 
  /* RLE: 013 Pixels @ 046,132*/ 13, 0x01, 
  /* RLE: 006 Pixels @ 059,132*/ 6, 0x00, 
  /* RLE: 010 Pixels @ 065,132*/ 10, 0x01, 
  /* RLE: 132 Pixels @ 075,132*/ 132, 0x00, 
  /* RLE: 013 Pixels @ 047,133*/ 13, 0x01, 
  /* RLE: 005 Pixels @ 060,133*/ 5, 0x00, 
  /* RLE: 009 Pixels @ 065,133*/ 9, 0x01, 
  /* RLE: 134 Pixels @ 074,133*/ 134, 0x00, 
  /* RLE: 013 Pixels @ 048,134*/ 13, 0x01, 
  /* RLE: 004 Pixels @ 061,134*/ 4, 0x00, 
  /* RLE: 008 Pixels @ 065,134*/ 8, 0x01, 
  /* RLE: 141 Pixels @ 073,134*/ 141, 0x00, 
  /* RLE: 008 Pixels @ 054,135*/ 8, 0x01, 
  /* RLE: 003 Pixels @ 062,135*/ 3, 0x00, 
  /* RLE: 007 Pixels @ 065,135*/ 7, 0x01, 
  /* RLE: 143 Pixels @ 072,135*/ 143, 0x00, 
  /* RLE: 008 Pixels @ 055,136*/ 8, 0x01, 
  /* ABS: 002 Pixels @ 063,136*/ 0, 2, 0x00, 
  /* RLE: 007 Pixels @ 065,136*/ 7, 0x01, 
  /* RLE: 144 Pixels @ 072,136*/ 144, 0x00, 
  /* RLE: 008 Pixels @ 056,137*/ 8, 0x01, 
  /* RLE: 001 Pixels @ 064,137*/ 1, 0x00, 
  /* RLE: 007 Pixels @ 065,137*/ 7, 0x01, 
  /* RLE: 145 Pixels @ 072,137*/ 145, 0x00, 
  /* RLE: 007 Pixels @ 057,138*/ 7, 0x01, 
  /* RLE: 001 Pixels @ 064,138*/ 1, 0x00, 
  /* RLE: 008 Pixels @ 065,138*/ 8, 0x01, 
  /* RLE: 144 Pixels @ 073,138*/ 144, 0x00, 
  /* RLE: 008 Pixels @ 057,139*/ 8, 0x01, 
  /* RLE: 001 Pixels @ 065,139*/ 1, 0x00, 
  /* RLE: 007 Pixels @ 066,139*/ 7, 0x01, 
  /* RLE: 145 Pixels @ 073,139*/ 145, 0x00, 
  /* RLE: 007 Pixels @ 058,140*/ 7, 0x01, 
  /* RLE: 001 Pixels @ 065,140*/ 1, 0x00, 
  /* RLE: 008 Pixels @ 066,140*/ 8, 0x01, 
  /* RLE: 144 Pixels @ 074,140*/ 144, 0x00, 
  /* RLE: 007 Pixels @ 058,141*/ 7, 0x01, 
  /* ABS: 002 Pixels @ 065,141*/ 0, 2, 0x00, 
  /* RLE: 008 Pixels @ 067,141*/ 8, 0x01, 
  /* RLE: 144 Pixels @ 075,141*/ 144, 0x00, 
  /* RLE: 006 Pixels @ 059,142*/ 6, 0x01, 
  /* RLE: 003 Pixels @ 065,142*/ 3, 0x00, 
  /* RLE: 008 Pixels @ 068,142*/ 8, 0x01, 
  /* RLE: 142 Pixels @ 076,142*/ 142, 0x00, 
  /* RLE: 007 Pixels @ 058,143*/ 7, 0x01, 
  /* RLE: 004 Pixels @ 065,143*/ 4, 0x00, 
  /* RLE: 008 Pixels @ 069,143*/ 8, 0x01, 
  /* RLE: 139 Pixels @ 077,143*/ 139, 0x00, 
  /* RLE: 010 Pixels @ 056,144*/ 10, 0x01, 
  /* RLE: 004 Pixels @ 066,144*/ 4, 0x00, 
  /* RLE: 008 Pixels @ 070,144*/ 8, 0x01, 
  /* RLE: 136 Pixels @ 078,144*/ 136, 0x00, 
  /* RLE: 012 Pixels @ 054,145*/ 12, 0x01, 
  /* RLE: 005 Pixels @ 066,145*/ 5, 0x00, 
  /* RLE: 008 Pixels @ 071,145*/ 8, 0x01, 
  /* RLE: 135 Pixels @ 079,145*/ 135, 0x00, 
  /* RLE: 011 Pixels @ 054,146*/ 11, 0x01, 
  /* RLE: 007 Pixels @ 065,146*/ 7, 0x00, 
  /* RLE: 007 Pixels @ 072,146*/ 7, 0x01, 
  /* RLE: 136 Pixels @ 079,146*/ 136, 0x00, 
  /* RLE: 010 Pixels @ 055,147*/ 10, 0x01, 
  /* RLE: 007 Pixels @ 065,147*/ 7, 0x00, 
  /* RLE: 008 Pixels @ 072,147*/ 8, 0x01, 
  /* RLE: 135 Pixels @ 080,147*/ 135, 0x00, 
  /* RLE: 010 Pixels @ 055,148*/ 10, 0x01, 
  /* RLE: 008 Pixels @ 065,148*/ 8, 0x00, 
  /* RLE: 007 Pixels @ 073,148*/ 7, 0x01, 
  /* RLE: 136 Pixels @ 080,148*/ 136, 0x00, 
  /* RLE: 009 Pixels @ 056,149*/ 9, 0x01, 
  /* RLE: 009 Pixels @ 065,149*/ 9, 0x00, 
  /* RLE: 007 Pixels @ 074,149*/ 7, 0x01, 
  /* RLE: 136 Pixels @ 081,149*/ 136, 0x00, 
  /* RLE: 007 Pixels @ 057,150*/ 7, 0x01, 
  /* RLE: 010 Pixels @ 064,150*/ 10, 0x00, 
  /* RLE: 007 Pixels @ 074,150*/ 7, 0x01, 
  /* RLE: 137 Pixels @ 081,150*/ 137, 0x00, 
  /* RLE: 005 Pixels @ 058,151*/ 5, 0x01, 
  /* RLE: 012 Pixels @ 063,151*/ 12, 0x00, 
  /* RLE: 006 Pixels @ 075,151*/ 6, 0x01, 
  /* RLE: 154 Pixels @ 081,151*/ 154, 0x00, 
  /* RLE: 006 Pixels @ 075,152*/ 6, 0x01, 
  /* RLE: 153 Pixels @ 081,152*/ 153, 0x00, 
  /* RLE: 008 Pixels @ 074,153*/ 8, 0x01, 
  /* RLE: 148 Pixels @ 082,153*/ 148, 0x00, 
  /* RLE: 012 Pixels @ 070,154*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 082,154*/ 148, 0x00, 
  /* RLE: 012 Pixels @ 070,155*/ 12, 0x01, 
  /* RLE: 148 Pixels @ 082,155*/ 148, 0x00, 
  /* RLE: 011 Pixels @ 070,156*/ 11, 0x01, 
  /* RLE: 150 Pixels @ 081,156*/ 150, 0x00, 
  /* RLE: 010 Pixels @ 071,157*/ 10, 0x01, 
  /* RLE: 150 Pixels @ 081,157*/ 150, 0x00, 
  /* RLE: 010 Pixels @ 071,158*/ 10, 0x01, 
  /* RLE: 151 Pixels @ 081,158*/ 151, 0x00, 
  /* RLE: 008 Pixels @ 072,159*/ 8, 0x01, 
  /* RLE: 153 Pixels @ 080,159*/ 153, 0x00, 
  /* RLE: 006 Pixels @ 073,160*/ 6, 0x01, 
  /* RLE: 081 Pixels @ 079,160*/ 81, 0x00, 
  0
};  /* 1927 for 25760 pixels */

static const GUI_BITMAP _bmLadyBug = {
  160,               /* XSize */
  161,               /* YSize */
  40,                /* BytesPerLine */
  GUI_COMPRESS_RLE4, /* BitsPerPixel */
  _acLadyBug,        /* Pointer to picture data (indices) */
  &_PalLadyBug       /* Pointer to palette */
  ,GUI_DRAW_RLE4
};

/*********************************************************************
*
*       static code
*
**********************************************************************
*/
#define MAG_FACTOR 3

/*********************************************************************
*
*       _ShowMagnifiedBitmap
*/
static void _ShowMagnifiedBitmap(void) {
  int xCenter, yCenter, x0, y0;

  xCenter = LCD_GetXSize() >> 1;
  yCenter = LCD_GetYSize() >> 1;
  x0      = xCenter - (GUIDEMO_bm4bpp.XSize >> 1) * MAG_FACTOR;
  y0      = yCenter - (GUIDEMO_bm4bpp.YSize >> 1) * MAG_FACTOR;
  GUI_DrawBitmapMag(&GUIDEMO_bm4bpp, x0, y0, MAG_FACTOR, MAG_FACTOR);
  GUIDEMO_ShowInfo("Bitmaps can be magnified...");
}



/*********************************************************************
*
*       _BitmapDemo 
*/
static void _BitmapDemo(void) {
  int x,     y;
  int xSize, ySize;

  xSize = LCD_GetXSize();
  ySize = LCD_GetYSize();
  //
  // RLE Bitmap
  //
  GUI_DrawBitmap(&_bmLadyBug, 20, 50);
  GUIDEMO_ShowInfo("RLE Compressed bitmaps");
  GUIDEMO_Wait(3000);
  GUIDEMO_DrawBk(0);
  //
  // 1/2/4/8 bpp bitmaps
  //
  GUI_DrawBitmap(&GUIDEMO_bm4bpp, 20, 50);
  GUIDEMO_ShowInfo("1/2/4/8 bpp bitmaps");
  GUIDEMO_Wait(3000);
  //
  // Tile display with image
  //
  for (x = 0; x < xSize / GUIDEMO_bm4bpp.XSize + 1; x++) {
    for (y = 0; y < ySize / GUIDEMO_bm4bpp.YSize + 1; y++) {
      GUI_DrawBitmap(&GUIDEMO_bm4bpp, GUIDEMO_bm4bpp.XSize * x, GUIDEMO_bm4bpp.YSize * y);
    }
  }
  GUIDEMO_Wait(2000);
  //
  // Magnified bitmap
  //
  GUIDEMO_ShowInfo("Bitmaps may also be\n"
                   "magnified and rotated");
  _ShowMagnifiedBitmap();
}

/*********************************************************************
*
*       public code
*
**********************************************************************
*/
/*********************************************************************
*
*       GUIDEMO_Bitmap
*/
void GUIDEMO_Bitmap(void) {
  GUIDEMO_ShowIntro("Bitmaps", "Showing\n"
                               "different bitmaps with\n"
                               "and without compression");
  GUIDEMO_ShowInfoWin();
  GUIDEMO_DrawBk(0);
  _BitmapDemo();
  GUIDEMO_Wait(4000);
}

#else

void GUIDEMO_Bitmap(void) {}

#endif

/*************************** End of file ****************************/
