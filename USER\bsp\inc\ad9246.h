#ifndef _AD9246_H
#define _AD9246_H
#include "bsp.h"

//端口定义
#define AD9246_PWDN(n)		(n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_4,GP<PERSON>_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_4,GP<PERSON>_PIN_RESET))
#define AD9246_CS(n)		(n?HAL_GPIO_WritePin(GPIOI,GPIO_PIN_8,GP<PERSON>_PIN_SET):HAL_GPIO_WritePin(GPIOI,GPIO_PIN_8,GPIO_PIN_RESET))
#define AD9246_SCLK(n)		(n?HAL_GPIO_WritePin(GPIOI,GPIO_PIN_9,GP<PERSON>_PIN_SET):HAL_GPIO_WritePin(GPIOI,GPIO_PIN_9,GP<PERSON>_PIN_RESET))
#define AD9246_SDIO(n)		(n?HAL_GPIO_WritePin(GPIOI,GPIO_PIN_10,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOI,GPIO_PIN_10,GPIO_PIN_RESET))

void AD9246_Init(void);

#endif
