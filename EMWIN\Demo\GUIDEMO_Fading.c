/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : GUIDEMO_Fading.c
Purpose     : Demo of fading effect function
---------------------------END-OF-HEADER------------------------------
*/

/**
  ******************************************************************************
  * @file    GUIDEMO_Fading.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Demo of fading effect function
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


#include <stddef.h>

#include "GUIDEMO.h"

#if (SHOW_GUIDEMO_FADING && GUI_SUPPORT_MEMDEV)

/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#ifdef WIN32
  #define TIME_PER_FRAME 10
#else
  #define TIME_PER_FRAME 40
#endif

#define NUM_FADINGS 3
#define NUM_SCREENS 2
#define TIME_FADING 1500

/*********************************************************************
*
*       Static data
*
**********************************************************************
*/
/*********************************************************************
*
*       _bmSeggerLogo_160x80
*/
static GUI_CONST_STORAGE unsigned long acSeggerLogo_160x80[] = {
  0xFF000000, 0xFF000000, 0xFF000000, 0x7F292929, 0x20292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x20292929, 0x7F292929, 0xFF000000, 0xFF000000, 0xFF000000,
  0xFF000000, 0xEF292929, 0x30292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x30292929, 0xEF292929, 0xFF000000,
  0xFF000000, 0x30292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x30292929, 0xFF000000,
  0x9F292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00797979, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x9F292929,
  0x30292929, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x30292929,
  0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x00A35B55, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00BD8A86, 0x0096433D, 0x0096433D, 0x00BD8A86, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00D8B9B6, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00CBA19E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00E5D0CF, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00BD8A86, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D8B9B6, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BD8A86, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B0726E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00CBA19E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E5D0CF, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00F2E8E7, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00BD8A86,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x009D4F49, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D8B9B6, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x00AA6661, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00E5D0CF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BD8A86, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00AA6661, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B0726E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x009D4F49, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B0726E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E5D0CF,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00E5D0CF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E5D0CF,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00E5D0CF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x009D4F49, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B0726E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00AA6661, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B0726E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BD8A86, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D8B9B6, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x00AA6661, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E,
        0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00CBA19E, 0x00E5D0CF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x009D4F49, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00BD8A86,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E5D0CF, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00F2E8E7, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00AFAFAF, 0x00878787, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00878787, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00D7D7D7, 0x00AFAFAF, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00AFAFAF, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00D7D7D7, 0x00949494, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00797979, 0x00AFAFAF, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00BCBCBC, 0x00878787, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00878787, 0x00AFAFAF, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E4E4E4, 0x00BCBCBC, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00A1A1A1, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00AFAFAF, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00CACACA, 0x00CACACA, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00CBA19E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00444444, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00444444,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BCBCBC, 0x00444444,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x00BCBCBC, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B0726E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00515151, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00797979, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00515151, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D,
        0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00D7D7D7, 0x00FFFFFF, 0x00F2F2F2, 0x00AFAFAF, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00363636, 0x00878787, 0x00CACACA, 0x00CACACA, 0x00AFAFAF, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00878787, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00444444, 0x00A1A1A1, 0x00CACACA, 0x00CACACA, 0x00AFAFAF, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00444444,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00A1A1A1, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00878787, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00E4E4E4, 0x00292929, 0x00292929, 0x00292929, 0x00515151, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00515151, 0x00292929, 0x00292929, 0x006C6C6C, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00444444, 0x00292929, 0x00292929, 0x00292929,
        0x00515151, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00797979, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00363636, 0x00292929, 0x00292929, 0x00292929,
        0x00515151, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00797979, 0x00292929, 0x00292929, 0x00292929, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00E4E4E4, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00A1A1A1, 0x00A1A1A1, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x005F5F5F, 0x006C6C6C, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929, 0x00363636,
        0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x005F5F5F, 0x00797979, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BD8A86, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00515151, 0x00878787, 0x00BCBCBC, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00515151,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929, 0x00797979,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00515151,
        0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D8B9B6, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x006C6C6C, 0x00AFAFAF, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929, 0x00878787,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00CACACA, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00797979, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00515151, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00C49592, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BCBCBC, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929, 0x00949494,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929, 0x00CACACA,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00878787, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00DEC4C2, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F8F3F3, 0x00A35B55, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00949494, 0x005F5F5F, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929, 0x00878787,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929, 0x00A1A1A1,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00444444,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x009D4F49, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00BD8A86, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00A1A1A1, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00515151,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00797979, 0x005F5F5F, 0x005F5F5F, 0x00515151, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929, 0x00797979,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00797979, 0x005F5F5F, 0x005F5F5F, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00878787, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AA6661, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00D1ADAA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00AA6661, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00878787, 0x00363636, 0x00515151, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00CACACA, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929, 0x00363636,
        0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00797979, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00B77E7A, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00EBDCDB, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x009D4F49, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BCBCBC, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00444444, 0x00292929, 0x00292929, 0x00292929,
        0x00363636, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00363636, 0x00292929, 0x00292929, 0x00292929,
        0x00515151, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00D7D7D7, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B0726E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00A35B55, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00CBA19E, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00DEC4C2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00444444, 0x00AFAFAF, 0x00D7D7D7, 0x00FFFFFF, 0x00D7D7D7, 0x00AFAFAF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00444444, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00797979, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00797979, 0x00BCBCBC, 0x00CACACA, 0x00BCBCBC, 0x00797979, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00363636, 0x00878787, 0x00CACACA, 0x00CACACA, 0x00AFAFAF, 0x005F5F5F, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00363636,
        0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x006C6C6C, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00B77E7A, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00EBDCDB, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00D8B9B6, 0x0096433D, 0x0096433D, 0x0096433D, 0x0096433D, 0x00CBA19E, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00515151, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00949494,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00797979, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00363636, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x00A35B55, 0x0096433D, 0x0096433D, 0x00D8B9B6, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00BD8A86, 0x0096433D, 0x0096433D, 0x00BD8A86, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BCBCBC, 0x00444444, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x005F5F5F, 0x00F2F2F2, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00CACACA, 0x00444444,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00878787, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00363636,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00797979, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00363636, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00363636, 0x00FFFFFF, 0x00FFFFFF, 0x00A1A1A1, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00EBDCDB, 0x00F8F3F3, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2E8E7, 0x00F2E8E7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00BCBCBC, 0x00878787, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00949494, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00D7D7D7, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00BCBCBC, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00BCBCBC, 0x00797979, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00878787, 0x00D7D7D7, 0x00FFFFFF, 0x00F2F2F2, 0x006C6C6C, 0x005F5F5F, 0x00D7D7D7, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2,
        0x00AFAFAF, 0x00797979, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x005F5F5F, 0x00949494, 0x00E4E4E4, 0x00FFFFFF, 0x00D7D7D7, 0x005F5F5F, 0x005F5F5F, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00E4E4E4, 0x00A1A1A1, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00A1A1A1, 0x00E4E4E4, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00A1A1A1, 0x00949494, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00F2F2F2, 0x00949494, 0x006C6C6C, 0x00A1A1A1, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x00949494, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00949494, 0x00292929, 0x00292929, 0x00292929,
  0x00292929, 0x00292929, 0x00292929, 0x006C6C6C, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x006C6C6C, 0x00292929, 0x00292929, 0x00292929,
  0x30292929, 0x00292929, 0x00292929, 0x00292929, 0x00AFAFAF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF,
        0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00FFFFFF, 0x00AFAFAF, 0x00292929, 0x00292929, 0x00292929, 0x30292929,
  0x9F292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00797979, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494,
        0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00949494, 0x00797979, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x9F292929,
  0xFF000000, 0x30292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x30292929, 0xFF000000,
  0xFF000000, 0xEF292929, 0x30292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x30292929, 0xEF292929, 0xFF000000,
  0xFF000000, 0xFF000000, 0xFF000000, 0x7F292929, 0x20292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929,
        0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x00292929, 0x20292929, 0x7F292929, 0xFF000000, 0xFF000000, 0xFF000000
};

static GUI_CONST_STORAGE GUI_BITMAP _bmSeggerLogo_160x80 = {
  160, /* XSize */
  80, /* YSize */
  640, /* BytesPerLine */
  32, /* BitsPerPixel */
  (unsigned char *)acSeggerLogo_160x80,  /* Pointer to picture data */
  NULL  /* Pointer to palette */
 ,GUI_DRAW_BMP8888
};

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/
/*********************************************************************
*
*       _DrawFrame
*/
static void _DrawFrame(int x0, int y0, int x1, int y1, int b) {
  GUI_FillRect(x0, y0, x1, y0 + b - 1);
  GUI_FillRect(x0, y0 + b, x0 + b - 1, y1 - b);
  GUI_FillRect(x1 - b + 1, y0 + b, x1, y1 - b);
  GUI_FillRect(x0, y1 - b + 1, x1, y1);
}

/*********************************************************************
*
*       _DrawLogo
*/
static void _DrawLogo(int xSize, GUI_MEMDEV_Handle hLogo, GUI_MEMDEV_Handle hMemWork, GUI_RECT * pRect) {
  int xSizeLogo, ySizeLogo, Factor, xFactor, yFactor;

  xSizeLogo = GUI_MEMDEV_GetXSize(hLogo);
  ySizeLogo = GUI_MEMDEV_GetYSize(hLogo);
  if ((xSizeLogo < (pRect->x1 - pRect->x0 + 1)) && (ySizeLogo < (pRect->y1 - pRect->y0 + 1))) {
    GUI_MEMDEV_WriteAt(hLogo,
                       pRect->x0 + (pRect->x1 - pRect->x0 + 1 - xSizeLogo) / 2,
                       pRect->y0 + (pRect->y1 - pRect->y0 + 1 - ySizeLogo) / 2);
  } else {
    xFactor = ((pRect->x1 - pRect->x0 + 1) * 1000) / xSizeLogo;
    yFactor = ((pRect->y1 - pRect->y0 + 1) * 1000) / ySizeLogo;
    Factor = (xFactor > yFactor) ? yFactor : xFactor;
    GUI_MEMDEV_RotateHQ(hLogo, hMemWork,
                        (xSize - xSizeLogo) / 2,
                        pRect->y0 + ((pRect->y1 - pRect->y0 + 1) - ySizeLogo) / 2,
                        0, Factor);
  }
}

/*********************************************************************
*
*       _DrawText
*/
static void _DrawText(GUI_RECT * pRect) {
  GUI_SetFont(GUI_FONT_20_ASCII);
  GUI_SetColor(GUI_WHITE);
  GUI_SetTextMode(GUI_TM_TRANS);
  GUI_DispStringInRectWrap("Fading between two screens", pRect, GUI_TA_HCENTER | GUI_TA_VCENTER, GUI_WRAPMODE_WORD);
}

/*********************************************************************
*
*       _Paint0
*/
static void _Paint0(int xSize, int ySize, GUI_MEMDEV_Handle hLogo, GUI_MEMDEV_Handle hMemWork) {
  int ySizeText;
  GUI_RECT rLogo, rText;

  //
  // Background
  //
  GUI_SetBkColor(GUI_MAGENTA);
  GUI_Clear();
  GUI_SetColor(GUI_WHITE);
  _DrawFrame(10, 10, xSize - 11, ySize - 11, 5);
  GUI_SetColor(GUI_BLUE);
  GUI_FillRect(15, 15, xSize - 16, ySize - 16);
  //
  // Screen0
  //
  GUI_SetColor(GUI_WHITE);
  GUI_SetFont(GUI_FONT_24B_ASCII);
  GUI_SetTextMode(GUI_TM_TRANS);
  ySizeText = GUI_GetFontDistY();
  GUI_DispStringHCenterAt("Screen 0", xSize / 2, (ySize - ySizeText) / 2);
  //
  // Logo
  //
  rLogo.x0 = 25;
  rLogo.y0 = 25;
  rLogo.x1 = xSize - 25 - 1;
  rLogo.y1 = (ySize - ySizeText) / 2 - 10 - 1;
  _DrawLogo(xSize, hLogo, hMemWork, &rLogo);
  //
  // Message
  //
  rText.x0 = 25;
  rText.y0 = (ySize - ySizeText) / 2 + ySizeText + 10;
  rText.x1 = xSize - 25 - 1;
  rText.y1 = ySize - 25;
  _DrawText(&rText);
}

/*********************************************************************
*
*       _Paint1
*/
static void _Paint1(int xSize, int ySize, GUI_MEMDEV_Handle hLogo, GUI_MEMDEV_Handle hMemWork) {
  int ySizeText;
  GUI_RECT rLogo, rText;

  //
  // Background
  //
  GUI_SetBkColor(GUI_CYAN);
  GUI_Clear();
  GUI_SetColor(GUI_WHITE);
  _DrawFrame(10, 10, xSize - 11, ySize - 11, 5);
  GUI_SetColor(GUI_BLUE);
  GUI_FillRect(15, 15, xSize - 16, ySize - 16);
  //
  // Screen1
  //
  GUI_SetColor(GUI_WHITE);
  GUI_SetFont(GUI_FONT_24B_ASCII);
  GUI_SetTextMode(GUI_TM_TRANS);
  ySizeText = GUI_GetFontDistY();
  GUI_DispStringHCenterAt("Screen 1", xSize / 2, (ySize - ySizeText) / 2);
  //
  // Logo
  //
  rLogo.x0 = 25;
  rLogo.y0 = (ySize - ySizeText) / 2 + ySizeText + 10;
  rLogo.x1 = xSize - 25 - 1;
  rLogo.y1 = ySize - 25;
  _DrawLogo(xSize, hLogo, hMemWork, &rLogo);
  //
  // Message
  //
  rText.x0 = 25;
  rText.y0 = 25;
  rText.x1 = xSize - 25 - 1;
  rText.y1 = (ySize - ySizeText) / 2 - 10 - 1;
  _DrawText(&rText);
}

/*********************************************************************
*
*       _FadeScreens
*/
static int _FadeScreens(GUI_MEMDEV_Handle hScreen0, GUI_MEMDEV_Handle hScreen1) {

  GUI_MEMDEV_Write(hScreen1);
  //
  // Fade in Title and check if the user wants to cancel the demo
  //
  GUI_MEMDEV_FadeDevices(hScreen0, hScreen1, TIME_FADING);
  if (GUIDEMO_CheckCancel() == 1) {
    return 1;
  }
  return 0;
}

/*********************************************************************
*
*       _FadingDemo
*/
static void _FadingDemo(void) {
  GUI_MEMDEV_Handle    hLogo, hMemControl;
  GUI_MEMDEV_Handle    ahMem[NUM_SCREENS];
  void              (* apFunc[])(int, int, GUI_MEMDEV_Handle, GUI_MEMDEV_Handle) = { _Paint0, _Paint1 };
  int                  xSize, ySize, i, j;

  xSize = LCD_GetXSize();
  ySize = LCD_GetYSize();
  //
  // Store Control window in MEMDEV
  //
  GUI_Exec();
  hMemControl = GUI_MEMDEV_CreateEx(xSize - CONTROL_SIZE_X, ySize - CONTROL_SIZE_Y, CONTROL_SIZE_X, CONTROL_SIZE_Y, GUI_MEMDEV_NOTRANS);
  GUI_MEMDEV_CopyFromLCD(hMemControl);
  //
  // Create logo device
  //
  hLogo = GUI_MEMDEV_CreateFixed(0, 0, _bmSeggerLogo_160x80.XSize, _bmSeggerLogo_160x80.YSize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_888);
  GUI_MEMDEV_Select(hLogo);
  GUI_SetBkColor(GUI_TRANSPARENT);
  GUI_Clear();
  GUI_DrawBitmap(&_bmSeggerLogo_160x80, 0, 0);
  //
  // Create screen devices
  //
  for (i = 0; i < NUM_SCREENS; i++) {
    ahMem[i] = GUI_MEMDEV_CreateFixed(0, 0, xSize, ySize, GUI_MEMDEV_HASTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_888);
    GUI_MEMDEV_Select(ahMem[i]);
    apFunc[i](xSize, ySize, hLogo, ahMem[i]);
    GUI_MEMDEV_Write(hMemControl);
  }
  GUI_MEMDEV_Select(0);
  //
  // Show fading
  //
  for (j = 0; j < NUM_FADINGS; j++) {
    for (i = 0; i < NUM_SCREENS; i++) {
      if (_FadeScreens(ahMem[i], ahMem[i ^ 1])) {
        j = NUM_FADINGS;
        break;
      }
    }
  }
  //
  // Delete devices
  //
  GUI_MEMDEV_Delete(hMemControl);
  GUI_MEMDEV_Delete(hLogo);
  for (i = 0; i < NUM_SCREENS; i++) {
    if (ahMem[i]) {
      GUI_MEMDEV_Delete(ahMem[i]);
    }
  }
}


/*********************************************************************
*
*       Public code
*
**********************************************************************
*/
/*********************************************************************
*
*       GUIDEMO_Fading
*/
void GUIDEMO_Fading(void) {
  GUIDEMO_HideInfoWin();
  GUIDEMO_ShowIntro("Fading",
                    "Fading between\n"
                    "two screens");
  GUIDEMO_HideControlWin();
  #if GUI_SUPPORT_CURSOR
    GUI_CURSOR_Hide();
  #endif
  _FadingDemo();
  #if GUI_SUPPORT_CURSOR
    GUI_CURSOR_Show();
  #endif
}

#else

void GUIDEMO_Fading(void) {}

#endif

/*************************** End of file ****************************/
