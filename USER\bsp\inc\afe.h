#ifndef __AFE_H
#define __AFE_H	 
#include "bsp.h"


#define FILTER_LP_200K		0x00
#define FILTER_LP_800K		0x01
#define FILTER_LP_1000K		0x02
#define FILTER_LP_2000K		0x03
							
#define FILTER_HP_40K		0x00
#define FILTER_HP_200K		0x01
#define FILTER_HP_400K		0x02
#define FILTER_HP_800K		0x03

// #define FILTER_LP_200K		0x04
// #define FILTER_LP_800K		0x03
// #define FILTER_LP_1000K		0x02
// #define FILTER_LP_2000K		0x01
// #define FILTER_LP_10000K	0x00
							
// #define FILTER_HP_40K		0x00
// #define FILTER_HP_80K		0x01
// #define FILTER_HP_200K		0x02
// #define FILTER_HP_400K		0x03
// #define FILTER_HP_800K		0x04

// LP-A0(PG4)、LP-A1(PG3)、LP-A2(PG2)、HP-A0(PI2)、HP-A1(PI1)、HP-A2(PI0)
#define LP_A0(n)	(n?HAL_GPIO_WritePin(GPIOG,GPIO_PIN_4,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOG,GPIO_PIN_4,GPIO_PIN_RESET))
#define LP_A1(n)	(n?HAL_GPIO_WritePin(GPIOG,GPIO_PIN_3,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOG,GPIO_PIN_3,GPIO_PIN_RESET))
#define LP_A2(n)	(n?HAL_GPIO_WritePin(GPIOG,GPIO_PIN_2,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOG,GPIO_PIN_2,GPIO_PIN_RESET))

#define HP_A0(n)	(n?HAL_GPIO_WritePin(GPIOI,GPIO_PIN_2,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOI,GPIO_PIN_2,GPIO_PIN_RESET))
#define HP_A1(n)	(n?HAL_GPIO_WritePin(GPIOI,GPIO_PIN_1,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOI,GPIO_PIN_1,GPIO_PIN_RESET))
#define HP_A2(n)	(n?HAL_GPIO_WritePin(GPIOI,GPIO_PIN_0,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOI,GPIO_PIN_0,GPIO_PIN_RESET))


void AFE_init(void);
uint8_t AFE_SetFilter_LP(uint16_t KHz);
uint8_t AFE_SetFilter_HP(uint16_t KHz);

void AFE_SetInnerPreampValid(void);
void AFE_SetExtPreampValid(void);
uint8_t AFE_IsUseInnerPreamp(void);

#endif

