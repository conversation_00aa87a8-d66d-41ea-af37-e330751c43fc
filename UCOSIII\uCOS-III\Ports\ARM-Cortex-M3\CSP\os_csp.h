/*
************************************************************************************************************************
*                                                      uC/OS-III
*                                                The Real-Time Kernel
*
*
*                                      (c) Copyright 2009, Micrium, Weston, FL
*                                                 All Rights Reserved
*
*                                            ARM Cortex M3 OS Chip Support
*                                                      Generic
*
* File      : OS_CPU.H
* Version   : V3.01.1
* By        : JJL
*             FT
* For       : ARMv7M Cortex-M3
* Mode      : Thumb2
************************************************************************************************************************
*/

#ifndef  OS_CSP_MODULE
#define  OS_CSP_MODULE


/*
************************************************************************************************************************
*                                                 INCLUDE HEADER FILES
************************************************************************************************************************
*/


/*
*********************************************************************************************************
*                                                       EXTERNS
*********************************************************************************************************
*/

#ifdef   OS_CSP_GLOBALS
#define  OS_CSP_EXT
#else
#define  OS_CSP_EXT  extern
#endif


/*
************************************************************************************************************************
*                                                CONFIGURATION DEFAULTS
************************************************************************************************************************
*/


/*
************************************************************************************************************************
*                                                  EXCEPTION DEFINES
************************************************************************************************************************
*/


/*
************************************************************************************************************************
*                                                        MACROS
************************************************************************************************************************
*/



/*
************************************************************************************************************************
*                                                   GLOBAL VARIABLES
************************************************************************************************************************
*/



/*
************************************************************************************************************************
*                                                      PROTOTYPES
*
* Note(s): (1) 'OS_CSP_TickInit()' provides the tick interrupt initialization.
*
*          (2) 'OS_CPU_IntHandler()' implements the global OS interrupt handler.
************************************************************************************************************************
*/

void  OS_CSP_TickInit    (void);

void  OS_CPU_IntHandler  (void);

#endif
