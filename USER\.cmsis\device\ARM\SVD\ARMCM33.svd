<?xml version="1.0" encoding="utf-8"?>

<!-- File naming: <vendor>_<part/series name>.svd -->

<!--
  Copyright (C) 2012 - 2018 Arm Limited. All rights reserved.

  Purpose: System Viewer Description (SVD) Example (Schema Version 1.1)
           This is a description of a none-existent and incomplete device
           for demonstration purposes only.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are met:
   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.
   - Neither the name of ARM nor the names of its contributors may be used
     to endorse or promote products derived from this software without
     specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE
  LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
  CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
  SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
  ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
  POSSIBILITY OF SUCH DAMAGE.
 -->

<device schemaVersion="1.3" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="CMSIS-SVD.xsd" >
  <vendor>ARM Ltd.</vendor>                                       <!-- device vendor name -->
  <vendorID>ARM</vendorID>                                        <!-- device vendor short name -->
  <name>ARMCM33</name>                                            <!-- name of part-->
  <series>ARMv8-M Mainline</series>                               <!-- device series the device belongs to -->
  <version>1.0</version>                                          <!-- version of this description, adding CMSIS-SVD 1.1 tags -->
  <description>ARM 32-bit Cortex-M33 based device<./description>
  <licenseText>                                                   <!-- this license text will appear in header file. \n force line breaks -->
    ARM Limited (ARM) is supplying this software for use with Cortex-M\n
    processor based microcontroller, but can be equally used for other\n
    suitable  processor architectures. This file can be freely distributed.\n
    Modifications to this file shall be clearly marked.\n
    \n
    THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED\n
    OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF\n
    MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.\n
    ARM SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL, OR\n
    CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
  </licenseText>

  <cpu>                                                           <!-- details about the cpu embedded in the device -->
    <name>CM33</name>
    <revision>r0p0</revision>
    <endian>little</endian>
    <mpuPresent>true</mpuPresent>
    <fpuPresent>false</fpuPresent>
    <vtorPresent>true</vtorPresent>
    <nvicPrioBits>3</nvicPrioBits>
    <vendorSystickConfig>false</vendorSystickConfig>
    <sauNumRegions>4</sauNumRegions>
    <sauRegionsConfig enabled="true" protectionWhenDisabled="s">
      <region enabled="true" name="SauRegion0">
        <base>0x00000000</base>
        <limit>0x001FFFE0</limit>
        <!-- secure / non-secure callable -->
        <access>c</access>
      </region>
      <region enabled="true" name="SauRegion1">
        <base>0x00200000</base>
        <limit>0x003FFFE0</limit>
        <!-- non-secure -->
        <access>n</access>
      </region>
      <region enabled="true" name="SauRegion2">
        <base>0x20200000</base>
        <limit>0x203FFFE0</limit>
        <!-- non-secure -->
        <access>n</access>
      </region>
      <region enabled="true" name="SauRegion3">
        <base>0x40000000</base>
        <limit>0x40040000</limit>
        <!-- non-secure -->
        <access>n</access>
      </region>
    </sauRegionsConfig>
  </cpu>

  <addressUnitBits>8</addressUnitBits>                            <!-- byte addressable memory -->
  <width>32</width>                                               <!-- bus width is 32 bits -->
  <!-- default settings implicitly inherited by subsequent sections -->
  <size>32</size>                                                 <!-- this is the default size (number of bits) of all peripherals
                                                                       and register that do not define "size" themselves -->
  <access>read-write</access>                                     <!-- default access permission for all subsequent registers -->
  <resetValue>0x00000000</resetValue>                             <!-- by default all bits of the registers are initialized to 0 on reset -->
  <resetMask>0xFFFFFFFF</resetMask>                               <!-- by default all 32Bits of the registers are used -->

  </device>
