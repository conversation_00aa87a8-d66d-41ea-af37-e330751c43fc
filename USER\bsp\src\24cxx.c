#include "includes.h"

//兼容 BL24C128A
//////////////////////////////////////////////////////////////////////////////////	 
static void AT_IIC_Init(void);
void AT24_test();
//uint8_t test_rd[10];
//uint8_t test_wr[10]={1,2,3,4,5,6,7,8,9,10};
//int32_t tst_4B=0;
//初始化IIC接口
void AT24CXX_Init(void)
{
	AT_IIC_Init();//IIC初始化
//	AT24_test();
}

//IIC初始化
// SDA(PE14), SCLK(PE13)
static void AT_IIC_Init(void)
{
	GPIO_InitTypeDef GPIO_Initure;
	__HAL_RCC_GPIOE_CLK_ENABLE();   //使能GPIOH时钟

	//PH4,5初始化设置
	GPIO_Initure.Pin = GPIO_PIN_2 | GPIO_PIN_3;
	GPIO_Initure.Mode = GPIO_MODE_OUTPUT_PP;  //推挽输出
	GPIO_Initure.Pull = GPIO_PULLUP;          //上拉
	GPIO_Initure.Speed = GPIO_SPEED_HIGH;     //快速
	HAL_GPIO_Init(GPIOE, &GPIO_Initure);

	AT_IIC_SDA(1);
	AT_IIC_SCL(1);
}

//产生IIC起始信号
static void AT_IIC_Start(void)
{
	AT_SDA_OUT();     //sda线输出
	AT_IIC_SDA(1);	  	  
	AT_IIC_SCL(1);
	delay_us(4);
 	AT_IIC_SDA(0);//START:when CLK is high,DATA change form high to low 
	delay_us(4);
	AT_IIC_SCL(0);//钳住I2C总线，准备发送或接收数据 
}	  
//产生IIC停止信号
static void AT_IIC_Stop(void)
{
	AT_SDA_OUT();//sda线输出
	AT_IIC_SCL(0);
	AT_IIC_SDA(0);//STOP:when CLK is high DATA change form low to high
 	delay_us(4);
	AT_IIC_SCL(1); 
	AT_IIC_SDA(1);//发送I2C总线结束信号
	delay_us(4);							   	
}
//等待应答信号到来
//返回值：1，接收应答失败
//        0，接收应答成功
static u8 AT_IIC_Wait_Ack(void)
{
	u8 ucErrTime=0;
	AT_SDA_IN();      //SDA设置为输入  
	AT_IIC_SDA(1);delay_us(1);	   
	AT_IIC_SCL(1);delay_us(1);	 
	while(AT_READ_SDA)
	{
		ucErrTime++;
		if(ucErrTime>250)
		{
			AT_IIC_Stop();
			return 1;
		}
	}
	AT_IIC_SCL(0);//时钟输出0 	   
	return 0;  
} 
//产生ACK应答
static void AT_IIC_Ack(void)
{
	AT_IIC_SCL(0);
	AT_SDA_OUT();
	AT_IIC_SDA(0);
	delay_us(2);
	AT_IIC_SCL(1);
	delay_us(2);
	AT_IIC_SCL(0);
}
//不产生ACK应答		    
static void AT_IIC_NAck(void)
{
	AT_IIC_SCL(0);
	AT_SDA_OUT();
	AT_IIC_SDA(1);
	delay_us(2);
	AT_IIC_SCL(1);
	delay_us(2);
	AT_IIC_SCL(0);
}					 				     
//IIC发送一个字节
//返回从机有无应答
//1，有应答
//0，无应答			  
static void AT_IIC_Send_Byte(u8 txd)
{                        
    u8 t;   
	AT_SDA_OUT(); 	    
    AT_IIC_SCL(0);//拉低时钟开始数据传输
    for(t=0;t<8;t++)
    {              
        AT_IIC_SDA((txd&0x80)>>7);
        txd<<=1; 	  
		delay_us(2);   //对TEA5767这三个延时都是必须的
		AT_IIC_SCL(1);
		delay_us(2); 
		AT_IIC_SCL(0);	
		delay_us(2);
    }	 
} 	    
//读1个字节，ack=1时，发送ACK，ack=0，发送nACK   
static u8 AT_IIC_Read_Byte(unsigned char ack)
{
	unsigned char i,receive=0;
	AT_SDA_IN();//SDA设置为输入
    for(i=0;i<8;i++ )
	{
        AT_IIC_SCL(0); 
        delay_us(2);
		AT_IIC_SCL(1);
        receive<<=1;
        if(AT_READ_SDA)receive++;   
		delay_us(1); 
    }					 
    if (!ack)
        AT_IIC_NAck();//发送nACK
    else
        AT_IIC_Ack(); //发送ACK   
    return receive;
}

//在AT24CXX指定地址读出一个数据
//ReadAddr:开始读数的地址  
//返回值  :读到的数据
u8 AT24CXX_ReadOneByte(u16 ReadAddr)
{
	u8 temp=0;

	#if SYSTEM_SUPPORT_OS	 	//使用OS
		CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
//		OSIntEnter();    
		OS_CRITICAL_ENTER(); 	//进入任务保护
	#endif
	AT_IIC_Start();  
	if(EE_TYPE>AT24C16)
	{
		AT_IIC_Send_Byte(0XA0);	   //发送写命令
		AT_IIC_Wait_Ack();
		AT_IIC_Send_Byte(ReadAddr>>8);//发送高地址	    
	}else AT_IIC_Send_Byte(0XA0+((ReadAddr/256)<<1));   //发送器件地址0XA0,写数据 	   
	AT_IIC_Wait_Ack(); 
	AT_IIC_Send_Byte(ReadAddr%256);   //发送低地址
	AT_IIC_Wait_Ack();	    
	AT_IIC_Start();  	 	   
	AT_IIC_Send_Byte(0XA1);           //进入接收模式			   
	AT_IIC_Wait_Ack();	 
	temp=AT_IIC_Read_Byte(0);		   
	AT_IIC_Stop();//产生一个停止条件	
	#if SYSTEM_SUPPORT_OS	 	//使用OS
//		OSIntExit();  	
		OS_CRITICAL_EXIT(); 	//退出任务保护		
	#endif
	
	return temp;
}
//在AT24CXX指定地址写入一个数据
//WriteAddr  :写入数据的目的地址    
//DataToWrite:要写入的数据
void AT24CXX_WriteOneByte(u16 WriteAddr,u8 DataToWrite)
{
	
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
//		OSIntEnter();    
		OS_CRITICAL_ENTER(); 	//进入任务保护
	#endif
	AT_IIC_Start();  
	if(EE_TYPE>AT24C16)
	{
		AT_IIC_Send_Byte(0XA0);	    //发送写命令
		AT_IIC_Wait_Ack();
		AT_IIC_Send_Byte(WriteAddr>>8);//发送高地址	  
	}else AT_IIC_Send_Byte(0XA0+((WriteAddr/256)<<1));   //发送器件地址0XA0,写数据 	 
	AT_IIC_Wait_Ack();	   
	AT_IIC_Send_Byte(WriteAddr%256);   //发送低地址
	AT_IIC_Wait_Ack(); 	 										  		   
	AT_IIC_Send_Byte(DataToWrite);     //发送字节							   
	AT_IIC_Wait_Ack();
	AT_IIC_Stop();//产生一个停止条件 
	#if SYSTEM_SUPPORT_OS	 	//使用OS
//		OSIntExit();  	
		OS_CRITICAL_EXIT(); 	//退出任务保护	
	#endif
	
	delay_ms(5);	 
}
//在AT24CXX里面的指定地址开始写入长度为Len的数据
//该函数用于写入16bit或者32bit的数据.
//WriteAddr  :开始写入的地址  
//DataToWrite:数据数组首地址
//Len        :要写入数据的长度2,4
void AT24CXX_WriteLenByte(u16 WriteAddr,u32 DataToWrite,u8 Len)
{
	u8 t;
	for(t=0;t<Len;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t,(DataToWrite>>(8*t))&0xff);
	}												    
}

//在AT24CXX里面的指定地址开始读出长度为Len的数据
//该函数用于读出16bit或者32bit的数据.
//ReadAddr   :开始读出的地址 
//返回值     :数据
//Len        :要读出数据的长度2,4
u32 AT24CXX_ReadLenByte(u16 ReadAddr,u8 Len)
{  	
	u8 t;
	u32 temp=0;
	for(t=0;t<Len;t++)
	{
		temp<<=8;
		temp+=AT24CXX_ReadOneByte(ReadAddr+Len-t-1); 	 				   
	}
	return temp;												    
}
//检查AT24CXX是否正常
//这里用了24XX的首个4Bytes地址(0)来存储标志字(0x12345678).
//返回1:检测失败
//返回0:检测成功
u8 AT24CXX_Check(void)
{
	u32 temp;
	temp=AT24CXX_Read4Bytes(0*4);//避免每次开机都写AT24CXX			   
	if(temp==0x12345678)return 0;		   
	else//排除第一次初始化的情况
	{
		AT24CXX_Write4Bytes(0*4, 0x12345678);
	    temp=AT24CXX_Read4Bytes(0*4);	  
		if(temp==0x12345678)return 0;
	}
	return 1;											  
}

//在AT24CXX里面的指定地址开始读出指定个数的数据
//ReadAddr :开始读出的地址 对24c02为0~255
//pBuffer  :数据数组首地址
//NumToRead:要读出数据的个数
void AT24CXX_Read(u16 ReadAddr,u8 *pBuffer,u16 NumToRead)
{
	while(NumToRead)
	{
		*pBuffer++=AT24CXX_ReadOneByte(ReadAddr++);	
		NumToRead--;
	}
}  
//在AT24CXX里面的指定地址开始写入指定个数的数据
//WriteAddr :开始写入的地址 对24c02为0~255
//pBuffer   :数据数组首地址
//NumToWrite:要写入数据的个数
void AT24CXX_Write(u16 WriteAddr,u8 *pBuffer,u16 NumToWrite)
{
	while(NumToWrite--)
	{
		AT24CXX_WriteOneByte(WriteAddr,*pBuffer);
		WriteAddr++;
		pBuffer++;
	}
}

void AT24CXX_Write4Bytes(u16 WriteAddr,int32_t DataToWrite)
{
	u8 t;
	for(t=0;t<4;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t,(DataToWrite>>(8*t))&0xff);
		
	}												    
}

int32_t AT24CXX_Read4Bytes(u16 ReadAddr)
{
	u8 t;
	int32_t temp=0;
	for(t=0;t<4;t++)
	{
		temp<<=8;
		temp+=AT24CXX_ReadOneByte(ReadAddr+4-t-1); 	 				   
	}
	return temp;												    
}
void AT24CXX_WriteFloat32(u16 WriteAddr,float DataToWrite)
{
	u8 t;
	uint8_t buf[4]; 
	float32_cvrt2chars((char*)buf, DataToWrite);
	for(t=0;t<4;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t, buf[t]);
	}												    
}
float AT24CXX_ReadFloat32(u16 ReadAddr)
{
	u8 t;
	uint8_t buf[4]; 
	float temp=0;
	for(t=0;t<4;t++)
	{
		buf[3-t] = AT24CXX_ReadOneByte(ReadAddr+4-t-1); 	 				   
	}
	temp = chars_cvrt2float32((char*)buf);
	return temp;												    
}
void AT24CXX_WriteFloat64(u16 WriteAddr, double DataToWrite)
{
	u8 t;
	uint8_t buf[8]; 
	float64_cvrt2chars((char*)buf, DataToWrite);
	for(t=0;t<8;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t, buf[t]);
	}												    
}
double AT24CXX_ReadFloat64(u16 ReadAddr)
{
	u8 t;
	uint8_t buf[8]; 
	double temp=0;
	for(t=0;t<8;t++)
	{
		buf[7-t] = AT24CXX_ReadOneByte(ReadAddr+8-t-1); 	 				   
	}
	temp = chars_cvrt2float64((char*)buf);
	return temp;												    
}

//应用部分
/***
// 32bits 每个参数, 地址加4
// 0~4:N/A; 5:上次声速; 6:亮度; 7:语言; 8:单位制; 9:校准试块的厚度(1000=10.00mm); 
// 10:用户存储声速1; 11:用户存储声速2; 12:用户存储声速3; 
	___________________________________________________________________________________________________________________________
	|0                       |2-4 |5       |6         |7       |8       |9           |10          |11              |12      |13  |14   |
	-------------------------------------------------------------------------------------------------------------------------
	|AT24Cxx标志(0x12345678) |SN  |DEV功能 |brightness|Language|Unit    |是否是简版  |启动零点,ns |参考信号初值,ns |探头类型|N/A |声速 |
	-------------------------------------------------------------------------------------------------------------------------
	___________________________________________________________________________________________________________________________
	|20 |21       |22           |18         |19          |20           |21         |22    |23              |
	-------------------------------------------------------------------------------------------------------------------------
	|   |emit_pul |拆机次数记录 |发射脉冲数 |发射频率,KHz|AScan-X单位  |BScan-X单位|检波  |编码器单脉冲长度|
	-------------------------------------------------------------------------------------------------------------------------
	_____________________________________________________________________________________________________________________________________________________________________________________________
	|24    |25        |26        |27    |28        |29          |30      |31          |32                |33                |34                |35          |36          |37           |38    |
	------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
	|Theme |AScan颜色 |BScan颜色 | 声音 |开发者使能|温度补偿开关|温度来源|固定温度数值|单波系数对应的温度|多波系数对应的温度|温度传感器误差补偿|双探头L1反转 |双探头L2翻转 |双探头S1翻转 |双探头S2翻转
	------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
     ______________________________________________________________________________________________________________________________________
	|39    |40          |41      |42         |43      |44       |45        |46         |47           |48        |49         |50
	--------------------------------------------------------------------------------------------------------------------------------------------
	|Usr7-v|菜单调试项目|硬件滤波|Emit-AB相位|Chirp_en|调试波形 |调试窗显示|刹车脉冲数 |纵波反转使能 |C翻转使能 |S1翻转使能 |S2翻转使能
	--------------------------------------------------------------------------------------------------------------------------------------------
	_________________________________________________________________________________________________________________
    |51        |52        |53-54   | 55-56   | 57            | 58              | 59
    -----------------------------------------------------------------------------------------------------------
    |LBrakefrq |TBrakefrq | 单波K1 |单波K2   | 单波目标轴力  |单波目标轴力使能 | 模子调制参数自动计算使能
    ----------------------------------------------------------------------------------------------------------
	 __________________________________________________________________________________________________________________
	|60(f32)      |61(f32)            |62(f32)    |63       |64                   |65       |66         |67         |68       |69 |70       |71
	------------------------------------------------------------------------------------------------------------------
	|初始纵横波比 |K_ratioLT_F校准K值 |夹持长度比 |存储异常 |双探头显示单探头波形 |拍照水印 |拉力机数值 |参考信号EN |快速模式 |   |刹车半波 |刹车延迟半周期
	------------------------------------------------------------------------------------------------------------------

	//500~599 系统数据区（Bootloader使用,App禁止修改!!!!）
	_________________________________________________________________________________________________________________
	| 500-509 | 510-511         | 512-519 | 520           | 525-528    | 530         |
	-----------------------------------------------------------------------------------------------------------
	| 保留    | 硬件代号(ASCII) | 保留    | 软件代号(int) | 软件版本名 | App有效标志 |
	----------------------------------------------------------------------------------------------------------
	
***/

//void SetDevSN(char *str) {
//	strcpy(g_Dev.sn, str);
//	AT24CXX_Write4Bytes(2*4, chars_cvrt2integer(g_Dev.sn, 4));
//	AT24CXX_Write4Bytes(3*4, chars_cvrt2integer(g_Dev.sn + 4, 4));
//	AT24CXX_Write4Bytes(4*4, chars_cvrt2integer(g_Dev.sn + 8, 4));
//}

uint32_t ATtestBuf[100];
float tstATf[4];
void AT24_test() {
	int32_t tmp, tmp2, i;
	
//	delay_ms(500);
//	for(i = 1; i <= 4; i++) {
//			AT24CXX_Write4Bytes ((i * 20 + 1) * 4, 	(i * 20 + 1)); //通道使能
//			AT24CXX_Write4Bytes ((i * 20 + 2) * 4, 	(i * 20 + 2)); //发射脉冲
//			AT24CXX_Write4Bytes ((i * 20 + 3) * 4, 	(i * 20 + 3)); //发射频率
//			AT24CXX_Write4Bytes ((i * 20 + 4) * 4, 	(i * 20 + 4)); //脉冲方向
//			AT24CXX_Write4Bytes ((i * 20 + 5) * 4, 	(i * 20 + 5)); //发射延迟
//			AT24CXX_Write4Bytes ((i * 20 + 6) * 4, 	(i * 20 + 6)); //平均
//			AT24CXX_Write4Bytes ((i * 20 + 7) * 4, 	(i * 20 + 7)); //采样频率
//			AT24CXX_Write4Bytes ((i * 20 + 8) * 4, 	(i * 20 + 8)); //采样开始时间
//			AT24CXX_Write4Bytes ((i * 20 + 9) * 4, 	(i * 20 + 9)); //采样时常
//		}
//	delay_ms(500);
//	for(i = 1; i <= 4; i++) {
//		ATtestBuf[i * 20 + 1] = AT24CXX_Read4Bytes ((i * 20 + 1) * 4);
//		ATtestBuf[i * 20 + 2] = AT24CXX_Read4Bytes ((i * 20 + 2) * 4);
//		ATtestBuf[i * 20 + 3] = AT24CXX_ReadFloat32((i * 20 + 3) * 4);
//		ATtestBuf[i * 20 + 4] = AT24CXX_Read4Bytes ((i * 20 + 4) * 4);
//		ATtestBuf[i * 20 + 5] = AT24CXX_Read4Bytes ((i * 20 + 5) * 4);
//		ATtestBuf[i * 20 + 6] = AT24CXX_Read4Bytes ((i * 20 + 6) * 4);
//		ATtestBuf[i * 20 + 7] = AT24CXX_Read4Bytes ((i * 20 + 7) * 4);
//		ATtestBuf[i * 20 + 8] = AT24CXX_Read4Bytes ((i * 20 + 8) * 4);
//		ATtestBuf[i * 20 + 9] = AT24CXX_Read4Bytes ((i * 20 + 9) * 4);
//	}

	delay_ms(500);
	for(i = 0; i < 100; i++) {
		AT24CXX_Write4Bytes (i * 4, 0xffffffff);
	}
	delay_ms(500);
	for(i = 0; i < 100; i++) {
		AT24CXX_Write4Bytes (i * 4, i + 50);
	}
	delay_ms(500);
	for(i = 0; i < 100; i++) {
		ATtestBuf[i] = AT24CXX_Read4Bytes (i * 4);
	}
	while(1){}
		
	tstATf[0] = AT24CXX_ReadFloat32((1 * 20 + 3) * 4);
	tstATf[1] = AT24CXX_ReadFloat32((2 * 20 + 3) * 4);
	tstATf[2] = AT24CXX_ReadFloat32((3 * 20 + 3) * 4);
	tstATf[3] = AT24CXX_ReadFloat32((4 * 20 + 3) * 4);
	delay_ms(500);
	
	EEPROM_LoadData();
	delay_ms(500);
	EEPROM_Para_Reset();
	delay_ms(500);
	EEPROM_LoadData();
	delay_ms(500);
}

//初始化读取存储数据
uint8_t EEPROM_LoadData(void)
{
	int32_t tmp, tmp2, i;
//	for(i = 0; i < 100; i++) {
//		ATtestBuf[i] = AT24CXX_Read4Bytes (i * 4);
//	}
	
	//读取modbus地址
//	if(Dev_SetModbusAddr		(AT24CXX_Read4Bytes (1 * 4))) 					{ Dev_SetFaultCode(FAULT_DEV); } //modbus地址
	
	//读取SN
	int32_cvrt2chars(g_Dev.sn, AT24CXX_Read4Bytes(2*4));
	int32_cvrt2chars(g_Dev.sn + 4, AT24CXX_Read4Bytes(3*4));
	int32_cvrt2chars(g_Dev.sn + 8, AT24CXX_Read4Bytes(4*4));
	g_Dev.sn[sizeof(g_Dev.sn) - 1] = 0x00;
	
//	if(Emat_SetEmitTrigMode		(AT24CXX_Read4Bytes (10 * 4))) 					{ Dev_SetFaultCode(FAULT_EMAT); } //触发模式
//	if(Emat_SetEmitRepeatTimes	(AT24CXX_Read4Bytes (11 * 4))) 					{ Dev_SetFaultCode(FAULT_EMAT); } //重复次数
//	if(Enc_SetTrigPulNum		(AT24CXX_Read4Bytes (12 * 4))) 					{ Dev_SetFaultCode(FAULT_ENC); }  //编码器触发脉冲数
//	if(Emat_SetSweepBgnFreq		(AT24CXX_Read4Bytes (13 * 4))) 					{ Dev_SetFaultCode(FAULT_EMAT); } //扫频起始频率, Hz
//	if(Emat_SetSweepEndFreq		(AT24CXX_Read4Bytes (14 * 4))) 					{ Dev_SetFaultCode(FAULT_EMAT); } //扫频终止频率, Hz
//	if(Emat_SetSweepStepFreq	(AT24CXX_Read4Bytes (15 * 4))) 					{ Dev_SetFaultCode(FAULT_EMAT); } //扫频步进频率, Hz
	
	//CH1~CH4, 偏移地址20
//	for(i = 1; i <= CHN_NUM; i++) {
//		if(Chn_SetChnEn			(i, AT24CXX_Read4Bytes ((i * 20 + 1) * 4))) 	{ Dev_SetFaultCode(FAULT_EMAT); } //通道使能
//		if(Emat_SetEmitPulNum	(i, AT24CXX_Read4Bytes ((i * 20 + 2) * 4))) 	{ Dev_SetFaultCode(FAULT_EMAT); } //发射脉冲
//		if(Emat_SetEmitFreq		(i, AT24CXX_ReadFloat32((i * 20 + 3) * 4))) 	{ Dev_SetFaultCode(FAULT_EMAT); } //发射频率
////		if(Emat_SetPulDir		(i, AT24CXX_Read4Bytes ((i * 20 + 4) * 4))) 	{ Dev_SetFaultCode(FAULT_EMAT); } //脉冲方向
//		if(Emat_SetEmitDly		(i, AT24CXX_Read4Bytes ((i * 20 + 5) * 4))) 	{ Dev_SetFaultCode(FAULT_EMAT); } //发射延迟
//		if(DAQ_SetAvg			(i, AT24CXX_Read4Bytes ((i * 20 + 6) * 4))) 	{ Dev_SetFaultCode(FAULT_DAQ); }  //平均
//		if(DAQ_SetFs			(i, AT24CXX_Read4Bytes ((i * 20 + 7) * 4))) 	{ Dev_SetFaultCode(FAULT_DAQ); }  //采样频率
//		if(DAQ_SetWaveBgnTime	(i, AT24CXX_Read4Bytes ((i * 20 + 8) * 4))) 	{ Dev_SetFaultCode(FAULT_DAQ); }  //采样开始时间
//		if(DAQ_SetWaveLenTime	(i, AT24CXX_Read4Bytes ((i * 20 + 9) * 4))) 	{ Dev_SetFaultCode(FAULT_DAQ); }  //采样时常
////		if(DAQ_SetGain			(i, AT24CXX_ReadFloat32((i * 20 + 10) * 4))) 	{ Dev_SetFaultCode(FAULT_DAQ); }  //增益
//	}

	return 0;
}
//保存存储参数, item 0:不保存, 1保存所有, 2保存菜单参数, 3保存用户声速
uint8_t EEPROM_SaveData(uint8_t item)
{
	int32_t i;
	
	if(item == 0) {
		return 1; //不保存
	}
//	#if SYSTEM_SUPPORT_OS	 	//使用OS
//		CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
//		OSIntEnter();    
//	#endif
//	CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
//	OS_CRITICAL_ENTER(); 	//进入任务保护
//	OS_CRITICAL_EXIT(); 	//退出任务保护
	if((item == 1) || (item == 2)) {
////		CHANNEL_T *chn[4];
////		chn[0] = &g_CHN1; chn[1] = &g_CHN2; chn[2] = &g_CHN3; chn[3] = &g_CHN4;
//		
//		AT24CXX_Write4Bytes 	(10 * 4, 			g_Dev.trig_mode				); //触发模式
//		AT24CXX_Write4Bytes 	(11 * 4, 			g_Dev.repeat_times			); //重复次数
//		AT24CXX_Write4Bytes 	(12 * 4, 			g_Dev.encTrigPul_num		); //编码器触发脉冲数
////		AT24CXX_Write4Bytes 	(13 * 4, 			g_Emat[0].sweepFreq_bgnFreq	); //扫频起始频率, Hz
////		AT24CXX_Write4Bytes 	(14 * 4, 			g_Emat[0].sweepFreq_endFreq	); //扫频终止频率, Hz
////		AT24CXX_Write4Bytes 	(15 * 4, 			g_Emat[0].sweepFreq_stepFreq); //扫频步进频率, Hz
//		
//		//21 ~ 99
//		for(i = 1; i <= CHN_NUM; i++) {
//			AT24CXX_Write4Bytes ((i * 20 + 1) * 4, 	g_CHN[i-1].enable			); //通道使能
//			AT24CXX_Write4Bytes ((i * 20 + 2) * 4, 	g_Emat[i-1].emitPulNum		); //发射脉冲
//			AT24CXX_WriteFloat32((i * 20 + 3) * 4, 	g_Emat[i-1].emitFreq_MHz	); //发射频率
//			AT24CXX_Write4Bytes ((i * 20 + 4) * 4, 	g_Emat[i-1].emitPulDir		); //脉冲方向
//			AT24CXX_Write4Bytes ((i * 20 + 5) * 4, 	g_Emat[i-1].emitDly_ns		); //发射延迟
//			AT24CXX_Write4Bytes ((i * 20 + 6) * 4, 	g_Daq[i-1].avg				); //平均
//			AT24CXX_Write4Bytes ((i * 20 + 7) * 4, 	g_Daq[i-1].fs_MHz			); //采样频率
//			AT24CXX_Write4Bytes ((i * 20 + 8) * 4, 	g_Daq[i-1].waveBgn_ns		); //采样开始时间
//			AT24CXX_Write4Bytes ((i * 20 + 9) * 4, 	g_Daq[i-1].waveLen_ns		); //采样时常
////			AT24CXX_WriteFloat32((i * 20 + 10) * 4, 	g_Daq[i-1].gainVal			); //增益
//		}
	}
	if(item == 3) {
		//存储SN码
		AT24CXX_Write4Bytes(2 * 4, chars_cvrt2integer(g_Dev.sn, 4));
		AT24CXX_Write4Bytes(3 * 4, chars_cvrt2integer(g_Dev.sn + 4, 4));
		AT24CXX_Write4Bytes(4 * 4, chars_cvrt2integer(g_Dev.sn + 8, 4));
	}
	if(item == 4) { //modbus地址
//		AT24CXX_Write4Bytes(1 * 4, g_Dev.modbus_addr); //modbus地址
	}
	if((item == 1) || (item == 5)) { //
//		//温度补偿相关参数存储
//		AT24CXX_Write4Bytes(29*4,  g_Temperature.tempComp_en);	//温度补偿开关	
//		AT24CXX_Write4Bytes(30*4,  g_Temperature.tempComp_src); //温度补偿来源
//		AT24CXX_WriteFloat32(31*4, g_Temperature.tempComp_temp);//温度补偿温度
//		
//		AT24CXX_WriteFloat32(33*4, g_MultWavePara.temperature); //多波系数对应的温度
//		AT24CXX_WriteFloat32(34*4, g_Temperature.sensor_errorComp); //温度传感器误差补偿
	}

//	#if SYSTEM_SUPPORT_OS	 	//使用OS
//		OSIntExit();  											 
//	#endif
//	OS_CRITICAL_EXIT(); 	//退出任务保护
//	AT24_test();
	return 0;
}

void EEPROM_Para_Reset(void) {
	int32_t i;
	
//	Emat_SetEmitTrigMode	(TRIG_MODE_INT	); //触发模式
//	Emat_SetEmitRepeatTimes	(5				); //重复次数
//	Enc_SetTrigPulNum		(100			); //编码器触发脉冲数
//	Emat_SetSweepBgnFreq	(3000000		); //扫频起始频率, Hz
//	Emat_SetSweepEndFreq	(5000000		); //扫频终止频率, Hz
//	Emat_SetSweepStepFreq	(100000			); //扫频步进频率, Hz
//	
//	//CH1~CH4, 偏移地址20
//	for(i = 1; i <= CHN_NUM; i++) {
//		Chn_SetChnEn		(i, 1			); //通道使能
//		Emat_SetEmitPulNum	(i, 3			); //发射脉冲
//		Emat_SetEmitFreq	(i, 4.0			); //发射频率
//		Emat_SetPulDir		(i, 1			); //脉冲方向
//		Emat_SetEmitDly		(i, 0			); //发射延迟
//		DAQ_SetAvg			(i, 1			); //平均
//		DAQ_SetFs			(i, 50			); //采样频率
//		DAQ_SetWaveBgnTime	(i, 0			); //采样开始时间
//		DAQ_SetWaveLenTime	(i, 10000		); //采样时常
////		DAQ_SetGain			(i, 50.0		); //增益
//	}
//	EEPROM_SaveData(1);
	
}



