#include "smbus_sw.h"
#include "delay.h"
#include <stdio.h>

//////////////////////////////////////////////////////////////////////////////////	 
// SMBus Software Implementation for TI Smart Lithium Battery Communication
////////////////////////////////////////////////////////////////////////////////// 

// PEC lookup table for CRC-8 calculation
static const uint8_t pec_table[256] = {
    0x00, 0x07, 0x0E, 0x09, 0x1C, 0x1B, 0x12, 0x15, 0x38, 0x3F, 0x36, 0x31, 0x24, 0x23, 0x2A, 0x2D,
    0x70, 0x77, 0x7E, 0x79, 0x6C, 0x6B, 0x62, 0x65, 0x48, 0x4F, 0x46, 0x41, 0x54, 0x53, 0x5A, 0x5D,
    0xE0, 0xE7, 0xEE, 0xE9, 0xFC, 0xFB, 0xF2, 0xF5, 0xD8, 0xDF, 0xD6, 0xD1, 0xC4, 0xC3, 0xCA, 0xCD,
    0x90, 0x97, 0x9E, 0x99, 0x8C, 0x8B, 0x82, 0x85, 0xA8, 0xAF, 0xA6, 0xA1, 0xB4, 0xB3, 0xBA, 0xBD,
    0xC7, 0xC0, 0xC9, 0xCE, 0xDB, 0xDC, 0xD5, 0xD2, 0xFF, 0xF8, 0xF1, 0xF6, 0xE3, 0xE4, 0xED, 0xEA,
    0xB7, 0xB0, 0xB9, 0xBE, 0xAB, 0xAC, 0xA5, 0xA2, 0x8F, 0x88, 0x81, 0x86, 0x93, 0x94, 0x9D, 0x9A,
    0x27, 0x20, 0x29, 0x2E, 0x3B, 0x3C, 0x35, 0x32, 0x1F, 0x18, 0x11, 0x16, 0x03, 0x04, 0x0D, 0x0A,
    0x57, 0x50, 0x59, 0x5E, 0x4B, 0x4C, 0x45, 0x42, 0x6F, 0x68, 0x61, 0x66, 0x73, 0x74, 0x7D, 0x7A,
    0x89, 0x8E, 0x87, 0x80, 0x95, 0x92, 0x9B, 0x9C, 0xB1, 0xB6, 0xBF, 0xB8, 0xAD, 0xAA, 0xA3, 0xA4,
    0xF9, 0xFE, 0xF7, 0xF0, 0xE5, 0xE2, 0xEB, 0xEC, 0xC1, 0xC6, 0xCF, 0xC8, 0xDD, 0xDA, 0xD3, 0xD4,
    0x69, 0x6E, 0x67, 0x60, 0x75, 0x72, 0x7B, 0x7C, 0x51, 0x56, 0x5F, 0x58, 0x4D, 0x4A, 0x43, 0x44,
    0x19, 0x1E, 0x17, 0x10, 0x05, 0x02, 0x0B, 0x0C, 0x21, 0x26, 0x2F, 0x28, 0x3D, 0x3A, 0x33, 0x34,
    0x4E, 0x49, 0x40, 0x47, 0x52, 0x55, 0x5C, 0x5B, 0x76, 0x71, 0x78, 0x7F, 0x6A, 0x6D, 0x64, 0x63,
    0x3E, 0x39, 0x30, 0x37, 0x22, 0x25, 0x2C, 0x2B, 0x06, 0x01, 0x08, 0x0F, 0x1A, 0x1D, 0x14, 0x13,
    0xAE, 0xA9, 0xA0, 0xA7, 0xB2, 0xB5, 0xBC, 0xBB, 0x96, 0x91, 0x98, 0x9F, 0x8A, 0x8D, 0x84, 0x83,
    0xDE, 0xD9, 0xD0, 0xD7, 0xC2, 0xC5, 0xCC, 0xCB, 0xE6, 0xE1, 0xE8, 0xEF, 0xFA, 0xFD, 0xF4, 0xF3
};

/**
 * @brief Initialize SMBus GPIO pins and configuration
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // Enable GPIO clock
    SMBUS_GPIO_CLK_ENABLE();
    
    // Configure SDA and SCL pins as open-drain outputs
    GPIO_InitStruct.Pin = SMBUS_SDA_PIN | SMBUS_SCL_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(SMBUS_GPIO_PORT, &GPIO_InitStruct);
    
    // Set both pins high (idle state)
    SMBus_SDA_High();
    SMBus_SCL_High();
    
    delay_us(SMBUS_DELAY_US * 10); // Allow bus to settle
    
    printf("SMBus initialized on PB14(SDA), PB15(SCL)\r\n");
    return SMBUS_OK;
}

/**
 * @brief Deinitialize SMBus GPIO pins
 */
void SMBus_DeInit(void)
{
    HAL_GPIO_DeInit(SMBUS_GPIO_PORT, SMBUS_SDA_PIN | SMBUS_SCL_PIN);
}

/**
 * @brief Set SDA line high
 */
void SMBus_SDA_High(void)
{
    HAL_GPIO_WritePin(SMBUS_GPIO_PORT, SMBUS_SDA_PIN, GPIO_PIN_SET);
}

/**
 * @brief Set SDA line low
 */
void SMBus_SDA_Low(void)
{
    HAL_GPIO_WritePin(SMBUS_GPIO_PORT, SMBUS_SDA_PIN, GPIO_PIN_RESET);
}

/**
 * @brief Set SCL line high
 */
void SMBus_SCL_High(void)
{
    HAL_GPIO_WritePin(SMBUS_GPIO_PORT, SMBUS_SCL_PIN, GPIO_PIN_SET);
}

/**
 * @brief Set SCL line low
 */
void SMBus_SCL_Low(void)
{
    HAL_GPIO_WritePin(SMBUS_GPIO_PORT, SMBUS_SCL_PIN, GPIO_PIN_RESET);
}

/**
 * @brief Read SDA line state
 * @return 1 if high, 0 if low
 */
uint8_t SMBus_SDA_Read(void)
{
    return HAL_GPIO_ReadPin(SMBUS_GPIO_PORT, SMBUS_SDA_PIN) == GPIO_PIN_SET ? 1 : 0;
}

/**
 * @brief Read SCL line state
 * @return 1 if high, 0 if low
 */
uint8_t SMBus_SCL_Read(void)
{
    return HAL_GPIO_ReadPin(SMBUS_GPIO_PORT, SMBUS_SCL_PIN) == GPIO_PIN_SET ? 1 : 0;
}

/**
 * @brief Generate SMBus start condition
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_Start(void)
{
    SMBus_SDA_High();
    SMBus_SCL_High();
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SDA_Low();
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SCL_Low();
    delay_us(SMBUS_DELAY_US);
    
    return SMBUS_OK;
}

/**
 * @brief Generate SMBus stop condition
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_Stop(void)
{
    SMBus_SDA_Low();
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SCL_High();
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SDA_High();
    delay_us(SMBUS_DELAY_US);
    
    return SMBUS_OK;
}

/**
 * @brief Send a byte over SMBus
 * @param data Byte to send
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_SendByte(uint8_t data)
{
    for (int i = 7; i >= 0; i--) {
        if (data & (1 << i)) {
            SMBus_SDA_High();
        } else {
            SMBus_SDA_Low();
        }
        delay_us(SMBUS_DELAY_US);
        
        SMBus_SCL_High();
        delay_us(SMBUS_DELAY_US);
        
        SMBus_SCL_Low();
        delay_us(SMBUS_DELAY_US);
    }
    
    return SMBUS_OK;
}

/**
 * @brief Receive a byte over SMBus
 * @param data Pointer to store received byte
 * @param ack 1 to send ACK, 0 to send NACK
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_ReceiveByte(uint8_t *data, uint8_t ack)
{
    uint8_t byte = 0;
    
    SMBus_SDA_High(); // Release SDA for input
    
    for (int i = 7; i >= 0; i--) {
        SMBus_SCL_High();
        delay_us(SMBUS_DELAY_US);
        
        if (SMBus_SDA_Read()) {
            byte |= (1 << i);
        }
        
        SMBus_SCL_Low();
        delay_us(SMBUS_DELAY_US);
    }
    
    // Send ACK/NACK
    if (ack) {
        SMBus_SDA_Low();
    } else {
        SMBus_SDA_High();
    }
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SCL_High();
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SCL_Low();
    delay_us(SMBUS_DELAY_US);
    
    *data = byte;
    return SMBUS_OK;
}

/**
 * @brief Wait for ACK from slave device
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_WaitAck(void)
{
    uint32_t timeout = SMBUS_TIMEOUT_US / SMBUS_DELAY_US;
    
    SMBus_SDA_High(); // Release SDA
    delay_us(SMBUS_DELAY_US);
    
    SMBus_SCL_High();
    delay_us(SMBUS_DELAY_US);
    
    while (SMBus_SDA_Read() && timeout--) {
        delay_us(SMBUS_DELAY_US);
    }
    
    SMBus_SCL_Low();
    delay_us(SMBUS_DELAY_US);
    
    return timeout > 0 ? SMBUS_OK : SMBUS_ERROR_NACK;
}

/**
 * @brief Calculate PEC (Packet Error Check) using CRC-8
 * @param data Pointer to data array
 * @param length Length of data
 * @return Calculated PEC value
 */
uint8_t SMBus_CalculatePEC(uint8_t *data, uint8_t length)
{
    uint8_t pec = 0;
    
    for (uint8_t i = 0; i < length; i++) {
        pec = pec_table[pec ^ data[i]];
    }
    
    return pec;
}

/**
 * @brief Verify PEC value
 * @param data Pointer to data array
 * @param length Length of data
 * @param received_pec Received PEC value
 * @return 1 if PEC is valid, 0 if invalid
 */
uint8_t SMBus_VerifyPEC(uint8_t *data, uint8_t length, uint8_t received_pec)
{
    uint8_t calculated_pec = SMBus_CalculatePEC(data, length);
    return (calculated_pec == received_pec) ? 1 : 0;
}

/**
 * @brief Read word (16-bit) from SMBus device with PEC
 * @param device_addr Device address (7-bit)
 * @param command Command/register address
 * @param data Pointer to store read data
 * @return SMBus_Status_t status
 */
SMBus_Status_t SMBus_ReadWithPEC(uint8_t device_addr, uint8_t command, uint16_t *data)
{
    SMBus_Status_t status;
    uint8_t pec_data[5];
    uint8_t low_byte, high_byte, pec_received;
    uint8_t retry_count = 0;
    
    while (retry_count < SMBUS_MAX_RETRIES) {
        // Start condition
        status = SMBus_Start();
        if (status != SMBUS_OK) {
            retry_count++;
            continue;
        }
        
        // Send device address with write bit
        status = SMBus_SendByte((device_addr << 1) | SMBUS_WRITE);
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Wait for ACK
        status = SMBus_WaitAck();
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Send command
        status = SMBus_SendByte(command);
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Wait for ACK
        status = SMBus_WaitAck();
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Repeated start
        status = SMBus_Start();
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Send device address with read bit
        status = SMBus_SendByte((device_addr << 1) | SMBUS_READ);
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Wait for ACK
        status = SMBus_WaitAck();
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Read low byte with ACK
        status = SMBus_ReceiveByte(&low_byte, 1);
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Read high byte with ACK
        status = SMBus_ReceiveByte(&high_byte, 1);
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Read PEC with NACK
        status = SMBus_ReceiveByte(&pec_received, 0);
        if (status != SMBUS_OK) {
            SMBus_Stop();
            retry_count++;
            continue;
        }
        
        // Stop condition
        SMBus_Stop();
        
        // Verify PEC
        pec_data[0] = (device_addr << 1) | SMBUS_WRITE;
        pec_data[1] = command;
        pec_data[2] = (device_addr << 1) | SMBUS_READ;
        pec_data[3] = low_byte;
        pec_data[4] = high_byte;
        
        if (SMBus_VerifyPEC(pec_data, 5, pec_received)) {
            // PEC is valid, combine bytes (little-endian)
            *data = (uint16_t)low_byte | ((uint16_t)high_byte << 8);
            return SMBUS_OK;
        } else {
            retry_count++;
        }
    }
    
    return SMBUS_ERROR_PEC;
}

/**
 * @brief Reset SMBus by toggling SCL
 */
void SMBus_Reset(void)
{
    // Generate 9 clock pulses to reset any stuck devices
    SMBus_SDA_High();
    for (int i = 0; i < 9; i++) {
        SMBus_SCL_Low();
        delay_us(SMBUS_DELAY_US);
        SMBus_SCL_High();
        delay_us(SMBUS_DELAY_US);
    }
    
    // Generate stop condition
    SMBus_Stop();
}

/**
 * @brief Get error string for status code
 * @param status SMBus status code
 * @return Error string
 */
const char* SMBus_GetErrorString(SMBus_Status_t status)
{
    switch (status) {
        case SMBUS_OK: return "OK";
        case SMBUS_ERROR_TIMEOUT: return "Timeout";
        case SMBUS_ERROR_NACK: return "NACK";
        case SMBUS_ERROR_PEC: return "PEC Error";
        case SMBUS_ERROR_INVALID_PARAM: return "Invalid Parameter";
        case SMBUS_ERROR_BUS_BUSY: return "Bus Busy";
        default: return "Unknown Error";
    }
}
