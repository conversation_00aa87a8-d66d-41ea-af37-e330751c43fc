#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m35p -xc -mcmse
; command above MUST be in first line (no comment above!)

;Note: Add '-mcmse' to first line if your software model is "Secure Mode".
;      #! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m35p -xc -mcmse


/*
;-------- <<< Use Configuration Wizard in Context Menu >>> -------------------
*/

/*--------------------- Flash Configuration ----------------------------------
; <h> Flash Configuration
;   <o0> Flash Base Address <0x0-0xFFFFFFFF:8>
;   <o1> Flash Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>
 *----------------------------------------------------------------------------*/
#define __ROM_BASE      0x00000000
#define __ROM_SIZE      0x00080000

/*--------------------- Embedded RAM Configuration ---------------------------
; <h> RAM Configuration
;   <o0> RAM Base Address    <0x0-0xFFFFFFFF:8>
;   <o1> RAM Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>
 *----------------------------------------------------------------------------*/
#define __RAM_BASE      0x20000000
#define __RAM_SIZE      0x00040000

/*--------------------- Stack / Heap Configuration ---------------------------
; <h> Stack / Heap Configuration
;   <o0> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
;   <o1> Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
; </h>
 *----------------------------------------------------------------------------*/
#define __STACK_SIZE    0x00000200
#define __HEAP_SIZE     0x00000C00

/*--------------------- CMSE Veneer Configuration ---------------------------
; <h> CMSE Veneer Configuration
;   <o0>  CMSE Veneer Size (in Bytes) <0x0-0xFFFFFFFF:32>
; </h>
 *----------------------------------------------------------------------------*/
#define __CMSEVENEER_SIZE    0x200

/*
;------------- <<< end of configuration section >>> ---------------------------
*/


/*----------------------------------------------------------------------------
  User Stack & Heap boundary definition
 *----------------------------------------------------------------------------*/
#define __STACK_TOP    (__RAM_BASE + __RAM_SIZE - __STACKSEAL_SIZE) /* starts at end of RAM - 8 byte stack seal */
#define __HEAP_BASE    (AlignExpr(+0, 8))                           /* starts after RW_RAM section, 8 byte aligned */

/* ----------------------------------------------------------------------------
  Stack seal size definition
 *----------------------------------------------------------------------------*/
#if defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3U)
#define __STACKSEAL_SIZE   ( 8 )
#else
#define __STACKSEAL_SIZE   ( 0 )
#endif


/*----------------------------------------------------------------------------
  Region base & size definition
 *----------------------------------------------------------------------------*/
#if defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3U)
#define __CV_BASE          ( __ROM_BASE + __ROM_SIZE - __CMSEVENEER_SIZE )
#define __CV_SIZE          ( __CMSEVENEER_SIZE )
#else
#define __CV_SIZE          ( 0 )
#endif

#define __RO_BASE          ( __ROM_BASE )
#define __RO_SIZE          ( __ROM_SIZE - __CV_SIZE )

#define __RW_BASE          ( __RAM_BASE )
#define __RW_SIZE          ( __RAM_SIZE - __STACK_SIZE - __HEAP_SIZE )


/*----------------------------------------------------------------------------
  Scatter Region definition
 *----------------------------------------------------------------------------*/
LR_ROM __RO_BASE __RO_SIZE  {                       ; load region size_region
  ER_ROM __RO_BASE __RO_SIZE  {                     ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   .ANY (+XO)
  }

  RW_RAM __RW_BASE __RW_SIZE  {                     ; RW data
   .ANY (+RW +ZI)
  }

#if __HEAP_SIZE > 0
  ARM_LIB_HEAP  __HEAP_BASE EMPTY  __HEAP_SIZE  {   ; Reserve empty region for heap
  }
#endif

  ARM_LIB_STACK __STACK_TOP EMPTY -__STACK_SIZE {   ; Reserve empty region for stack
  }

#if defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3U)
  STACKSEAL +0 EMPTY __STACKSEAL_SIZE {             ; Reserve empty region for stack seal immediately after stack
  }
#endif
}

#if defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3U)
LR_CMSE_VENEER __CV_BASE ALIGN 32 __CV_SIZE  {      ; own load/execution region for CMSE Veneers
  ER_CMSE_VENEER __CV_BASE __CV_SIZE  {
   *(Veneer$$CMSE)
  }
}
#endif
