/**********************************************************************************************************
* @file    ad9704.c
* @project 
* <AUTHOR>
* @date    2022/08/16
* @version v1.0
* @brief   DAC IC, 用于调节峰值保持输出电压
* @modify
*
* Copyright (C), 2018-2022, 苏州博昇科技有限公司, www.phaserise.com
*********************************************************************************************************/
#include "ad9704.h"

#define DAC_CSB(n)			(n ? HAL_GPIO_WritePin(GPIOF,GPIO_PIN_7,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOF,GPIO_PIN_7,GPIO_PIN_RESET))
#define DAC_MDE_SDIO(n)		(n ? HAL_GPIO_WritePin(GPIOF,GPIO_PIN_8,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOF,GPIO_PIN_8,GPIO_PIN_RESET))
#define DAC_CMD_SCLK(n)		(n ? HAL_GPIO_WritePin(GPIOF,GPIO_PIN_9,GPIO_PIN_SET) : HAL_GPIO_WritePin(GPIOF,GPIO_PIN_9,GPIO_PIN_RESET))

// CSB-DA(F7), MDE/SDIO(F8), CMD/SCLK(F9)
void AD9704_Init(void) {
	GPIO_InitTypeDef GPIO_InitStruct;
	__HAL_RCC_GPIOF_CLK_ENABLE();

	GPIO_InitStruct.Pin = GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_HIGH;
	HAL_GPIO_Init(GPIOF,&GPIO_InitStruct);

	DAC_CSB(0); //1:power down
	DAC_MDE_SDIO(0); //0:直接二进制; 1:二进制补码
	DAC_CMD_SCLK(1); //0:单端时钟; 1:差分时钟
}

