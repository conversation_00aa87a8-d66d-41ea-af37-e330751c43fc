#ifndef __SMBUS_SW_H
#define __SMBUS_SW_H

#include "sys.h"
#include "stm32f7xx_hal.h"

//////////////////////////////////////////////////////////////////////////////////	 
// SMBus Software Implementation for TI Smart Lithium Battery Communication
// Hardware: STM32F7, GPIOB pins PB14=SDA, PB15=SCL
// Protocol: SMBus with PEC (Packet Error Checking)
////////////////////////////////////////////////////////////////////////////////// 

// SMBus GPIO pin definitions
#define SMBUS_SDA_PIN           GPIO_PIN_14
#define SMBUS_SCL_PIN           GPIO_PIN_15
#define SMBUS_GPIO_PORT         GPIOB
#define SMBUS_GPIO_CLK_ENABLE() __HAL_RCC_GPIOB_CLK_ENABLE()

// SMBus timing parameters (microseconds)
#define SMBUS_DELAY_US          5       // Standard SMBus timing (100kHz)
#define SMBUS_TIMEOUT_US        10000   // 10ms timeout
#define SMBUS_MAX_RETRIES       3       // Maximum retry attempts

// SMBus protocol definitions
#define SMBUS_READ              0x01
#define SMBUS_WRITE             0x00
#define SMBUS_ACK               0
#define SMBUS_NACK              1

// TI Smart Battery SMBus Commands
#define SMBUS_VOLTAGE_CMD       0x09    // Battery voltage (mV)
#define SMBUS_CURRENT_CMD       0x0A    // Battery current (mA)
#define SMBUS_TEMPERATURE_CMD   0x08    // Battery temperature (0.1K)
#define SMBUS_RELATIVE_SOC_CMD  0x0D    // Relative state of charge (%)
#define SMBUS_ABSOLUTE_SOC_CMD  0x0E    // Absolute state of charge (%)
#define SMBUS_REMAINING_CAP_CMD 0x0F    // Remaining capacity (mAh)
#define SMBUS_FULL_CAP_CMD      0x10    // Full charge capacity (mAh)
#define SMBUS_CYCLE_COUNT_CMD   0x17    // Cycle count
#define SMBUS_DESIGN_CAP_CMD    0x18    // Design capacity (mAh)
#define SMBUS_DESIGN_VOLT_CMD   0x19    // Design voltage (mV)
#define SMBUS_BATTERY_STATUS_CMD 0x16   // Battery status
#define SMBUS_SAFETY_STATUS_CMD 0x51    // Safety status (if supported)

// Battery default I2C address (7-bit)
#define TI_BATTERY_ADDR         0x0B    // Default TI smart battery address

// SMBus error codes
typedef enum {
    SMBUS_OK = 0,
    SMBUS_ERROR_TIMEOUT,
    SMBUS_ERROR_NACK,
    SMBUS_ERROR_PEC,
    SMBUS_ERROR_INVALID_PARAM,
    SMBUS_ERROR_BUS_BUSY
} SMBus_Status_t;

// SMBus data structure for read operations
typedef struct {
    uint8_t address;        // Device address
    uint8_t command;        // Command/register
    uint16_t data;          // Data read from device
    uint8_t pec;            // Packet Error Check byte
    SMBus_Status_t status;  // Operation status
} SMBus_Transaction_t;

// Function declarations

// Initialization and configuration
SMBus_Status_t SMBus_Init(void);
void SMBus_DeInit(void);

// Low-level GPIO control
void SMBus_SDA_High(void);
void SMBus_SDA_Low(void);
void SMBus_SCL_High(void);
void SMBus_SCL_Low(void);
uint8_t SMBus_SDA_Read(void);
uint8_t SMBus_SCL_Read(void);
void SMBus_SDA_Output(void);
void SMBus_SDA_Input(void);

// SMBus protocol functions
SMBus_Status_t SMBus_Start(void);
SMBus_Status_t SMBus_Stop(void);
SMBus_Status_t SMBus_SendByte(uint8_t data);
SMBus_Status_t SMBus_ReceiveByte(uint8_t *data, uint8_t ack);
SMBus_Status_t SMBus_WaitAck(void);
void SMBus_SendAck(void);
void SMBus_SendNack(void);

// High-level SMBus operations
SMBus_Status_t SMBus_ReadWord(uint8_t device_addr, uint8_t command, uint16_t *data);
SMBus_Status_t SMBus_WriteWord(uint8_t device_addr, uint8_t command, uint16_t data);
SMBus_Status_t SMBus_ReadWithPEC(uint8_t device_addr, uint8_t command, uint16_t *data);
SMBus_Status_t SMBus_WriteWithPEC(uint8_t device_addr, uint8_t command, uint16_t data);

// PEC (Packet Error Checking) functions
uint8_t SMBus_CalculatePEC(uint8_t *data, uint8_t length);
uint8_t SMBus_VerifyPEC(uint8_t *data, uint8_t length, uint8_t received_pec);

// Utility functions
SMBus_Status_t SMBus_ScanDevice(uint8_t device_addr);
void SMBus_Reset(void);
const char* SMBus_GetErrorString(SMBus_Status_t status);

// Delay function (requires TIM10 configuration)
void delay_us(uint32_t us);

// Debug functions (optional)
#ifdef SMBUS_DEBUG
void SMBus_PrintTransaction(SMBus_Transaction_t *transaction);
void SMBus_PrintStatus(SMBus_Status_t status);
#endif

#endif /* __SMBUS_SW_H */
