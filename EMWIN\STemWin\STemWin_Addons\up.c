/*********************************************************************
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                           www.segger.com                           *
**********************************************************************
*                                                                    *
* C-file generated by                                                *
*                                                                    *
*        Bitmap converter for emWin V5.18.                           *
*        Compiled Sep 24 2012, 15:52:34                              *
*        (C) 1998 - 2012 Segger Microcontroller GmbH & Co. KG        *
*                                                                    *
**********************************************************************
*                                                                    *
* Source file: up                                                    *
* Dimensions:  36 * 36                                               *
* NumColors:   32bpp: 16777216 + 256                                 *
*                                                                    *
**********************************************************************
*/

#include <stdlib.h>

#include "GUI.h"

#ifndef GUI_CONST_STORAGE
  #define GUI_CONST_STORAGE const
#endif

extern GUI_CONST_STORAGE GUI_BITMAP bmup;

static GUI_CONST_STORAGE unsigned char _acup[] = {
  /* RLE: 053 Pixels @ 000,000 */ 53, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 002 Pixels @ 017,001 */ 0, 2, 0x02, 0x02, 0x03, 0xFB, 0x01, 0x02, 0x02, 0xFC, 
  /* RLE: 033 Pixels @ 019,001 */ 33, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 004 Pixels @ 016,002 */ 0, 4, 0x02, 0x03, 0x04, 0xF8, 0x34, 0x73, 0x95, 0x58, 0x33, 0x6E, 0x8D, 0x62, 0x02, 0x02, 0x03, 0xFA, 
  /* RLE: 031 Pixels @ 020,002 */ 31, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 006 Pixels @ 015,003 */ 0, 6, 0x02, 0x04, 0x05, 0xF7, 0x34, 0x73, 0x96, 0x55, 0x43, 0xAB, 0xE4, 0x00, 0x44, 0xAD, 0xE5, 0x00, 0x35, 0x70, 0x90, 0x5F, 0x02, 0x03, 0x03, 0xFA, 
  /* RLE: 029 Pixels @ 021,003 */ 29, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 008 Pixels @ 014,004 */ 0, 8, 0x03, 0x04, 0x05, 0xF7, 0x36, 0x75, 0x96, 0x53, 0x43, 0xAA, 0xE1, 0x00, 0x42, 0xAA, 0xE2, 0x00, 0x42, 0xAB, 0xE3, 0x00, 0x46, 0xAD, 0xE5, 0x00, 0x37, 0x73, 0x93, 0x5C, 0x02, 0x03, 0x04, 0xF9, 
  /* RLE: 027 Pixels @ 022,004 */ 27, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,005 */ 0, 10, 0x03, 0x05, 0x06, 0xF6, 0x39, 0x77, 0x99, 0x50, 0x43, 0xA8, 0xDF, 0x00, 0x42, 0xA8, 0xDF, 0x00, 0x43, 0xA9, 0xE1, 0x00, 0x43, 0xAA, 0xE1, 0x00, 0x44, 0xAC, 0xE3, 0x00, 0x48, 0xAE, 0xE5, 0x00, 
        0x38, 0x74, 0x94, 0x59, 0x02, 0x03, 0x04, 0xF8, 
  /* RLE: 025 Pixels @ 023,005 */ 25, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 012 Pixels @ 012,006 */ 0, 12, 0x04, 0x06, 0x07, 0xF4, 0x41, 0x7E, 0x9F, 0x4B, 0x49, 0xAB, 0xE0, 0x00, 0x44, 0xA9, 0xDF, 0x00, 0x42, 0xA8, 0xDE, 0x00, 0x43, 0xA9, 0xDF, 0x00, 0x44, 0xAA, 0xE1, 0x00, 0x45, 0xAB, 0xE2, 0x00, 
        0x46, 0xAC, 0xE3, 0x00, 0x49, 0xAE, 0xE5, 0x00, 0x3B, 0x77, 0x97, 0x55, 0x03, 0x04, 0x05, 0xF7, 
  /* RLE: 023 Pixels @ 024,006 */ 23, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 014 Pixels @ 011,007 */ 0, 14, 0x04, 0x07, 0x08, 0xF3, 0x42, 0x7F, 0xA1, 0x47, 0x4D, 0xAE, 0xE2, 0x00, 0x4C, 0xAE, 0xE2, 0x00, 0x4E, 0xB0, 0xE4, 0x00, 0x4E, 0xB0, 0xE4, 0x00, 0x48, 0xAC, 0xE1, 0x00, 0x45, 0xAA, 0xE0, 0x00, 
        0x46, 0xAB, 0xE1, 0x00, 0x47, 0xAC, 0xE2, 0x00, 0x47, 0xAD, 0xE3, 0x00, 0x4B, 0xAF, 0xE5, 0x00, 0x40, 0x7C, 0x9D, 0x51, 0x04, 0x05, 0x06, 0xF6, 
  /* RLE: 021 Pixels @ 025,007 */ 21, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 016 Pixels @ 010,008 */ 0, 16, 0x05, 0x08, 0x09, 0xF2, 0x44, 0x83, 0xA4, 0x43, 0x4C, 0xAB, 0xDF, 0x00, 0x4B, 0xAC, 0xE1, 0x00, 0x4C, 0xAD, 0xE2, 0x00, 0x4E, 0xAF, 0xE3, 0x00, 0x50, 0xB1, 0xE5, 0x00, 0x51, 0xB3, 0xE6, 0x00, 
        0x4F, 0xB0, 0xE4, 0x00, 0x48, 0xAB, 0xE0, 0x00, 0x48, 0xAB, 0xE1, 0x00, 0x48, 0xAC, 0xE2, 0x00, 0x49, 0xAD, 0xE4, 0x00, 0x4C, 0xB0, 0xE5, 0x00, 0x42, 0x80, 0xA1, 0x4C, 0x04, 0x05, 0x06, 0xF6, 
  /* RLE: 019 Pixels @ 026,008 */ 19, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 018 Pixels @ 009,009 */ 0, 18, 0x06, 0x09, 0x0B, 0xF0, 0x45, 0x85, 0xA8, 0x3F, 0x4A, 0xA9, 0xDC, 0x00, 0x49, 0xA9, 0xDD, 0x00, 0x4B, 0xAA, 0xDE, 0x00, 0x4D, 0xAC, 0xE1, 0x00, 0x4E, 0xAE, 0xE2, 0x00, 0x50, 0xAF, 0xE2, 0x00, 
        0x51, 0xB1, 0xE5, 0x00, 0x52, 0xB3, 0xE6, 0x00, 0x52, 0xB2, 0xE5, 0x00, 0x4A, 0xAC, 0xE1, 0x00, 0x4A, 0xAC, 0xE1, 0x00, 0x4A, 0xAD, 0xE2, 0x00, 0x4A, 0xAE, 0xE4, 0x00, 0x4D, 0xB0, 0xE5, 0x00, 0x43, 0x83, 0xA4, 0x48, 0x04, 0x06, 0x07, 0xF4, 
  /* RLE: 017 Pixels @ 027,009 */ 17, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 020 Pixels @ 008,010 */ 0, 20, 0x06, 0x09, 0x0B, 0xEE, 0x45, 0x85, 0xA8, 0x3B, 0x49, 0xA6, 0xD9, 0x00, 0x48, 0xA6, 0xDA, 0x00, 0x4A, 0xA8, 0xDC, 0x00, 0x4C, 0xAA, 0xDD, 0x00, 0x4D, 0xAB, 0xDE, 0x00, 0x4F, 0xAD, 0xE1, 0x00, 
        0x51, 0xAF, 0xE2, 0x00, 0x52, 0xB0, 0xE3, 0x00, 0x53, 0xB1, 0xE4, 0x00, 0x54, 0xB3, 0xE6, 0x00, 0x53, 0xB2, 0xE5, 0x00, 0x4B, 0xAC, 0xE1, 0x00, 0x4B, 0xAD, 0xE2, 0x00, 0x4C, 0xAE, 0xE3, 0x00, 0x4C, 0xAF, 0xE4, 0x00, 0x4E, 0xB1, 0xE5, 0x00, 
        0x47, 0x87, 0xA9, 0x44, 0x05, 0x08, 0x09, 0xF2, 
  /* RLE: 015 Pixels @ 028,010 */ 15, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 022 Pixels @ 007,011 */ 0, 22, 0x07, 0x0B, 0x0D, 0xEC, 0x44, 0x84, 0xA7, 0x38, 0x46, 0xA3, 0xD6, 0x00, 0x46, 0xA3, 0xD7, 0x00, 0x48, 0xA6, 0xD8, 0x00, 0x4A, 0xA7, 0xDA, 0x00, 0x4C, 0xA9, 0xDC, 0x00, 0x4E, 0xAB, 0xDD, 0x00, 
        0x4F, 0xAC, 0xDF, 0x00, 0x52, 0xAE, 0xE1, 0x00, 0x56, 0xB2, 0xE4, 0x00, 0x58, 0xB4, 0xE6, 0x00, 0x5A, 0xB7, 0xE9, 0x00, 0x5B, 0xB8, 0xEA, 0x00, 0x55, 0xB3, 0xE5, 0x00, 0x4F, 0xAF, 0xE2, 0x00, 0x4D, 0xAE, 0xE3, 0x00, 0x4C, 0xAE, 0xE3, 0x00, 
        0x4D, 0xAF, 0xE4, 0x00, 0x50, 0xB1, 0xE5, 0x00, 0x4A, 0x8A, 0xAE, 0x40, 0x06, 0x09, 0x0B, 0xF0, 
  /* RLE: 013 Pixels @ 029,011 */ 13, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 024 Pixels @ 006,012 */ 0, 24, 0x09, 0x0D, 0x0F, 0xEA, 0x45, 0x85, 0xA9, 0x35, 0x44, 0xA0, 0xD4, 0x00, 0x44, 0xA1, 0xD4, 0x00, 0x47, 0xA3, 0xD6, 0x00, 0x48, 0xA4, 0xD8, 0x00, 0x4B, 0xA6, 0xD9, 0x00, 0x4C, 0xA8, 0xDB, 0x00, 
        0x51, 0xAC, 0xDE, 0x00, 0x57, 0xB2, 0xE3, 0x00, 0x5B, 0xB6, 0xE6, 0x00, 0x5C, 0xB6, 0xE8, 0x00, 0x5C, 0xB8, 0xE8, 0x00, 0x5D, 0xB8, 0xEA, 0x00, 0x5D, 0xB9, 0xEB, 0x00, 0x5E, 0xBA, 0xEB, 0x00, 0x54, 0xB2, 0xE5, 0x00, 0x54, 0xB2, 0xE5, 0x00, 
        0x52, 0xB2, 0xE5, 0x00, 0x4F, 0xB0, 0xE4, 0x00, 0x4D, 0xB0, 0xE4, 0x00, 0x50, 0xB1, 0xE5, 0x00, 0x4B, 0x8D, 0xB1, 0x3C, 0x06, 0x0A, 0x0C, 0xEE, 
  /* RLE: 011 Pixels @ 030,012 */ 11, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 026 Pixels @ 005,013 */ 0, 26, 0x09, 0x0E, 0x11, 0xE8, 0x45, 0x86, 0xAA, 0x32, 0x42, 0x9D, 0xD0, 0x00, 0x43, 0x9E, 0xD1, 0x00, 0x45, 0xA0, 0xD3, 0x00, 0x47, 0xA2, 0xD5, 0x00, 0x49, 0xA4, 0xD7, 0x00, 0x4B, 0xA6, 0xD9, 0x00, 
        0x54, 0xAD, 0xDE, 0x00, 0x59, 0xB2, 0xE2, 0x00, 0x5A, 0xB3, 0xE4, 0x00, 0x5C, 0xB4, 0xE5, 0x00, 0x5C, 0xB6, 0xE6, 0x00, 0x5D, 0xB6, 0xE8, 0x00, 0x5D, 0xB7, 0xE8, 0x00, 0x5F, 0xB8, 0xE9, 0x00, 0x5E, 0xB9, 0xEB, 0x00, 0x59, 0xB5, 0xE7, 0x00, 
        0x53, 0xB2, 0xE4, 0x00, 0x54, 0xB2, 0xE5, 0x00, 0x53, 0xB2, 0xE6, 0x00, 0x51, 0xB2, 0xE6, 0x00, 0x4E, 0xB0, 0xE5, 0x00, 0x50, 0xB1, 0xE6, 0x00, 0x4B, 0x8F, 0xB3, 0x39, 0x07, 0x0B, 0x0E, 0xED, 
  /* RLE: 009 Pixels @ 031,013 */ 9, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 028 Pixels @ 004,014 */ 0, 28, 0x09, 0x0E, 0x12, 0xE6, 0x44, 0x85, 0xAA, 0x2F, 0x3F, 0x9B, 0xCD, 0x00, 0x40, 0x9B, 0xCF, 0x00, 0x42, 0x9D, 0xD0, 0x00, 0x45, 0xA0, 0xD2, 0x00, 0x47, 0xA2, 0xD4, 0x00, 0x4A, 0xA5, 0xD5, 0x00, 
        0x54, 0xAC, 0xDD, 0x00, 0x57, 0xAF, 0xE0, 0x00, 0x59, 0xB1, 0xE1, 0x00, 0x5A, 0xB2, 0xE3, 0x00, 0x5B, 0xB3, 0xE4, 0x00, 0x5D, 0xB5, 0xE5, 0x00, 0x5D, 0xB5, 0xE6, 0x00, 0x5E, 0xB7, 0xE7, 0x00, 0x5E, 0xB8, 0xE8, 0x00, 0x5F, 0xB8, 0xE9, 0x00, 
        0x5D, 0xB8, 0xE8, 0x00, 0x54, 0xB0, 0xE3, 0x00, 0x54, 0xB1, 0xE4, 0x00, 0x53, 0xB2, 0xE4, 0x00, 0x53, 0xB2, 0xE5, 0x00, 0x51, 0xB2, 0xE6, 0x00, 0x4E, 0xB0, 0xE4, 0x00, 0x50, 0xB2, 0xE5, 0x00, 0x4D, 0x91, 0xB5, 0x37, 0x09, 0x0D, 0x0F, 0xEB, 
  /* RLE: 007 Pixels @ 032,014 */ 7, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 030 Pixels @ 003,015 */ 0, 30, 0x09, 0x0F, 0x13, 0xE4, 0x40, 0x82, 0xA8, 0x2C, 0x3D, 0x97, 0xCA, 0x00, 0x3E, 0x98, 0xCB, 0x00, 0x40, 0x9B, 0xCD, 0x00, 0x43, 0x9D, 0xD0, 0x00, 0x46, 0x9F, 0xD1, 0x00, 0x48, 0xA1, 0xD3, 0x00, 
        0x52, 0xA9, 0xD9, 0x00, 0x56, 0xAE, 0xDD, 0x00, 0x58, 0xAF, 0xDF, 0x00, 0x59, 0xAF, 0xE0, 0x00, 0x5A, 0xB2, 0xE1, 0x00, 0x5C, 0xB2, 0xE2, 0x00, 0x5C, 0xB4, 0xE4, 0x00, 0x5D, 0xB6, 0xE4, 0x00, 0x5D, 0xB6, 0xE6, 0x00, 0x5F, 0xB6, 0xE7, 0x00, 
        0x5E, 0xB8, 0xE7, 0x00, 0x5F, 0xB8, 0xE8, 0x00, 0x55, 0xB0, 0xE2, 0x00, 0x54, 0xB1, 0xE2, 0x00, 0x54, 0xB0, 0xE3, 0x00, 0x53, 0xB1, 0xE4, 0x00, 0x52, 0xB1, 0xE4, 0x00, 0x50, 0xB1, 0xE5, 0x00, 0x4E, 0xB0, 0xE4, 0x00, 0x50, 0xB1, 0xE5, 0x00, 
        0x50, 0x94, 0xB8, 0x35, 0x09, 0x0E, 0x11, 0xEA, 
  /* RLE: 005 Pixels @ 033,015 */ 5, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 176 Pixels @ 002,016 */ 0, 176, 0x0A, 0x11, 0x14, 0xE3, 0x41, 0x83, 0xA9, 0x2A, 0x38, 0x92, 0xC5, 0x00, 0x3B, 0x95, 0xC8, 0x00, 0x3E, 0x98, 0xCB, 0x00, 0x41, 0x9A, 0xCC, 0x00, 0x44, 0x9D, 0xCE, 0x00, 0x46, 0x9E, 0xD0, 0x00, 
        0x4C, 0xA3, 0xD4, 0x00, 0x54, 0xAB, 0xDB, 0x00, 0x56, 0xAC, 0xDC, 0x00, 0x57, 0xAE, 0xDD, 0x00, 0x5C, 0xB0, 0xDF, 0x00, 0x5A, 0xB0, 0xE0, 0x00, 0x5B, 0xB1, 0xE1, 0x00, 0x5D, 0xB3, 0xE1, 0x00, 0x5D, 0xB3, 0xE3, 0x00, 0x5E, 0xB5, 0xE4, 0x00, 
        0x5E, 0xB6, 0xE5, 0x00, 0x5F, 0xB6, 0xE7, 0x00, 0x5F, 0xB8, 0xE7, 0x00, 0x58, 0xB1, 0xE1, 0x00, 0x55, 0xAF, 0xE2, 0x00, 0x53, 0xB0, 0xE2, 0x00, 0x53, 0xB0, 0xE2, 0x00, 0x52, 0xB0, 0xE3, 0x00, 0x52, 0xB1, 0xE4, 0x00, 0x4F, 0xAF, 0xE3, 0x00, 
        0x4E, 0xB0, 0xE4, 0x00, 0x4F, 0xB1, 0xE5, 0x00, 0x51, 0x96, 0xBB, 0x32, 0x0A, 0x0F, 0x12, 0xE8, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x0B, 0x11, 0x15, 0xE2, 0x40, 0x83, 0xA9, 0x28, 0x36, 0x8F, 0xC2, 0x00, 
        0x36, 0x90, 0xC3, 0x00, 0x3A, 0x94, 0xC6, 0x00, 0x3F, 0x98, 0xC9, 0x00, 0x41, 0x9A, 0xCC, 0x00, 0x44, 0x9C, 0xCD, 0x00, 0x47, 0x9E, 0xCF, 0x00, 0x50, 0xA5, 0xD6, 0x00, 0x54, 0xA9, 0xD9, 0x00, 0x56, 0xAB, 0xDA, 0x00, 0x65, 0xB3, 0xDE, 0x02, 
        0x66, 0xB5, 0xE0, 0x00, 0x5B, 0xAF, 0xDE, 0x00, 0x5C, 0xB0, 0xDF, 0x00, 0x5D, 0xB2, 0xE0, 0x00, 0x5D, 0xB2, 0xE1, 0x00, 0x5E, 0xB4, 0xE3, 0x00, 0x5E, 0xB5, 0xE4, 0x00, 0x60, 0xB7, 0xE5, 0x00, 0x69, 0xBC, 0xE8, 0x00, 0x58, 0xB0, 0xE1, 0x00, 
        0x55, 0xAF, 0xE0, 0x00, 0x55, 0xAF, 0xE1, 0x00, 0x53, 0xAF, 0xE2, 0x00, 0x53, 0xB0, 0xE2, 0x00, 0x51, 0xAF, 0xE2, 0x00, 0x50, 0xAF, 0xE3, 0x00, 0x4E, 0xAF, 0xE3, 0x00, 0x4E, 0xB0, 0xE4, 0x00, 0x4F, 0xB0, 0xE5, 0x00, 0x51, 0x96, 0xBC, 0x2F, 
        0x09, 0x0F, 0x12, 0xE8, 0x00, 0x00, 0x00, 0xFF, 0x01, 0x01, 0x01, 0xFC, 0x42, 0x7D, 0x9E, 0x36, 0x33, 0x8D, 0xC0, 0x00, 0x34, 0x8E, 0xC1, 0x00, 0x37, 0x90, 0xC3, 0x00, 0x3A, 0x93, 0xC5, 0x00, 0x3E, 0x96, 0xC8, 0x00, 0x42, 0x99, 0xCA, 0x00, 
        0x44, 0x9B, 0xCC, 0x00, 0x47, 0x9D, 0xCE, 0x00, 0x52, 0xA7, 0xD6, 0x00, 0x54, 0xA8, 0xD7, 0x00, 0x63, 0xAF, 0xD8, 0x05, 0x35, 0x4F, 0x5E, 0x88, 0x66, 0xB3, 0xDE, 0x01, 0x5B, 0xAF, 0xDC, 0x00, 0x5C, 0xB0, 0xDE, 0x00, 0x5D, 0xB0, 0xDF, 0x00, 
        0x5E, 0xB2, 0xE0, 0x00, 0x5F, 0xB3, 0xE0, 0x00, 0x5E, 0xB3, 0xE2, 0x00, 0x61, 0xB5, 0xE3, 0x00, 0x6A, 0xA2, 0xC1, 0x2A, 0x63, 0xB4, 0xE0, 0x04, 0x56, 0xAF, 0xDF, 0x00, 0x55, 0xAF, 0xE0, 0x00, 0x54, 0xAF, 0xE0, 0x00, 0x52, 0xAE, 0xE1, 0x00, 
        0x51, 0xAE, 0xE1, 0x00, 0x50, 0xAF, 0xE2, 0x00, 0x4E, 0xAE, 0xE2, 0x00, 0x4E, 0xAF, 0xE3, 0x00, 0x4D, 0xAF, 0xE4, 0x00, 0x4F, 0xB0, 0xE5, 0x00, 0x4F, 0x8C, 0xAD, 0x40, 0x00, 0x00, 0x00, 0xFD, 0x00, 0x00, 0x00, 0xFF, 0x14, 0x23, 0x2B, 0xC4, 
        0x43, 0x8E, 0xB9, 0x10, 0x35, 0x8E, 0xC0, 0x00, 0x37, 0x90, 0xC2, 0x00, 0x3A, 0x92, 0xC4, 0x00, 0x3E, 0x95, 0xC7, 0x00, 0x41, 0x98, 0xC9, 0x00, 0x44, 0x9B, 0xCB, 0x00, 0x48, 0x9D, 0xCE, 0x00, 0x52, 0xA6, 0xD5, 0x00, 0x62, 0xAD, 0xD6, 0x06, 
        0x2B, 0x40, 0x4B, 0xA5, 0x09, 0x0E, 0x11, 0xE3, 0x67, 0xB3, 0xDD, 0x01, 0x5B, 0xAD, 0xDB, 0x00, 0x5C, 0xAF, 0xDC, 0x00, 0x5D, 0xB0, 0xDD, 0x00, 0x5E, 0xB1, 0xDF, 0x00, 0x5E, 0xB2, 0xE0, 0x00, 0x5F, 0xB2, 0xE0, 0x00, 0x61, 0xB4, 0xE2, 0x00, 
        0x44, 0x66, 0x79, 0x74, 0x2D, 0x46, 0x54, 0x9C, 0x61, 0xB1, 0xDE, 0x04, 0x55, 0xAE, 0xDF, 0x00, 0x54, 0xAE, 0xDF, 0x00, 0x52, 0xAE, 0xE0, 0x00, 0x51, 0xAD, 0xE0, 0x00, 0x50, 0xAD, 0xE0, 0x00, 0x4F, 0xAD, 0xE0, 0x00, 0x4D, 0xAE, 0xE2, 0x00, 
        0x4E, 0xAF, 0xE3, 0x00, 0x59, 0xA9, 0xD4, 0x15, 0x16, 0x24, 0x2B, 0xCC, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x15, 0x23, 0x2B, 0xC4, 0x46, 0x8F, 0xB9, 0x11, 0x38, 0x8F, 0xC1, 0x00, 0x3A, 0x91, 0xC3, 0x00, 
        0x3D, 0x94, 0xC5, 0x00, 0x41, 0x97, 0xC8, 0x00, 0x44, 0x99, 0xCA, 0x00, 0x48, 0x9D, 0xCC, 0x00, 0x61, 0xAA, 0xD2, 0x07, 0x29, 0x3C, 0x47, 0xA9, 0x00, 0x00, 0x00, 0xFF, 0x09, 0x0E, 0x11, 0xE3, 0x67, 0xB2, 0xDC, 0x01, 0x5B, 0xAD, 0xDA, 0x00, 
        0x5D, 0xAE, 0xDB, 0x00, 0x5D, 0xAF, 0xDD, 0x00, 0x5E, 0xB0, 0xDD, 0x00, 0x5F, 0xB1, 0xDF, 0x00, 0x5F, 0xB1, 0xDF, 0x00, 0x60, 0xB4, 0xE0, 0x00, 0x44, 0x66, 0x78, 0x74, 0x00, 0x00, 0x00, 0xFF, 0x2B, 0x43, 0x50, 0x9F, 0x62, 0xB2, 0xDD, 0x05, 
        0x54, 0xAD, 0xDE, 0x00, 0x52, 0xAD, 0xDF, 0x00, 0x51, 0xAD, 0xDE, 0x00, 0x50, 0xAD, 0xDF, 0x00, 0x4E, 0xAD, 0xDF, 0x00, 0x4E, 0xAD, 0xE0, 0x00, 0x5B, 0xA9, 0xD2, 0x15, 0x17, 0x24, 0x2B, 0xCB, 
  /* RLE: 005 Pixels @ 034,020 */ 5, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 030 Pixels @ 003,021 */ 0, 30, 0x15, 0x23, 0x2C, 0xC3, 0x49, 0x91, 0xBA, 0x11, 0x3B, 0x91, 0xC2, 0x00, 0x3D, 0x93, 0xC4, 0x00, 0x41, 0x96, 0xC6, 0x00, 0x44, 0x99, 0xC8, 0x00, 0x57, 0xA0, 0xC9, 0x08, 0x25, 0x37, 0x41, 0xAE, 
        0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x09, 0x0E, 0x11, 0xE3, 0x69, 0xB1, 0xDB, 0x01, 0x5C, 0xAC, 0xD9, 0x00, 0x5D, 0xAE, 0xDA, 0x00, 0x5F, 0xAE, 0xDC, 0x00, 0x5F, 0xB0, 0xDC, 0x00, 0x60, 0xB0, 0xDD, 0x00, 0x5F, 0xB1, 0xDF, 0x00, 
        0x61, 0xB2, 0xDF, 0x00, 0x42, 0x64, 0x77, 0x75, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x2B, 0x42, 0x4E, 0xA3, 0x61, 0xB1, 0xDC, 0x05, 0x53, 0xAC, 0xDD, 0x00, 0x51, 0xAC, 0xDE, 0x00, 0x4F, 0xAC, 0xDE, 0x00, 0x4F, 0xAC, 0xDE, 0x00, 
        0x5C, 0xA8, 0xD1, 0x15, 0x17, 0x24, 0x2B, 0xCB, 
  /* RLE: 007 Pixels @ 033,021 */ 7, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 028 Pixels @ 004,022 */ 0, 28, 0x16, 0x24, 0x2C, 0xC4, 0x4C, 0x93, 0xBB, 0x11, 0x3D, 0x92, 0xC3, 0x00, 0x40, 0x94, 0xC4, 0x00, 0x54, 0x9D, 0xC6, 0x09, 0x21, 0x33, 0x3D, 0xB1, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 
        0x00, 0x00, 0x00, 0xFF, 0x0A, 0x0E, 0x11, 0xE3, 0x69, 0xB2, 0xDA, 0x01, 0x5C, 0xAB, 0xD8, 0x00, 0x5E, 0xAD, 0xD9, 0x00, 0x5F, 0xAE, 0xDA, 0x00, 0x60, 0xAE, 0xDC, 0x00, 0x60, 0xB0, 0xDC, 0x00, 0x60, 0xB0, 0xDD, 0x00, 0x5F, 0xB0, 0xDD, 0x00, 
        0x41, 0x63, 0x76, 0x75, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x00, 0xFF, 0x2A, 0x40, 0x4B, 0xA6, 0x60, 0xB0, 0xDB, 0x06, 0x50, 0xAB, 0xDC, 0x00, 0x50, 0xAB, 0xDD, 0x00, 0x5D, 0xA7, 0xCF, 0x15, 0x18, 0x24, 0x2B, 0xCB, 
  /* RLE: 009 Pixels @ 032,022 */ 9, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 004 Pixels @ 005,023 */ 0, 4, 0x17, 0x25, 0x2C, 0xC3, 0x4F, 0x94, 0xBC, 0x11, 0x52, 0x9A, 0xC3, 0x0A, 0x20, 0x31, 0x3B, 0xB4, 
  /* RLE: 004 Pixels @ 009,023 */ 4, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,023 */ 0, 10, 0x0A, 0x0E, 0x11, 0xE3, 0x69, 0xB0, 0xD9, 0x01, 0x5C, 0xAB, 0xD7, 0x00, 0x5E, 0xAC, 0xD8, 0x00, 0x5F, 0xAD, 0xD9, 0x00, 0x60, 0xAF, 0xDB, 0x00, 0x61, 0xAF, 0xDB, 0x00, 0x5F, 0xAE, 0xDB, 0x00, 
        0x5C, 0xAC, 0xD9, 0x00, 0x42, 0x62, 0x75, 0x75, 
  /* RLE: 004 Pixels @ 023,023 */ 4, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 004 Pixels @ 027,023 */ 0, 4, 0x27, 0x3C, 0x48, 0xA9, 0x60, 0xAE, 0xD9, 0x07, 0x5E, 0xA7, 0xCE, 0x15, 0x18, 0x24, 0x2B, 0xCB, 
  /* RLE: 011 Pixels @ 031,023 */ 11, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 002 Pixels @ 006,024 */ 0, 2, 0x18, 0x25, 0x2C, 0xC4, 0x1F, 0x2F, 0x38, 0xB8, 
  /* RLE: 005 Pixels @ 008,024 */ 5, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,024 */ 0, 10, 0x0A, 0x0E, 0x11, 0xE3, 0x69, 0xB0, 0xD9, 0x01, 0x5D, 0xAA, 0xD6, 0x00, 0x5F, 0xAC, 0xD7, 0x00, 0x60, 0xAD, 0xD9, 0x00, 0x61, 0xAD, 0xD9, 0x00, 0x5E, 0xAD, 0xD8, 0x00, 0x5B, 0xAB, 0xD7, 0x00, 
        0x5C, 0xAB, 0xD8, 0x00, 0x42, 0x62, 0x74, 0x75, 
  /* RLE: 005 Pixels @ 023,024 */ 5, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 002 Pixels @ 028,024 */ 0, 2, 0x24, 0x37, 0x42, 0xB1, 0x18, 0x24, 0x2B, 0xCC, 
  /* RLE: 019 Pixels @ 030,024 */ 19, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,025 */ 0, 10, 0x0A, 0x0E, 0x10, 0xE3, 0x69, 0xAF, 0xD6, 0x01, 0x5B, 0xA8, 0xD4, 0x00, 0x5D, 0xAA, 0xD5, 0x00, 0x5F, 0xAB, 0xD5, 0x00, 0x5E, 0xAA, 0xD5, 0x00, 0x5D, 0xAA, 0xD6, 0x00, 0x5B, 0xAA, 0xD6, 0x00, 
        0x5C, 0xAB, 0xD7, 0x00, 0x42, 0x62, 0x74, 0x75, 
  /* RLE: 026 Pixels @ 023,025 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,026 */ 0, 10, 0x0A, 0x0E, 0x10, 0xE3, 0x67, 0xAD, 0xD4, 0x01, 0x59, 0xA6, 0xD1, 0x00, 0x5C, 0xA7, 0xD2, 0x00, 0x5E, 0xA9, 0xD3, 0x00, 0x5E, 0xA9, 0xD4, 0x00, 0x5C, 0xA9, 0xD5, 0x00, 0x5B, 0xA9, 0xD5, 0x00, 
        0x5B, 0xAA, 0xD6, 0x00, 0x42, 0x62, 0x74, 0x75, 
  /* RLE: 026 Pixels @ 023,026 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,027 */ 0, 10, 0x09, 0x0E, 0x10, 0xE3, 0x65, 0xAA, 0xD1, 0x01, 0x57, 0xA4, 0xCF, 0x00, 0x5A, 0xA6, 0xD1, 0x00, 0x5D, 0xA7, 0xD2, 0x00, 0x5D, 0xA8, 0xD3, 0x00, 0x5B, 0xA8, 0xD4, 0x00, 0x5A, 0xA8, 0xD3, 0x00, 
        0x5A, 0xA8, 0xD4, 0x00, 0x42, 0x62, 0x73, 0x75, 
  /* RLE: 026 Pixels @ 023,027 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,028 */ 0, 10, 0x09, 0x0E, 0x10, 0xE3, 0x63, 0xA8, 0xD0, 0x01, 0x54, 0xA0, 0xCC, 0x00, 0x56, 0xA3, 0xCD, 0x00, 0x58, 0xA4, 0xCE, 0x00, 0x59, 0xA5, 0xD0, 0x00, 0x58, 0xA6, 0xD1, 0x00, 0x58, 0xA6, 0xD1, 0x00, 
        0x58, 0xA6, 0xD2, 0x00, 0x42, 0x61, 0x73, 0x75, 
  /* RLE: 026 Pixels @ 023,028 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,029 */ 0, 10, 0x09, 0x0E, 0x10, 0xE3, 0x62, 0xA7, 0xCF, 0x01, 0x51, 0x9F, 0xCB, 0x00, 0x54, 0xA0, 0xCC, 0x00, 0x55, 0xA2, 0xCE, 0x00, 0x55, 0xA2, 0xCE, 0x00, 0x56, 0xA3, 0xCF, 0x00, 0x55, 0xA3, 0xD0, 0x00, 
        0x56, 0xA5, 0xD1, 0x00, 0x42, 0x61, 0x72, 0x75, 
  /* RLE: 026 Pixels @ 023,029 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,030 */ 0, 10, 0x09, 0x0D, 0x10, 0xE3, 0x60, 0xA5, 0xCD, 0x01, 0x4F, 0x9D, 0xC9, 0x00, 0x51, 0x9E, 0xCA, 0x00, 0x52, 0x9F, 0xCC, 0x00, 0x53, 0xA0, 0xCC, 0x00, 0x53, 0xA0, 0xCD, 0x00, 0x53, 0xA1, 0xCE, 0x00, 
        0x54, 0xA3, 0xCF, 0x00, 0x42, 0x61, 0x72, 0x75, 
  /* RLE: 026 Pixels @ 023,030 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,031 */ 0, 10, 0x09, 0x0D, 0x10, 0xE3, 0x5F, 0xA4, 0xCC, 0x01, 0x4C, 0x9A, 0xC7, 0x00, 0x4E, 0x9C, 0xC9, 0x00, 0x4F, 0x9D, 0xCA, 0x00, 0x50, 0x9E, 0xCB, 0x00, 0x50, 0x9F, 0xCB, 0x00, 0x50, 0x9F, 0xCC, 0x00, 
        0x52, 0xA1, 0xCE, 0x00, 0x42, 0x61, 0x71, 0x75, 
  /* RLE: 026 Pixels @ 023,031 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,032 */ 0, 10, 0x09, 0x0D, 0x10, 0xE3, 0x5C, 0xA2, 0xCA, 0x01, 0x4A, 0x98, 0xC5, 0x00, 0x4B, 0x99, 0xC6, 0x00, 0x4C, 0x9B, 0xC8, 0x00, 0x4D, 0x9C, 0xC8, 0x00, 0x4D, 0x9C, 0xCA, 0x00, 0x4D, 0x9D, 0xCA, 0x00, 
        0x4F, 0x9F, 0xCC, 0x00, 0x41, 0x60, 0x71, 0x75, 
  /* RLE: 026 Pixels @ 023,032 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* ABS: 010 Pixels @ 013,033 */ 0, 10, 0x09, 0x0D, 0x10, 0xE3, 0x67, 0xA9, 0xCE, 0x01, 0x5D, 0xA4, 0xCC, 0x00, 0x5E, 0xA5, 0xCD, 0x00, 0x5F, 0xA6, 0xCE, 0x00, 0x60, 0xA7, 0xCF, 0x00, 0x60, 0xA8, 0xD0, 0x00, 0x60, 0xA8, 0xD1, 0x00, 
        0x61, 0xAA, 0xD2, 0x00, 0x42, 0x61, 0x72, 0x75, 
  /* RLE: 026 Pixels @ 023,033 */ 26, 0x00, 0x00, 0x00, 0xFF, 
  /* RLE: 001 Pixels @ 013,034 */ 1, 0x01, 0x01, 0x02, 0xFC, 
  /* RLE: 004 Pixels @ 014,034 */ 4, 0x0C, 0x11, 0x14, 0xE0, 
  /* ABS: 005 Pixels @ 018,034 */ 0, 5, 0x0C, 0x12, 0x14, 0xE0, 0x0C, 0x12, 0x14, 0xE0, 0x0C, 0x12, 0x15, 0xE0, 0x0C, 0x12, 0x15, 0xE0, 0x07, 0x09, 0x0B, 0xEF, 
  /* RLE: 049 Pixels @ 023,034 */ 49, 0x00, 0x00, 0x00, 0xFF, 
  0
};  // 2623 for 1296 pixels

GUI_CONST_STORAGE GUI_BITMAP bmup = {
  36, // xSize
  36, // ySize
  144, // BytesPerLine
  32, // BitsPerPixel
  (unsigned char *)_acup,  // Pointer to picture data
  NULL,  // Pointer to palette
  GUI_DRAW_RLE32
};

/*************************** End of file ****************************/
