/*
*********************************************************************************************************
* @file
* @project ACUT-GCV1
* <AUTHOR> YL
* @date    2021/09/23
* @version v1.0
* @brief   AD9246 configure, 14bits.
* @modify
*
* Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com
*********************************************************************************************************
*/
#include "includes.h"


/**
  * @brief  FPGA端ADC初始化
  * @param
  * @retval
  */
  // FPGA 端 ADC SPI引脚初始化
  // SCLK-PI9, CSB-PI8, PWDN-PE6, SDIO-PI10
void AD9246_Init(void)//采用IO口PB0实现重复次数
{
	GPIO_InitTypeDef GPIO_InitStruct;
	__HAL_RCC_GPIOE_CLK_ENABLE();
	__HAL_RCC_GPIOI_CLK_ENABLE();

	GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLDOWN;
	GPIO_InitStruct.Speed = GPIO_SPEED_HIGH;
	HAL_GPIO_Init(GPIOI, &GPIO_InitStruct);

	GPIO_InitStruct.Pin = GPIO_PIN_4;
	HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

	AD9246_PWDN(0); //0关闭低功耗,ADC正常工作; 1进入低功耗
	AD9246_CS(1);
	AD9246_SCLK(1); //0:Binary; 1:Twos complement
	AD9246_SDIO(1); //0:DCS disable; 1:DCS enabled
}



