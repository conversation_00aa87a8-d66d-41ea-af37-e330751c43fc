/*********************************************************************
*                SEGGER Microcontroller GmbH & Co. KG                *
*        Solutions for real time microcontroller applications        *
*                           www.segger.com                           *
**********************************************************************
*                                                                    *
* C-file generated by                                                *
*                                                                    *
*        Bitmap converter for emWin V5.18.                           *
*        Compiled Sep 24 2012, 15:52:34                              *
*        (C) 1998 - 2012 Segger Microcontroller GmbH & Co. KG        *
*                                                                    *
**********************************************************************
*                                                                    *
* Source file: ok                                                    *
* Dimensions:  36 * 36                                               *
* NumColors:   32bpp: 16777216 + 256                                 *
*                                                                    *
**********************************************************************
*/

#include <stdlib.h>

#include "GUI.h"

#ifndef GUI_CONST_STORAGE
  #define GUI_CONST_STORAGE const
#endif

extern GUI_CONST_STORAGE GUI_BITMAP bmok;

static GUI_CONST_STORAGE unsigned char _acok[] = {
  /* RLE: 182 Pixels @ 000,000 */ 182, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,005 */ 1, 0xED, 0xFA, 0xFF, 0xFF, 
  /* RLE: 024 Pixels @ 003,005 */ 24, 0xE7, 0xF9, 0xFF, 0xFF, 
  /* ABS: 007 Pixels @ 027,005 */ 0, 7, 0xE6, 0xF9, 0xFF, 0xFF, 0xE4, 0xF7, 0xFE, 0xFF, 0xE3, 0xF6, 0xFE, 0xFB, 0xE5, 0xF8, 0xFE, 0xFF, 0xE7, 0xF9, 0xFF, 0xFF, 0xE7, 0xF9, 0xFF, 0xFF, 0xED, 0xFA, 0xFF, 0xFF, 
  /* RLE: 004 Pixels @ 034,005 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,006 */ 1, 0xB1, 0xEB, 0xFE, 0xFF, 
  /* RLE: 023 Pixels @ 003,006 */ 23, 0x97, 0xE4, 0xFD, 0xFF, 
  /* ABS: 008 Pixels @ 026,006 */ 0, 8, 0x96, 0xE4, 0xFD, 0xFF, 0x8B, 0xDD, 0xFB, 0xFF, 0x7C, 0xD3, 0xF7, 0xAE, 0x7E, 0xD4, 0xF8, 0x2B, 0x86, 0xD9, 0xF9, 0xE4, 0x93, 0xE2, 0xFD, 0xFF, 0x97, 0xE4, 0xFD, 0xFF, 0xB1, 0xEB, 0xFE, 0xFF, 
  /* RLE: 004 Pixels @ 034,006 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,007 */ 1, 0xAF, 0xEA, 0xFE, 0xFF, 
  /* RLE: 023 Pixels @ 003,007 */ 23, 0x94, 0xE3, 0xFD, 0xFF, 
  /* ABS: 008 Pixels @ 026,007 */ 0, 8, 0x89, 0xDC, 0xFA, 0xFF, 0x7B, 0xD2, 0xF7, 0xAE, 0x84, 0xDA, 0xFA, 0x06, 0x89, 0xDE, 0xFB, 0x00, 0x87, 0xDB, 0xFA, 0x25, 0x86, 0xDA, 0xF9, 0xE4, 0x91, 0xE1, 0xFC, 0xFF, 0xAF, 0xEA, 0xFE, 0xFF, 
  /* RLE: 004 Pixels @ 034,007 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,008 */ 1, 0xAD, 0xE9, 0xFD, 0xFF, 
  /* RLE: 021 Pixels @ 003,008 */ 21, 0x92, 0xE2, 0xFC, 0xFF, 
  /* ABS: 010 Pixels @ 024,008 */ 0, 10, 0x91, 0xE2, 0xFC, 0xFF, 0x87, 0xDA, 0xF9, 0xFF, 0x78, 0xD1, 0xF6, 0xAE, 0x7F, 0xD9, 0xF9, 0x05, 0x80, 0xDD, 0xFB, 0x00, 0x7F, 0xDD, 0xFB, 0x00, 0x82, 0xDD, 0xFB, 0x00, 0x83, 0xDA, 0xF9, 0x25, 
        0x83, 0xD8, 0xF8, 0xE4, 0xAA, 0xE7, 0xFC, 0xFF, 
  /* RLE: 004 Pixels @ 034,008 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,009 */ 1, 0xAB, 0xE9, 0xFC, 0xFF, 
  /* RLE: 020 Pixels @ 003,009 */ 20, 0x8F, 0xE1, 0xFB, 0xFF, 
  /* ABS: 011 Pixels @ 023,009 */ 0, 11, 0x8E, 0xE0, 0xFB, 0xFF, 0x84, 0xD9, 0xF9, 0xFF, 0x76, 0xD0, 0xF5, 0xAE, 0x7B, 0xD7, 0xF8, 0x05, 0x7A, 0xDA, 0xFA, 0x00, 0x76, 0xDA, 0xFA, 0x00, 0x74, 0xDA, 0xFA, 0x00, 0x77, 0xDA, 0xFA, 0x00, 
        0x7E, 0xDB, 0xFA, 0x00, 0x82, 0xD9, 0xF9, 0x25, 0xA1, 0xE1, 0xFA, 0xE3, 
  /* RLE: 004 Pixels @ 034,009 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,010 */ 1, 0xA8, 0xE7, 0xFB, 0xFF, 
  /* RLE: 019 Pixels @ 003,010 */ 19, 0x8B, 0xDF, 0xFA, 0xFF, 
  /* ABS: 017 Pixels @ 022,010 */ 0, 17, 0x8A, 0xDF, 0xFA, 0xFF, 0x81, 0xD8, 0xF8, 0xFF, 0x73, 0xCE, 0xF4, 0xAE, 0x78, 0xD6, 0xF8, 0x06, 0x76, 0xD9, 0xF9, 0x00, 0x71, 0xD8, 0xF9, 0x00, 0x6D, 0xD7, 0xF9, 0x00, 0x6C, 0xD7, 0xF9, 0x00, 
        0x6F, 0xD7, 0xF9, 0x00, 0x76, 0xD9, 0xF9, 0x00, 0x80, 0xDC, 0xFA, 0x00, 0x91, 0xDF, 0xFA, 0x25, 0xEF, 0xFA, 0xFE, 0xE4, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA5, 0xE6, 0xFB, 0xFF, 
  /* RLE: 019 Pixels @ 003,011 */ 19, 0x87, 0xDD, 0xFA, 0xFF, 
  /* ABS: 017 Pixels @ 022,011 */ 0, 17, 0x7E, 0xD6, 0xF7, 0xFF, 0x71, 0xCD, 0xF4, 0xAE, 0x74, 0xD4, 0xF7, 0x06, 0x72, 0xD7, 0xF8, 0x00, 0x6C, 0xD6, 0xF8, 0x00, 0x68, 0xD5, 0xF8, 0x00, 0x67, 0xD4, 0xF8, 0x00, 0x67, 0xD4, 0xF8, 0x00, 
        0x6A, 0xD5, 0xF8, 0x00, 0x71, 0xD7, 0xF9, 0x00, 0x7B, 0xD9, 0xF8, 0x00, 0x88, 0xDA, 0xF8, 0x22, 0xED, 0xFA, 0xFE, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA2, 0xE5, 0xFB, 0xFF, 
  /* RLE: 018 Pixels @ 003,012 */ 18, 0x83, 0xDC, 0xF9, 0xFF, 
  /* ABS: 013 Pixels @ 021,012 */ 0, 13, 0x7A, 0xD5, 0xF6, 0xFF, 0x6E, 0xCB, 0xF3, 0xAE, 0x71, 0xD2, 0xF6, 0x06, 0x6D, 0xD5, 0xF7, 0x00, 0x67, 0xD4, 0xF7, 0x00, 0x63, 0xD3, 0xF7, 0x00, 0x62, 0xD2, 0xF7, 0x00, 0x62, 0xD2, 0xF7, 0x00, 
        0x64, 0xD3, 0xF7, 0x00, 0x69, 0xD4, 0xF7, 0x00, 0x70, 0xD5, 0xF7, 0x00, 0x72, 0xD1, 0xF5, 0x22, 0x94, 0xD9, 0xF6, 0xE0, 
  /* RLE: 004 Pixels @ 034,012 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 007 Pixels @ 002,013 */ 0, 7, 0x9F, 0xE3, 0xFA, 0xFF, 0x7F, 0xDA, 0xF8, 0xFF, 0x7E, 0xD9, 0xF7, 0xFF, 0x78, 0xD4, 0xF5, 0xFF, 0x73, 0xD0, 0xF4, 0xCD, 0x7A, 0xD6, 0xF6, 0xFF, 0x7F, 0xDA, 0xF7, 0xFF, 
  /* RLE: 010 Pixels @ 009,013 */ 10, 0x7F, 0xDA, 0xF8, 0xFF, 
  /* ABS: 015 Pixels @ 019,013 */ 0, 15, 0x7F, 0xDA, 0xF7, 0xFF, 0x77, 0xD3, 0xF5, 0xFF, 0x6B, 0xCA, 0xF2, 0xAE, 0x6D, 0xD0, 0xF5, 0x05, 0x69, 0xD3, 0xF6, 0x00, 0x62, 0xD2, 0xF6, 0x00, 0x5E, 0xD1, 0xF6, 0x00, 0x5C, 0xD0, 0xF6, 0x00, 
        0x5D, 0xD0, 0xF6, 0x00, 0x5F, 0xD1, 0xF6, 0x00, 0x64, 0xD2, 0xF6, 0x00, 0x6B, 0xD3, 0xF6, 0x00, 0x6C, 0xCE, 0xF4, 0x22, 0x6D, 0xCB, 0xF2, 0xE0, 0x9B, 0xE0, 0xF8, 0xFF, 
  /* RLE: 004 Pixels @ 034,013 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 007 Pixels @ 002,014 */ 0, 7, 0x9C, 0xE2, 0xF8, 0xFF, 0x7A, 0xD8, 0xF6, 0xFF, 0x70, 0xCF, 0xF3, 0xFF, 0x68, 0xC8, 0xF1, 0x88, 0x6D, 0xCC, 0xF3, 0x01, 0x6E, 0xCD, 0xF3, 0x94, 0x75, 0xD3, 0xF5, 0xFF, 
  /* RLE: 010 Pixels @ 009,014 */ 10, 0x7B, 0xD8, 0xF6, 0xFF, 
  /* ABS: 015 Pixels @ 019,014 */ 0, 15, 0x73, 0xD1, 0xF4, 0xFF, 0x68, 0xC8, 0xF1, 0xAE, 0x68, 0xCE, 0xF4, 0x05, 0x64, 0xD0, 0xF5, 0x00, 0x5D, 0xCF, 0xF5, 0x00, 0x59, 0xCE, 0xF4, 0x00, 0x57, 0xCD, 0xF4, 0x00, 0x57, 0xCE, 0xF4, 0x00, 
        0x59, 0xCF, 0xF4, 0x00, 0x5F, 0xD0, 0xF5, 0x00, 0x66, 0xD0, 0xF5, 0x00, 0x69, 0xCC, 0xF3, 0x22, 0x69, 0xC9, 0xF1, 0xE0, 0x77, 0xD5, 0xF5, 0xFF, 0x9C, 0xE2, 0xF8, 0xFF, 
  /* RLE: 004 Pixels @ 034,014 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 009 Pixels @ 002,015 */ 0, 9, 0x98, 0xE0, 0xF8, 0xFF, 0x6D, 0xCD, 0xF2, 0xFF, 0x65, 0xC7, 0xF0, 0x88, 0x68, 0xCF, 0xF3, 0x00, 0x68, 0xD1, 0xF4, 0x00, 0x6A, 0xD0, 0xF4, 0x01, 0x6B, 0xCD, 0xF2, 0x94, 0x71, 0xD1, 0xF4, 0xFF, 
        0x76, 0xD6, 0xF6, 0xFF, 
  /* RLE: 006 Pixels @ 011,015 */ 6, 0x77, 0xD6, 0xF6, 0xFF, 
  /* ABS: 017 Pixels @ 017,015 */ 0, 17, 0x76, 0xD6, 0xF6, 0xFF, 0x6F, 0xD0, 0xF3, 0xFF, 0x65, 0xC6, 0xF0, 0xAE, 0x64, 0xCD, 0xF2, 0x06, 0x5F, 0xCF, 0xF4, 0x00, 0x58, 0xCD, 0xF3, 0x00, 0x53, 0xCC, 0xF3, 0x00, 0x51, 0xCB, 0xF3, 0x00, 
        0x51, 0xCB, 0xF3, 0x00, 0x54, 0xCC, 0xF3, 0x00, 0x59, 0xCE, 0xF3, 0x00, 0x61, 0xCF, 0xF4, 0x00, 0x65, 0xCB, 0xF1, 0x22, 0x66, 0xC7, 0xF0, 0xE0, 0x72, 0xD3, 0xF4, 0xFF, 0x76, 0xD6, 0xF6, 0xFF, 0x99, 0xE0, 0xF8, 0xFF, 
  /* RLE: 004 Pixels @ 034,015 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 002,016 */ 0, 10, 0x8D, 0xD7, 0xF4, 0xFF, 0x61, 0xC5, 0xEF, 0x88, 0x60, 0xCC, 0xF2, 0x00, 0x5A, 0xCD, 0xF2, 0x00, 0x57, 0xCD, 0xF2, 0x00, 0x5B, 0xCD, 0xF2, 0x00, 0x62, 0xCE, 0xF2, 0x01, 0x67, 0xCB, 0xF1, 0x94, 
        0x6D, 0xD0, 0xF3, 0xFF, 0x72, 0xD4, 0xF4, 0xFF, 
  /* RLE: 004 Pixels @ 012,016 */ 4, 0x72, 0xD5, 0xF4, 0xFF, 
  /* ABS: 018 Pixels @ 016,016 */ 0, 18, 0x72, 0xD4, 0xF4, 0xFF, 0x6B, 0xCE, 0xF2, 0xFF, 0x61, 0xC5, 0xEF, 0xAE, 0x60, 0xCB, 0xF1, 0x06, 0x59, 0xCC, 0xF2, 0x00, 0x52, 0xCB, 0xF2, 0x00, 0x4D, 0xCA, 0xF1, 0x00, 0x4B, 0xC9, 0xF1, 0x00, 
        0x4C, 0xC9, 0xF1, 0x00, 0x4E, 0xCA, 0xF1, 0x00, 0x54, 0xCB, 0xF2, 0x00, 0x5C, 0xCD, 0xF2, 0x00, 0x61, 0xC9, 0xF0, 0x22, 0x63, 0xC6, 0xEF, 0xE0, 0x6E, 0xD1, 0xF3, 0xFF, 0x72, 0xD5, 0xF4, 0xFF, 0x72, 0xD5, 0xF4, 0xFF, 0x95, 0xDF, 0xF7, 0xFF, 
  /* RLE: 004 Pixels @ 034,016 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 098 Pixels @ 002,017 */ 0, 98, 0x83, 0xD2, 0xF2, 0x88, 0x5E, 0xCA, 0xF1, 0x00, 0x54, 0xCA, 0xF1, 0x00, 0x4C, 0xC8, 0xF1, 0x00, 0x49, 0xC8, 0xF0, 0x00, 0x4C, 0xC8, 0xF1, 0x00, 0x53, 0xCA, 0xF1, 0x00, 0x5D, 0xCB, 0xF1, 0x01, 
        0x62, 0xC9, 0xF0, 0x94, 0x69, 0xCE, 0xF2, 0xFF, 0x6D, 0xD2, 0xF3, 0xFF, 0x6D, 0xD3, 0xF3, 0xFF, 0x6D, 0xD3, 0xF3, 0xFF, 0x6D, 0xD2, 0xF3, 0xFF, 0x67, 0xCC, 0xF1, 0xFF, 0x5D, 0xC3, 0xEE, 0xAE, 0x5B, 0xC8, 0xF0, 0x05, 0x54, 0xCA, 0xF1, 0x00, 
        0x4C, 0xC8, 0xF1, 0x00, 0x47, 0xC7, 0xF0, 0x00, 0x45, 0xC6, 0xF0, 0x00, 0x46, 0xC6, 0xF0, 0x00, 0x48, 0xC7, 0xF0, 0x00, 0x4E, 0xC9, 0xF1, 0x00, 0x57, 0xCA, 0xF1, 0x00, 0x5D, 0xC7, 0xEF, 0x21, 0x5F, 0xC4, 0xEE, 0xE0, 0x6A, 0xCF, 0xF2, 0xFF, 
        0x6D, 0xD3, 0xF3, 0xFF, 0x6D, 0xD3, 0xF3, 0xFF, 0x6D, 0xD3, 0xF3, 0xFF, 0x92, 0xDE, 0xF6, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3, 0xEB, 0xF9, 0xA7, 0x64, 0xCC, 0xF1, 0x00, 0x58, 0xCB, 0xF1, 0x00, 
        0x4A, 0xC8, 0xF0, 0x00, 0x42, 0xC5, 0xEF, 0x00, 0x40, 0xC4, 0xEF, 0x00, 0x41, 0xC4, 0xEF, 0x00, 0x46, 0xC6, 0xEF, 0x00, 0x4E, 0xC8, 0xF0, 0x00, 0x58, 0xC9, 0xF0, 0x01, 0x5F, 0xC7, 0xEF, 0x94, 0x64, 0xCC, 0xF1, 0xFF, 0x68, 0xD0, 0xF2, 0xFF, 
        0x68, 0xD0, 0xF2, 0xFF, 0x63, 0xCA, 0xF0, 0xFF, 0x5A, 0xC1, 0xEC, 0xAE, 0x57, 0xC7, 0xEF, 0x05, 0x4E, 0xC8, 0xF0, 0x00, 0x46, 0xC6, 0xEF, 0x00, 0x41, 0xC4, 0xEF, 0x00, 0x3F, 0xC4, 0xEF, 0x00, 0x40, 0xC4, 0xEF, 0x00, 0x42, 0xC5, 0xEF, 0x00, 
        0x49, 0xC7, 0xF0, 0x00, 0x51, 0xC8, 0xF0, 0x00, 0x58, 0xC5, 0xEE, 0x21, 0x5C, 0xC2, 0xED, 0xE0, 0x65, 0xCD, 0xF1, 0xFF, 0x69, 0xD0, 0xF3, 0xFF, 0x69, 0xD1, 0xF3, 0xFF, 0x69, 0xD1, 0xF3, 0xFF, 0x69, 0xD1, 0xF3, 0xFF, 0x8E, 0xDC, 0xF6, 0xFF, 
        0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0xFC, 0xFE, 0xF6, 0x76, 0xD1, 0xF2, 0x45, 0x55, 0xC9, 0xEF, 0x00, 0x47, 0xC6, 0xEE, 0x00, 0x3E, 0xC3, 0xED, 0x00, 0x3B, 0xC1, 0xED, 0x00, 0x39, 0xC1, 0xED, 0x00, 
        0x3B, 0xC2, 0xED, 0x00, 0x40, 0xC3, 0xEE, 0x00, 0x48, 0xC6, 0xEF, 0x00, 0x53, 0xC7, 0xEF, 0x01, 0x59, 0xC4, 0xED, 0x94, 0x5D, 0xC6, 0xEE, 0xFF, 0x5C, 0xC5, 0xED, 0xFF, 0x55, 0xBF, 0xEB, 0xAE, 0x52, 0xC4, 0xED, 0x06, 0x49, 0xC6, 0xEE, 0x00, 
        0x41, 0xC4, 0xEE, 0x00, 0x3B, 0xC2, 0xED, 0x00, 0x3A, 0xC1, 0xED, 0x00, 0x3A, 0xC1, 0xED, 0x00, 0x3D, 0xC2, 0xED, 0x00, 0x43, 0xC4, 0xEE, 0x00, 0x4C, 0xC6, 0xEE, 0x00, 0x54, 0xC3, 0xED, 0x22, 0x58, 0xC0, 0xEC, 0xE0, 0x61, 0xCB, 0xF0, 0xFF, 
  /* RLE: 005 Pixels @ 028,019 */ 5, 0x64, 0xCF, 0xF1, 0xFF, 
  /* RLE: 001 Pixels @ 033,019 */ 1, 0x8B, 0xDB, 0xF5, 0xFF, 
  /* RLE: 004 Pixels @ 034,019 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 025 Pixels @ 002,020 */ 0, 25, 0x83, 0xD3, 0xF1, 0xF6, 0x56, 0xC4, 0xED, 0x45, 0x4B, 0xC5, 0xEE, 0x00, 0x3F, 0xC3, 0xED, 0x00, 0x38, 0xC0, 0xEC, 0x00, 0x34, 0xBF, 0xEC, 0x00, 0x33, 0xBE, 0xEC, 0x00, 0x35, 0xBF, 0xEC, 0x00, 
        0x3A, 0xC1, 0xEC, 0x00, 0x42, 0xC3, 0xED, 0x00, 0x4A, 0xC3, 0xED, 0x01, 0x4B, 0xBE, 0xEB, 0x94, 0x4A, 0xBC, 0xEA, 0xAE, 0x49, 0xC1, 0xEC, 0x06, 0x43, 0xC3, 0xED, 0x00, 0x3B, 0xC1, 0xED, 0x00, 0x36, 0xBF, 0xEC, 0x00, 0x34, 0xBF, 0xEC, 0x00, 
        0x34, 0xBF, 0xEC, 0x00, 0x37, 0xC0, 0xEC, 0x00, 0x3D, 0xC2, 0xED, 0x00, 0x47, 0xC3, 0xED, 0x00, 0x50, 0xC1, 0xEC, 0x22, 0x55, 0xBE, 0xEB, 0xE0, 0x5D, 0xC9, 0xEF, 0xFF, 
  /* RLE: 006 Pixels @ 027,020 */ 6, 0x5F, 0xCC, 0xF0, 0xFF, 
  /* RLE: 001 Pixels @ 033,020 */ 1, 0x87, 0xD9, 0xF4, 0xFF, 
  /* RLE: 004 Pixels @ 034,020 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 024 Pixels @ 002,021 */ 0, 24, 0x83, 0xD7, 0xF3, 0xFF, 0x56, 0xC4, 0xEC, 0xF6, 0x51, 0xC2, 0xEC, 0x45, 0x45, 0xC2, 0xEC, 0x00, 0x3A, 0xC0, 0xEC, 0x00, 0x32, 0xBE, 0xEB, 0x00, 0x2E, 0xBD, 0xEA, 0x00, 0x2E, 0xBC, 0xEA, 0x00, 
        0x2F, 0xBD, 0xEA, 0x00, 0x34, 0xBE, 0xEB, 0x00, 0x39, 0xBF, 0xEB, 0x00, 0x3C, 0xBF, 0xEB, 0x01, 0x3C, 0xBF, 0xEB, 0x05, 0x39, 0xBF, 0xEB, 0x00, 0x34, 0xBE, 0xEB, 0x00, 0x30, 0xBD, 0xEA, 0x00, 0x2E, 0xBC, 0xEA, 0x00, 0x2E, 0xBC, 0xEA, 0x00, 
        0x31, 0xBD, 0xEB, 0x00, 0x38, 0xBF, 0xEB, 0x00, 0x42, 0xC1, 0xEC, 0x00, 0x4C, 0xBF, 0xEB, 0x21, 0x51, 0xBD, 0xEA, 0xE0, 0x58, 0xC7, 0xEE, 0xFF, 
  /* RLE: 007 Pixels @ 026,021 */ 7, 0x5B, 0xCB, 0xEF, 0xFF, 
  /* RLE: 001 Pixels @ 033,021 */ 1, 0x84, 0xD8, 0xF3, 0xFF, 
  /* RLE: 004 Pixels @ 034,021 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 023 Pixels @ 002,022 */ 0, 23, 0x80, 0xD7, 0xF2, 0xFF, 0x56, 0xC8, 0xED, 0xFF, 0x52, 0xC2, 0xEB, 0xF6, 0x4C, 0xC0, 0xEB, 0x45, 0x40, 0xC1, 0xEB, 0x00, 0x34, 0xBE, 0xEA, 0x00, 0x2C, 0xBB, 0xEA, 0x00, 0x29, 0xBA, 0xE9, 0x00, 
        0x28, 0xBA, 0xE9, 0x00, 0x29, 0xBA, 0xE9, 0x00, 0x2B, 0xBB, 0xE9, 0x00, 0x2D, 0xBC, 0xEA, 0x00, 0x2D, 0xBB, 0xEA, 0x00, 0x2B, 0xBB, 0xE9, 0x00, 0x29, 0xBB, 0xE9, 0x00, 0x28, 0xBA, 0xE9, 0x00, 0x28, 0xBA, 0xE9, 0x00, 0x2B, 0xBB, 0xE9, 0x00, 
        0x32, 0xBD, 0xEA, 0x00, 0x3D, 0xBF, 0xEB, 0x00, 0x48, 0xBD, 0xEA, 0x21, 0x4E, 0xBB, 0xE9, 0xE0, 0x54, 0xC5, 0xEC, 0xFF, 
  /* RLE: 008 Pixels @ 025,022 */ 8, 0x56, 0xC9, 0xEE, 0xFF, 
  /* RLE: 001 Pixels @ 033,022 */ 1, 0x80, 0xD7, 0xF2, 0xFF, 
  /* RLE: 004 Pixels @ 034,022 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 022 Pixels @ 002,023 */ 0, 22, 0x7D, 0xD5, 0xF2, 0xFF, 0x52, 0xC7, 0xED, 0xFF, 0x51, 0xC6, 0xEC, 0xFF, 0x4E, 0xC0, 0xEA, 0xF6, 0x48, 0xBE, 0xE9, 0x45, 0x3C, 0xBE, 0xEA, 0x00, 0x2F, 0xBC, 0xE9, 0x00, 0x27, 0xB9, 0xE8, 0x00, 
        0x23, 0xB8, 0xE8, 0x00, 0x22, 0xB7, 0xE8, 0x00, 0x22, 0xB7, 0xE8, 0x00, 0x23, 0xB8, 0xE8, 0x00, 0x23, 0xB8, 0xE8, 0x00, 0x22, 0xB7, 0xE8, 0x00, 0x22, 0xB7, 0xE8, 0x00, 0x23, 0xB8, 0xE8, 0x00, 0x26, 0xB9, 0xE8, 0x00, 0x2D, 0xBB, 0xE9, 0x00, 
        0x38, 0xBD, 0xEA, 0x00, 0x44, 0xBB, 0xE8, 0x21, 0x4B, 0xB9, 0xE8, 0xE0, 0x50, 0xC3, 0xEB, 0xFF, 
  /* RLE: 009 Pixels @ 024,023 */ 9, 0x52, 0xC7, 0xED, 0xFF, 
  /* RLE: 001 Pixels @ 033,023 */ 1, 0x7D, 0xD5, 0xF2, 0xFF, 
  /* RLE: 004 Pixels @ 034,023 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 010 Pixels @ 002,024 */ 0, 10, 0x7A, 0xD4, 0xF0, 0xFF, 0x4E, 0xC5, 0xEB, 0xFF, 0x4E, 0xC5, 0xEB, 0xFF, 0x4D, 0xC4, 0xEB, 0xFF, 0x4B, 0xBE, 0xE9, 0xF6, 0x45, 0xBC, 0xE8, 0x45, 0x37, 0xBC, 0xE9, 0x00, 0x2A, 0xB9, 0xE8, 0x00, 
        0x22, 0xB7, 0xE7, 0x00, 0x1E, 0xB6, 0xE6, 0x00, 
  /* RLE: 004 Pixels @ 012,024 */ 4, 0x1C, 0xB5, 0xE6, 0x00, 
  /* ABS: 007 Pixels @ 016,024 */ 0, 7, 0x1D, 0xB5, 0xE6, 0x00, 0x21, 0xB6, 0xE7, 0x00, 0x28, 0xB9, 0xE8, 0x00, 0x34, 0xBB, 0xE8, 0x00, 0x40, 0xB9, 0xE7, 0x22, 0x48, 0xB8, 0xE7, 0xE0, 0x4C, 0xC2, 0xEA, 0xFF, 
  /* RLE: 010 Pixels @ 023,024 */ 10, 0x4E, 0xC5, 0xEB, 0xFF, 
  /* RLE: 001 Pixels @ 033,024 */ 1, 0x7A, 0xD4, 0xF0, 0xFF, 
  /* RLE: 004 Pixels @ 034,024 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* ABS: 020 Pixels @ 002,025 */ 0, 20, 0x77, 0xD3, 0xF0, 0xFF, 0x4A, 0xC4, 0xEB, 0xFF, 0x4A, 0xC4, 0xEB, 0xFF, 0x4A, 0xC4, 0xEB, 0xFF, 0x49, 0xC3, 0xEB, 0xFF, 0x47, 0xBD, 0xE8, 0xF6, 0x41, 0xBB, 0xE8, 0x45, 0x33, 0xBA, 0xE8, 0x00, 
        0x26, 0xB7, 0xE7, 0x00, 0x1D, 0xB5, 0xE6, 0x00, 0x19, 0xB3, 0xE5, 0x00, 0x17, 0xB3, 0xE5, 0x00, 0x17, 0xB3, 0xE5, 0x00, 0x18, 0xB3, 0xE5, 0x00, 0x1C, 0xB4, 0xE6, 0x00, 0x23, 0xB7, 0xE7, 0x00, 0x2F, 0xB9, 0xE7, 0x00, 0x3D, 0xB8, 0xE6, 0x21, 
        0x45, 0xB6, 0xE6, 0xE0, 0x48, 0xC0, 0xEA, 0xFF, 
  /* RLE: 011 Pixels @ 022,025 */ 11, 0x4A, 0xC4, 0xEB, 0xFF, 
  /* RLE: 001 Pixels @ 033,025 */ 1, 0x77, 0xD3, 0xF0, 0xFF, 
  /* RLE: 004 Pixels @ 034,025 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,026 */ 1, 0x74, 0xD1, 0xEF, 0xFF, 
  /* RLE: 004 Pixels @ 003,026 */ 4, 0x46, 0xC2, 0xEA, 0xFF, 
  /* ABS: 014 Pixels @ 007,026 */ 0, 14, 0x45, 0xC1, 0xEA, 0xFF, 0x44, 0xBB, 0xE7, 0xF6, 0x3E, 0xB9, 0xE7, 0x44, 0x2F, 0xB9, 0xE7, 0x00, 0x21, 0xB6, 0xE6, 0x00, 0x18, 0xB3, 0xE5, 0x00, 0x14, 0xB2, 0xE4, 0x00, 0x14, 0xB1, 0xE4, 0x00, 
        0x17, 0xB2, 0xE5, 0x00, 0x1E, 0xB5, 0xE5, 0x00, 0x2B, 0xB7, 0xE6, 0x00, 0x3A, 0xB6, 0xE5, 0x21, 0x42, 0xB5, 0xE5, 0xE0, 0x45, 0xBF, 0xE9, 0xFF, 
  /* RLE: 012 Pixels @ 021,026 */ 12, 0x46, 0xC2, 0xEA, 0xFF, 
  /* RLE: 001 Pixels @ 033,026 */ 1, 0x74, 0xD1, 0xEF, 0xFF, 
  /* RLE: 004 Pixels @ 034,026 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,027 */ 1, 0x72, 0xD1, 0xEF, 0xFF, 
  /* RLE: 005 Pixels @ 003,027 */ 5, 0x42, 0xC1, 0xE9, 0xFF, 
  /* ABS: 012 Pixels @ 008,027 */ 0, 12, 0x42, 0xC0, 0xE9, 0xFF, 0x41, 0xB9, 0xE6, 0xF6, 0x3A, 0xB8, 0xE6, 0x45, 0x2B, 0xB7, 0xE6, 0x00, 0x1D, 0xB4, 0xE5, 0x00, 0x15, 0xB1, 0xE4, 0x00, 0x15, 0xB1, 0xE4, 0x00, 0x1B, 0xB3, 0xE4, 0x00, 
        0x27, 0xB6, 0xE5, 0x00, 0x37, 0xB4, 0xE4, 0x21, 0x3F, 0xB3, 0xE4, 0xE0, 0x42, 0xBD, 0xE8, 0xFF, 
  /* RLE: 013 Pixels @ 020,027 */ 13, 0x42, 0xC1, 0xE9, 0xFF, 
  /* RLE: 001 Pixels @ 033,027 */ 1, 0x72, 0xD1, 0xEF, 0xFF, 
  /* RLE: 004 Pixels @ 034,027 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,028 */ 1, 0x6F, 0xCF, 0xEE, 0xFF, 
  /* RLE: 006 Pixels @ 003,028 */ 6, 0x3F, 0xBF, 0xE8, 0xFF, 
  /* ABS: 010 Pixels @ 009,028 */ 0, 10, 0x3F, 0xBE, 0xE8, 0xFF, 0x3E, 0xB8, 0xE6, 0xF6, 0x38, 0xB7, 0xE5, 0x45, 0x29, 0xB6, 0xE5, 0x00, 0x1E, 0xB4, 0xE4, 0x00, 0x1D, 0xB3, 0xE4, 0x00, 0x26, 0xB5, 0xE5, 0x00, 0x34, 0xB3, 0xE4, 0x21, 
        0x3D, 0xB2, 0xE3, 0xE0, 0x3F, 0xBC, 0xE7, 0xFF, 
  /* RLE: 014 Pixels @ 019,028 */ 14, 0x3F, 0xBF, 0xE8, 0xFF, 
  /* RLE: 001 Pixels @ 033,028 */ 1, 0x6F, 0xCF, 0xEE, 0xFF, 
  /* RLE: 004 Pixels @ 034,028 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,029 */ 1, 0x6D, 0xCE, 0xEE, 0xFF, 
  /* RLE: 008 Pixels @ 003,029 */ 8, 0x3C, 0xBD, 0xE8, 0xFF, 
  /* ABS: 007 Pixels @ 011,029 */ 0, 7, 0x3C, 0xB7, 0xE5, 0xF6, 0x37, 0xB6, 0xE5, 0x45, 0x2E, 0xB7, 0xE5, 0x00, 0x2D, 0xB6, 0xE5, 0x00, 0x35, 0xB3, 0xE3, 0x21, 0x3B, 0xB1, 0xE3, 0xE0, 0x3C, 0xBB, 0xE7, 0xFF, 
  /* RLE: 015 Pixels @ 018,029 */ 15, 0x3C, 0xBD, 0xE8, 0xFF, 
  /* RLE: 001 Pixels @ 033,029 */ 1, 0x6D, 0xCE, 0xEE, 0xFF, 
  /* RLE: 004 Pixels @ 034,029 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,030 */ 1, 0x6B, 0xCE, 0xED, 0xFF, 
  /* RLE: 008 Pixels @ 003,030 */ 8, 0x3A, 0xBD, 0xE7, 0xFF, 
  /* ABS: 006 Pixels @ 011,030 */ 0, 6, 0x3A, 0xBC, 0xE7, 0xFF, 0x3A, 0xB6, 0xE4, 0xF6, 0x39, 0xB3, 0xE3, 0x44, 0x39, 0xB1, 0xE2, 0x21, 0x39, 0xB0, 0xE2, 0xE0, 0x3A, 0xBA, 0xE6, 0xFF, 
  /* RLE: 016 Pixels @ 017,030 */ 16, 0x3A, 0xBD, 0xE7, 0xFF, 
  /* RLE: 001 Pixels @ 033,030 */ 1, 0x6B, 0xCE, 0xED, 0xFF, 
  /* RLE: 004 Pixels @ 034,030 */ 4, 0xFF, 0xFF, 0xFF, 0xFF, 
  /* RLE: 001 Pixels @ 002,031 */ 1, 0xB5, 0xE6, 0xF6, 0xFF, 
  /* RLE: 009 Pixels @ 003,031 */ 9, 0x9C, 0xDE, 0xF3, 0xFF, 
  /* ABS: 005 Pixels @ 012,031 */ 0, 5, 0x9C, 0xDD, 0xF3, 0xFF, 0x9C, 0xD9, 0xF1, 0xF6, 0x9C, 0xD8, 0xF1, 0xE4, 0x9C, 0xDB, 0xF2, 0xFF, 0x9C, 0xDD, 0xF3, 0xFF, 
  /* RLE: 016 Pixels @ 017,031 */ 16, 0x9C, 0xDE, 0xF3, 0xFF, 
  /* RLE: 001 Pixels @ 033,031 */ 1, 0xB5, 0xE6, 0xF6, 0xFF, 
  /* RLE: 146 Pixels @ 034,031 */ 146, 0xFF, 0xFF, 0xFF, 0xFF, 
  0
};  // 2323 for 1296 pixels

GUI_CONST_STORAGE GUI_BITMAP bmok = {
  36, // xSize
  36, // ySize
  144, // BytesPerLine
  32, // BitsPerPixel
  (unsigned char *)_acok,  // Pointer to picture data
  NULL,  // Pointer to palette
  GUI_DRAW_RLE32
};

extern GUI_CONST_STORAGE GUI_BITMAP bmOK_disabled;

static GUI_CONST_STORAGE unsigned char _acOK_disabled[] = {
  /* ABS: 255 Pixels @ 000,000 */ 0, 255, 0x14, 0x14, 0x13, 0xFD, 0x10, 0x10, 0x10, 0xFD, 0x17, 0x17, 0x16, 0xFD, 0x19, 0x19, 0x18, 0xFC, 0x11, 0x11, 0x10, 0xFD, 0x13, 0x13, 0x12, 0xFD, 0x1B, 0x1B, 0x1A, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x10, 0x10, 0x10, 0xFE, 0x18, 0x18, 0x17, 0xFD, 0x18, 0x18, 0x17, 0xFC, 0x11, 0x11, 0x10, 0xFD, 0x13, 0x13, 0x13, 0xFD, 0x1B, 0x1B, 0x1A, 0xFC, 0x13, 0x13, 0x12, 0xFD, 0x11, 0x11, 0x10, 0xFD, 0x19, 0x19, 0x18, 0xFC, 0x17, 0x17, 0x16, 0xFC, 
        0x11, 0x11, 0x10, 0xFD, 0x14, 0x14, 0x13, 0xFD, 0x1B, 0x1B, 0x1A, 0xFC, 0x12, 0x12, 0x12, 0xFD, 0x11, 0x11, 0x10, 0xFE, 0x1A, 0x1A, 0x19, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x11, 0x11, 0x10, 0xFD, 0x15, 0x15, 0x14, 0xFD, 0x1B, 0x1B, 0x1A, 0xFC, 
        0x12, 0x12, 0x11, 0xFD, 0x11, 0x11, 0x11, 0xFD, 0x1A, 0x1A, 0x19, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x11, 0x11, 0x10, 0xFD, 0x16, 0x16, 0x15, 0xFD, 0x1A, 0x1A, 0x19, 0xFC, 0x10, 0x10, 0x10, 0xFE, 0x10, 0x10, 0x10, 0xFD, 0x1B, 0x1B, 0x1A, 0xFB, 
        0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x17, 0x17, 0x16, 0xFD, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 
        0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 
        0x19, 0x19, 0x18, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1D, 0x1D, 0x1B, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 
        0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 
        0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFD, 0x11, 0x11, 0x10, 0xFD, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 
        0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1D, 0x1D, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x16, 0x16, 0x16, 0xFC, 0x13, 0x13, 0x12, 0xFD, 0x13, 0x13, 0x12, 0xFE, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1B, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 
        0x1D, 0x1D, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x32, 0x32, 0x30, 0xBB, 0x4A, 0x4A, 0x4A, 0x1C, 0x28, 0x28, 0x27, 0xBE, 0x15, 0x15, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFC, 0x1B, 0x1B, 0x1A, 0xFC, 0x15, 0x15, 0x15, 0xFC, 
        0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 0x4D, 0x4D, 0x4D, 0x00, 0x4B, 0x4B, 0x4B, 0x0C, 0x2A, 0x2A, 0x29, 0xBE, 
        0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x1A, 0x1A, 0x19, 0xFC, 0x13, 0x13, 0x13, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 
  /* ABS: 060 Pixels @ 003,007 */ 0, 60, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 0x4D, 0x4D, 0x4D, 0x00, 0x4D, 0x4D, 0x4D, 0x00, 0x4D, 0x4D, 0x4D, 0x00, 
        0x4B, 0x4B, 0x4B, 0x0C, 0x28, 0x28, 0x27, 0xBE, 0x1A, 0x1A, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x13, 0x13, 0x12, 0xFD, 0x10, 0x10, 0x10, 0xFE, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1D, 0x1D, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 
        0x1D, 0x1D, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1C, 0x1C, 0x1B, 0xFA, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFB, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 005 Pixels @ 027,008 */ 5, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 030 Pixels @ 032,008 */ 0, 30, 0x4C, 0x4C, 0x4C, 0x0C, 0x29, 0x29, 0x28, 0xBE, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x18, 0x18, 0x17, 0xFD, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1D, 0x1D, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 
        0x1D, 0x1D, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 007 Pixels @ 026,009 */ 7, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 028 Pixels @ 033,009 */ 0, 28, 0x4B, 0x4B, 0x4B, 0x0C, 0x27, 0x27, 0x27, 0xBF, 0x1D, 0x1D, 0x1B, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 
        0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 025,010 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 026 Pixels @ 034,010 */ 0, 26, 0x41, 0x41, 0x41, 0x3A, 0x15, 0x15, 0x14, 0xFD, 0x11, 0x11, 0x10, 0xFD, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 024,011 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 026 Pixels @ 033,011 */ 0, 26, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x13, 0x13, 0x12, 0xFD, 0x13, 0x13, 0x13, 0xFD, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x1D, 0x1D, 0x1C, 0xF1, 0x32, 0x32, 0x32, 0x84, 0x1B, 0x1B, 0x1A, 0xF0, 0x1D, 0x1D, 0x1B, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 023,012 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 026 Pixels @ 032,012 */ 0, 26, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFC, 0x1B, 0x1B, 0x1A, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x1D, 0x1D, 0x1C, 0xF1, 0x45, 0x45, 0x45, 0x41, 0x4D, 0x4D, 0x4D, 0x00, 0x45, 0x45, 0x44, 0x3F, 0x19, 0x19, 0x18, 0xF1, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 022,013 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 026 Pixels @ 031,013 */ 0, 26, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFD, 0x13, 0x13, 0x12, 0xFD, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x1D, 0x1D, 0x1C, 0xF1, 0x45, 0x45, 0x45, 0x41, 0x4D, 0x4D, 0x4D, 0x00, 0x4D, 0x4D, 0x4D, 0x00, 0x4D, 0x4D, 0x4D, 0x00, 0x3F, 0x3F, 0x3F, 0x3F, 0x1E, 0x1E, 0x1D, 0xF0, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 021,014 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 010 Pixels @ 030,014 */ 0, 10, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x13, 0x13, 0x12, 0xFD, 0x11, 0x11, 0x10, 0xFE, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x1D, 0x1D, 0x1C, 0xF1, 0x45, 0x45, 0x45, 0x41, 
  /* RLE: 005 Pixels @ 004,015 */ 5, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 011 Pixels @ 009,015 */ 0, 11, 0x42, 0x42, 0x42, 0x3F, 0x18, 0x18, 0x17, 0xF1, 0x18, 0x18, 0x18, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 020,015 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 010 Pixels @ 029,015 */ 0, 10, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x19, 0x19, 0x18, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xF1, 0x45, 0x45, 0x45, 0x41, 
  /* RLE: 007 Pixels @ 003,016 */ 7, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 009 Pixels @ 010,016 */ 0, 9, 0x42, 0x42, 0x42, 0x3F, 0x1D, 0x1D, 0x1C, 0xF0, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 
        0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 019,016 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 010 Pixels @ 028,016 */ 0, 10, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 
        0x17, 0x17, 0x16, 0xFC, 0x41, 0x41, 0x41, 0x4E, 
  /* RLE: 009 Pixels @ 002,017 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 007 Pixels @ 011,017 */ 0, 7, 0x3F, 0x3F, 0x3E, 0x40, 0x19, 0x19, 0x18, 0xF1, 0x1D, 0x1D, 0x1C, 0xFB, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 018,017 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 012 Pixels @ 027,017 */ 0, 12, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x14, 0x14, 0x13, 0xFD, 0x11, 0x11, 0x10, 0xFD, 0x31, 0x31, 0x30, 0x94, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 009 Pixels @ 003,018 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 005 Pixels @ 012,018 */ 0, 5, 0x45, 0x45, 0x45, 0x3E, 0x1A, 0x1A, 0x19, 0xF1, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x30, 0xBB, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 017,018 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 014 Pixels @ 026,018 */ 0, 14, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1D, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x13, 0x13, 0x13, 0xFD, 0x14, 0x14, 0x13, 0xFD, 0x1C, 0x1C, 0x1B, 0xFA, 0x30, 0x30, 0x2F, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 009 Pixels @ 004,019 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 003 Pixels @ 013,019 */ 0, 3, 0x3E, 0x3E, 0x3D, 0x40, 0x34, 0x34, 0x33, 0xB0, 0x4B, 0x4B, 0x4B, 0x0B, 
  /* RLE: 009 Pixels @ 016,019 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 016 Pixels @ 025,019 */ 0, 16, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFC, 0x1B, 0x1B, 0x1A, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x34, 0x34, 0x33, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 019 Pixels @ 005,020 */ 19, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 018 Pixels @ 024,020 */ 0, 18, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x18, 0x18, 0x17, 0xFD, 0x12, 0x12, 0x12, 0xFD, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFC, 0x30, 0x30, 0x2F, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 017 Pixels @ 006,021 */ 17, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 020 Pixels @ 023,021 */ 0, 20, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x13, 0x13, 0x12, 0xFD, 0x11, 0x11, 0x10, 0xFE, 0x1D, 0x1D, 0x1B, 0xFA, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 
        0x33, 0x33, 0x33, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 015 Pixels @ 007,022 */ 15, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 022 Pixels @ 022,022 */ 0, 22, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x1A, 0x1A, 0x19, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x30, 0x30, 0x2F, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 013 Pixels @ 008,023 */ 13, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 024 Pixels @ 021,023 */ 0, 24, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1B, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1C, 0x1C, 0x1B, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x18, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x31, 0x31, 0x30, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 011 Pixels @ 009,024 */ 11, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 026 Pixels @ 020,024 */ 0, 26, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x14, 0x14, 0x13, 0xFD, 0x11, 0x11, 0x10, 0xFD, 0x18, 0x18, 0x18, 0xFB, 
        0x1C, 0x1C, 0x1B, 0xFB, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x32, 0x32, 0x32, 0x93, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 009 Pixels @ 010,025 */ 9, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 028 Pixels @ 019,025 */ 0, 28, 0x44, 0x44, 0x44, 0x21, 0x1E, 0x1E, 0x1E, 0xDD, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFD, 0x15, 0x15, 0x14, 0xFD, 
        0x1C, 0x1C, 0x1B, 0xFA, 0x14, 0x14, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFB, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1C, 0x1C, 0x1A, 0xFB, 0x30, 0x30, 0x2F, 0x90, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 007 Pixels @ 011,026 */ 7, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 030 Pixels @ 018,026 */ 0, 30, 0x44, 0x44, 0x44, 0x1F, 0x1E, 0x1E, 0x1E, 0xDB, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1A, 0xFC, 
        0x1B, 0x1B, 0x1A, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 
        0x34, 0x34, 0x33, 0x8F, 0x4D, 0x4D, 0x4D, 0x01, 
  /* RLE: 005 Pixels @ 012,027 */ 5, 0x4D, 0x4D, 0x4D, 0x00, 
  /* ABS: 255 Pixels @ 017,027 */ 0, 255, 0x44, 0x44, 0x44, 0x1F, 0x1E, 0x1E, 0x1D, 0xDB, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 
        0x17, 0x17, 0x16, 0xFD, 0x12, 0x12, 0x11, 0xFD, 0x15, 0x15, 0x14, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x30, 0x30, 0x2F, 0x8F, 0x4D, 0x4D, 0x4D, 0x01, 0x4D, 0x4D, 0x4D, 0x00, 0x4D, 0x4D, 0x4D, 0x00, 0x4D, 0x4D, 0x4D, 0x00, 0x44, 0x44, 0x44, 0x1F, 0x1E, 0x1E, 0x1E, 0xDB, 0x14, 0x14, 0x13, 0xFC, 
        0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 
        0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 0x13, 0x13, 0x12, 0xFD, 0x11, 0x11, 0x11, 0xFE, 0x1D, 0x1D, 0x1C, 0xFA, 0x17, 0x17, 0x16, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x33, 0x33, 0x32, 0x8F, 
        0x4D, 0x4D, 0x4D, 0x01, 0x4D, 0x4D, 0x4D, 0x00, 0x44, 0x44, 0x44, 0x1F, 0x1E, 0x1E, 0x1E, 0xDB, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1A, 0x1A, 0x19, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1B, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x31, 0x31, 0x30, 0x90, 0x44, 0x44, 0x44, 0x23, 0x1E, 0x1E, 0x1E, 0xDB, 0x14, 0x14, 0x13, 0xFC, 
        0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 
        0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1C, 0x1C, 0x1B, 0xFC, 0x16, 0x16, 0x15, 0xFC, 
        0x14, 0x14, 0x13, 0xFC, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x15, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1D, 0x1D, 0x1B, 0xFA, 0x13, 0x13, 0x12, 0xFD, 0x11, 0x11, 0x10, 0xFD, 0x19, 0x19, 0x18, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1B, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 
        0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 0x18, 0x18, 0x17, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 
        0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFD, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 
        0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 
        0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFB, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 0x1A, 0x1A, 0x19, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x15, 0x15, 0x15, 0xFC, 
        0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x16, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1A, 0x1A, 0x19, 0xFB, 0x1B, 0x1B, 0x1A, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x15, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x16, 0x16, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
        0x1B, 0x1B, 0x1A, 0xFB, 0x1A, 0x1A, 0x19, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x16, 0x16, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x15, 0xFC, 0x14, 0x14, 0x13, 0xFC, 
  /* ABS: 052 Pixels @ 020,034 */ 0, 52, 0x1B, 0x1B, 0x1A, 0xFB, 0x19, 0x19, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x17, 0x17, 0x16, 0xFC, 0x1D, 0x1D, 0x1C, 0xFA, 0x15, 0x15, 0x14, 0xFC, 0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFB, 
        0x18, 0x18, 0x18, 0xFB, 0x14, 0x14, 0x13, 0xFC, 0x18, 0x18, 0x17, 0xFB, 0x1C, 0x1C, 0x1B, 0xFB, 0x15, 0x15, 0x14, 0xFC, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFA, 0x16, 0x16, 0x15, 0xFD, 0x10, 0x10, 0x10, 0xFE, 0x15, 0x15, 0x14, 0xFC, 
        0x1C, 0x1C, 0x1B, 0xFC, 0x15, 0x15, 0x14, 0xFD, 0x13, 0x13, 0x12, 0xFD, 0x19, 0x19, 0x18, 0xFC, 0x1A, 0x1A, 0x19, 0xFC, 0x13, 0x13, 0x12, 0xFD, 0x15, 0x15, 0x14, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 0x15, 0x15, 0x14, 0xFD, 0x13, 0x13, 0x13, 0xFD, 
        0x1A, 0x1A, 0x19, 0xFC, 0x19, 0x19, 0x18, 0xFD, 0x13, 0x13, 0x12, 0xFD, 0x16, 0x16, 0x15, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 0x14, 0x14, 0x13, 0xFD, 0x13, 0x13, 0x13, 0xFD, 0x1B, 0x1B, 0x1A, 0xFC, 0x18, 0x18, 0x17, 0xFD, 0x13, 0x13, 0x12, 0xFD, 
        0x16, 0x16, 0x15, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 0x14, 0x14, 0x13, 0xFD, 0x14, 0x14, 0x13, 0xFD, 0x1B, 0x1B, 0x1A, 0xFC, 0x17, 0x17, 0x16, 0xFD, 0x13, 0x13, 0x12, 0xFD, 0x17, 0x17, 0x16, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 0x13, 0x13, 0x13, 0xFD, 
        0x14, 0x14, 0x13, 0xFC, 0x1C, 0x1C, 0x1B, 0xFC, 0x16, 0x16, 0x16, 0xFD, 0x12, 0x12, 0x11, 0xFD, 
  0
};  // 4389 for 1296 pixels

GUI_CONST_STORAGE GUI_BITMAP bmOK_disabled = {
  36, // xSize
  36, // ySize
  144, // BytesPerLine
  32, // BitsPerPixel
  (unsigned char *)_acOK_disabled,  // Pointer to picture data
  NULL,  // Pointer to palette
  GUI_DRAW_RLE32
};

/*************************** End of file ****************************/
