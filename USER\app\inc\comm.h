/**
  *********************************************************************************************************
  * Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com 
  * @file 
  * @project ACUT_GCV1
  * <AUTHOR> YL
  * @date    
  * @version v1.0.0
  * @brief   Header file of comm.c
  * @modify
  * 20211110：yl
  *			Developing;
  * 2021-11-10：
  *   增加 调频、峰值保持、滤波 指令;
  *********************************************************************************************************
  * @attention
  ******************************************************************************
  */
#ifndef _COMM_H
#define _COMM_H
#include "bsp.h"


#define COM_RS485			    (0x01 << 0) //(UART3)
//#define COM_RS485_2			(0x01 << 1)
#define COM_RS232			    (0x01 << 2) //(UART8)
#define COM_USB				    (0x01 << 3)
#define COM_NET				    (0x01 << 4)
#define COM_RS422_1			  (0x01 << 5)
//#define COM_RS422_2			(0x01 << 6)

#define TXBUF_LEN 					    13
		
#define CACHE_DEPTH					    30
#define RXBUF_LEN					      32
#define TXWAVE_EXTRA_DATA_LEN		20

typedef struct
{
	uint8_t		devCommPort;
	//接收
	uint8_t		rxPC_cache[CACHE_DEPTH][RXBUF_LEN];		//缓存来自上位机的数据
	uint8_t		rxPC_cache_src[CACHE_DEPTH];			//指令来源(485/232/net)缓冲数组
//	uint8_t		rxPC_numInCache;						//接收PC数据的缓存数目
	uint8_t		rxPC_rdCache_id;						//当前接收数据读缓存位置
	uint8_t		rxPC_wrCache_id;						//当前接收数据写缓存位置
	//发送
	uint8_t		txBuf[TXBUF_LEN];						//平均次数, 单位次
//	uint8_t	 	uartRxFrameDone;
//	uint8_t	 	uartRxCnt;
//	uint8_t	 	uartRxBuf[50];

}COM_T;
extern COM_T g_Com;

	
#define ALL_CHN						0x00
#define READ						0x00
#define WRITE						0x01
#define OK							0x01
#define NG							0x00
			

#define CMD_SN						0x02 //
#define CMD_DEV_ADDR				0x03 //
#define CMD_COM_MODE				0x05 //通讯方式

#define CMD_RTC						0x0B //
#define CMD_PAR_RESET				0x0C //
#define CMD_READ_DEV_PAR			0x0E //读取参数
#define CMD_READ_FIRMWARE_VER		0x0F //固件版本号



//#define CMD_VELC					0x10 //声速
//#define CMD_ZEROPT				0x11 //零点
//#define CMD_CALI_VELC				0x12 //校准声速
//#define CMD_CALI_ZEROPT			0x13 //校准零点
//#define CMD_PRESET_THICK_RANGE	0x15 //预设厚度范围,mm
//#define CMD_BLIND_TIME			0x16 //盲区指定

#define CMD_EMAT_DAQ				0x10 //EMAT参数
#define CMD_EMAT_GAIN				0x11 //EMAT增益
#define CMD_EMAT_VOL				0x12 //EMAT发射电压Vpp
#define CMD_EMAT_FILTER			0x13 //滤波器
#define CMD_EMAT_HV_SW			0x14 //高压通道开关
#define CMD_PREAMP          0x15 //前放控制
#define CMD_EMIT_ENCODE			0x17 //编码发射指令(230728新增)
#define CMD_CHN_ENABLE			0x18 //通道开关
#define CMD_ENC_CLR					0x19 //清除编码器数据
#define CMD_EMAT_RUN				0x23 //EMAT启/停
#define CMD_EMAT_SWEEP			0x25 //扫频参数
#define CMD_EMAT_CHIRP			0x26 //线性调频
#define CMD_EMAT_PPKEEP			0x27 //峰值保持
#define CMD_EMAT_DATA				0x30 //数据上传


#define CMD_DBG_WAVEDATA		0xE1 //调试波形开关
#define CMD_DBG_CRC					0xE2 //CRC16开关指令


#define CMD_MSG						    0x01
#define CMD_MSG_CRC_ERR				0x01
#define CMD_MSG_ADDR_ERR			0x02


void Comm_rdCacheId_Inc(void);
void Comm_RxOneFrameDone(uint8_t *buf, uint16_t len, uint8_t commtype);


uint8_t RxDataFromUpper_Exe(uint8_t *buf, uint8_t src, uint8_t len);
uint8_t Modbus_Ack2Master(uint8_t addr, uint8_t cmd, uint8_t *buf, uint16_t datalen);
uint8_t Modbus_Ack2Master_4G(uint8_t addr, uint8_t cmd, uint8_t *buf, uint8_t datalen);
uint8_t GaugeData_Transmit(uint8_t port);
void SimGaugeTx_Continous_Task(void);

void Comm_SendWaveData(uint8_t commtype, uint8_t *pExtraBuf, uint8_t extra_len, int16_t *pWaveBuf, uint16_t wave_len);

#endif
