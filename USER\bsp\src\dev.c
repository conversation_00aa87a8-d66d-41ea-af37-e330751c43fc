#include "bsp.h"
//////////////////////////////////////////////////////////////////////////////////	 
//本程序只供学习使用，未经作者许可，不得用于其它任何用途
//Phaserise SuZhou. Yang Long.
////////////////////////////////////////////////////////////////////////////////// 	

DEV_T g_Dev;
//设备卫士初始化
void Dev_GuardInit() {
	g_Dev.guard.devWell 	= 1;
	g_Dev.guard.faultCode	= 0;
}
//设置错误码
void Dev_SetFaultCode(uint32_t code) {
	g_Dev.guard.devWell 	= 0;
	g_Dev.guard.faultCode 	|= code;
}

/**
  * @brief  设置Modbus地址
  * @param  
  * @retval None
  */
uint8_t Dev_SetModbusAddr(uint8_t addr) {
	
	if((addr > 10) || (addr <= 0))
		return 1;
	
	g_Dev.modbus_addr = addr;
	return 0; //OK 
}

