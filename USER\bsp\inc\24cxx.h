#ifndef _24CXX_H
#define _24CXX_H
#include "bsp.h"
//////////////////////////////////////////////////////////////////////////////////	 

#define AT24C01		127
#define AT24C02		255
#define AT24C04		511
#define AT24C08		1023
#define AT24C16		2047
#define AT24C32		4095
#define AT24C64		8191
#define AT24C128	16383
#define AT24C256	32767  
//STM32 F746开发板使用的是24c02，所以定义EE_TYPE为AT24C02
#define EE_TYPE AT24C128

uint8_t EEPROM_LoadData(void);
uint8_t EEPROM_SaveData(uint8_t item);
u8 AT24CXX_ReadOneByte(u16 ReadAddr);							//指定地址读取一个字节
void AT24CXX_WriteOneByte(u16 WriteAddr, u8 DataToWrite);		//指定地址写入一个字节
void AT24CXX_WriteLenByte(u16 WriteAddr, u32 DataToWrite, u8 Len);//指定地址开始写入指定长度的数据
u32 AT24CXX_ReadLenByte(u16 ReadAddr, u8 Len);					//指定地址开始读取指定长度数据
void AT24CXX_Write(u16 WriteAddr, u8* pBuffer, u16 NumToWrite);	//从指定地址开始写入指定长度的数据
void AT24CXX_Read(u16 ReadAddr, u8* pBuffer, u16 NumToRead);   	//从指定地址开始读出指定长度的数据
void AT24CXX_Write4Bytes(u16 WriteAddr, int32_t DataToWrite);
int32_t AT24CXX_Read4Bytes(u16 ReadAddr);
u8 AT24CXX_Check(void);  //检查器件
void AT24CXX_Init(void); //初始化IIC
void EEPROM_Para_Reset(void);

//IIC所有操作函数
// SDA(PE3), SCLK(PE2)
#define AT_SDA_IN()  {GPIOE->MODER &= ~(3<<(3*2)); GPIOE->MODER |= 0<<3*2;}
#define AT_SDA_OUT() {GPIOE->MODER &= ~(3<<(3*2)); GPIOE->MODER |= 1<<3*2;} 
//IO操作
#define AT_IIC_SCL(n)  (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_2,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_2,GPIO_PIN_RESET)) //SCL
#define AT_IIC_SDA(n)  (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_3,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_3,GPIO_PIN_RESET)) //SDA
#define AT_READ_SDA    HAL_GPIO_ReadPin(GPIOE,GPIO_PIN_3)  //输入SDA


void SetDevSN(char* str);

#endif
