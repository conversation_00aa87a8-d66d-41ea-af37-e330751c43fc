/*
*********************************************************************************************************
* @file 
* @project ST100_V4
* <AUTHOR>
* @date    2021/06/
* @version v1.0
* @brief
* @modify
*
* Copyright (C), 2018-2028, 苏州博昇科技有限公司, www.phaserise.com
*********************************************************************************************************
*/
#ifndef __BSP_H
#define __BSP_H


#define ACUT_53SUO 	0 //53所空耦
#define ACUT_PR111 	1 //PR111型号空耦

#include "sys.h"
#include "delay.h"
#include "mpu.h"
#include "utils.h"
#include "led.h"
#include "beep.h"
#include "usart.h"
#include "24cxx.h"
//#include "ad5304.h"
#include "ad9106.h"
#include "iwdg.h"
#include "qspi.h"
#include "rtc_rx8010.h"
#include "sdmmc_sdcard.h"
#include "sdram.h"
#include "timer.h"
#include "daq.h"
#include "fpga.h"
#include "emat.h"
//#include "ad9258.h"
#include "ad9246.h"
#include "ds18b20.h"
#include "dp83848.h"
#include "mbcrc.h"
#include "oled.h"
#include "sgm5347.h"
#include "dev.h"
#include "dac.h"
#include "fmc.h"
#include "afe.h"
#include "ad9704.h"
#include "fan.h"
#include "smbus_sw.h"
#include "ti_battery.h"

#define PI 3.1415926


//#define ACUT_53SUO 	0 //53所空耦
//#define ACUT_PR111 	1 //PR111型号空耦

#if ACUT_53SUO
	#define EXIST_PPKEEP	0
#endif
#if ACUT_PR111
	#define EXIST_PPKEEP	1
#endif


void Bsp_Init(void);

#endif

/*************************** End of file ****************************/
