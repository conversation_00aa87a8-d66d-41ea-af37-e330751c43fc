/*********************************************************************
*          Portions COPYRIGHT 2013 STMicroelectronics                *
*          Portions SEGGER Microcontroller GmbH & Co. KG             *
*        Solutions for real time microcontroller applications        *
**********************************************************************
*                                                                    *
*        (c) 1996 - 2013  SEGGER Microcontroller GmbH & Co. KG       *
*                                                                    *
*        Internet: www.segger.com    Support:  <EMAIL>    *
*                                                                    *
**********************************************************************

** emWin V5.22 - Graphical user interface for embedded applications **
All  Intellectual Property rights  in the Software belongs to  SEGGER.
emWin is protected by  international copyright laws.  Knowledge of the
source code may not be used to write a similar product.  This file may
only be used in accordance with the following terms:

The  software has  been licensed  to STMicroelectronics International
N.V. a Dutch company with a Swiss branch and its headquarters in Plan-
les-Ouates, Geneva, 39 Chemin du Champ des Filles, Switzerland for the
purposes of creating libraries for ARM Cortex-M-based 32-bit microcon_
troller products commercialized by Licensee only, sublicensed and dis_
tributed under the terms and conditions of the End User License Agree_
ment supplied by STMicroelectronics International N.V.
Full source code is available at: www.segger.com

We appreciate your understanding and fairness.
----------------------------------------------------------------------
File        : MEMDEV_Speedometer.c
Purpose     : Shows how to use memory devices for rotation.
---------------------------END-OF-HEADER------------------------------
*/

/**
  ******************************************************************************
  * @file    MEMDEV_Speedometer.c
  * <AUTHOR> Application Team
  * @version V1.1.1
  * @date    15-November-2013
  * @brief   Shows how to use memory devices for rotation.
  ******************************************************************************
  * @attention
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software 
  * distributed under the License is distributed on an "AS IS" BASIS, 
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */


#include "GUIDEMO.h"
#include "arm_math.h"

#if (SHOW_GUIDEMO_SPEEDOMETER && GUI_SUPPORT_MEMDEV)

/*********************************************************************
*
*       Defines
*
**********************************************************************
*/
#define COLOR_RING0    0xCCCCCC
#define COLOR_RING1    0x333333
#define COLOR_SCALE0   0x111111
#define COLOR_SCALE1   0x111111
#define COLOR_DIGIT    0xCCCCCC
#define COLOR_CMARK    0xCCCCCC
#define COLOR_BACK0    0xFF3333
#define COLOR_BACK1    0x550000
#define COLOR_DISPLAY0 0x000000
#define COLOR_DISPLAY1 0xCCCCCC
#define COLOR_NEEDLE   0x2080FF

#define R_SCALE             101
#define R_DIGIT              88
#define R_CHECK              75
#define R_RING              110
#define R_KNOB               10

#define YPOS_LABEL ((R_RING << 1) - 30)

#define W_RING0               7
#define W_RING1               3

#define L_CHECK0             10
#define L_CHECK1              2

#define T_MAX             10000
#define T_ROLL             2000
#define T_ROLL             2000
#define MAG                   6

#define T_MIN_FRAME_ROLL     30
#define T_MIN_FRAME_NEEDLE   30

#define MAX_SPEED           240

#define TIME_TITLE_FADE      250
#define DELAY_TITLE_FADEIN  1200
#define DELAY_TITLE_FADEOUT  500

/*********************************************************************
*
*       Static (const) data
*
**********************************************************************
*/
static const char _acText[] = "Speedometer\nDemo";

/*********************************************************************
*
*       _aNeedle
*/
static const GUI_POINT _aNeedle[] = {
  { MAG * (+ R_KNOB),       - 10},
  { MAG * (  R_CHECK),      -  3},
  { MAG * (  R_CHECK - 10),    0},
  { MAG * (  R_CHECK),      +  3},
  { MAG * (+ R_KNOB),       + 10},
};

/*********************************************************************
*
*       GUI_FontDigit11 (needed for the scale digits)
*/
GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0030[ 33] = { /* code 0030, DIGIT ZERO */
  0x1B, 0xFE, 0xA1,
  0xBF, 0xFF, 0xFA,
  0xEF, 0x21, 0xFE,
  0xFF, 0x00, 0xFF,
  0xFF, 0x00, 0xFF,
  0xFF, 0x00, 0xFF,
  0xFF, 0x00, 0xFF,
  0xFF, 0x00, 0xFF,
  0xEF, 0x12, 0xFF,
  0xAF, 0xFF, 0xFB,
  0x1B, 0xFF, 0xB1
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0031[ 22] = { /* code 0031, DIGIT ONE */
  0x00, 0x5F,
  0x06, 0xFF,
  0xDF, 0xFF,
  0x02, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF,
  0x00, 0xFF
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0032[ 33] = { /* code 0032, DIGIT TWO */
  0x2D, 0xFC, 0x20,
  0xBF, 0xFF, 0xB0,
  0xFF, 0x1C, 0xF0,
  0xFF, 0x0D, 0xE0,
  0x00, 0x5F, 0xA0,
  0x00, 0xCF, 0x40,
  0x05, 0xFA, 0x00,
  0x0D, 0xF2, 0x00,
  0x7F, 0x80, 0x00,
  0xCF, 0xFF, 0xF0,
  0xEF, 0xFF, 0xF0
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0033[ 33] = { /* code 0033, DIGIT THREE */
  0x6E, 0xFD, 0x40,
  0xEF, 0xFF, 0xC0,
  0xFF, 0x1F, 0xF0,
  0x00, 0x1F, 0xE0,
  0x00, 0xFF, 0x70,
  0x00, 0xFF, 0xD0,
  0x00, 0x2F, 0xF0,
  0xFF, 0x0F, 0xF0,
  0xFF, 0x1F, 0xE0,
  0xDF, 0xFF, 0xB0,
  0x3D, 0xFC, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0034[ 44] = { /* code 0034, DIGIT FOUR */
  0x00, 0x4F, 0xFF, 0x00,
  0x00, 0xBF, 0xFF, 0x00,
  0x02, 0xFD, 0xFF, 0x00,
  0x08, 0xF7, 0xFF, 0x00,
  0x0E, 0xF1, 0xFF, 0x00,
  0x6F, 0xA0, 0xFF, 0x00,
  0xCF, 0x30, 0xFF, 0x00,
  0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xF0,
  0x00, 0x00, 0xFF, 0x00,
  0x00, 0x00, 0xFF, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0035[ 33] = { /* code 0035, DIGIT FIVE */
  0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF,
  0xFF, 0x00, 0x00,
  0xFF, 0xAF, 0xD4,
  0xFF, 0xFF, 0xFC,
  0xFF, 0x22, 0xFF,
  0x00, 0x00, 0xFF,
  0xFF, 0x00, 0xFF,
  0xEF, 0x21, 0xFF,
  0xBF, 0xFF, 0xFB,
  0x1B, 0xFF, 0xB2
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0036[ 33] = { /* code 0036, DIGIT SIX */
  0x1A, 0xFF, 0xB2,
  0xBF, 0xFF, 0xFB,
  0xFF, 0x21, 0xFF,
  0xFF, 0x00, 0x00,
  0xFF, 0x9F, 0xD4,
  0xFF, 0xFF, 0xFD,
  0xFF, 0x22, 0xFF,
  0xFF, 0x00, 0xFF,
  0xFF, 0x22, 0xFF,
  0xBF, 0xFF, 0xFC,
  0x1B, 0xFF, 0xB2
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0037[ 33] = { /* code 0037, DIGIT SEVEN */
  0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xF0,
  0x00, 0x2F, 0xC0,
  0x00, 0x5F, 0x90,
  0x00, 0x8F, 0x60,
  0x00, 0xCF, 0x30,
  0x00, 0xFE, 0x00,
  0x03, 0xFB, 0x00,
  0x07, 0xF8, 0x00,
  0x0A, 0xF5, 0x00,
  0x0D, 0xF2, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0038[ 33] = { /* code 0038, DIGIT EIGHT */
  0x3D, 0xFD, 0x40,
  0xDF, 0xFF, 0xD0,
  0xFF, 0x1F, 0xF0,
  0xDF, 0x1F, 0xE0,
  0x7F, 0xFF, 0x70,
  0xDF, 0xFF, 0xC0,
  0xFF, 0x1F, 0xF0,
  0xFF, 0x0F, 0xF0,
  0xFF, 0x1F, 0xF0,
  0xBF, 0xFF, 0xC0,
  0x3C, 0xFD, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit11_0039[ 33] = { /* code 0039, DIGIT NINE */
  0x2B, 0xFF, 0xB1,
  0xCF, 0xFF, 0xFB,
  0xFF, 0x22, 0xFF,
  0xFF, 0x00, 0xFF,
  0xFF, 0x22, 0xFF,
  0xDF, 0xFF, 0xFF,
  0x4D, 0xF9, 0xFF,
  0x00, 0x00, 0xFF,
  0xFF, 0x12, 0xFF,
  0xBF, 0xFF, 0xFB,
  0x2B, 0xFF, 0xA1
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontDigit11_CharInfo[10] = {
   {   6,  11,   1,   4,   8, acGUI_FontDigit11_0030 } /* code 0030, DIGIT ZERO */
  ,{   4,  11,   0,   4,   5, acGUI_FontDigit11_0031 } /* code 0031, DIGIT ONE */
  ,{   5,  11,   1,   4,   7, acGUI_FontDigit11_0032 } /* code 0032, DIGIT TWO */
  ,{   5,  11,   1,   4,   7, acGUI_FontDigit11_0033 } /* code 0033, DIGIT THREE */
  ,{   7,  11,   0,   4,   7, acGUI_FontDigit11_0034 } /* code 0034, DIGIT FOUR */
  ,{   6,  11,   1,   4,   8, acGUI_FontDigit11_0035 } /* code 0035, DIGIT FIVE */
  ,{   6,  11,   1,   4,   8, acGUI_FontDigit11_0036 } /* code 0036, DIGIT SIX */
  ,{   5,  11,   0,   4,   5, acGUI_FontDigit11_0037 } /* code 0037, DIGIT SEVEN */
  ,{   5,  11,   1,   4,   7, acGUI_FontDigit11_0038 } /* code 0038, DIGIT EIGHT */
  ,{   6,  11,   1,   4,   8, acGUI_FontDigit11_0039 } /* code 0039, DIGIT NINE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontDigit11_Prop1 = {
   0x0030 /* first character */
  ,0x0039 /* last character  */
  ,&GUI_FontDigit11_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontDigit11 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,18 /* height of font  */
  ,18 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontDigit11_Prop1}
  ,18 /* Baseline */
  ,9 /* Height of lowercase characters */
  ,11 /* Height of capital characters */
};

/*********************************************************************
*
*       GUI_FontDigit19 (needed for digital display)
*/
GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0030[133] = { /* code 0030, DIGIT ZERO */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x7C, 0xFF, 0xFB, 0x40, 0x00,
  0x00, 0x1B, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x00, 0x9F, 0xFF, 0xED, 0xFF, 0xFF, 0x50,
  0x03, 0xFF, 0xFA, 0x00, 0x2D, 0xFF, 0xD0,
  0x08, 0xFF, 0xF1, 0x00, 0x05, 0xFF, 0xF3,
  0x0C, 0xFF, 0xB0, 0x00, 0x01, 0xFF, 0xF7,
  0x0E, 0xFF, 0x80, 0x00, 0x00, 0xDF, 0xFA,
  0x0F, 0xFF, 0x70, 0x00, 0x00, 0xBF, 0xFB,
  0x0F, 0xFF, 0x70, 0x00, 0x00, 0xBF, 0xFB,
  0x0F, 0xFF, 0x70, 0x00, 0x00, 0xBF, 0xFB,
  0x0E, 0xFF, 0x80, 0x00, 0x00, 0xDF, 0xF9,
  0x0C, 0xFF, 0xA0, 0x00, 0x00, 0xFF, 0xF7,
  0x08, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xF4,
  0x03, 0xFF, 0xF7, 0x00, 0x0B, 0xFF, 0xE0,
  0x00, 0xBF, 0xFF, 0xB9, 0xCF, 0xFF, 0x60,
  0x00, 0x2E, 0xFF, 0xFF, 0xFF, 0xFA, 0x00,
  0x00, 0x01, 0xAF, 0xFF, 0xFE, 0x70, 0x00,
  0x00, 0x00, 0x01, 0x44, 0x30, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0031[133] = { /* code 0031, DIGIT ONE */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xBF, 0x70,
  0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x4E, 0xFF, 0xB0,
  0x00, 0x00, 0x04, 0x8C, 0xFF, 0xFF, 0xB0,
  0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0xB0,
  0x00, 0x00, 0x2E, 0xFF, 0xFF, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x22, 0x2D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0x70,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0032[126] = { /* code 0032, DIGIT TWO */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x02, 0x8D, 0xFF, 0xFB, 0x50, 0x00,
  0x00, 0x5E, 0xFF, 0xFF, 0xFF, 0xFB, 0x10,
  0x04, 0xFF, 0xFF, 0xED, 0xFF, 0xFF, 0xA0,
  0x0D, 0xFF, 0xE4, 0x00, 0x2D, 0xFF, 0xF2,
  0x1F, 0xFF, 0x70, 0x00, 0x05, 0xFF, 0xF6,
  0x1F, 0xFF, 0x10, 0x00, 0x04, 0xFF, 0xF5,
  0x06, 0xB6, 0x00, 0x00, 0x08, 0xFF, 0xF3,
  0x00, 0x00, 0x00, 0x00, 0x6F, 0xFF, 0xC0,
  0x00, 0x00, 0x00, 0x2A, 0xFF, 0xFE, 0x30,
  0x00, 0x00, 0x06, 0xEF, 0xFF, 0xD3, 0x00,
  0x00, 0x02, 0xBF, 0xFF, 0xE7, 0x00, 0x00,
  0x00, 0x3E, 0xFF, 0xFA, 0x10, 0x00, 0x00,
  0x02, 0xEF, 0xFD, 0x40, 0x00, 0x00, 0x00,
  0x0B, 0xFF, 0xE1, 0x00, 0x00, 0x00, 0x00,
  0x2F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3,
  0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7,
  0x09, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD3
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0033[133] = { /* code 0033, DIGIT THREE */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x04, 0xAE, 0xFF, 0xEA, 0x30, 0x00,
  0x00, 0x9F, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x07, 0xFF, 0xFF, 0xDE, 0xFF, 0xFF, 0x30,
  0x0C, 0xFF, 0xD2, 0x00, 0x5F, 0xFF, 0x70,
  0x0C, 0xFF, 0x30, 0x00, 0x0C, 0xFF, 0x90,
  0x03, 0x75, 0x00, 0x00, 0x0C, 0xFF, 0x80,
  0x00, 0x00, 0x00, 0x13, 0x8F, 0xFF, 0x20,
  0x00, 0x00, 0x06, 0xFF, 0xFF, 0xE5, 0x00,
  0x00, 0x00, 0x09, 0xFF, 0xFF, 0xFA, 0x10,
  0x00, 0x00, 0x01, 0x8B, 0xEF, 0xFF, 0x90,
  0x00, 0x00, 0x00, 0x00, 0x1D, 0xFF, 0xF1,
  0x03, 0x72, 0x00, 0x00, 0x08, 0xFF, 0xF2,
  0x2F, 0xFD, 0x00, 0x00, 0x09, 0xFF, 0xF2,
  0x3F, 0xFF, 0x80, 0x00, 0x2E, 0xFF, 0xD0,
  0x0D, 0xFF, 0xFC, 0x9A, 0xEF, 0xFF, 0x70,
  0x03, 0xEF, 0xFF, 0xFF, 0xFF, 0xFA, 0x00,
  0x00, 0x2A, 0xFF, 0xFF, 0xFD, 0x60, 0x00,
  0x00, 0x00, 0x14, 0x44, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0034[133] = { /* code 0034, DIGIT FOUR */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xAF, 0xE3, 0x00,
  0x00, 0x00, 0x00, 0x08, 0xFF, 0xF7, 0x00,
  0x00, 0x00, 0x00, 0x5F, 0xFF, 0xF7, 0x00,
  0x00, 0x00, 0x02, 0xEF, 0xFF, 0xF7, 0x00,
  0x00, 0x00, 0x1C, 0xFD, 0xEF, 0xF7, 0x00,
  0x00, 0x00, 0xAF, 0xF3, 0xDF, 0xF7, 0x00,
  0x00, 0x07, 0xFF, 0x60, 0xDF, 0xF7, 0x00,
  0x00, 0x3F, 0xFA, 0x00, 0xDF, 0xF7, 0x00,
  0x01, 0xEF, 0xD1, 0x00, 0xDF, 0xF7, 0x00,
  0x0B, 0xFF, 0x30, 0x00, 0xDF, 0xF7, 0x00,
  0x7F, 0xFD, 0x99, 0x99, 0xEF, 0xFC, 0x95,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x4F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC,
  0x01, 0x22, 0x22, 0x22, 0xDF, 0xF8, 0x20,
  0x00, 0x00, 0x00, 0x00, 0xDF, 0xF7, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xDF, 0xF7, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xAF, 0xF5, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x04, 0x30, 0x00,
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0035[126] = { /* code 0035, DIGIT FIVE */
  0x00, 0x4B, 0xBB, 0xBB, 0xBB, 0xB9, 0x00,
  0x00, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x20,
  0x05, 0xFF, 0xA4, 0x44, 0x44, 0x42, 0x00,
  0x07, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x0A, 0xFF, 0x40, 0x12, 0x10, 0x00, 0x00,
  0x0C, 0xFF, 0x7D, 0xFF, 0xFD, 0x60, 0x00,
  0x0E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x00,
  0x0E, 0xFF, 0xEA, 0x8B, 0xFF, 0xFF, 0x60,
  0x04, 0xB8, 0x10, 0x00, 0x3E, 0xFF, 0xC0,
  0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0x00, 0x06, 0xFF, 0xF2,
  0x07, 0xB4, 0x00, 0x00, 0x09, 0xFF, 0xF0,
  0x3F, 0xFE, 0x30, 0x00, 0x3F, 0xFF, 0xB0,
  0x2F, 0xFF, 0xF9, 0x69, 0xFF, 0xFF, 0x40,
  0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x00, 0x5D, 0xFF, 0xFF, 0xFB, 0x40, 0x00,
  0x00, 0x00, 0x24, 0x44, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0036[133] = { /* code 0036, DIGIT SIX */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x4A, 0xEF, 0xFE, 0xA3, 0x00,
  0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0x00, 0x7F, 0xFF, 0xC8, 0x9F, 0xFF, 0xD0,
  0x01, 0xEF, 0xF9, 0x00, 0x05, 0xFF, 0xD0,
  0x06, 0xFF, 0xE0, 0x00, 0x00, 0x49, 0x30,
  0x0A, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00,
  0x0D, 0xFF, 0x73, 0xAD, 0xDC, 0x81, 0x00,
  0x0F, 0xFF, 0xDF, 0xFF, 0xFF, 0xFE, 0x30,
  0x0F, 0xFF, 0xFF, 0xC9, 0xCF, 0xFF, 0xE1,
  0x0F, 0xFF, 0xF6, 0x00, 0x07, 0xFF, 0xF7,
  0x0F, 0xFF, 0xC0, 0x00, 0x00, 0xDF, 0xFA,
  0x0E, 0xFF, 0x90, 0x00, 0x00, 0xBF, 0xFB,
  0x0B, 0xFF, 0xB0, 0x00, 0x00, 0xDF, 0xFA,
  0x06, 0xFF, 0xF4, 0x00, 0x04, 0xFF, 0xF7,
  0x00, 0xDF, 0xFE, 0x96, 0x9F, 0xFF, 0xE1,
  0x00, 0x3E, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0x00, 0x02, 0xAF, 0xFF, 0xFF, 0xB3, 0x00,
  0x00, 0x00, 0x01, 0x44, 0x41, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0037[126] = { /* code 0037, DIGIT SEVEN */
  0x3B, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0x80,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF3,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2,
  0x03, 0x44, 0x44, 0x44, 0x4D, 0xFF, 0x70,
  0x00, 0x00, 0x00, 0x00, 0x9F, 0xF9, 0x00,
  0x00, 0x00, 0x00, 0x05, 0xFF, 0xC0, 0x00,
  0x00, 0x00, 0x00, 0x1E, 0xFF, 0x30, 0x00,
  0x00, 0x00, 0x00, 0xAF, 0xF9, 0x00, 0x00,
  0x00, 0x00, 0x03, 0xFF, 0xF2, 0x00, 0x00,
  0x00, 0x00, 0x0A, 0xFF, 0xA0, 0x00, 0x00,
  0x00, 0x00, 0x3F, 0xFF, 0x40, 0x00, 0x00,
  0x00, 0x00, 0x9F, 0xFD, 0x00, 0x00, 0x00,
  0x00, 0x01, 0xEF, 0xF9, 0x00, 0x00, 0x00,
  0x00, 0x05, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x09, 0xFF, 0xF1, 0x00, 0x00, 0x00,
  0x00, 0x0B, 0xFF, 0xC0, 0x00, 0x00, 0x00,
  0x00, 0x07, 0xFF, 0x60, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x33, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0038[133] = { /* code 0038, DIGIT EIGHT */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x02, 0x9D, 0xFF, 0xFC, 0x71, 0x00,
  0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFD, 0x20,
  0x01, 0xEF, 0xFF, 0xA8, 0xBF, 0xFF, 0xB0,
  0x07, 0xFF, 0xF3, 0x00, 0x07, 0xFF, 0xF2,
  0x09, 0xFF, 0xC0, 0x00, 0x02, 0xFF, 0xF4,
  0x07, 0xFF, 0xE0, 0x00, 0x03, 0xFF, 0xF2,
  0x01, 0xEF, 0xF9, 0x32, 0x4D, 0xFF, 0xB0,
  0x00, 0x3E, 0xFF, 0xFF, 0xFF, 0xFB, 0x10,
  0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFC, 0x30,
  0x05, 0xFF, 0xFC, 0x54, 0x7E, 0xFF, 0xE2,
  0x0D, 0xFF, 0xD1, 0x00, 0x03, 0xFF, 0xF8,
  0x0F, 0xFF, 0x80, 0x00, 0x00, 0xCF, 0xFB,
  0x0F, 0xFF, 0x80, 0x00, 0x00, 0xCF, 0xFB,
  0x0D, 0xFF, 0xD1, 0x00, 0x04, 0xFF, 0xF8,
  0x07, 0xFF, 0xFD, 0x86, 0x9F, 0xFF, 0xF3,
  0x00, 0xAF, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x00, 0x06, 0xDF, 0xFF, 0xFF, 0xB4, 0x00,
  0x00, 0x00, 0x02, 0x44, 0x31, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontDigit19_0039[133] = { /* code 0039, DIGIT NINE */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x02, 0x9D, 0xFF, 0xEB, 0x40, 0x00,
  0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xF8, 0x00,
  0x03, 0xFF, 0xFF, 0xBA, 0xEF, 0xFF, 0x60,
  0x0A, 0xFF, 0xF3, 0x00, 0x0B, 0xFF, 0xE1,
  0x0E, 0xFF, 0x90, 0x00, 0x02, 0xFF, 0xF5,
  0x0F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xF8,
  0x0F, 0xFF, 0x80, 0x00, 0x01, 0xFF, 0xFB,
  0x0D, 0xFF, 0xD1, 0x00, 0x07, 0xFF, 0xFB,
  0x07, 0xFF, 0xFD, 0x76, 0xAF, 0xFF, 0xFB,
  0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xEF, 0xFA,
  0x00, 0x07, 0xDF, 0xFF, 0xD4, 0xBF, 0xF9,
  0x00, 0x00, 0x02, 0x21, 0x00, 0xDF, 0xF6,
  0x00, 0x25, 0x00, 0x00, 0x02, 0xFF, 0xF2,
  0x02, 0xEF, 0xA0, 0x00, 0x0B, 0xFF, 0xC0,
  0x03, 0xFF, 0xFB, 0x45, 0xBF, 0xFF, 0x40,
  0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x00, 0x18, 0xFF, 0xFF, 0xFC, 0x50, 0x00,
  0x00, 0x00, 0x13, 0x44, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontDigit19_CharInfo[10] = {
   {  14,  19,   0,   6,  15, acGUI_FontDigit19_0030 } /* code 0030, DIGIT ZERO */
  ,{  13,  19,  -1,   6,  15, acGUI_FontDigit19_0031 } /* code 0031, DIGIT ONE */
  ,{  14,  18,   0,   6,  15, acGUI_FontDigit19_0032 } /* code 0032, DIGIT TWO */
  ,{  14,  19,   0,   6,  15, acGUI_FontDigit19_0033 } /* code 0033, DIGIT THREE */
  ,{  14,  19,   0,   6,  15, acGUI_FontDigit19_0034 } /* code 0034, DIGIT FOUR */
  ,{  14,  18,   0,   7,  15, acGUI_FontDigit19_0035 } /* code 0035, DIGIT FIVE */
  ,{  14,  19,   0,   6,  15, acGUI_FontDigit19_0036 } /* code 0036, DIGIT SIX */
  ,{  14,  18,   0,   7,  15, acGUI_FontDigit19_0037 } /* code 0037, DIGIT SEVEN */
  ,{  14,  19,   0,   6,  15, acGUI_FontDigit19_0038 } /* code 0038, DIGIT EIGHT */
  ,{  14,  19,   0,   6,  15, acGUI_FontDigit19_0039 } /* code 0039, DIGIT NINE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontDigit19_Prop1 = {
   0x0030 /* first character */
  ,0x0039 /* last character  */
  ,&GUI_FontDigit19_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontDigit19 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,30 /* height of font  */
  ,30 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontDigit19_Prop1}
  ,30 /* Baseline */
  ,14 /* Height of lowercase characters */
  ,19 /* Height of capital characters */
};

/*********************************************************************
*
*       GUI_FontRounded33
*/
GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0020[  1] = { /* code 0020, SPACE */
  0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0021[ 63] = { /* code 0021, EXCLAMATION MARK */
  0x2A, 0xB7, 0x00,
  0xBF, 0xFF, 0x40,
  0xDF, 0xFF, 0x60,
  0xDF, 0xFF, 0x60,
  0xDF, 0xFF, 0x60,
  0xDF, 0xFF, 0x60,
  0xCF, 0xFF, 0x50,
  0xAF, 0xFF, 0x30,
  0x8F, 0xFF, 0x10,
  0x6F, 0xFE, 0x00,
  0x4F, 0xFC, 0x00,
  0x2F, 0xFA, 0x00,
  0x0F, 0xF8, 0x00,
  0x0B, 0xF4, 0x00,
  0x00, 0x00, 0x00,
  0x02, 0x30, 0x00,
  0x6F, 0xFC, 0x10,
  0xDF, 0xFF, 0x60,
  0xDF, 0xFF, 0x60,
  0x5F, 0xFB, 0x10,
  0x01, 0x20, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0022[ 45] = { /* code 0022, QUOTATION MARK */
  0x06, 0xB8, 0x00, 0x3B, 0xA1,
  0x1F, 0xFF, 0x50, 0xCF, 0xF8,
  0x2F, 0xFF, 0x60, 0xDF, 0xF9,
  0x2F, 0xFF, 0x60, 0xDF, 0xF9,
  0x2F, 0xFF, 0x60, 0xDF, 0xF9,
  0x2F, 0xFF, 0x60, 0xDF, 0xF9,
  0x2F, 0xFF, 0x60, 0xDF, 0xF9,
  0x0D, 0xFF, 0x30, 0xAF, 0xF6,
  0x01, 0x63, 0x00, 0x05, 0x40
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0023[168] = { /* code 0023, NUMBER SIGN */
  0x00, 0x00, 0x02, 0x20, 0x00, 0x12, 0x00, 0x00,
  0x00, 0x00, 0x4F, 0xF2, 0x03, 0xFF, 0x40, 0x00,
  0x00, 0x00, 0x8F, 0xF3, 0x07, 0xFF, 0x50, 0x00,
  0x00, 0x00, 0xAF, 0xF1, 0x08, 0xFF, 0x30, 0x00,
  0x00, 0x00, 0xCF, 0xE0, 0x0B, 0xFF, 0x10, 0x00,
  0x00, 0x00, 0xFF, 0xC0, 0x0C, 0xFE, 0x00, 0x00,
  0x03, 0x9A, 0xFF, 0xD9, 0x9F, 0xFE, 0x97, 0x00,
  0x0B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x00,
  0x00, 0x08, 0xFF, 0x30, 0x6F, 0xF5, 0x00, 0x00,
  0x00, 0x0A, 0xFF, 0x10, 0x8F, 0xF3, 0x00, 0x00,
  0x00, 0x0C, 0xFE, 0x00, 0xAF, 0xF1, 0x00, 0x00,
  0x3B, 0xBF, 0xFF, 0xBB, 0xEF, 0xFB, 0xA1, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x00,
  0x3B, 0xCF, 0xFD, 0xBC, 0xFF, 0xEB, 0xA1, 0x00,
  0x00, 0x6F, 0xF6, 0x03, 0xFF, 0x80, 0x00, 0x00,
  0x00, 0x8F, 0xF3, 0x06, 0xFF, 0x60, 0x00, 0x00,
  0x00, 0xAF, 0xF2, 0x08, 0xFF, 0x30, 0x00, 0x00,
  0x00, 0xCF, 0xF0, 0x0A, 0xFF, 0x20, 0x00, 0x00,
  0x00, 0xCF, 0xB0, 0x0A, 0xFD, 0x00, 0x00, 0x00,
  0x00, 0x15, 0x10, 0x01, 0x52, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0024[192] = { /* code 0024, DOLLAR SIGN */
  0x00, 0x00, 0x00, 0x03, 0xA0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x19, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x01, 0x8D, 0xFF, 0xFF, 0xFC, 0x71, 0x00,
  0x00, 0x3E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x40,
  0x02, 0xEF, 0xFF, 0xBC, 0xFA, 0xCF, 0xFF, 0xF3,
  0x09, 0xFF, 0xE3, 0x07, 0xF2, 0x08, 0xFF, 0xF7,
  0x0D, 0xFF, 0x80, 0x07, 0xF2, 0x00, 0xAF, 0xF6,
  0x0D, 0xFF, 0x90, 0x07, 0xF2, 0x00, 0x17, 0x50,
  0x0C, 0xFF, 0xF5, 0x07, 0xF2, 0x00, 0x00, 0x00,
  0x07, 0xFF, 0xFF, 0xDC, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0xEA, 0x50, 0x00,
  0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x20,
  0x00, 0x00, 0x17, 0xBE, 0xFF, 0xFF, 0xFF, 0xD1,
  0x00, 0x00, 0x00, 0x07, 0xF7, 0xAF, 0xFF, 0xF7,
  0x02, 0x41, 0x00, 0x07, 0xF2, 0x03, 0xEF, 0xFB,
  0x2E, 0xFD, 0x00, 0x07, 0xF2, 0x00, 0x9F, 0xFD,
  0x5F, 0xFF, 0x50, 0x07, 0xF2, 0x00, 0x9F, 0xFC,
  0x2F, 0xFF, 0xC1, 0x07, 0xF2, 0x03, 0xFF, 0xF8,
  0x09, 0xFF, 0xFD, 0x8A, 0xF8, 0xAF, 0xFF, 0xE2,
  0x01, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50,
  0x00, 0x06, 0xCF, 0xFF, 0xFF, 0xFE, 0xA2, 0x00,
  0x00, 0x00, 0x02, 0x4A, 0xF6, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x07, 0xF2, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x05, 0xD1, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0025[252] = { /* code 0025, PERCENT SIGN */
  0x00, 0x01, 0x32, 0x00, 0x00, 0x00, 0x00, 0x04, 0xD5, 0x00, 0x00, 0x00,
  0x02, 0xBF, 0xFF, 0xD5, 0x00, 0x00, 0x00, 0x0D, 0xF7, 0x00, 0x00, 0x00,
  0x1E, 0xFF, 0xEF, 0xFF, 0x50, 0x00, 0x00, 0x6F, 0xE1, 0x00, 0x00, 0x00,
  0x7F, 0xF9, 0x04, 0xFF, 0xC0, 0x00, 0x00, 0xEF, 0x70, 0x00, 0x00, 0x00,
  0xBF, 0xF3, 0x00, 0xDF, 0xF1, 0x00, 0x07, 0xFE, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xF1, 0x00, 0xBF, 0xF3, 0x00, 0x1E, 0xF6, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xF1, 0x00, 0xBF, 0xF3, 0x00, 0x7F, 0xD0, 0x00, 0x00, 0x00, 0x00,
  0xBF, 0xF3, 0x00, 0xDF, 0xF1, 0x01, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFA, 0x05, 0xFF, 0xC0, 0x08, 0xFC, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x1D, 0xFF, 0xFF, 0xFF, 0x40, 0x2F, 0xF4, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xAF, 0xFF, 0xC4, 0x00, 0x9F, 0xB0, 0x02, 0xAE, 0xFD, 0x81, 0x00,
  0x00, 0x00, 0x21, 0x00, 0x03, 0xFF, 0x30, 0x3E, 0xFF, 0xFF, 0xFB, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0A, 0xFA, 0x00, 0xBF, 0xF8, 0x2C, 0xFF, 0x60,
  0x00, 0x00, 0x00, 0x00, 0x3F, 0xF3, 0x01, 0xFF, 0xE0, 0x04, 0xFF, 0xA0,
  0x00, 0x00, 0x00, 0x00, 0xBF, 0x90, 0x03, 0xFF, 0xB0, 0x01, 0xFF, 0xD0,
  0x00, 0x00, 0x00, 0x04, 0xFF, 0x20, 0x03, 0xFF, 0xB0, 0x00, 0xFF, 0xD0,
  0x00, 0x00, 0x00, 0x0C, 0xF8, 0x00, 0x01, 0xFF, 0xC0, 0x03, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x5F, 0xE1, 0x00, 0x00, 0xDF, 0xF3, 0x07, 0xFF, 0x80,
  0x00, 0x00, 0x00, 0xDF, 0x70, 0x00, 0x00, 0x6F, 0xFE, 0xCF, 0xFE, 0x20,
  0x00, 0x00, 0x05, 0xFE, 0x10, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xD3, 0x00,
  0x00, 0x00, 0x06, 0xF6, 0x00, 0x00, 0x00, 0x00, 0x14, 0x63, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0026[189] = { /* code 0026, AMPERSAND */
  0x00, 0x00, 0x03, 0x79, 0x97, 0x30, 0x00, 0x00, 0x00,
  0x00, 0x01, 0xAF, 0xFF, 0xFF, 0xFB, 0x10, 0x00, 0x00,
  0x00, 0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00,
  0x00, 0x2F, 0xFF, 0xC3, 0x39, 0xFF, 0xF4, 0x00, 0x00,
  0x00, 0x6F, 0xFF, 0x30, 0x00, 0xEF, 0xF7, 0x00, 0x00,
  0x00, 0x6F, 0xFF, 0x30, 0x00, 0xEF, 0xF7, 0x00, 0x00,
  0x00, 0x3F, 0xFF, 0xB0, 0x07, 0xFF, 0xF5, 0x00, 0x00,
  0x00, 0x0B, 0xFF, 0xFA, 0x9F, 0xFF, 0xD0, 0x00, 0x00,
  0x00, 0x02, 0xEF, 0xFF, 0xFF, 0xFE, 0x30, 0x00, 0x00,
  0x00, 0x07, 0xEF, 0xFF, 0xFF, 0xB2, 0x00, 0x00, 0x00,
  0x01, 0xBF, 0xFF, 0xFF, 0xFF, 0xA0, 0x00, 0x69, 0x40,
  0x0A, 0xFF, 0xFF, 0xAE, 0xFF, 0xF8, 0x04, 0xFF, 0xF1,
  0x4F, 0xFF, 0xE4, 0x05, 0xFF, 0xFF, 0x6C, 0xFF, 0xE0,
  0x9F, 0xFF, 0x60, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x80,
  0xBF, 0xFF, 0x20, 0x00, 0x09, 0xFF, 0xFF, 0xFD, 0x10,
  0xBF, 0xFF, 0x40, 0x00, 0x00, 0xDF, 0xFF, 0xF5, 0x00,
  0x9F, 0xFF, 0xC1, 0x00, 0x1A, 0xFF, 0xFF, 0xFD, 0x10,
  0x3F, 0xFF, 0xFE, 0xBC, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x4E, 0xFF, 0xF1,
  0x00, 0x5D, 0xFF, 0xFF, 0xFD, 0x70, 0x03, 0xEF, 0xA0,
  0x00, 0x00, 0x35, 0x64, 0x20, 0x00, 0x00, 0x12, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0027[ 18] = { /* code 0027, APOSTROPHE */
  0x19, 0xB5,
  0x6F, 0xFE,
  0x7F, 0xFF,
  0x7F, 0xFF,
  0x7F, 0xFF,
  0x7F, 0xFF,
  0x7F, 0xFF,
  0x4F, 0xFC,
  0x03, 0x51
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0028[104] = { /* code 0028, LEFT PARENTHESIS */
  0x00, 0x00, 0x6D, 0x90,
  0x00, 0x01, 0xEF, 0xF0,
  0x00, 0x08, 0xFF, 0xB0,
  0x00, 0x1E, 0xFF, 0x70,
  0x00, 0x6F, 0xFF, 0x10,
  0x00, 0xCF, 0xFB, 0x00,
  0x02, 0xFF, 0xF7, 0x00,
  0x06, 0xFF, 0xF3, 0x00,
  0x0A, 0xFF, 0xF0, 0x00,
  0x0D, 0xFF, 0xC0, 0x00,
  0x1F, 0xFF, 0xB0, 0x00,
  0x2F, 0xFF, 0x90, 0x00,
  0x2F, 0xFF, 0x90, 0x00,
  0x2F, 0xFF, 0x90, 0x00,
  0x2F, 0xFF, 0xA0, 0x00,
  0x0F, 0xFF, 0xB0, 0x00,
  0x0C, 0xFF, 0xE0, 0x00,
  0x08, 0xFF, 0xF2, 0x00,
  0x04, 0xFF, 0xF5, 0x00,
  0x00, 0xEF, 0xF9, 0x00,
  0x00, 0x9F, 0xFD, 0x00,
  0x00, 0x3F, 0xFF, 0x30,
  0x00, 0x0C, 0xFF, 0x90,
  0x00, 0x05, 0xFF, 0xE0,
  0x00, 0x00, 0xCF, 0xE0,
  0x00, 0x00, 0x16, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0029[104] = { /* code 0029, RIGHT PARENTHESIS */
  0x9D, 0x60, 0x00, 0x00,
  0xFF, 0xE1, 0x00, 0x00,
  0xBF, 0xF8, 0x00, 0x00,
  0x7F, 0xFE, 0x00, 0x00,
  0x2F, 0xFF, 0x60, 0x00,
  0x0B, 0xFF, 0xC0, 0x00,
  0x07, 0xFF, 0xF2, 0x00,
  0x04, 0xFF, 0xF6, 0x00,
  0x00, 0xFF, 0xFA, 0x00,
  0x00, 0xCF, 0xFD, 0x00,
  0x00, 0xBF, 0xFF, 0x10,
  0x00, 0x9F, 0xFF, 0x20,
  0x00, 0x9F, 0xFF, 0x20,
  0x00, 0x9F, 0xFF, 0x20,
  0x00, 0xAF, 0xFF, 0x20,
  0x00, 0xBF, 0xFF, 0x00,
  0x00, 0xEF, 0xFC, 0x00,
  0x02, 0xFF, 0xF8, 0x00,
  0x05, 0xFF, 0xF4, 0x00,
  0x09, 0xFF, 0xE0, 0x00,
  0x0E, 0xFF, 0x90, 0x00,
  0x3F, 0xFF, 0x30, 0x00,
  0x9F, 0xFB, 0x00, 0x00,
  0xEF, 0xF5, 0x00, 0x00,
  0xEF, 0xC0, 0x00, 0x00,
  0x36, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_002A[ 60] = { /* code 002A, ASTERISK */
  0x00, 0x00, 0x7B, 0x20, 0x00, 0x00,
  0x00, 0x00, 0xDF, 0x60, 0x00, 0x00,
  0x01, 0x00, 0xDF, 0x60, 0x01, 0x00,
  0x8F, 0xB6, 0xEF, 0x88, 0xEE, 0x20,
  0xAF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30,
  0x04, 0x9D, 0xFF, 0xFB, 0x62, 0x00,
  0x00, 0x1D, 0xFF, 0xF7, 0x00, 0x00,
  0x00, 0xBF, 0xE9, 0xFF, 0x40, 0x00,
  0x05, 0xFF, 0x60, 0xCF, 0xC0, 0x00,
  0x02, 0xC9, 0x00, 0x3D, 0x80, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_002B[128] = { /* code 002B, PLUS SIGN */
  0x00, 0x00, 0x00, 0x02, 0x30, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x3F, 0xF6, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x02, 0x44, 0x44, 0x9F, 0xFC, 0x44, 0x44, 0x30,
  0x4F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB,
  0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFA, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x2D, 0xE4, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_002C[ 27] = { /* code 002C, COMMA */
  0x00, 0x23, 0x00,
  0x07, 0xFF, 0xB0,
  0x0F, 0xFF, 0xF6,
  0x0F, 0xFF, 0xF9,
  0x07, 0xFF, 0xF9,
  0x00, 0x05, 0xF8,
  0x00, 0x2D, 0xF3,
  0x08, 0xFF, 0x80,
  0x0C, 0xC5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_002D[ 25] = { /* code 002D, HYPHEN-MINUS */
  0x03, 0x56, 0x66, 0x53, 0x00,
  0x9F, 0xFF, 0xFF, 0xFF, 0x90,
  0xDF, 0xFF, 0xFF, 0xFF, 0xD0,
  0xAF, 0xFF, 0xFF, 0xFF, 0xA0,
  0x04, 0x66, 0x66, 0x64, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_002E[ 18] = { /* code 002E, FULL STOP */
  0x00, 0x23, 0x00,
  0x07, 0xFF, 0xB0,
  0x0F, 0xFF, 0xF4,
  0x0F, 0xFF, 0xF4,
  0x06, 0xFF, 0xA0,
  0x00, 0x12, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_002F[105] = { /* code 002F, SOLIDUS */
  0x00, 0x00, 0x00, 0x09, 0xD5,
  0x00, 0x00, 0x00, 0x3F, 0xFB,
  0x00, 0x00, 0x00, 0x9F, 0xF8,
  0x00, 0x00, 0x00, 0xEF, 0xF2,
  0x00, 0x00, 0x05, 0xFF, 0xB0,
  0x00, 0x00, 0x0B, 0xFF, 0x60,
  0x00, 0x00, 0x2F, 0xFE, 0x10,
  0x00, 0x00, 0x7F, 0xF9, 0x00,
  0x00, 0x00, 0xDF, 0xF3, 0x00,
  0x00, 0x04, 0xFF, 0xD0, 0x00,
  0x00, 0x0A, 0xFF, 0x70, 0x00,
  0x00, 0x1F, 0xFF, 0x20, 0x00,
  0x00, 0x6F, 0xFB, 0x00, 0x00,
  0x00, 0xCF, 0xF5, 0x00, 0x00,
  0x02, 0xFF, 0xE0, 0x00, 0x00,
  0x08, 0xFF, 0x90, 0x00, 0x00,
  0x0E, 0xFF, 0x30, 0x00, 0x00,
  0x4F, 0xFC, 0x00, 0x00, 0x00,
  0xAF, 0xF7, 0x00, 0x00, 0x00,
  0xAF, 0xE1, 0x00, 0x00, 0x00,
  0x17, 0x30, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0030[168] = { /* code 0030, DIGIT ZERO */
  0x00, 0x00, 0x00, 0x23, 0x10, 0x00, 0x00, 0x00,
  0x00, 0x03, 0xBF, 0xFF, 0xFD, 0x70, 0x00, 0x00,
  0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFC, 0x10, 0x00,
  0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0x0C, 0xFF, 0xFA, 0x31, 0x5E, 0xFF, 0xF5, 0x00,
  0x3F, 0xFF, 0xD0, 0x00, 0x06, 0xFF, 0xFB, 0x00,
  0x8F, 0xFF, 0x70, 0x00, 0x00, 0xEF, 0xFF, 0x10,
  0xBF, 0xFF, 0x40, 0x00, 0x00, 0xBF, 0xFF, 0x40,
  0xDF, 0xFF, 0x20, 0x00, 0x00, 0x9F, 0xFF, 0x60,
  0xEF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0xFF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0xDF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0xCF, 0xFF, 0x10, 0x00, 0x00, 0x8F, 0xFF, 0x50,
  0xAF, 0xFF, 0x30, 0x00, 0x00, 0xAF, 0xFF, 0x30,
  0x7F, 0xFF, 0x60, 0x00, 0x00, 0xDF, 0xFF, 0x10,
  0x3F, 0xFF, 0xB0, 0x00, 0x04, 0xFF, 0xFB, 0x00,
  0x0C, 0xFF, 0xF8, 0x00, 0x3D, 0xFF, 0xF5, 0x00,
  0x04, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xB0, 0x00,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFD, 0x10, 0x00,
  0x00, 0x04, 0xDF, 0xFF, 0xFF, 0x91, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x56, 0x31, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0031[105] = { /* code 0031, DIGIT ONE */
  0x00, 0x00, 0x00, 0x01, 0x20,
  0x00, 0x00, 0x00, 0x4F, 0xF7,
  0x00, 0x00, 0x00, 0xDF, 0xFB,
  0x00, 0x00, 0x08, 0xFF, 0xFB,
  0x00, 0x37, 0xBF, 0xFF, 0xFB,
  0x1D, 0xFF, 0xFF, 0xFF, 0xFB,
  0x3F, 0xFF, 0xFF, 0xFF, 0xFB,
  0x09, 0xBB, 0xBD, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x06, 0xFF, 0xFB,
  0x00, 0x00, 0x05, 0xFF, 0xFA,
  0x00, 0x00, 0x01, 0xEF, 0xF5,
  0x00, 0x00, 0x00, 0x15, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0032[160] = { /* code 0032, DIGIT TWO */
  0x00, 0x00, 0x00, 0x13, 0x32, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x6C, 0xFF, 0xFF, 0xD8, 0x10, 0x00,
  0x00, 0x1C, 0xFF, 0xFF, 0xFF, 0xFF, 0xE3, 0x00,
  0x01, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x30,
  0x08, 0xFF, 0xFE, 0x61, 0x15, 0xEF, 0xFF, 0xB0,
  0x0E, 0xFF, 0xF4, 0x00, 0x00, 0x5F, 0xFF, 0xF0,
  0x0F, 0xFF, 0xC0, 0x00, 0x00, 0x1F, 0xFF, 0xF0,
  0x0D, 0xFF, 0x60, 0x00, 0x00, 0x2F, 0xFF, 0xF0,
  0x01, 0x55, 0x00, 0x00, 0x00, 0x8F, 0xFF, 0xB0,
  0x00, 0x00, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x40,
  0x00, 0x00, 0x00, 0x03, 0xCF, 0xFF, 0xF9, 0x00,
  0x00, 0x00, 0x01, 0x8F, 0xFF, 0xFF, 0x70, 0x00,
  0x00, 0x00, 0x3D, 0xFF, 0xFF, 0xB3, 0x00, 0x00,
  0x00, 0x06, 0xFF, 0xFF, 0xD5, 0x00, 0x00, 0x00,
  0x00, 0x7F, 0xFF, 0xF8, 0x10, 0x00, 0x00, 0x00,
  0x04, 0xFF, 0xFE, 0x40, 0x00, 0x00, 0x00, 0x00,
  0x0C, 0xFF, 0xFA, 0x66, 0x66, 0x66, 0x66, 0x20,
  0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1,
  0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2,
  0x07, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0033[168] = { /* code 0033, DIGIT THREE */
  0x00, 0x00, 0x00, 0x13, 0x31, 0x00, 0x00, 0x00,
  0x00, 0x01, 0x8D, 0xFF, 0xFF, 0xD7, 0x00, 0x00,
  0x00, 0x5E, 0xFF, 0xFF, 0xFF, 0xFF, 0xB1, 0x00,
  0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00,
  0x09, 0xFF, 0xFD, 0x41, 0x29, 0xFF, 0xFE, 0x00,
  0x0B, 0xFF, 0xE2, 0x00, 0x00, 0xBF, 0xFF, 0x30,
  0x06, 0xFF, 0x50, 0x00, 0x00, 0x9F, 0xFF, 0x30,
  0x00, 0x11, 0x00, 0x00, 0x00, 0xCF, 0xFE, 0x00,
  0x00, 0x00, 0x00, 0x15, 0x7C, 0xFF, 0xF7, 0x00,
  0x00, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 0x70, 0x00,
  0x00, 0x00, 0x00, 0xEF, 0xFF, 0xFF, 0xE5, 0x00,
  0x00, 0x00, 0x00, 0x4A, 0xCF, 0xFF, 0xFF, 0x30,
  0x00, 0x00, 0x00, 0x00, 0x02, 0xDF, 0xFF, 0x90,
  0x00, 0x31, 0x00, 0x00, 0x00, 0x6F, 0xFF, 0xB0,
  0x0B, 0xFE, 0x30, 0x00, 0x00, 0x6F, 0xFF, 0xB0,
  0x2F, 0xFF, 0xB0, 0x00, 0x00, 0x9F, 0xFF, 0xA0,
  0x1F, 0xFF, 0xF8, 0x00, 0x06, 0xFF, 0xFF, 0x50,
  0x09, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF, 0xFC, 0x00,
  0x01, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD1, 0x00,
  0x00, 0x07, 0xDF, 0xFF, 0xFF, 0xD7, 0x10, 0x00,
  0x00, 0x00, 0x03, 0x46, 0x42, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0034[168] = { /* code 0034, DIGIT FOUR */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0B, 0xFF, 0x90, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x8F, 0xFF, 0xE0, 0x00,
  0x00, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x00, 0x2E, 0xFF, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x01, 0xDF, 0xFF, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x0A, 0xFF, 0x7D, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x7F, 0xFB, 0x0D, 0xFF, 0xF0, 0x00,
  0x00, 0x04, 0xFF, 0xD1, 0x0D, 0xFF, 0xF0, 0x00,
  0x00, 0x2E, 0xFF, 0x30, 0x0D, 0xFF, 0xF0, 0x00,
  0x00, 0xCF, 0xF7, 0x00, 0x0D, 0xFF, 0xF0, 0x00,
  0x09, 0xFF, 0xB0, 0x00, 0x0D, 0xFF, 0xF0, 0x00,
  0x4F, 0xFF, 0x76, 0x66, 0x6E, 0xFF, 0xF6, 0x51,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC,
  0x08, 0xBB, 0xBB, 0xBB, 0xBF, 0xFF, 0xFB, 0xA3,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xF0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xB0, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0035[160] = { /* code 0035, DIGIT FIVE */
  0x00, 0x2B, 0xDD, 0xDD, 0xDD, 0xDD, 0xC4, 0x00,
  0x00, 0xAF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00,
  0x00, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x00,
  0x01, 0xFF, 0xFB, 0x99, 0x99, 0x99, 0x72, 0x00,
  0x03, 0xFF, 0xF1, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x06, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x08, 0xFF, 0xB0, 0x58, 0x97, 0x40, 0x00, 0x00,
  0x0B, 0xFF, 0xCE, 0xFF, 0xFF, 0xFD, 0x40, 0x00,
  0x0D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x00,
  0x0C, 0xFF, 0xFC, 0x87, 0xBF, 0xFF, 0xFE, 0x10,
  0x03, 0xBB, 0x50, 0x00, 0x03, 0xEF, 0xFF, 0x60,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xA0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x52, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xA0,
  0x0C, 0xFE, 0x30, 0x00, 0x00, 0xBF, 0xFF, 0x70,
  0x2F, 0xFF, 0xE3, 0x00, 0x08, 0xFF, 0xFF, 0x30,
  0x0D, 0xFF, 0xFF, 0xCB, 0xEF, 0xFF, 0xF9, 0x00,
  0x03, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0, 0x00,
  0x00, 0x29, 0xFF, 0xFF, 0xFF, 0xC5, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x56, 0x41, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0036[168] = { /* code 0036, DIGIT SIX */
  0x00, 0x00, 0x00, 0x13, 0x31, 0x00, 0x00, 0x00,
  0x00, 0x01, 0x7D, 0xFF, 0xFF, 0xD6, 0x00, 0x00,
  0x00, 0x2D, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0x01, 0xDF, 0xFF, 0xEB, 0xCF, 0xFF, 0xF6, 0x00,
  0x08, 0xFF, 0xF9, 0x00, 0x06, 0xFF, 0xF8, 0x00,
  0x1F, 0xFF, 0xC0, 0x00, 0x00, 0x6F, 0xD2, 0x00,
  0x5F, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x9F, 0xFF, 0x20, 0x14, 0x43, 0x00, 0x00, 0x00,
  0xBF, 0xFF, 0x2A, 0xFF, 0xFF, 0xE8, 0x10, 0x00,
  0xDF, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00,
  0xEF, 0xFF, 0xFF, 0xB9, 0xBF, 0xFF, 0xFA, 0x00,
  0xFF, 0xFF, 0xE3, 0x00, 0x05, 0xFF, 0xFF, 0x20,
  0xEF, 0xFF, 0x70, 0x00, 0x00, 0xAF, 0xFF, 0x50,
  0xCF, 0xFF, 0x40, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0xAF, 0xFF, 0x40, 0x00, 0x00, 0x8F, 0xFF, 0x60,
  0x6F, 0xFF, 0x80, 0x00, 0x00, 0xBF, 0xFF, 0x40,
  0x1E, 0xFF, 0xF4, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x07, 0xFF, 0xFF, 0xDB, 0xDF, 0xFF, 0xF7, 0x00,
  0x00, 0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00,
  0x00, 0x05, 0xDF, 0xFF, 0xFF, 0xD5, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x46, 0x42, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0037[160] = { /* code 0037, DIGIT SEVEN */
  0x3C, 0xDD, 0xDD, 0xDD, 0xDD, 0xDD, 0xDC, 0x40,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0,
  0xAF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0,
  0x17, 0x99, 0x99, 0x99, 0x99, 0xCF, 0xFF, 0x40,
  0x00, 0x00, 0x00, 0x00, 0x03, 0xEF, 0xF7, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x1E, 0xFF, 0x90, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xBF, 0xFD, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x05, 0xFF, 0xF4, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x1E, 0xFF, 0xA0, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFF, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x01, 0xEF, 0xFB, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x07, 0xFF, 0xF5, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x0E, 0xFF, 0xE0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x5F, 0xFF, 0xA0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xAF, 0xFF, 0x50, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0xFF, 0x10, 0x00, 0x00, 0x00,
  0x00, 0x03, 0xFF, 0xFD, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x04, 0xFF, 0xF9, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x01, 0xEF, 0xF3, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x25, 0x30, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0038[168] = { /* code 0038, DIGIT EIGHT */
  0x00, 0x00, 0x01, 0x33, 0x20, 0x00, 0x00, 0x00,
  0x00, 0x07, 0xCF, 0xFF, 0xFE, 0xA3, 0x00, 0x00,
  0x01, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00,
  0x0B, 0xFF, 0xFF, 0xCB, 0xEF, 0xFF, 0xF4, 0x00,
  0x3F, 0xFF, 0xE3, 0x00, 0x08, 0xFF, 0xFB, 0x00,
  0x6F, 0xFF, 0x70, 0x00, 0x00, 0xEF, 0xFD, 0x00,
  0x5F, 0xFF, 0x60, 0x00, 0x00, 0xDF, 0xFD, 0x00,
  0x2F, 0xFF, 0xB0, 0x00, 0x03, 0xFF, 0xFA, 0x00,
  0x09, 0xFF, 0xFB, 0x66, 0x8E, 0xFF, 0xE2, 0x00,
  0x00, 0x8F, 0xFF, 0xFF, 0xFF, 0xFD, 0x30, 0x00,
  0x03, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0x91, 0x00,
  0x3E, 0xFF, 0xFB, 0x54, 0x7E, 0xFF, 0xFA, 0x00,
  0xAF, 0xFF, 0xA0, 0x00, 0x02, 0xEF, 0xFF, 0x20,
  0xDF, 0xFF, 0x20, 0x00, 0x00, 0x9F, 0xFF, 0x60,
  0xFF, 0xFF, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0xDF, 0xFF, 0x30, 0x00, 0x00, 0xAF, 0xFF, 0x50,
  0x9F, 0xFF, 0xD2, 0x00, 0x07, 0xFF, 0xFF, 0x20,
  0x2E, 0xFF, 0xFF, 0xCB, 0xEF, 0xFF, 0xF9, 0x00,
  0x04, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0x00, 0x29, 0xEF, 0xFF, 0xFF, 0xD6, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x56, 0x42, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0039[168] = { /* code 0039, DIGIT NINE */
  0x00, 0x00, 0x01, 0x33, 0x10, 0x00, 0x00, 0x00,
  0x00, 0x07, 0xDF, 0xFF, 0xFD, 0x70, 0x00, 0x00,
  0x01, 0xCF, 0xFF, 0xFF, 0xFF, 0xFC, 0x10, 0x00,
  0x0C, 0xFF, 0xFF, 0xED, 0xFF, 0xFF, 0xC0, 0x00,
  0x5F, 0xFF, 0xE4, 0x00, 0x2C, 0xFF, 0xF7, 0x00,
  0xBF, 0xFF, 0x50, 0x00, 0x02, 0xFF, 0xFD, 0x00,
  0xDF, 0xFF, 0x10, 0x00, 0x00, 0xCF, 0xFF, 0x20,
  0xFF, 0xFF, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0x50,
  0xDF, 0xFF, 0x20, 0x00, 0x00, 0xEF, 0xFF, 0x60,
  0xAF, 0xFF, 0xA0, 0x00, 0x07, 0xFF, 0xFF, 0x70,
  0x3F, 0xFF, 0xFC, 0x87, 0xBF, 0xFF, 0xFF, 0x70,
  0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x60,
  0x00, 0x6E, 0xFF, 0xFF, 0xF9, 0x8F, 0xFF, 0x40,
  0x00, 0x00, 0x46, 0x75, 0x20, 0x8F, 0xFF, 0x20,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xCF, 0xFE, 0x00,
  0x06, 0xDA, 0x10, 0x00, 0x03, 0xFF, 0xF9, 0x00,
  0x0F, 0xFF, 0xA1, 0x00, 0x2D, 0xFF, 0xF3, 0x00,
  0x0E, 0xFF, 0xFD, 0x9A, 0xEF, 0xFF, 0x90, 0x00,
  0x06, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x00, 0x00,
  0x00, 0x4C, 0xFF, 0xFF, 0xFD, 0x60, 0x00, 0x00,
  0x00, 0x00, 0x24, 0x64, 0x20, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_003A[ 48] = { /* code 003A, COLON */
  0x00, 0x11, 0x00,
  0x06, 0xFF, 0x90,
  0x0F, 0xFF, 0xF4,
  0x1F, 0xFF, 0xF4,
  0x08, 0xFF, 0xB0,
  0x00, 0x33, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x23, 0x00,
  0x07, 0xFF, 0xB0,
  0x0F, 0xFF, 0xF4,
  0x0F, 0xFF, 0xF4,
  0x06, 0xFF, 0xA0,
  0x00, 0x12, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_003B[ 57] = { /* code 003B, SEMICOLON */
  0x00, 0x11, 0x00,
  0x06, 0xFF, 0x90,
  0x0F, 0xFF, 0xF4,
  0x1F, 0xFF, 0xF4,
  0x08, 0xFF, 0xB0,
  0x00, 0x33, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x00, 0x00,
  0x00, 0x23, 0x00,
  0x07, 0xFF, 0xB0,
  0x0F, 0xFF, 0xF6,
  0x0F, 0xFF, 0xF9,
  0x07, 0xFF, 0xF9,
  0x00, 0x05, 0xF8,
  0x00, 0x2D, 0xF3,
  0x08, 0xFF, 0x80,
  0x0C, 0xC5, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_003C[120] = { /* code 003C, LESS-THAN SIGN */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x81,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x4B, 0xFF, 0xF8,
  0x00, 0x00, 0x00, 0x00, 0x5C, 0xFF, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x7D, 0xFF, 0xFF, 0xFD, 0x70,
  0x00, 0x01, 0x7E, 0xFF, 0xFF, 0xFC, 0x50, 0x00,
  0x02, 0x9F, 0xFF, 0xFF, 0xFA, 0x30, 0x00, 0x00,
  0x2E, 0xFF, 0xFF, 0xE8, 0x20, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0xFE, 0x40, 0x00, 0x00, 0x00, 0x00,
  0x1C, 0xFF, 0xFF, 0xFC, 0x60, 0x00, 0x00, 0x00,
  0x00, 0x5B, 0xFF, 0xFF, 0xFE, 0x71, 0x00, 0x00,
  0x00, 0x00, 0x3A, 0xFF, 0xFF, 0xFF, 0x93, 0x00,
  0x00, 0x00, 0x00, 0x29, 0xFF, 0xFF, 0xFF, 0xB1,
  0x00, 0x00, 0x00, 0x00, 0x18, 0xEF, 0xFF, 0xF8,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xDF, 0xF6,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x40
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_003D[ 88] = { /* code 003D, EQUALS SIGN */
  0x04, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x50,
  0x6F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA,
  0x19, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xA2,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x00,
  0x3E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB,
  0x3E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6,
  0x01, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_003E[120] = { /* code 003E, GREATER-THAN SIGN */
  0x07, 0x94, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0xC5, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x4F, 0xFF, 0xFF, 0xD7, 0x10, 0x00, 0x00, 0x00,
  0x05, 0xCF, 0xFF, 0xFF, 0xE8, 0x10, 0x00, 0x00,
  0x00, 0x04, 0xAF, 0xFF, 0xFF, 0xF9, 0x20, 0x00,
  0x00, 0x00, 0x02, 0x8E, 0xFF, 0xFF, 0xFA, 0x30,
  0x00, 0x00, 0x00, 0x01, 0x7D, 0xFF, 0xFF, 0xF5,
  0x00, 0x00, 0x00, 0x00, 0x03, 0xDF, 0xFF, 0xF8,
  0x00, 0x00, 0x00, 0x04, 0xBF, 0xFF, 0xFF, 0xD2,
  0x00, 0x00, 0x06, 0xDF, 0xFF, 0xFF, 0xD6, 0x00,
  0x00, 0x28, 0xEF, 0xFF, 0xFF, 0xB5, 0x00, 0x00,
  0x09, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0xFF, 0x92, 0x00, 0x00, 0x00, 0x00,
  0x3F, 0xFE, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_003F[147] = { /* code 003F, QUESTION MARK */
  0x00, 0x02, 0x8B, 0xBB, 0x95, 0x00, 0x00,
  0x00, 0x9F, 0xFF, 0xFF, 0xFF, 0xE4, 0x00,
  0x0B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x40,
  0x6F, 0xFF, 0xE7, 0x46, 0xCF, 0xFF, 0xE0,
  0xBF, 0xFF, 0x40, 0x00, 0x1E, 0xFF, 0xF3,
  0xCF, 0xFB, 0x00, 0x00, 0x0B, 0xFF, 0xF6,
  0x4C, 0xB2, 0x00, 0x00, 0x1E, 0xFF, 0xF3,
  0x00, 0x00, 0x00, 0x00, 0xAF, 0xFF, 0xD0,
  0x00, 0x00, 0x00, 0x2C, 0xFF, 0xFF, 0x40,
  0x00, 0x00, 0x02, 0xEF, 0xFF, 0xE4, 0x00,
  0x00, 0x00, 0x1D, 0xFF, 0xFC, 0x20, 0x00,
  0x00, 0x00, 0x6F, 0xFF, 0xA0, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xFF, 0x20, 0x00, 0x00,
  0x00, 0x00, 0x1C, 0xF8, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x01, 0x30, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x4F, 0xFD, 0x10, 0x00, 0x00,
  0x00, 0x00, 0xCF, 0xFF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0xCF, 0xFF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0x4E, 0xFC, 0x10, 0x00, 0x00,
  0x00, 0x00, 0x01, 0x20, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0040[231] = { /* code 0040, COMMERCIAL AT */
  0x00, 0x00, 0x00, 0x03, 0x8C, 0xDD, 0xDB, 0x72, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x04, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA2, 0x00, 0x00,
  0x00, 0x00, 0x8F, 0xFF, 0xFB, 0x87, 0x9C, 0xFF, 0xFE, 0x30, 0x00,
  0x00, 0x08, 0xFF, 0xF8, 0x10, 0x00, 0x00, 0x29, 0xFF, 0xE2, 0x00,
  0x00, 0x6F, 0xFE, 0x40, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFB, 0x00,
  0x01, 0xEF, 0xF5, 0x00, 0x5B, 0xFD, 0x91, 0x6B, 0x3A, 0xFF, 0x40,
  0x07, 0xFF, 0xA0, 0x08, 0xFF, 0xFF, 0xFC, 0xEF, 0x82, 0xFF, 0x90,
  0x0C, 0xFF, 0x30, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0x60, 0xCF, 0xB0,
  0x1F, 0xFE, 0x00, 0xDF, 0xF9, 0x10, 0x7F, 0xFF, 0x30, 0xAF, 0xB0,
  0x2F, 0xFB, 0x03, 0xFF, 0xB0, 0x00, 0x0E, 0xFF, 0x10, 0x9F, 0xB0,
  0x2F, 0xFB, 0x06, 0xFF, 0x70, 0x00, 0x0C, 0xFD, 0x00, 0xBF, 0x90,
  0x2F, 0xFC, 0x07, 0xFF, 0x70, 0x00, 0x0E, 0xFB, 0x00, 0xEF, 0x40,
  0x0E, 0xFE, 0x06, 0xFF, 0xC1, 0x00, 0x9F, 0xF8, 0x08, 0xFC, 0x00,
  0x0A, 0xFF, 0x42, 0xFF, 0xFE, 0xAD, 0xFF, 0xFC, 0xBF, 0xE3, 0x00,
  0x05, 0xFF, 0xC0, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x30, 0x00,
  0x00, 0xCF, 0xF7, 0x1A, 0xFF, 0xF9, 0x8F, 0xFE, 0x81, 0x00, 0x00,
  0x00, 0x2E, 0xFF, 0x80, 0x24, 0x10, 0x03, 0x30, 0x7A, 0x10, 0x00,
  0x00, 0x03, 0xEF, 0xFD, 0x73, 0x00, 0x02, 0x7D, 0xFF, 0x30, 0x00,
  0x00, 0x00, 0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x5B, 0xFF, 0xFF, 0xFF, 0xE9, 0x20, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x14, 0x67, 0x63, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0041[189] = { /* code 0041, LATIN CAPITAL LETTER A */
  0x00, 0x00, 0x00, 0x19, 0xBB, 0xA2, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x8F, 0xFF, 0xFC, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xEF, 0xFF, 0xFF, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x05, 0xFF, 0xFF, 0xFF, 0x90, 0x00, 0x00,
  0x00, 0x00, 0x0B, 0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x00,
  0x00, 0x00, 0x1F, 0xFF, 0xDB, 0xFF, 0xF5, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xFF, 0x87, 0xFF, 0xFA, 0x00, 0x00,
  0x00, 0x00, 0xDF, 0xFF, 0x32, 0xFF, 0xFF, 0x10, 0x00,
  0x00, 0x03, 0xFF, 0xFD, 0x00, 0xCF, 0xFF, 0x60, 0x00,
  0x00, 0x09, 0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xB0, 0x00,
  0x00, 0x0E, 0xFF, 0xF3, 0x00, 0x2F, 0xFF, 0xF2, 0x00,
  0x00, 0x5F, 0xFF, 0xD0, 0x00, 0x0D, 0xFF, 0xF7, 0x00,
  0x00, 0xBF, 0xFF, 0xA4, 0x44, 0x4A, 0xFF, 0xFC, 0x00,
  0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x0D, 0xFF, 0xFE, 0xDD, 0xDD, 0xDD, 0xEF, 0xFF, 0xD0,
  0x4F, 0xFF, 0xF3, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xF4,
  0x9F, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x0D, 0xFF, 0xF9,
  0xBF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xFB,
  0x6F, 0xFE, 0x20, 0x00, 0x00, 0x00, 0x02, 0xEF, 0xF6,
  0x03, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0042[180] = { /* code 0042, LATIN CAPITAL LETTER B */
  0x04, 0x67, 0x77, 0x77, 0x76, 0x53, 0x00, 0x00, 0x00,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 0x00,
  0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x90, 0x00,
  0xFF, 0xFF, 0xDB, 0xBB, 0xCE, 0xFF, 0xFF, 0xF3, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x8F, 0xFF, 0xF8, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x0D, 0xFF, 0xF9, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x0C, 0xFF, 0xF8, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x3F, 0xFF, 0xF3, 0x00,
  0xFF, 0xFF, 0xB7, 0x77, 0x79, 0xFF, 0xFF, 0x70, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA1, 0x00,
  0xFF, 0xFF, 0xA6, 0x66, 0x67, 0xCF, 0xFF, 0xFA, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x09, 0xFF, 0xFF, 0x10,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x40,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x40,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x09, 0xFF, 0xFF, 0x30,
  0xFF, 0xFF, 0xA6, 0x66, 0x68, 0xCF, 0xFF, 0xFD, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5, 0x00,
  0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x60, 0x00,
  0x3C, 0xFF, 0xFF, 0xFF, 0xFF, 0xEB, 0x71, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0043[189] = { /* code 0043, LATIN CAPITAL LETTER C */
  0x00, 0x00, 0x00, 0x5A, 0xCD, 0xDC, 0x94, 0x00, 0x00,
  0x00, 0x00, 0x5E, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4, 0x00,
  0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x00, 0x7F, 0xFF, 0xFF, 0xDA, 0xBD, 0xFF, 0xFF, 0xF3,
  0x02, 0xFF, 0xFF, 0xE6, 0x00, 0x00, 0x7F, 0xFF, 0xFA,
  0x09, 0xFF, 0xFF, 0x40, 0x00, 0x00, 0x08, 0xFF, 0xFB,
  0x0F, 0xFF, 0xFA, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xF6,
  0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00, 0x00, 0x02, 0x10,
  0x7F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xD0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x6F, 0xFF, 0xF1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x3F, 0xFF, 0xF5, 0x00, 0x00, 0x00, 0x00, 0x6C, 0xA2,
  0x0E, 0xFF, 0xFC, 0x00, 0x00, 0x00, 0x04, 0xFF, 0xFB,
  0x09, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x1D, 0xFF, 0xFB,
  0x02, 0xEF, 0xFF, 0xFB, 0x52, 0x25, 0xDF, 0xFF, 0xF6,
  0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0,
  0x00, 0x05, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x10,
  0x00, 0x00, 0x29, 0xEF, 0xFF, 0xFF, 0xFD, 0x60, 0x00,
  0x00, 0x00, 0x00, 0x03, 0x57, 0x75, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0044[180] = { /* code 0044, LATIN CAPITAL LETTER D */
  0x04, 0x67, 0x77, 0x77, 0x65, 0x30, 0x00, 0x00, 0x00,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA3, 0x00, 0x00,
  0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x60, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x00,
  0xFF, 0xFF, 0x82, 0x22, 0x24, 0xAF, 0xFF, 0xFE, 0x10,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0x80,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xCF, 0xFF, 0xD0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF1,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF3,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x2F, 0xFF, 0xF4,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x2F, 0xFF, 0xF4,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xF4,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xF2,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xAF, 0xFF, 0xE0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xA0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x3D, 0xFF, 0xFF, 0x30,
  0xFF, 0xFF, 0xC9, 0x99, 0x9B, 0xFF, 0xFF, 0xF9, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x3C, 0xFF, 0xFF, 0xFF, 0xFE, 0xB7, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0045[160] = { /* code 0045, LATIN CAPITAL LETTER E */
  0x03, 0x67, 0x77, 0x77, 0x77, 0x77, 0x76, 0x30,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF3,
  0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD1,
  0xDF, 0xFF, 0xA2, 0x22, 0x22, 0x22, 0x22, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0xD9, 0x99, 0x99, 0x99, 0x95, 0x00,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0xDF, 0xFF, 0xEB, 0xBB, 0xBB, 0xBB, 0xB6, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0xD9, 0x99, 0x99, 0x99, 0x98, 0x60,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7,
  0xAF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9,
  0x2C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC3
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0046[168] = { /* code 0046, LATIN CAPITAL LETTER F */
  0x03, 0x67, 0x77, 0x77, 0x77, 0x77, 0x74, 0x00,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB0,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x50,
  0xDF, 0xFF, 0xA2, 0x22, 0x22, 0x22, 0x20, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0xB6, 0x66, 0x66, 0x65, 0x20, 0x00,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF3, 0x00,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5, 0x00,
  0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD1, 0x00,
  0xDF, 0xFF, 0xA2, 0x22, 0x22, 0x20, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xDF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x6F, 0xFE, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x52, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0047[210] = { /* code 0047, LATIN CAPITAL LETTER G */
  0x00, 0x00, 0x00, 0x48, 0xBD, 0xDD, 0xB8, 0x20, 0x00, 0x00,
  0x00, 0x00, 0x4D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x10, 0x00,
  0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD1, 0x00,
  0x00, 0x7F, 0xFF, 0xFF, 0xEB, 0xAC, 0xFF, 0xFF, 0xFA, 0x00,
  0x03, 0xFF, 0xFF, 0xE6, 0x00, 0x00, 0x1A, 0xFF, 0xFF, 0x10,
  0x0A, 0xFF, 0xFF, 0x40, 0x00, 0x00, 0x00, 0xCF, 0xFE, 0x00,
  0x1F, 0xFF, 0xFA, 0x00, 0x00, 0x00, 0x00, 0x19, 0xA3, 0x00,
  0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0xD0, 0x00, 0x00, 0x04, 0x66, 0x66, 0x64, 0x10,
  0x7F, 0xFF, 0xD0, 0x00, 0x00, 0xAF, 0xFF, 0xFF, 0xFF, 0xD0,
  0x7F, 0xFF, 0xD0, 0x00, 0x00, 0xDF, 0xFF, 0xFF, 0xFF, 0xF2,
  0x6F, 0xFF, 0xF1, 0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xF2,
  0x3F, 0xFF, 0xF5, 0x00, 0x00, 0x01, 0x22, 0x3F, 0xFF, 0xF2,
  0x0E, 0xFF, 0xFB, 0x00, 0x00, 0x00, 0x00, 0x6F, 0xFF, 0xF2,
  0x09, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0x02, 0xEF, 0xFF, 0xF2,
  0x02, 0xEF, 0xFF, 0xFA, 0x30, 0x01, 0x7E, 0xFF, 0xFF, 0xF2,
  0x00, 0x5F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF2,
  0x00, 0x05, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE5, 0xFF, 0xF2,
  0x00, 0x00, 0x29, 0xEF, 0xFF, 0xFF, 0xFA, 0x20, 0xDF, 0xD0,
  0x00, 0x00, 0x00, 0x03, 0x67, 0x75, 0x20, 0x00, 0x15, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0048[189] = { /* code 0048, LATIN CAPITAL LETTER H */
  0x2A, 0xB7, 0x00, 0x00, 0x00, 0x00, 0x07, 0xBA, 0x20,
  0xCF, 0xFF, 0x40, 0x00, 0x00, 0x00, 0x4F, 0xFF, 0xC0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x82, 0x22, 0x22, 0x22, 0x8F, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xC9, 0x99, 0x99, 0x99, 0xCF, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xEF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0,
  0x7F, 0xFE, 0x20, 0x00, 0x00, 0x00, 0x2E, 0xFF, 0x70,
  0x03, 0x51, 0x00, 0x00, 0x00, 0x00, 0x01, 0x53, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0049[ 63] = { /* code 0049, LATIN CAPITAL LETTER I */
  0x2A, 0xB7, 0x00,
  0xCF, 0xFF, 0x40,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70,
  0xEF, 0xFF, 0x70,
  0x7F, 0xFE, 0x20,
  0x03, 0x51, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_004A[147] = { /* code 004A, LATIN CAPITAL LETTER J */
  0x00, 0x00, 0x00, 0x00, 0x02, 0xAB, 0x70,
  0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xF4,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x00, 0x00, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x05, 0x97, 0x00, 0x00, 0x0F, 0xFF, 0xF7,
  0x3F, 0xFF, 0x50, 0x00, 0x0F, 0xFF, 0xF7,
  0x7F, 0xFF, 0x90, 0x00, 0x0F, 0xFF, 0xF7,
  0x7F, 0xFF, 0xA0, 0x00, 0x3F, 0xFF, 0xF5,
  0x5F, 0xFF, 0xF4, 0x01, 0xBF, 0xFF, 0xF1,
  0x0E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0,
  0x04, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x10,
  0x00, 0x4C, 0xFF, 0xFF, 0xFF, 0xA1, 0x00,
  0x00, 0x00, 0x36, 0x76, 0x51, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_004B[189] = { /* code 004B, LATIN CAPITAL LETTER K */
  0x2A, 0xB7, 0x00, 0x00, 0x00, 0x01, 0xAB, 0x60, 0x00,
  0xCF, 0xFF, 0x40, 0x00, 0x00, 0x1B, 0xFF, 0xF3, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0xBF, 0xFF, 0xF3, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x0A, 0xFF, 0xFF, 0x80, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x8F, 0xFF, 0xFA, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x07, 0xFF, 0xFF, 0xA0, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x6F, 0xFF, 0xFB, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x75, 0xFF, 0xFF, 0xB1, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xBE, 0xFF, 0xFF, 0x40, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xAE, 0xFF, 0xFF, 0x30, 0x00, 0x00,
  0xFF, 0xFF, 0xFA, 0x05, 0xFF, 0xFF, 0xC0, 0x00, 0x00,
  0xFF, 0xFF, 0xB0, 0x00, 0xAF, 0xFF, 0xF7, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x1E, 0xFF, 0xFF, 0x20, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x06, 0xFF, 0xFF, 0xC0, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0xBF, 0xFF, 0xF7, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x2E, 0xFF, 0xFE, 0x10,
  0xEF, 0xFF, 0x70, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x30,
  0x7F, 0xFE, 0x20, 0x00, 0x00, 0x00, 0xBF, 0xFD, 0x00,
  0x03, 0x51, 0x00, 0x00, 0x00, 0x00, 0x05, 0x40, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_004C[160] = { /* code 004C, LATIN CAPITAL LETTER L */
  0x2A, 0xB7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xCF, 0xFF, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xDB, 0xBB, 0xBB, 0xBB, 0xA3, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x10,
  0x3C, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_004D[210] = { /* code 004D, LATIN CAPITAL LETTER M */
  0x3A, 0xBB, 0xA2, 0x00, 0x00, 0x00, 0x00, 0x2A, 0xBB, 0xA3,
  0xDF, 0xFF, 0xFD, 0x00, 0x00, 0x00, 0x00, 0xCF, 0xFF, 0xFD,
  0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x08, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x00, 0x2F, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFD, 0xEF, 0xF7, 0x00, 0x00, 0x7F, 0xFD, 0xFF, 0xFF,
  0xFF, 0xFD, 0x9F, 0xFC, 0x00, 0x00, 0xBF, 0xF9, 0xFF, 0xFF,
  0xFF, 0xFD, 0x4F, 0xFF, 0x20, 0x01, 0xFF, 0xF4, 0xFF, 0xFF,
  0xFF, 0xFD, 0x0E, 0xFF, 0x60, 0x05, 0xFF, 0xE0, 0xFF, 0xFF,
  0xFF, 0xFD, 0x0A, 0xFF, 0xB0, 0x0A, 0xFF, 0x90, 0xFF, 0xFF,
  0xFF, 0xFD, 0x05, 0xFF, 0xF1, 0x0E, 0xFF, 0x50, 0xFF, 0xFF,
  0xFF, 0xFD, 0x01, 0xFF, 0xF5, 0x4F, 0xFE, 0x00, 0xFF, 0xFF,
  0xFF, 0xFD, 0x00, 0xAF, 0xFA, 0x8F, 0xFA, 0x00, 0xFF, 0xFF,
  0xFF, 0xFD, 0x00, 0x5F, 0xFE, 0xDF, 0xF5, 0x00, 0xFF, 0xFF,
  0xFF, 0xFD, 0x00, 0x1F, 0xFF, 0xFF, 0xF1, 0x00, 0xFF, 0xFF,
  0xFF, 0xFD, 0x00, 0x0B, 0xFF, 0xFF, 0xA0, 0x00, 0xFF, 0xFF,
  0xFF, 0xFD, 0x00, 0x06, 0xFF, 0xFF, 0x60, 0x00, 0xFF, 0xFF,
  0xFF, 0xFD, 0x00, 0x01, 0xFF, 0xFF, 0x10, 0x00, 0xEF, 0xFF,
  0xAF, 0xF8, 0x00, 0x00, 0xAF, 0xFA, 0x00, 0x00, 0x9F, 0xFA,
  0x05, 0x40, 0x00, 0x00, 0x04, 0x40, 0x00, 0x00, 0x04, 0x40
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_004E[189] = { /* code 004E, LATIN CAPITAL LETTER N */
  0x2A, 0xBB, 0x30, 0x00, 0x00, 0x00, 0x06, 0xBB, 0x30,
  0xCF, 0xFF, 0xE2, 0x00, 0x00, 0x00, 0x2F, 0xFF, 0xE0,
  0xFF, 0xFF, 0xFA, 0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0x40, 0x00, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xD0, 0x00, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0xFF, 0x20, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0xAF, 0xFF, 0xB0, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x4B, 0xFF, 0xF6, 0x00, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x42, 0xFF, 0xFE, 0x10, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x7F, 0xFF, 0x90, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x0D, 0xFF, 0xF4, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x03, 0xFF, 0xFD, 0x4F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x00, 0x9F, 0xFF, 0xBF, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x00, 0x1D, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x00, 0x05, 0xFF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0x40, 0x00, 0x00, 0x2E, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0x30, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0xE0,
  0xAF, 0xFD, 0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0x70,
  0x04, 0x51, 0x00, 0x00, 0x00, 0x00, 0x04, 0x63, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_004F[210] = { /* code 004F, LATIN CAPITAL LETTER O */
  0x00, 0x00, 0x01, 0x6A, 0xCD, 0xDC, 0xA6, 0x10, 0x00, 0x00,
  0x00, 0x00, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x00, 0x1B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00,
  0x00, 0xBF, 0xFF, 0xFF, 0xDA, 0xAD, 0xFF, 0xFF, 0xFB, 0x00,
  0x07, 0xFF, 0xFF, 0xD3, 0x00, 0x00, 0x3C, 0xFF, 0xFF, 0x70,
  0x0E, 0xFF, 0xFD, 0x10, 0x00, 0x00, 0x01, 0xDF, 0xFF, 0xE1,
  0x4F, 0xFF, 0xF5, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xF5,
  0x8F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xFF, 0xF9,
  0xBF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFB,
  0xBF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xFD,
  0xBF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xFD,
  0xBF, 0xFF, 0xA0, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFB,
  0x9F, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xF9,
  0x7F, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x00, 0x2F, 0xFF, 0xF6,
  0x2F, 0xFF, 0xF8, 0x00, 0x00, 0x00, 0x00, 0x9F, 0xFF, 0xF1,
  0x0B, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0x90,
  0x03, 0xFF, 0xFF, 0xF9, 0x42, 0x24, 0xAF, 0xFF, 0xFE, 0x10,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4, 0x00,
  0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x30, 0x00,
  0x00, 0x00, 0x2A, 0xFF, 0xFF, 0xFF, 0xFE, 0x71, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x03, 0x67, 0x75, 0x30, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0050[189] = { /* code 0050, LATIN CAPITAL LETTER P */
  0x04, 0x67, 0x77, 0x77, 0x76, 0x63, 0x00, 0x00, 0x00,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0x00, 0x00,
  0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF6, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x02, 0x7F, 0xFF, 0xFC, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x08, 0xFF, 0xFF, 0x10,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x04, 0xFF, 0xFF, 0x20,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x10,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x1D, 0xFF, 0xFE, 0x00,
  0xFF, 0xFF, 0xB7, 0x77, 0x7A, 0xEF, 0xFF, 0xF8, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD1, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x20, 0x00,
  0xFF, 0xFF, 0xED, 0xDD, 0xDD, 0xCA, 0x50, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xEF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFE, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0051[220] = { /* code 0051, LATIN CAPITAL LETTER Q */
  0x00, 0x00, 0x01, 0x6A, 0xCD, 0xDC, 0xA6, 0x10, 0x00, 0x00,
  0x00, 0x00, 0x7E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x00, 0x1B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC1, 0x00,
  0x00, 0xBF, 0xFF, 0xFF, 0xDA, 0xAD, 0xFF, 0xFF, 0xFB, 0x00,
  0x07, 0xFF, 0xFF, 0xD3, 0x00, 0x00, 0x3C, 0xFF, 0xFF, 0x70,
  0x0E, 0xFF, 0xFD, 0x10, 0x00, 0x00, 0x01, 0xDF, 0xFF, 0xE1,
  0x4F, 0xFF, 0xF5, 0x00, 0x00, 0x00, 0x00, 0x5F, 0xFF, 0xF5,
  0x8F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x0E, 0xFF, 0xF9,
  0xBF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFB,
  0xBF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xFD,
  0xBF, 0xFF, 0x90, 0x00, 0x00, 0x00, 0x00, 0x09, 0xFF, 0xFD,
  0xBF, 0xFF, 0xA0, 0x00, 0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFB,
  0x9F, 0xFF, 0xC0, 0x00, 0x00, 0x02, 0x00, 0x0C, 0xFF, 0xF9,
  0x7F, 0xFF, 0xF2, 0x00, 0x00, 0xAF, 0xB1, 0x2F, 0xFF, 0xF6,
  0x2F, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0xFC, 0xAF, 0xFF, 0xF1,
  0x0B, 0xFF, 0xFF, 0x50, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x90,
  0x03, 0xFF, 0xFF, 0xF9, 0x42, 0x2A, 0xFF, 0xFF, 0xFE, 0x20,
  0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x00, 0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x30,
  0x00, 0x00, 0x2A, 0xFF, 0xFF, 0xFF, 0xFD, 0x8E, 0xFF, 0xE2,
  0x00, 0x00, 0x00, 0x03, 0x67, 0x75, 0x30, 0x02, 0xEF, 0xF1,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x30
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0052[189] = { /* code 0052, LATIN CAPITAL LETTER R */
  0x04, 0x67, 0x77, 0x77, 0x77, 0x65, 0x30, 0x00, 0x00,
  0x8F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x50, 0x00,
  0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x29, 0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xEF, 0xFF, 0x90,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0x90,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xDF, 0xFF, 0x70,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x08, 0xFF, 0xFE, 0x10,
  0xFF, 0xFF, 0xDB, 0xBB, 0xBC, 0xFF, 0xFF, 0xF5, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x30, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00,
  0xFF, 0xFF, 0xB7, 0x77, 0x77, 0xCF, 0xFF, 0xFB, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x0B, 0xFF, 0xFE, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0x30,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x01, 0xFF, 0xFF, 0x60,
  0xEF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xEF, 0xFF, 0x90,
  0x7F, 0xFE, 0x20, 0x00, 0x00, 0x00, 0x6F, 0xFF, 0x40,
  0x03, 0x51, 0x00, 0x00, 0x00, 0x00, 0x02, 0x52, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0053[189] = { /* code 0053, LATIN CAPITAL LETTER S */
  0x00, 0x01, 0x6A, 0xCD, 0xDC, 0x94, 0x00, 0x00, 0x00,
  0x00, 0x6E, 0xFF, 0xFF, 0xFF, 0xFF, 0xD5, 0x00, 0x00,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70, 0x00,
  0x3F, 0xFF, 0xFE, 0x97, 0x8B, 0xFF, 0xFF, 0xF3, 0x00,
  0x9F, 0xFF, 0xD1, 0x00, 0x00, 0x3E, 0xFF, 0xF7, 0x00,
  0xBF, 0xFF, 0x80, 0x00, 0x00, 0x03, 0xEF, 0xF4, 0x00,
  0xBF, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x26, 0x30, 0x00,
  0x9F, 0xFF, 0xFB, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x4F, 0xFF, 0xFF, 0xFF, 0xDA, 0x73, 0x00, 0x00, 0x00,
  0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 0x00,
  0x00, 0x5D, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB1, 0x00,
  0x00, 0x00, 0x37, 0xBF, 0xFF, 0xFF, 0xFF, 0xF9, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x47, 0xCF, 0xFF, 0xFF, 0x10,
  0x03, 0x30, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xFF, 0x30,
  0x7F, 0xFA, 0x00, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0x40,
  0xBF, 0xFF, 0x70, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0x30,
  0xAF, 0xFF, 0xF8, 0x10, 0x00, 0x4E, 0xFF, 0xFE, 0x00,
  0x2E, 0xFF, 0xFF, 0xFC, 0xBE, 0xFF, 0xFF, 0xF6, 0x00,
  0x04, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0x18, 0xEF, 0xFF, 0xFF, 0xFF, 0xB4, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x57, 0x76, 0x41, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0054[189] = { /* code 0054, LATIN CAPITAL LETTER T */
  0x03, 0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x76, 0x30,
  0x6F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB,
  0x4F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5,
  0x01, 0x44, 0x44, 0x7F, 0xFF, 0xF7, 0x44, 0x44, 0x20,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x4F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x3F, 0xFF, 0xF3, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x0B, 0xFF, 0xC0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x45, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0055[189] = { /* code 0055, LATIN CAPITAL LETTER U */
  0x2A, 0xB7, 0x00, 0x00, 0x00, 0x00, 0x07, 0xBA, 0x20,
  0xCF, 0xFF, 0x40, 0x00, 0x00, 0x00, 0x4F, 0xFF, 0xC0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0xEF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0,
  0xCF, 0xFF, 0xA0, 0x00, 0x00, 0x00, 0xAF, 0xFF, 0xC0,
  0x9F, 0xFF, 0xF3, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0x90,
  0x3F, 0xFF, 0xFE, 0x73, 0x23, 0x7E, 0xFF, 0xFF, 0x30,
  0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFA, 0x00,
  0x01, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xB1, 0x00,
  0x00, 0x06, 0xCF, 0xFF, 0xFF, 0xFF, 0xD6, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x56, 0x76, 0x52, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0056[189] = { /* code 0056, LATIN CAPITAL LETTER V */
  0x1A, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x02, 0xAB, 0x60,
  0xAF, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xF2,
  0xBF, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0x3F, 0xFF, 0xF3,
  0x7F, 0xFF, 0xF2, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xE0,
  0x2F, 0xFF, 0xF6, 0x00, 0x00, 0x00, 0xCF, 0xFF, 0x90,
  0x0B, 0xFF, 0xFB, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0x30,
  0x06, 0xFF, 0xFF, 0x10, 0x00, 0x07, 0xFF, 0xFD, 0x00,
  0x01, 0xFF, 0xFF, 0x50, 0x00, 0x0B, 0xFF, 0xF8, 0x00,
  0x00, 0xAF, 0xFF, 0xA0, 0x00, 0x1F, 0xFF, 0xF2, 0x00,
  0x00, 0x5F, 0xFF, 0xE0, 0x00, 0x5F, 0xFF, 0xB0, 0x00,
  0x00, 0x0E, 0xFF, 0xF3, 0x00, 0xAF, 0xFF, 0x60, 0x00,
  0x00, 0x0A, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0x10, 0x00,
  0x00, 0x04, 0xFF, 0xFD, 0x05, 0xFF, 0xFA, 0x00, 0x00,
  0x00, 0x00, 0xEF, 0xFF, 0x29, 0xFF, 0xF5, 0x00, 0x00,
  0x00, 0x00, 0x8F, 0xFF, 0x7E, 0xFF, 0xE0, 0x00, 0x00,
  0x00, 0x00, 0x3F, 0xFF, 0xEF, 0xFF, 0x90, 0x00, 0x00,
  0x00, 0x00, 0x0D, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x07, 0xFF, 0xFF, 0xFD, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x02, 0xFF, 0xFF, 0xF7, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x9F, 0xFF, 0xD1, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x03, 0x64, 0x10, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0057[273] = { /* code 0057, LATIN CAPITAL LETTER W */
  0x19, 0xB7, 0x00, 0x00, 0x00, 0x2A, 0xBA, 0x20, 0x00, 0x00, 0x07, 0xB9, 0x10,
  0x9F, 0xFF, 0x40, 0x00, 0x00, 0xBF, 0xFF, 0xB0, 0x00, 0x00, 0x3F, 0xFF, 0x90,
  0xBF, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xF1, 0x00, 0x00, 0x7F, 0xFF, 0xB0,
  0x8F, 0xFF, 0xB0, 0x00, 0x03, 0xFF, 0xFF, 0xF4, 0x00, 0x00, 0xAF, 0xFF, 0x80,
  0x4F, 0xFF, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0xF7, 0x00, 0x00, 0xDF, 0xFF, 0x40,
  0x1F, 0xFF, 0xF3, 0x00, 0x0A, 0xFF, 0xFF, 0xFB, 0x00, 0x01, 0xFF, 0xFF, 0x10,
  0x0B, 0xFF, 0xF5, 0x00, 0x0E, 0xFF, 0xDF, 0xFE, 0x00, 0x04, 0xFF, 0xFB, 0x00,
  0x07, 0xFF, 0xF8, 0x00, 0x2F, 0xFF, 0x6F, 0xFF, 0x30, 0x07, 0xFF, 0xF7, 0x00,
  0x04, 0xFF, 0xFB, 0x00, 0x6F, 0xFE, 0x0E, 0xFF, 0x70, 0x0B, 0xFF, 0xF4, 0x00,
  0x00, 0xFF, 0xFE, 0x00, 0x9F, 0xFA, 0x0A, 0xFF, 0xA0, 0x0E, 0xFF, 0xF0, 0x00,
  0x00, 0xBF, 0xFF, 0x30, 0xDF, 0xF7, 0x07, 0xFF, 0xD0, 0x2F, 0xFF, 0xB0, 0x00,
  0x00, 0x7F, 0xFF, 0x61, 0xFF, 0xF3, 0x03, 0xFF, 0xF2, 0x5F, 0xFF, 0x70, 0x00,
  0x00, 0x3F, 0xFF, 0x95, 0xFF, 0xE0, 0x00, 0xEF, 0xF5, 0x8F, 0xFF, 0x30, 0x00,
  0x00, 0x0E, 0xFF, 0xC8, 0xFF, 0xB0, 0x00, 0xBF, 0xF9, 0xBF, 0xFE, 0x00, 0x00,
  0x00, 0x0B, 0xFF, 0xFC, 0xFF, 0x70, 0x00, 0x7F, 0xFC, 0xEF, 0xFB, 0x00, 0x00,
  0x00, 0x07, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x3F, 0xFF, 0xFF, 0xF7, 0x00, 0x00,
  0x00, 0x03, 0xFF, 0xFF, 0xFE, 0x00, 0x00, 0x0E, 0xFF, 0xFF, 0xF3, 0x00, 0x00,
  0x00, 0x00, 0xEF, 0xFF, 0xFB, 0x00, 0x00, 0x0B, 0xFF, 0xFF, 0xE0, 0x00, 0x00,
  0x00, 0x00, 0xAF, 0xFF, 0xF7, 0x00, 0x00, 0x07, 0xFF, 0xFF, 0xA0, 0x00, 0x00,
  0x00, 0x00, 0x3E, 0xFF, 0xE1, 0x00, 0x00, 0x01, 0xEF, 0xFE, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x55, 0x10, 0x00, 0x00, 0x00, 0x15, 0x52, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0058[168] = { /* code 0058, LATIN CAPITAL LETTER X */
  0x03, 0xBB, 0x30, 0x00, 0x00, 0x01, 0xAB, 0x70,
  0x1E, 0xFF, 0xE1, 0x00, 0x00, 0x09, 0xFF, 0xF5,
  0x2F, 0xFF, 0xF9, 0x00, 0x00, 0x3F, 0xFF, 0xF7,
  0x0C, 0xFF, 0xFF, 0x30, 0x00, 0xCF, 0xFF, 0xE1,
  0x02, 0xFF, 0xFF, 0xB0, 0x07, 0xFF, 0xFF, 0x40,
  0x00, 0x7F, 0xFF, 0xF5, 0x2E, 0xFF, 0xF9, 0x00,
  0x00, 0x0C, 0xFF, 0xFD, 0xBF, 0xFF, 0xD1, 0x00,
  0x00, 0x02, 0xFF, 0xFF, 0xFF, 0xFF, 0x40, 0x00,
  0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xF9, 0x00, 0x00,
  0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xD1, 0x00, 0x00,
  0x00, 0x00, 0x0C, 0xFF, 0xFF, 0xD1, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xFF, 0xFF, 0xF9, 0x00, 0x00,
  0x00, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0x40, 0x00,
  0x00, 0x0D, 0xFF, 0xFD, 0xDF, 0xFF, 0xD1, 0x00,
  0x00, 0x8F, 0xFF, 0xF4, 0x5F, 0xFF, 0xF8, 0x00,
  0x04, 0xFF, 0xFF, 0xA0, 0x0B, 0xFF, 0xFF, 0x30,
  0x1D, 0xFF, 0xFE, 0x10, 0x02, 0xFF, 0xFF, 0xD0,
  0x7F, 0xFF, 0xF6, 0x00, 0x00, 0x8F, 0xFF, 0xF7,
  0xAF, 0xFF, 0xB0, 0x00, 0x00, 0x1D, 0xFF, 0xF9,
  0x5F, 0xFE, 0x20, 0x00, 0x00, 0x05, 0xFF, 0xF4,
  0x02, 0x52, 0x00, 0x00, 0x00, 0x00, 0x25, 0x20
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0059[189] = { /* code 0059, LATIN CAPITAL LETTER Y */
  0x19, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x2B, 0xB4, 0x00,
  0x9F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0xCF, 0xFF, 0x20,
  0xBF, 0xFF, 0xE1, 0x00, 0x00, 0x06, 0xFF, 0xFF, 0x30,
  0x4F, 0xFF, 0xF8, 0x00, 0x00, 0x0D, 0xFF, 0xFC, 0x00,
  0x0B, 0xFF, 0xFF, 0x20, 0x00, 0x7F, 0xFF, 0xF3, 0x00,
  0x02, 0xFF, 0xFF, 0xA0, 0x01, 0xEF, 0xFF, 0xA0, 0x00,
  0x00, 0x8F, 0xFF, 0xF3, 0x08, 0xFF, 0xFE, 0x10, 0x00,
  0x00, 0x0D, 0xFF, 0xFB, 0x1F, 0xFF, 0xF7, 0x00, 0x00,
  0x00, 0x05, 0xFF, 0xFF, 0xCF, 0xFF, 0xC0, 0x00, 0x00,
  0x00, 0x00, 0xBF, 0xFF, 0xFF, 0xFF, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x2F, 0xFF, 0xFF, 0xF9, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x07, 0xFF, 0xFF, 0xE1, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xFF, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xEF, 0xFF, 0x70, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x7F, 0xFE, 0x20, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x03, 0x51, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_005A[180] = { /* code 005A, LATIN CAPITAL LETTER Z */
  0x00, 0x26, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x30,
  0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF7,
  0x02, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF5,
  0x00, 0x14, 0x44, 0x44, 0x44, 0x4C, 0xFF, 0xFF, 0xA0,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x7F, 0xFF, 0xFC, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0xD1, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x3E, 0xFF, 0xFE, 0x30, 0x00,
  0x00, 0x00, 0x00, 0x02, 0xEF, 0xFF, 0xF4, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x1C, 0xFF, 0xFF, 0x70, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xBF, 0xFF, 0xF9, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x08, 0xFF, 0xFF, 0xB0, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x6F, 0xFF, 0xFD, 0x10, 0x00, 0x00, 0x00,
  0x00, 0x04, 0xFF, 0xFF, 0xE2, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x2E, 0xFF, 0xFF, 0x40, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xDF, 0xFF, 0xF6, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x0B, 0xFF, 0xFF, 0xFB, 0xBB, 0xBB, 0xBB, 0xBB, 0x71,
  0x2F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8,
  0x1F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9,
  0x07, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC2
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_005B[100] = { /* code 005B, LEFT SQUARE BRACKET */
  0x04, 0x77, 0x77, 0x50,
  0x5F, 0xFF, 0xFF, 0xF5,
  0x9F, 0xFF, 0xFF, 0xF6,
  0x9F, 0xFE, 0x77, 0x50,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFD, 0x00, 0x00,
  0x9F, 0xFF, 0xFF, 0xC2,
  0x8F, 0xFF, 0xFF, 0xF7,
  0x2C, 0xFF, 0xFF, 0xD3
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_005C[105] = { /* code 005C, REVERSE SOLIDUS */
  0x5D, 0x90, 0x00, 0x00, 0x00,
  0xBF, 0xF3, 0x00, 0x00, 0x00,
  0x8F, 0xF9, 0x00, 0x00, 0x00,
  0x2F, 0xFE, 0x00, 0x00, 0x00,
  0x0B, 0xFF, 0x50, 0x00, 0x00,
  0x06, 0xFF, 0xB0, 0x00, 0x00,
  0x01, 0xEF, 0xF2, 0x00, 0x00,
  0x00, 0x9F, 0xF7, 0x00, 0x00,
  0x00, 0x3F, 0xFD, 0x00, 0x00,
  0x00, 0x0D, 0xFF, 0x40, 0x00,
  0x00, 0x07, 0xFF, 0xA0, 0x00,
  0x00, 0x02, 0xFF, 0xF1, 0x00,
  0x00, 0x00, 0xBF, 0xF6, 0x00,
  0x00, 0x00, 0x5F, 0xFC, 0x00,
  0x00, 0x00, 0x0E, 0xFF, 0x20,
  0x00, 0x00, 0x09, 0xFF, 0x80,
  0x00, 0x00, 0x03, 0xFF, 0xE0,
  0x00, 0x00, 0x00, 0xCF, 0xF4,
  0x00, 0x00, 0x00, 0x7F, 0xFA,
  0x00, 0x00, 0x00, 0x1E, 0xFA,
  0x00, 0x00, 0x00, 0x03, 0x71
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_005D[100] = { /* code 005D, RIGHT SQUARE BRACKET */
  0x26, 0x77, 0x75, 0x10,
  0xDF, 0xFF, 0xFF, 0xD0,
  0xDF, 0xFF, 0xFF, 0xF2,
  0x27, 0x7A, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x00, 0x06, 0xFF, 0xF2,
  0x8E, 0xFF, 0xFF, 0xF2,
  0xEF, 0xFF, 0xFF, 0xF1,
  0x8F, 0xFF, 0xFE, 0x70
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_005E[ 77] = { /* code 005E, CIRCUMFLEX ACCENT */
  0x00, 0x00, 0x02, 0xBC, 0x40, 0x00, 0x00,
  0x00, 0x00, 0x0C, 0xFF, 0xE1, 0x00, 0x00,
  0x00, 0x00, 0x5F, 0xFF, 0xF8, 0x00, 0x00,
  0x00, 0x00, 0xDF, 0xFF, 0xFF, 0x20, 0x00,
  0x00, 0x07, 0xFF, 0xEC, 0xFF, 0x90, 0x00,
  0x00, 0x1E, 0xFF, 0x74, 0xFF, 0xF3, 0x00,
  0x00, 0x8F, 0xFE, 0x00, 0xBF, 0xFB, 0x00,
  0x02, 0xFF, 0xF6, 0x00, 0x3F, 0xFF, 0x40,
  0x09, 0xFF, 0xC0, 0x00, 0x09, 0xFF, 0xC0,
  0x1F, 0xFF, 0x40, 0x00, 0x02, 0xFF, 0xF4,
  0x1D, 0xF9, 0x00, 0x00, 0x00, 0x6F, 0xF3
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_005F[ 16] = { /* code 005F, LOW LINE */
  0xAF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF3,
  0x36, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0060[ 18] = { /* code 0060, GRAVE ACCENT */
  0x37, 0x30, 0x00,
  0xEF, 0xF7, 0x00,
  0xCF, 0xFF, 0xA1,
  0x1B, 0xFF, 0xFC,
  0x00, 0x6E, 0xFF,
  0x00, 0x02, 0x73
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0061[128] = { /* code 0061, LATIN SMALL LETTER A */
  0x00, 0x00, 0x25, 0x77, 0x65, 0x10, 0x00, 0x00,
  0x00, 0x3B, 0xFF, 0xFF, 0xFF, 0xFB, 0x20, 0x00,
  0x04, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0x00,
  0x0D, 0xFF, 0xFC, 0x87, 0xAF, 0xFF, 0xF7, 0x00,
  0x0E, 0xFF, 0x70, 0x00, 0x06, 0xFF, 0xF9, 0x00,
  0x03, 0x95, 0x00, 0x00, 0x05, 0xFF, 0xF9, 0x00,
  0x00, 0x00, 0x13, 0x56, 0x8E, 0xFF, 0xF9, 0x00,
  0x00, 0x7C, 0xFF, 0xFF, 0xFF, 0xFF, 0xF9, 0x00,
  0x1C, 0xFF, 0xFF, 0xFF, 0xDB, 0xFF, 0xF9, 0x00,
  0x8F, 0xFF, 0xD7, 0x31, 0x04, 0xFF, 0xF9, 0x00,
  0xCF, 0xFF, 0x30, 0x00, 0x06, 0xFF, 0xF9, 0x00,
  0xDF, 0xFF, 0x20, 0x00, 0x1D, 0xFF, 0xFA, 0x00,
  0xBF, 0xFF, 0xC5, 0x46, 0xDF, 0xFF, 0xFC, 0x00,
  0x3F, 0xFF, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0x00,
  0x04, 0xDF, 0xFF, 0xFD, 0x50, 0x9F, 0xFC, 0x00,
  0x00, 0x03, 0x64, 0x20, 0x00, 0x04, 0x51, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0062[168] = { /* code 0062, LATIN SMALL LETTER B */
  0x08, 0xB9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x26, 0x76, 0x20, 0x00, 0x00,
  0x7F, 0xFF, 0x78, 0xFF, 0xFF, 0xFA, 0x10, 0x00,
  0x7F, 0xFF, 0xDF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0x7F, 0xFF, 0xFF, 0xB9, 0xCF, 0xFF, 0xF7, 0x00,
  0x7F, 0xFF, 0xF6, 0x00, 0x07, 0xFF, 0xFE, 0x00,
  0x7F, 0xFF, 0xB0, 0x00, 0x00, 0xDF, 0xFF, 0x40,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x8F, 0xFF, 0x70,
  0x7F, 0xFF, 0x50, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x50, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x9F, 0xFF, 0x60,
  0x7F, 0xFF, 0xB0, 0x00, 0x00, 0xEF, 0xFF, 0x30,
  0x7F, 0xFF, 0xF7, 0x00, 0x09, 0xFF, 0xFD, 0x00,
  0x7F, 0xFF, 0xFF, 0xDB, 0xEF, 0xFF, 0xF6, 0x00,
  0x7F, 0xFF, 0x9F, 0xFF, 0xFF, 0xFF, 0xA0, 0x00,
  0x3F, 0xFD, 0x08, 0xFF, 0xFF, 0xE7, 0x00, 0x00,
  0x03, 0x51, 0x00, 0x14, 0x63, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0063[112] = { /* code 0063, LATIN SMALL LETTER C */
  0x00, 0x00, 0x01, 0x57, 0x75, 0x20, 0x00,
  0x00, 0x02, 0xAF, 0xFF, 0xFF, 0xFB, 0x30,
  0x00, 0x3E, 0xFF, 0xFF, 0xFF, 0xFF, 0xF4,
  0x01, 0xDF, 0xFF, 0xEA, 0xAD, 0xFF, 0xFC,
  0x07, 0xFF, 0xFD, 0x20, 0x00, 0xBF, 0xFE,
  0x0D, 0xFF, 0xF4, 0x00, 0x00, 0x1B, 0xD6,
  0x1F, 0xFF, 0xE0, 0x00, 0x00, 0x00, 0x00,
  0x2F, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00,
  0x2F, 0xFF, 0xB0, 0x00, 0x00, 0x00, 0x00,
  0x2F, 0xFF, 0xD0, 0x00, 0x00, 0x02, 0x30,
  0x0E, 0xFF, 0xF3, 0x00, 0x00, 0x3E, 0xFB,
  0x09, 0xFF, 0xFD, 0x20, 0x02, 0xDF, 0xFE,
  0x02, 0xEF, 0xFF, 0xFC, 0xBF, 0xFF, 0xF9,
  0x00, 0x5E, 0xFF, 0xFF, 0xFF, 0xFF, 0xD1,
  0x00, 0x02, 0xAF, 0xFF, 0xFF, 0xE8, 0x10,
  0x00, 0x00, 0x01, 0x35, 0x53, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0064[147] = { /* code 0064, LATIN SMALL LETTER D */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x4B, 0xB3,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xEF, 0xFC,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0x00, 0x00, 0x47, 0x74, 0x00, 0xFF, 0xFF,
  0x00, 0x4E, 0xFF, 0xFF, 0xD2, 0xFF, 0xFF,
  0x05, 0xFF, 0xFF, 0xFF, 0xFD, 0xFF, 0xFF,
  0x1E, 0xFF, 0xFE, 0xAA, 0xEF, 0xFF, 0xFF,
  0x7F, 0xFF, 0xD2, 0x00, 0x1C, 0xFF, 0xFF,
  0xBF, 0xFF, 0x60, 0x00, 0x04, 0xFF, 0xFF,
  0xEF, 0xFF, 0x10, 0x00, 0x00, 0xEF, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0xCF, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0xCF, 0xFF,
  0xEF, 0xFF, 0x10, 0x00, 0x00, 0xEF, 0xFF,
  0xBF, 0xFF, 0x70, 0x00, 0x04, 0xFF, 0xFF,
  0x6F, 0xFF, 0xE3, 0x00, 0x2D, 0xFF, 0xFF,
  0x0D, 0xFF, 0xFF, 0xCC, 0xFF, 0xFF, 0xFF,
  0x03, 0xEF, 0xFF, 0xFF, 0xFE, 0xAF, 0xFE,
  0x00, 0x3B, 0xFF, 0xFF, 0xD3, 0x6F, 0xFB,
  0x00, 0x00, 0x25, 0x53, 0x00, 0x04, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0065[128] = { /* code 0065, LATIN SMALL LETTER E */
  0x00, 0x00, 0x04, 0x67, 0x63, 0x00, 0x00, 0x00,
  0x00, 0x08, 0xEF, 0xFF, 0xFF, 0xD5, 0x00, 0x00,
  0x01, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xA0, 0x00,
  0x0B, 0xFF, 0xFC, 0x64, 0x7D, 0xFF, 0xF7, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x01, 0xDF, 0xFF, 0x10,
  0xAF, 0xFF, 0x30, 0x00, 0x00, 0x6F, 0xFF, 0x50,
  0xDF, 0xFF, 0x99, 0x99, 0x99, 0xBF, 0xFF, 0x70,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0xFF, 0xFF, 0xBB, 0xBB, 0xBB, 0xBB, 0xB9, 0x20,
  0xEF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xBF, 0xFF, 0x50, 0x00, 0x00, 0x08, 0x91, 0x00,
  0x5F, 0xFF, 0xE4, 0x00, 0x02, 0xCF, 0xFA, 0x00,
  0x0B, 0xFF, 0xFF, 0xDB, 0xCF, 0xFF, 0xFA, 0x00,
  0x01, 0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD2, 0x00,
  0x00, 0x06, 0xDF, 0xFF, 0xFF, 0xD7, 0x10, 0x00,
  0x00, 0x00, 0x02, 0x46, 0x42, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0066[105] = { /* code 0066, LATIN SMALL LETTER F */
  0x00, 0x00, 0x3A, 0xDD, 0xA2,
  0x00, 0x05, 0xFF, 0xFF, 0xFA,
  0x00, 0x0E, 0xFF, 0xFF, 0xF9,
  0x00, 0x2F, 0xFF, 0xE4, 0x51,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x01, 0x5F, 0xFF, 0xC2, 0x10,
  0x7F, 0xFF, 0xFF, 0xFF, 0xF8,
  0x9F, 0xFF, 0xFF, 0xFF, 0xFA,
  0x17, 0x9F, 0xFF, 0xD7, 0x71,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x4F, 0xFF, 0xB0, 0x00,
  0x00, 0x3F, 0xFF, 0xB0, 0x00,
  0x00, 0x0D, 0xFF, 0x70, 0x00,
  0x00, 0x01, 0x53, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0067[147] = { /* code 0067, LATIN SMALL LETTER G */
  0x00, 0x00, 0x47, 0x75, 0x00, 0x06, 0x71,
  0x00, 0x4D, 0xFF, 0xFF, 0xE3, 0x7F, 0xFB,
  0x04, 0xFF, 0xFF, 0xFF, 0xFE, 0xBF, 0xFE,
  0x1E, 0xFF, 0xFE, 0xA9, 0xDF, 0xFF, 0xFF,
  0x7F, 0xFF, 0xD1, 0x00, 0x0B, 0xFF, 0xFF,
  0xBF, 0xFF, 0x40, 0x00, 0x02, 0xFF, 0xFF,
  0xEF, 0xFF, 0x00, 0x00, 0x00, 0xEF, 0xFF,
  0xFF, 0xFD, 0x00, 0x00, 0x00, 0xDF, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0xDF, 0xFF,
  0xEF, 0xFF, 0x30, 0x00, 0x01, 0xFF, 0xFF,
  0xAF, 0xFF, 0xA0, 0x00, 0x08, 0xFF, 0xFF,
  0x4F, 0xFF, 0xFB, 0x55, 0xAF, 0xFF, 0xFF,
  0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0x00, 0x9F, 0xFF, 0xFF, 0xF6, 0xDF, 0xFF,
  0x00, 0x02, 0x79, 0x97, 0x10, 0xDF, 0xFD,
  0x00, 0x20, 0x00, 0x00, 0x01, 0xFF, 0xFC,
  0x0A, 0xFD, 0x30, 0x00, 0x06, 0xFF, 0xF8,
  0x0F, 0xFF, 0xE7, 0x23, 0x7F, 0xFF, 0xF2,
  0x0B, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x01, 0xAF, 0xFF, 0xFF, 0xFF, 0xF7, 0x00,
  0x00, 0x03, 0x7A, 0xBB, 0xA6, 0x10, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0068[147] = { /* code 0068, LATIN SMALL LETTER H */
  0x08, 0xB9, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0x60, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x05, 0x77, 0x40, 0x00,
  0x7F, 0xFF, 0x75, 0xEF, 0xFF, 0xFD, 0x30,
  0x7F, 0xFF, 0xCF, 0xFF, 0xFF, 0xFF, 0xE2,
  0x7F, 0xFF, 0xFF, 0xEB, 0xEF, 0xFF, 0xF9,
  0x7F, 0xFF, 0xF9, 0x00, 0x0A, 0xFF, 0xFD,
  0x7F, 0xFF, 0xC0, 0x00, 0x02, 0xFF, 0xFE,
  0x7F, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x6F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFE,
  0x3F, 0xFF, 0x30, 0x00, 0x00, 0xBF, 0xFA,
  0x02, 0x62, 0x00, 0x00, 0x00, 0x05, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0069[ 63] = { /* code 0069, LATIN SMALL LETTER I */
  0x07, 0xDB, 0x20,
  0x3F, 0xFF, 0xA0,
  0x3F, 0xFF, 0xB0,
  0x0B, 0xFF, 0x40,
  0x00, 0x31, 0x00,
  0x03, 0x76, 0x00,
  0x1E, 0xFF, 0x70,
  0x3F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x3F, 0xFF, 0xB0,
  0x0D, 0xFF, 0x60,
  0x01, 0x54, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_006A[104] = { /* code 006A, LATIN SMALL LETTER J */
  0x00, 0x07, 0xDB, 0x20,
  0x00, 0x3F, 0xFF, 0xA0,
  0x00, 0x3F, 0xFF, 0xB0,
  0x00, 0x0B, 0xFF, 0x40,
  0x00, 0x00, 0x31, 0x00,
  0x00, 0x03, 0x76, 0x00,
  0x00, 0x1E, 0xFF, 0x70,
  0x00, 0x3F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x00, 0x4F, 0xFF, 0xB0,
  0x17, 0xAF, 0xFF, 0xA0,
  0x6F, 0xFF, 0xFF, 0x60,
  0x5F, 0xFF, 0xFC, 0x00,
  0x06, 0x98, 0x50, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_006B[147] = { /* code 006B, LATIN SMALL LETTER K */
  0x08, 0xB9, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x5F, 0xFF, 0x60, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x03, 0x72, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x5F, 0xFE, 0x10,
  0x7F, 0xFF, 0x70, 0x03, 0xFF, 0xFF, 0x30,
  0x7F, 0xFF, 0x70, 0x2E, 0xFF, 0xFB, 0x00,
  0x7F, 0xFF, 0x72, 0xEF, 0xFF, 0xB1, 0x00,
  0x7F, 0xFF, 0x9D, 0xFF, 0xFB, 0x10, 0x00,
  0x7F, 0xFF, 0xFF, 0xFF, 0xE1, 0x00, 0x00,
  0x7F, 0xFF, 0xFF, 0xFF, 0xF6, 0x00, 0x00,
  0x7F, 0xFF, 0xFF, 0xFF, 0xFE, 0x30, 0x00,
  0x7F, 0xFF, 0xF8, 0xDF, 0xFF, 0xC0, 0x00,
  0x7F, 0xFF, 0x90, 0x3F, 0xFF, 0xF9, 0x00,
  0x7F, 0xFF, 0x70, 0x07, 0xFF, 0xFF, 0x50,
  0x7F, 0xFF, 0x70, 0x00, 0xBF, 0xFF, 0xE1,
  0x6F, 0xFF, 0x70, 0x00, 0x2E, 0xFF, 0xF3,
  0x3F, 0xFF, 0x30, 0x00, 0x05, 0xFF, 0xD0,
  0x02, 0x52, 0x00, 0x00, 0x00, 0x25, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_006C[ 63] = { /* code 006C, LATIN SMALL LETTER L */
  0x06, 0xBA, 0x10,
  0x2F, 0xFF, 0x90,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x4F, 0xFF, 0xB0,
  0x3F, 0xFF, 0xB0,
  0x0D, 0xFF, 0x60,
  0x01, 0x53, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_006D[192] = { /* code 006D, LATIN SMALL LETTER M */
  0x03, 0x73, 0x00, 0x04, 0x77, 0x40, 0x00, 0x02, 0x67, 0x63, 0x00, 0x00,
  0x3F, 0xFE, 0x14, 0xDF, 0xFF, 0xFB, 0x10, 0x9F, 0xFF, 0xFF, 0xC1, 0x00,
  0x7F, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xAA, 0xFF, 0xFF, 0xFF, 0xFC, 0x00,
  0x7F, 0xFF, 0xFF, 0xEB, 0xEF, 0xFF, 0xFF, 0xFE, 0xBF, 0xFF, 0xFF, 0x30,
  0x7F, 0xFF, 0xF8, 0x00, 0x1E, 0xFF, 0xFF, 0x70, 0x02, 0xEF, 0xFF, 0x60,
  0x7F, 0xFF, 0xC0, 0x00, 0x08, 0xFF, 0xFB, 0x00, 0x00, 0x9F, 0xFF, 0x70,
  0x7F, 0xFF, 0x80, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x6F, 0xFF, 0x70, 0x00, 0x07, 0xFF, 0xF7, 0x00, 0x00, 0x7F, 0xFF, 0x60,
  0x3F, 0xFF, 0x30, 0x00, 0x03, 0xFF, 0xF3, 0x00, 0x00, 0x3F, 0xFF, 0x30,
  0x02, 0x62, 0x00, 0x00, 0x00, 0x26, 0x20, 0x00, 0x00, 0x02, 0x62, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_006E[112] = { /* code 006E, LATIN SMALL LETTER N */
  0x04, 0x73, 0x00, 0x05, 0x77, 0x40, 0x00,
  0x3F, 0xFE, 0x05, 0xEF, 0xFF, 0xFD, 0x30,
  0x7F, 0xFF, 0x8F, 0xFF, 0xFF, 0xFF, 0xE2,
  0x7F, 0xFF, 0xFF, 0xEB, 0xEF, 0xFF, 0xF9,
  0x7F, 0xFF, 0xF9, 0x00, 0x0A, 0xFF, 0xFD,
  0x7F, 0xFF, 0xC0, 0x00, 0x02, 0xFF, 0xFE,
  0x7F, 0xFF, 0x80, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x6F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFE,
  0x3F, 0xFF, 0x30, 0x00, 0x00, 0xBF, 0xFA,
  0x02, 0x62, 0x00, 0x00, 0x00, 0x05, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_006F[128] = { /* code 006F, LATIN SMALL LETTER O */
  0x00, 0x00, 0x15, 0x77, 0x63, 0x00, 0x00, 0x00,
  0x00, 0x19, 0xFF, 0xFF, 0xFF, 0xD5, 0x00, 0x00,
  0x02, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00,
  0x0C, 0xFF, 0xFE, 0x87, 0xAF, 0xFF, 0xF5, 0x00,
  0x5F, 0xFF, 0xD1, 0x00, 0x06, 0xFF, 0xFD, 0x00,
  0xBF, 0xFF, 0x50, 0x00, 0x00, 0xCF, 0xFF, 0x30,
  0xEF, 0xFF, 0x10, 0x00, 0x00, 0x8F, 0xFF, 0x70,
  0xFF, 0xFD, 0x00, 0x00, 0x00, 0x6F, 0xFF, 0x70,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0x6F, 0xFF, 0x70,
  0xEF, 0xFF, 0x10, 0x00, 0x00, 0x8F, 0xFF, 0x70,
  0xBF, 0xFF, 0x60, 0x00, 0x00, 0xDF, 0xFF, 0x30,
  0x5F, 0xFF, 0xE2, 0x00, 0x08, 0xFF, 0xFD, 0x00,
  0x0C, 0xFF, 0xFF, 0xA9, 0xCF, 0xFF, 0xF6, 0x00,
  0x02, 0xDF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0x18, 0xEF, 0xFF, 0xFF, 0xC4, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x56, 0x42, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0070[168] = { /* code 0070, LATIN SMALL LETTER P */
  0x04, 0x73, 0x00, 0x26, 0x75, 0x20, 0x00, 0x00,
  0x4F, 0xFE, 0x1A, 0xFF, 0xFF, 0xF9, 0x10, 0x00,
  0x7F, 0xFF, 0xAF, 0xFF, 0xFF, 0xFF, 0xB0, 0x00,
  0x7F, 0xFF, 0xFF, 0xB9, 0xCF, 0xFF, 0xF7, 0x00,
  0x7F, 0xFF, 0xF5, 0x00, 0x08, 0xFF, 0xFE, 0x00,
  0x7F, 0xFF, 0xB0, 0x00, 0x00, 0xDF, 0xFF, 0x40,
  0x7F, 0xFF, 0x60, 0x00, 0x00, 0x8F, 0xFF, 0x70,
  0x7F, 0xFF, 0x40, 0x00, 0x00, 0x6F, 0xFF, 0x70,
  0x7F, 0xFF, 0x50, 0x00, 0x00, 0x7F, 0xFF, 0x70,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x9F, 0xFF, 0x70,
  0x7F, 0xFF, 0xC0, 0x00, 0x00, 0xDF, 0xFF, 0x30,
  0x7F, 0xFF, 0xF8, 0x00, 0x09, 0xFF, 0xFE, 0x00,
  0x7F, 0xFF, 0xFF, 0xDB, 0xDF, 0xFF, 0xF6, 0x00,
  0x7F, 0xFF, 0xCF, 0xFF, 0xFF, 0xFF, 0xA0, 0x00,
  0x7F, 0xFF, 0x76, 0xEF, 0xFF, 0xF8, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x14, 0x64, 0x10, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x4F, 0xFF, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x05, 0x95, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0071[147] = { /* code 0071, LATIN SMALL LETTER Q */
  0x00, 0x00, 0x47, 0x75, 0x00, 0x06, 0x71,
  0x00, 0x4D, 0xFF, 0xFF, 0xE4, 0x7F, 0xFB,
  0x04, 0xFF, 0xFF, 0xFF, 0xFE, 0xBF, 0xFE,
  0x1E, 0xFF, 0xFF, 0xAA, 0xEF, 0xFF, 0xFF,
  0x7F, 0xFF, 0xE2, 0x00, 0x1C, 0xFF, 0xFF,
  0xBF, 0xFF, 0x60, 0x00, 0x03, 0xFF, 0xFF,
  0xEF, 0xFF, 0x10, 0x00, 0x00, 0xEF, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0xCF, 0xFF,
  0xFF, 0xFE, 0x00, 0x00, 0x00, 0xCF, 0xFF,
  0xEF, 0xFF, 0x10, 0x00, 0x00, 0xEF, 0xFF,
  0xBF, 0xFF, 0x60, 0x00, 0x05, 0xFF, 0xFF,
  0x6F, 0xFF, 0xE3, 0x00, 0x2D, 0xFF, 0xFF,
  0x0D, 0xFF, 0xFF, 0xCC, 0xFF, 0xFF, 0xFF,
  0x04, 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF,
  0x00, 0x3C, 0xFF, 0xFF, 0xB1, 0xFF, 0xFF,
  0x00, 0x00, 0x35, 0x52, 0x00, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFE,
  0x00, 0x00, 0x00, 0x00, 0x00, 0xDF, 0xFB,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x81
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0072[ 80] = { /* code 0072, LATIN SMALL LETTER R */
  0x03, 0x73, 0x00, 0x37, 0x50,
  0x1E, 0xFF, 0x26, 0xFF, 0xF8,
  0x4F, 0xFF, 0x7F, 0xFF, 0xFC,
  0x4F, 0xFF, 0xDF, 0xFF, 0xF8,
  0x4F, 0xFF, 0xFE, 0x86, 0x50,
  0x4F, 0xFF, 0xF5, 0x00, 0x00,
  0x4F, 0xFF, 0xE0, 0x00, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x00,
  0x4F, 0xFF, 0xB0, 0x00, 0x00,
  0x3F, 0xFF, 0xB0, 0x00, 0x00,
  0x0D, 0xFF, 0x60, 0x00, 0x00,
  0x01, 0x54, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0073[112] = { /* code 0073, LATIN SMALL LETTER S */
  0x00, 0x00, 0x35, 0x77, 0x52, 0x00, 0x00,
  0x00, 0x3C, 0xFF, 0xFF, 0xFF, 0xD5, 0x00,
  0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x0C, 0xFF, 0xFC, 0x77, 0xAF, 0xFF, 0xD0,
  0x0F, 0xFF, 0xE0, 0x00, 0x05, 0xEF, 0x90,
  0x0F, 0xFF, 0xF7, 0x10, 0x00, 0x13, 0x00,
  0x0C, 0xFF, 0xFF, 0xFC, 0x95, 0x10, 0x00,
  0x03, 0xEF, 0xFF, 0xFF, 0xFF, 0xF8, 0x10,
  0x00, 0x29, 0xEF, 0xFF, 0xFF, 0xFF, 0xB0,
  0x00, 0x00, 0x03, 0x7B, 0xEF, 0xFF, 0xF5,
  0x07, 0xC7, 0x00, 0x00, 0x0A, 0xFF, 0xF7,
  0x0F, 0xFF, 0x90, 0x00, 0x07, 0xFF, 0xF7,
  0x0E, 0xFF, 0xFC, 0x76, 0x9F, 0xFF, 0xF2,
  0x04, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0x70,
  0x00, 0x29, 0xEF, 0xFF, 0xFF, 0xC4, 0x00,
  0x00, 0x00, 0x03, 0x46, 0x41, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0074[100] = { /* code 0074, LATIN SMALL LETTER T */
  0x00, 0x03, 0x74, 0x00, 0x00,
  0x00, 0x3F, 0xFF, 0x50, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x01, 0x7F, 0xFF, 0x82, 0x10,
  0x7F, 0xFF, 0xFF, 0xFF, 0xF5,
  0x9F, 0xFF, 0xFF, 0xFF, 0xF7,
  0x17, 0xAF, 0xFF, 0xB7, 0x60,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x70, 0x00,
  0x00, 0x6F, 0xFF, 0x80, 0x00,
  0x00, 0x5F, 0xFF, 0xFB, 0xA2,
  0x00, 0x2F, 0xFF, 0xFF, 0xF7,
  0x00, 0x06, 0xFF, 0xFF, 0xE3,
  0x00, 0x00, 0x14, 0x64, 0x10
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0075[112] = { /* code 0075, LATIN SMALL LETTER U */
  0x04, 0x74, 0x00, 0x00, 0x00, 0x17, 0x71,
  0x3F, 0xFF, 0x40, 0x00, 0x00, 0xCF, 0xFB,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFE,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x00, 0xFF, 0xFF,
  0x7F, 0xFF, 0x70, 0x00, 0x01, 0xFF, 0xFF,
  0x6F, 0xFF, 0xB0, 0x00, 0x06, 0xFF, 0xFF,
  0x5F, 0xFF, 0xF5, 0x00, 0x5E, 0xFF, 0xFF,
  0x1F, 0xFF, 0xFF, 0xEE, 0xFF, 0xFF, 0xFF,
  0x07, 0xFF, 0xFF, 0xFF, 0xFB, 0xAF, 0xFE,
  0x00, 0x7F, 0xFF, 0xFF, 0x80, 0x6F, 0xFA,
  0x00, 0x01, 0x46, 0x41, 0x00, 0x04, 0x50
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0076[112] = { /* code 0076, LATIN SMALL LETTER V */
  0x05, 0x61, 0x00, 0x00, 0x00, 0x37, 0x20,
  0x8F, 0xFC, 0x00, 0x00, 0x04, 0xFF, 0xE1,
  0xBF, 0xFF, 0x30, 0x00, 0x0A, 0xFF, 0xF4,
  0x9F, 0xFF, 0x70, 0x00, 0x0E, 0xFF, 0xF2,
  0x3F, 0xFF, 0xB0, 0x00, 0x4F, 0xFF, 0xB0,
  0x0D, 0xFF, 0xF1, 0x00, 0x8F, 0xFF, 0x60,
  0x08, 0xFF, 0xF5, 0x00, 0xDF, 0xFF, 0x10,
  0x02, 0xFF, 0xFA, 0x02, 0xFF, 0xFA, 0x00,
  0x00, 0xCF, 0xFE, 0x07, 0xFF, 0xF4, 0x00,
  0x00, 0x7F, 0xFF, 0x4B, 0xFF, 0xE0, 0x00,
  0x00, 0x1F, 0xFF, 0x9F, 0xFF, 0x90, 0x00,
  0x00, 0x0B, 0xFF, 0xFF, 0xFF, 0x30, 0x00,
  0x00, 0x05, 0xFF, 0xFF, 0xFD, 0x00, 0x00,
  0x00, 0x01, 0xFF, 0xFF, 0xF8, 0x00, 0x00,
  0x00, 0x00, 0x9F, 0xFF, 0xE2, 0x00, 0x00,
  0x00, 0x00, 0x04, 0x65, 0x20, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0077[176] = { /* code 0077, LATIN SMALL LETTER W */
  0x04, 0x72, 0x00, 0x00, 0x05, 0x75, 0x00, 0x00, 0x02, 0x74, 0x00,
  0x6F, 0xFE, 0x00, 0x00, 0x6F, 0xFF, 0x70, 0x00, 0x0D, 0xFF, 0x60,
  0x9F, 0xFF, 0x30, 0x00, 0xCF, 0xFF, 0xD0, 0x00, 0x3F, 0xFF, 0x90,
  0x7F, 0xFF, 0x70, 0x01, 0xFF, 0xFF, 0xF2, 0x00, 0x7F, 0xFF, 0x70,
  0x2F, 0xFF, 0xB0, 0x04, 0xFF, 0xFF, 0xF5, 0x00, 0xAF, 0xFF, 0x30,
  0x0D, 0xFF, 0xE0, 0x07, 0xFF, 0xEF, 0xF9, 0x00, 0xEF, 0xFD, 0x00,
  0x08, 0xFF, 0xF3, 0x0A, 0xFF, 0x7F, 0xFC, 0x02, 0xFF, 0xF8, 0x00,
  0x03, 0xFF, 0xF7, 0x0D, 0xFE, 0x0E, 0xFF, 0x05, 0xFF, 0xF4, 0x00,
  0x00, 0xEF, 0xFA, 0x2F, 0xFB, 0x0B, 0xFF, 0x38, 0xFF, 0xE0, 0x00,
  0x00, 0x9F, 0xFE, 0x5F, 0xF7, 0x07, 0xFF, 0x7C, 0xFF, 0xA0, 0x00,
  0x00, 0x5F, 0xFF, 0xBF, 0xF3, 0x03, 0xFF, 0xBF, 0xFF, 0x50, 0x00,
  0x00, 0x1F, 0xFF, 0xFF, 0xE0, 0x00, 0xEF, 0xFF, 0xFF, 0x10, 0x00,
  0x00, 0x0B, 0xFF, 0xFF, 0xB0, 0x00, 0xBF, 0xFF, 0xFB, 0x00, 0x00,
  0x00, 0x06, 0xFF, 0xFF, 0x70, 0x00, 0x7F, 0xFF, 0xF6, 0x00, 0x00,
  0x00, 0x01, 0xEF, 0xFE, 0x20, 0x00, 0x2E, 0xFF, 0xE1, 0x00, 0x00,
  0x00, 0x00, 0x15, 0x52, 0x00, 0x00, 0x02, 0x55, 0x10, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0078[112] = { /* code 0078, LATIN SMALL LETTER X */
  0x01, 0x75, 0x00, 0x00, 0x02, 0x73, 0x00,
  0x0D, 0xFF, 0x60, 0x00, 0x2E, 0xFF, 0x30,
  0x3F, 0xFF, 0xE1, 0x00, 0xBF, 0xFF, 0x50,
  0x0D, 0xFF, 0xFA, 0x05, 0xFF, 0xFE, 0x10,
  0x03, 0xFF, 0xFF, 0x4D, 0xFF, 0xF5, 0x00,
  0x00, 0x8F, 0xFF, 0xFF, 0xFF, 0xA0, 0x00,
  0x00, 0x0D, 0xFF, 0xFF, 0xFE, 0x10, 0x00,
  0x00, 0x03, 0xFF, 0xFF, 0xF5, 0x00, 0x00,
  0x00, 0x07, 0xFF, 0xFF, 0xF9, 0x00, 0x00,
  0x00, 0x3F, 0xFF, 0xFF, 0xFF, 0x40, 0x00,
  0x00, 0xCF, 0xFF, 0xCF, 0xFF, 0xD1, 0x00,
  0x08, 0xFF, 0xFC, 0x0D, 0xFF, 0xF9, 0x00,
  0x3F, 0xFF, 0xF3, 0x03, 0xFF, 0xFF, 0x40,
  0x7F, 0xFF, 0x80, 0x00, 0x9F, 0xFF, 0x70,
  0x3F, 0xFD, 0x10, 0x00, 0x1D, 0xFF, 0x30,
  0x02, 0x51, 0x00, 0x00, 0x01, 0x52, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_0079[147] = { /* code 0079, LATIN SMALL LETTER Y */
  0x04, 0x72, 0x00, 0x00, 0x00, 0x57, 0x10,
  0x6F, 0xFE, 0x10, 0x00, 0x06, 0xFF, 0xD0,
  0x9F, 0xFF, 0x60, 0x00, 0x0C, 0xFF, 0xF2,
  0x7F, 0xFF, 0xA0, 0x00, 0x1F, 0xFF, 0xD0,
  0x2F, 0xFF, 0xE0, 0x00, 0x5F, 0xFF, 0x80,
  0x0B, 0xFF, 0xF3, 0x00, 0x9F, 0xFF, 0x30,
  0x06, 0xFF, 0xF7, 0x00, 0xDF, 0xFC, 0x00,
  0x01, 0xFF, 0xFC, 0x02, 0xFF, 0xF7, 0x00,
  0x00, 0xAF, 0xFF, 0x16, 0xFF, 0xF2, 0x00,
  0x00, 0x5F, 0xFF, 0x5A, 0xFF, 0xB0, 0x00,
  0x00, 0x1E, 0xFF, 0x9E, 0xFF, 0x70, 0x00,
  0x00, 0x0A, 0xFF, 0xFF, 0xFF, 0x10, 0x00,
  0x00, 0x05, 0xFF, 0xFF, 0xFB, 0x00, 0x00,
  0x00, 0x00, 0xEF, 0xFF, 0xF5, 0x00, 0x00,
  0x00, 0x00, 0x9F, 0xFF, 0xF1, 0x00, 0x00,
  0x00, 0x00, 0x4F, 0xFF, 0xA0, 0x00, 0x00,
  0x00, 0x00, 0x5F, 0xFF, 0x50, 0x00, 0x00,
  0x02, 0xAD, 0xFF, 0xFE, 0x00, 0x00, 0x00,
  0x09, 0xFF, 0xFF, 0xF6, 0x00, 0x00, 0x00,
  0x07, 0xFF, 0xFF, 0xA0, 0x00, 0x00, 0x00,
  0x00, 0x69, 0x84, 0x00, 0x00, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_007A[120] = { /* code 007A, LATIN SMALL LETTER Z */
  0x00, 0x12, 0x22, 0x22, 0x22, 0x22, 0x20, 0x00,
  0x04, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x00,
  0x07, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00,
  0x03, 0xCF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xBF, 0xFF, 0xD1, 0x00,
  0x00, 0x00, 0x00, 0x0A, 0xFF, 0xFD, 0x10, 0x00,
  0x00, 0x00, 0x00, 0x9F, 0xFF, 0xE2, 0x00, 0x00,
  0x00, 0x00, 0x08, 0xFF, 0xFE, 0x30, 0x00, 0x00,
  0x00, 0x00, 0x7F, 0xFF, 0xF4, 0x00, 0x00, 0x00,
  0x00, 0x05, 0xFF, 0xFF, 0x50, 0x00, 0x00, 0x00,
  0x00, 0x4F, 0xFF, 0xF7, 0x00, 0x00, 0x00, 0x00,
  0x03, 0xEF, 0xFF, 0x92, 0x22, 0x22, 0x20, 0x00,
  0x1E, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x00,
  0x2F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x30,
  0x0A, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_007B[125] = { /* code 007B, LEFT CURLY BRACKET */
  0x00, 0x00, 0x26, 0x77, 0x60,
  0x00, 0x06, 0xFF, 0xFF, 0xF6,
  0x00, 0x1F, 0xFF, 0xFF, 0xE3,
  0x00, 0x3F, 0xFF, 0x90, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x5F, 0xFF, 0x40, 0x00,
  0x48, 0xEF, 0xFD, 0x00, 0x00,
  0xEF, 0xFE, 0x92, 0x00, 0x00,
  0xAF, 0xFF, 0xF7, 0x00, 0x00,
  0x01, 0x9F, 0xFF, 0x20, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x40, 0x00,
  0x00, 0x4F, 0xFF, 0x50, 0x00,
  0x00, 0x3F, 0xFF, 0xD7, 0x71,
  0x00, 0x0C, 0xFF, 0xFF, 0xF6,
  0x00, 0x02, 0xAE, 0xFF, 0xE3
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_007C[ 42] = { /* code 007C, VERTICAL LINE */
  0x2C, 0xC2,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x9F, 0xF9,
  0x7F, 0xF7,
  0x06, 0x60
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_007D[125] = { /* code 007D, RIGHT CURLY BRACKET */
  0x37, 0x76, 0x40, 0x00, 0x00,
  0xEF, 0xFF, 0xFD, 0x10, 0x00,
  0xAF, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0x2E, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xD0, 0x00,
  0x00, 0x06, 0xFF, 0xFA, 0x70,
  0x00, 0x00, 0x6C, 0xFF, 0xF6,
  0x00, 0x02, 0xDF, 0xFF, 0xF3,
  0x00, 0x0A, 0xFF, 0xF3, 0x00,
  0x00, 0x0B, 0xFF, 0xC0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0B, 0xFF, 0xB0, 0x00,
  0x00, 0x0D, 0xFF, 0xB0, 0x00,
  0x37, 0x9F, 0xFF, 0xA0, 0x00,
  0xEF, 0xFF, 0xFF, 0x50, 0x00,
  0xAF, 0xFF, 0xC6, 0x00, 0x00
};

GUI_CONST_STORAGE unsigned char acGUI_FontRounded33_007E[ 48] = { /* code 007E, TILDE */
  0x00, 0x02, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x04, 0xEF, 0xFE, 0x92, 0x00, 0x02, 0xDC, 0x00,
  0x4F, 0xFF, 0xFF, 0xFF, 0xB6, 0x7E, 0xFF, 0x10,
  0xBF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFD, 0x00,
  0xDF, 0xE4, 0x26, 0xCF, 0xFF, 0xFF, 0xF4, 0x00,
  0x59, 0x10, 0x00, 0x03, 0x9D, 0xDA, 0x30, 0x00
};

GUI_CONST_STORAGE GUI_CHARINFO_EXT GUI_FontRounded33_CharInfo[95] = {
   {   1,   1,   0,  27,   8, acGUI_FontRounded33_0020 } /* code 0020, SPACE */
  ,{   5,  21,   2,   7,   9, acGUI_FontRounded33_0021 } /* code 0021, EXCLAMATION MARK */
  ,{  10,   9,   2,   7,  14, acGUI_FontRounded33_0022 } /* code 0022, QUOTATION MARK */
  ,{  15,  21,   1,   7,  17, acGUI_FontRounded33_0023 } /* code 0023, NUMBER SIGN */
  ,{  16,  24,   0,   6,  17, acGUI_FontRounded33_0024 } /* code 0024, DOLLAR SIGN */
  ,{  23,  21,   2,   7,  27, acGUI_FontRounded33_0025 } /* code 0025, PERCENT SIGN */
  ,{  18,  21,   1,   7,  19, acGUI_FontRounded33_0026 } /* code 0026, AMPERSAND */
  ,{   4,   9,   2,   7,   9, acGUI_FontRounded33_0027 } /* code 0027, APOSTROPHE */
  ,{   7,  26,   1,   7,   9, acGUI_FontRounded33_0028 } /* code 0028, LEFT PARENTHESIS */
  ,{   7,  26,   1,   7,   9, acGUI_FontRounded33_0029 } /* code 0029, RIGHT PARENTHESIS */
  ,{  11,  10,   1,   7,  13, acGUI_FontRounded33_002A } /* code 002A, ASTERISK */
  ,{  16,  16,   0,  11,  16, acGUI_FontRounded33_002B } /* code 002B, PLUS SIGN */
  ,{   6,   9,   1,  22,   8, acGUI_FontRounded33_002C } /* code 002C, COMMA */
  ,{   9,   5,   1,  17,  11, acGUI_FontRounded33_002D } /* code 002D, HYPHEN-MINUS */
  ,{   6,   6,   1,  22,   8, acGUI_FontRounded33_002E } /* code 002E, FULL STOP */
  ,{  10,  21,   0,   7,  10, acGUI_FontRounded33_002F } /* code 002F, SOLIDUS */
  ,{  15,  21,   1,   7,  17, acGUI_FontRounded33_0030 } /* code 0030, DIGIT ZERO */
  ,{  10,  21,  -1,   7,  11, acGUI_FontRounded33_0031 } /* code 0031, DIGIT ONE */
  ,{  16,  20,   0,   7,  16, acGUI_FontRounded33_0032 } /* code 0032, DIGIT TWO */
  ,{  15,  21,   0,   7,  16, acGUI_FontRounded33_0033 } /* code 0033, DIGIT THREE */
  ,{  16,  21,   0,   7,  16, acGUI_FontRounded33_0034 } /* code 0034, DIGIT FOUR */
  ,{  15,  20,   0,   8,  16, acGUI_FontRounded33_0035 } /* code 0035, DIGIT FIVE */
  ,{  15,  21,   1,   7,  17, acGUI_FontRounded33_0036 } /* code 0036, DIGIT SIX */
  ,{  15,  20,   0,   8,  15, acGUI_FontRounded33_0037 } /* code 0037, DIGIT SEVEN */
  ,{  15,  21,   1,   7,  17, acGUI_FontRounded33_0038 } /* code 0038, DIGIT EIGHT */
  ,{  15,  21,   1,   7,  17, acGUI_FontRounded33_0039 } /* code 0039, DIGIT NINE */
  ,{   6,  16,   1,  12,   8, acGUI_FontRounded33_003A } /* code 003A, COLON */
  ,{   6,  19,   1,  12,   8, acGUI_FontRounded33_003B } /* code 003B, SEMICOLON */
  ,{  16,  15,   0,  12,  16, acGUI_FontRounded33_003C } /* code 003C, LESS-THAN SIGN */
  ,{  16,  11,   0,  14,  16, acGUI_FontRounded33_003D } /* code 003D, EQUALS SIGN */
  ,{  16,  15,   0,  12,  16, acGUI_FontRounded33_003E } /* code 003E, GREATER-THAN SIGN */
  ,{  14,  21,   1,   7,  15, acGUI_FontRounded33_003F } /* code 003F, QUESTION MARK */
  ,{  21,  21,   0,   7,  22, acGUI_FontRounded33_0040 } /* code 0040, COMMERCIAL AT */
  ,{  18,  21,   0,   7,  18, acGUI_FontRounded33_0041 } /* code 0041, LATIN CAPITAL LETTER A */
  ,{  17,  20,   2,   7,  19, acGUI_FontRounded33_0042 } /* code 0042, LATIN CAPITAL LETTER B */
  ,{  18,  21,   1,   7,  19, acGUI_FontRounded33_0043 } /* code 0043, LATIN CAPITAL LETTER C */
  ,{  18,  20,   2,   7,  20, acGUI_FontRounded33_0044 } /* code 0044, LATIN CAPITAL LETTER D */
  ,{  16,  20,   2,   7,  18, acGUI_FontRounded33_0045 } /* code 0045, LATIN CAPITAL LETTER E */
  ,{  15,  21,   2,   7,  17, acGUI_FontRounded33_0046 } /* code 0046, LATIN CAPITAL LETTER F */
  ,{  20,  21,   1,   7,  22, acGUI_FontRounded33_0047 } /* code 0047, LATIN CAPITAL LETTER G */
  ,{  17,  21,   2,   7,  21, acGUI_FontRounded33_0048 } /* code 0048, LATIN CAPITAL LETTER H */
  ,{   5,  21,   2,   7,   9, acGUI_FontRounded33_0049 } /* code 0049, LATIN CAPITAL LETTER I */
  ,{  14,  21,   0,   7,  15, acGUI_FontRounded33_004A } /* code 004A, LATIN CAPITAL LETTER J */
  ,{  17,  21,   2,   7,  18, acGUI_FontRounded33_004B } /* code 004B, LATIN CAPITAL LETTER K */
  ,{  15,  20,   2,   7,  16, acGUI_FontRounded33_004C } /* code 004C, LATIN CAPITAL LETTER L */
  ,{  20,  21,   2,   7,  24, acGUI_FontRounded33_004D } /* code 004D, LATIN CAPITAL LETTER M */
  ,{  17,  21,   2,   7,  21, acGUI_FontRounded33_004E } /* code 004E, LATIN CAPITAL LETTER N */
  ,{  20,  21,   1,   7,  22, acGUI_FontRounded33_004F } /* code 004F, LATIN CAPITAL LETTER O */
  ,{  17,  21,   2,   7,  19, acGUI_FontRounded33_0050 } /* code 0050, LATIN CAPITAL LETTER P */
  ,{  20,  22,   1,   7,  22, acGUI_FontRounded33_0051 } /* code 0051, LATIN CAPITAL LETTER Q */
  ,{  17,  21,   2,   7,  20, acGUI_FontRounded33_0052 } /* code 0052, LATIN CAPITAL LETTER R */
  ,{  17,  21,   1,   7,  18, acGUI_FontRounded33_0053 } /* code 0053, LATIN CAPITAL LETTER S */
  ,{  18,  21,   0,   7,  18, acGUI_FontRounded33_0054 } /* code 0054, LATIN CAPITAL LETTER T */
  ,{  17,  21,   2,   7,  21, acGUI_FontRounded33_0055 } /* code 0055, LATIN CAPITAL LETTER U */
  ,{  18,  21,   0,   7,  17, acGUI_FontRounded33_0056 } /* code 0056, LATIN CAPITAL LETTER V */
  ,{  25,  21,   0,   7,  25, acGUI_FontRounded33_0057 } /* code 0057, LATIN CAPITAL LETTER W */
  ,{  16,  21,   0,   7,  16, acGUI_FontRounded33_0058 } /* code 0058, LATIN CAPITAL LETTER X */
  ,{  17,  21,   0,   7,  17, acGUI_FontRounded33_0059 } /* code 0059, LATIN CAPITAL LETTER Y */
  ,{  18,  20,   0,   7,  18, acGUI_FontRounded33_005A } /* code 005A, LATIN CAPITAL LETTER Z */
  ,{   8,  25,   2,   7,  10, acGUI_FontRounded33_005B } /* code 005B, LEFT SQUARE BRACKET */
  ,{  10,  21,   0,   7,  10, acGUI_FontRounded33_005C } /* code 005C, REVERSE SOLIDUS */
  ,{   8,  25,   0,   7,  10, acGUI_FontRounded33_005D } /* code 005D, RIGHT SQUARE BRACKET */
  ,{  14,  11,   1,   8,  16, acGUI_FontRounded33_005E } /* code 005E, CIRCUMFLEX ACCENT */
  ,{  16,   2,  -1,  29,  13, acGUI_FontRounded33_005F } /* code 005F, LOW LINE */
  ,{   6,   6,   0,   6,   8, acGUI_FontRounded33_0060 } /* code 0060, GRAVE ACCENT */
  ,{  15,  16,   1,  12,  16, acGUI_FontRounded33_0061 } /* code 0061, LATIN SMALL LETTER A */
  ,{  15,  21,   1,   7,  16, acGUI_FontRounded33_0062 } /* code 0062, LATIN SMALL LETTER B */
  ,{  14,  16,   0,  12,  14, acGUI_FontRounded33_0063 } /* code 0063, LATIN SMALL LETTER C */
  ,{  14,  21,   1,   7,  16, acGUI_FontRounded33_0064 } /* code 0064, LATIN SMALL LETTER D */
  ,{  15,  16,   1,  12,  17, acGUI_FontRounded33_0065 } /* code 0065, LATIN SMALL LETTER E */
  ,{  10,  21,   0,   7,  10, acGUI_FontRounded33_0066 } /* code 0066, LATIN SMALL LETTER F */
  ,{  14,  21,   1,  12,  16, acGUI_FontRounded33_0067 } /* code 0067, LATIN SMALL LETTER G */
  ,{  14,  21,   1,   7,  16, acGUI_FontRounded33_0068 } /* code 0068, LATIN SMALL LETTER H */
  ,{   5,  21,   1,   7,   8, acGUI_FontRounded33_0069 } /* code 0069, LATIN SMALL LETTER I */
  ,{   7,  26,  -1,   7,   8, acGUI_FontRounded33_006A } /* code 006A, LATIN SMALL LETTER J */
  ,{  14,  21,   1,   7,  14, acGUI_FontRounded33_006B } /* code 006B, LATIN SMALL LETTER K */
  ,{   5,  21,   1,   7,   8, acGUI_FontRounded33_006C } /* code 006C, LATIN SMALL LETTER L */
  ,{  23,  16,   1,  12,  25, acGUI_FontRounded33_006D } /* code 006D, LATIN SMALL LETTER M */
  ,{  14,  16,   1,  12,  16, acGUI_FontRounded33_006E } /* code 006E, LATIN SMALL LETTER N */
  ,{  15,  16,   1,  12,  17, acGUI_FontRounded33_006F } /* code 006F, LATIN SMALL LETTER O */
  ,{  15,  21,   1,  12,  16, acGUI_FontRounded33_0070 } /* code 0070, LATIN SMALL LETTER P */
  ,{  14,  21,   1,  12,  16, acGUI_FontRounded33_0071 } /* code 0071, LATIN SMALL LETTER Q */
  ,{  10,  16,   1,  12,  11, acGUI_FontRounded33_0072 } /* code 0072, LATIN SMALL LETTER R */
  ,{  14,  16,   0,  12,  14, acGUI_FontRounded33_0073 } /* code 0073, LATIN SMALL LETTER S */
  ,{  10,  20,   0,   8,  10, acGUI_FontRounded33_0074 } /* code 0074, LATIN SMALL LETTER T */
  ,{  14,  16,   1,  12,  16, acGUI_FontRounded33_0075 } /* code 0075, LATIN SMALL LETTER U */
  ,{  14,  16,   0,  12,  13, acGUI_FontRounded33_0076 } /* code 0076, LATIN SMALL LETTER V */
  ,{  21,  16,   0,  12,  21, acGUI_FontRounded33_0077 } /* code 0077, LATIN SMALL LETTER W */
  ,{  13,  16,   0,  12,  13, acGUI_FontRounded33_0078 } /* code 0078, LATIN SMALL LETTER X */
  ,{  14,  21,   0,  12,  13, acGUI_FontRounded33_0079 } /* code 0079, LATIN SMALL LETTER Y */
  ,{  15,  15,   0,  12,  15, acGUI_FontRounded33_007A } /* code 007A, LATIN SMALL LETTER Z */
  ,{  10,  25,   0,   7,  10, acGUI_FontRounded33_007B } /* code 007B, LEFT CURLY BRACKET */
  ,{   4,  21,   1,   7,   6, acGUI_FontRounded33_007C } /* code 007C, VERTICAL LINE */
  ,{  10,  25,   0,   7,  10, acGUI_FontRounded33_007D } /* code 007D, RIGHT CURLY BRACKET */
  ,{  15,   6,   1,  16,  16, acGUI_FontRounded33_007E } /* code 007E, TILDE */
};

GUI_CONST_STORAGE GUI_FONT_PROP_EXT GUI_FontRounded33_Prop1 = {
   0x0020 /* first character */
  ,0x007E /* last character  */
  ,&GUI_FontRounded33_CharInfo[  0] /* address of first character */
  ,(GUI_CONST_STORAGE GUI_FONT_PROP_EXT *)0 /* pointer to next GUI_FONT_PROP_EXT */
};

GUI_CONST_STORAGE GUI_FONT GUI_FontRounded33 = {
   GUI_FONTTYPE_PROP_AA4_EXT /* type of font    */
  ,33 /* height of font  */
  ,33 /* space of font y */
  ,1 /* magnification x */
  ,1 /* magnification y */
  ,{&GUI_FontRounded33_Prop1}
  ,33 /* Baseline */
  ,16 /* Height of lowercase characters */
  ,21 /* Height of capital characters */
};

/*********************************************************************
*
*       _SeggerLogo50x25
*/
static GUI_CONST_STORAGE GUI_COLOR ColorsSeggerLogo50x25[] = {
     0x00FF00,0xFFFFFF,0x908F91,0x201F23
    ,0xA02020,0xB85858,0x58575A,0xF9F1F1
    ,0xE7C7C7,0x2E2D31,0x747376,0xA62E2E
    ,0xEDD5D5,0xB24A4A,0x9D9D9F,0xB9B9BA
    ,0xC7C7C8,0xD5D5D6,0x3C3B3F,0xBE6666
    ,0xCA8282,0xE1B9B9,0xE3E3E4,0xF1F1F1
    ,0xAC3C3C,0x828183,0xABABAD,0xDBABAB
    ,0x4A494C,0xD59D9D,0xF3E3E3,0x666568
    ,0xC47474,0xD09090
};

static GUI_CONST_STORAGE GUI_LOGPALETTE PalSeggerLogo50x25 = {
  34,	/* number of entries */
  1, 	/* Has transparency */
  &ColorsSeggerLogo50x25[0]
};

static GUI_CONST_STORAGE unsigned char acSeggerLogo50x25[] = {
  0x00, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x00,
  0x03, 0x09, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x09, 0x03,
  0x03, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x07, 0x21, 0x07, 0x01, 0x01, 0x15, 0x15, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x0D, 0x01, 0x01, 0x0D, 0x04, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x13, 0x04, 0x14, 0x01, 0x08, 0x04, 0x0B, 0x07, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x07, 0x18, 0x04, 0x15, 0x01, 0x1D, 0x04, 0x0D, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x01, 0x0C, 0x04, 0x0B, 0x1E, 0x01, 0x13, 0x04, 0x14, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x0C, 0x0C, 0x01, 0x1D, 0x04, 0x0D, 0x01, 0x07, 0x18, 0x04, 0x1B, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x0D, 0x07, 0x01, 0x13, 0x04, 0x14, 0x01, 0x0C, 0x0B, 0x0B, 0x0C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x20, 0x01, 0x07, 0x18, 0x04, 0x15, 0x01, 0x1B, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x05, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x04, 0x1B, 0x01, 0x0C, 0x0B, 0x0B, 0x1E, 0x01, 0x14, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x13, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x04, 0x18, 0x01, 0x01, 0x20, 0x04, 0x21, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x04, 0x1B, 0x01, 0x0C, 0x0B, 0x0B, 0x1E, 0x01, 0x14, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
        0x05, 0x05, 0x05, 0x05, 0x13, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x20, 0x01, 0x07, 0x18, 0x04, 0x15, 0x01, 0x1B, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
        0x04, 0x04, 0x04, 0x04, 0x05, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x0D, 0x07, 0x01, 0x13, 0x04, 0x14, 0x01, 0x0C, 0x0B, 0x0B, 0x0C, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x0C, 0x0C, 0x01, 0x1D, 0x04, 0x0D, 0x01, 0x07, 0x18, 0x04, 0x1B, 0x01, 0x1A, 0x02, 0x02, 0x16, 0x01, 0x0E, 0x02, 0x02, 0x0E, 0x17, 0x17, 0x1A, 0x02, 0x0E, 0x16, 0x01, 0x01, 0x1A, 0x02, 0x02, 0x16, 0x01, 0x10, 0x02, 0x02, 0x02, 0x10, 0x11,
        0x02, 0x02, 0x02, 0x11, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x01, 0x0C, 0x04, 0x0B, 0x1E, 0x01, 0x13, 0x04, 0x14, 0x01, 0x0E, 0x03, 0x19, 0x12, 0x1C, 0x10, 0x03, 0x1C, 0x06, 0x06, 0x16, 0x1C, 0x09, 0x19, 0x12, 0x09, 0x01, 0x19, 0x03, 0x0A, 0x12, 0x03, 0x16, 0x06, 0x09, 0x06, 0x06, 0x0E, 0x02,
        0x03, 0x06, 0x1C, 0x03, 0x11, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x07, 0x18, 0x04, 0x15, 0x01, 0x1D, 0x04, 0x0D, 0x01, 0x01, 0x0E, 0x03, 0x0A, 0x0E, 0x0F, 0x10, 0x03, 0x0A, 0x02, 0x02, 0x11, 0x03, 0x1A, 0x17, 0x0F, 0x02, 0x01, 0x03, 0x19, 0x01, 0x0F, 0x02, 0x17, 0x06, 0x12, 0x02, 0x02, 0x10, 0x02,
        0x03, 0x10, 0x02, 0x03, 0x11, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x13, 0x04, 0x14, 0x01, 0x08, 0x04, 0x0B, 0x07, 0x01, 0x01, 0x17, 0x0E, 0x0A, 0x09, 0x09, 0x1A, 0x03, 0x0A, 0x02, 0x02, 0x10, 0x03, 0x0F, 0x0F, 0x06, 0x03, 0x10, 0x03, 0x02, 0x11, 0x06, 0x03, 0x02, 0x06, 0x12, 0x02, 0x02, 0x11, 0x02,
        0x03, 0x06, 0x12, 0x09, 0x17, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x08, 0x04, 0x0D, 0x01, 0x01, 0x0D, 0x04, 0x08, 0x01, 0x01, 0x01, 0x02, 0x09, 0x1A, 0x0A, 0x03, 0x0E, 0x03, 0x0A, 0x02, 0x02, 0x11, 0x12, 0x12, 0x02, 0x1F, 0x03, 0x10, 0x06, 0x09, 0x02, 0x0A, 0x03, 0x02, 0x06, 0x12, 0x02, 0x02, 0x1A, 0x02,
        0x03, 0x01, 0x0F, 0x03, 0x10, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x07, 0x21, 0x07, 0x01, 0x01, 0x15, 0x15, 0x01, 0x01, 0x01, 0x01, 0x17, 0x19, 0x06, 0x06, 0x0F, 0x01, 0x02, 0x02, 0x02, 0x02, 0x11, 0x16, 0x0A, 0x06, 0x0A, 0x0A, 0x11, 0x17, 0x19, 0x06, 0x1F, 0x0A, 0x0F, 0x0F, 0x02, 0x02, 0x02, 0x0E, 0x0F,
        0x19, 0x01, 0x16, 0x1F, 0x16, 0x01, 0x02, 0x03,
  0x03, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
        0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x03,
  0x03, 0x09, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
        0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x09, 0x03,
  0x00, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
        0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x00
};

extern GUI_CONST_STORAGE GUI_BITMAP bmSeggerLogo50x25;

GUI_CONST_STORAGE GUI_BITMAP bmSeggerLogo50x25 = {
  50, /* XSize */
  25, /* YSize */
  50, /* BytesPerLine */
  8, /* BitsPerPixel */
  acSeggerLogo50x25,  /* Pointer to picture data (indices) */
  &PalSeggerLogo50x25  /* Pointer to palette */
};

/*********************************************************************
*
*       Types
*
**********************************************************************
*/
typedef struct {
  GUI_AUTODEV_INFO  AutoDevInfo;
  GUI_MEMDEV_Handle hScale;
  GUI_MEMDEV_Handle hScaleRot;
  GUI_MEMDEV_Handle hMemBk;
  GUI_MEMDEV_Handle hMemColor;
  GUI_POINT         aPoints[GUI_COUNTOF(_aNeedle)];
  int               xSize, ySize;
  float             Angle;
  float             Speed;
  U32               FontColor;
} PARAM;

/*********************************************************************
*
*       Static code
*
**********************************************************************
*/
/*********************************************************************
*
*       _CreateRoundedRect
*/
static GUI_MEMDEV_Handle _CreateRoundedRect(int xSize, int ySize, int r, U32 Color0, U32 Color1) {
  GUI_MEMDEV_Handle hMem;
  GUI_MEMDEV_Handle hMemRoundedRect;

  hMem            = GUI_MEMDEV_CreateFixed(0, 0, xSize << 2, ySize << 2, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  hMemRoundedRect = GUI_MEMDEV_CreateFixed(0, 0, xSize     , ySize     , GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  GUI_MEMDEV_Select(hMemRoundedRect);
  GUI_SetBkColor(GUI_TRANSPARENT);
  GUI_Clear();
  GUI_MEMDEV_Select(hMem);
  GUI_Clear();
  GUI_SetColor(Color0);
  GUI_FillRoundedRect(0, 0, (xSize << 2) - 1, (ySize << 2) - 1, r << 2);
  GUI_SetColor(Color1);
  GUI_DrawRoundedFrame(0, 0, (xSize << 2) - 1, (ySize << 2) - 1, r << 2, 2);
  GUI_MEMDEV_RotateHQ(hMem, hMemRoundedRect, (-xSize * 3) >> 1, (-ySize * 3) >> 1, 0, 250);
  GUI_MEMDEV_Delete(hMem);
  GUI_MEMDEV_Select(0);
  return hMemRoundedRect;
}

/*********************************************************************
*
*       _ReplaceColorsGradient
*/
static void _ReplaceColorsGradient(GUI_MEMDEV_Handle hMem, GUI_MEMDEV_Handle hMemGradient) {
  U32 * pData;
  U32 * pDataGradient;
  int   i, j, xSize, ySize;
  U32   ColorGradient, Color;

  xSize         = GUI_MEMDEV_GetXSize(hMem);
  ySize         = GUI_MEMDEV_GetYSize(hMem);
  pData         = (U32 *)GUI_MEMDEV_GetDataPtr(hMem);
  pDataGradient = (U32 *)GUI_MEMDEV_GetDataPtr(hMemGradient);
  for (i = 0; i < ySize; i++) {
    ColorGradient = *pDataGradient++;
    for (j = 0; j < xSize; j++) {
      Color = *pData;
      if (Color) {
        *pData = ColorGradient | ((Color & 0xFF) ^ 0xFF) << 24;
      } else {
        *pData = GUI_TRANSPARENT;
      }
      *pData++;
    }
  }
}

/*********************************************************************
*
*       _ReplaceColors
*/
static void _ReplaceColors(GUI_MEMDEV_Handle hMem, U32 Color) {
  U32 * pData;
  U32   ColorOld;
  int   i, j, xSize, ySize;

  xSize = GUI_MEMDEV_GetXSize(hMem);
  ySize = GUI_MEMDEV_GetYSize(hMem);
  pData = (U32 *)GUI_MEMDEV_GetDataPtr(hMem);
  for (i = 0; i < ySize; i++) {
    for (j = 0; j < xSize; j++) {
      ColorOld = *pData;
      if (ColorOld != GUI_TRANSPARENT) {
        *pData = Color | ((ColorOld & 0xFF) ^ 0xFF) << 24;
      }
      *pData++;
    }
  }
}

/*********************************************************************
*
*       _CreateCircle
*/
static GUI_MEMDEV_Handle _CreateCircle(int r, U32 Color0, U32 Color1) {
  GUI_MEMDEV_Handle hMemCircle;
  GUI_MEMDEV_Handle hMemGradient;
  int               Width;

  Width        = r * 2 + 1;
  hMemCircle   = GUI_MEMDEV_CreateFixed(0, 0, Width, Width, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  hMemGradient = GUI_MEMDEV_CreateFixed(0, 0,     1, Width, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  GUI_MEMDEV_Select(hMemCircle);
  //
  // Set all indices to 0
  //
  GUI_SetBkColorIndex(0);
  GUI_Clear();
  //
  // Draw circle
  //
  GUI_AA_FillCircle(r, r, r);
  //
  // Draw gradient
  //
  GUI_MEMDEV_Select(hMemGradient);
  GUI_DrawGradientV(0, 0, 0, Width, Color0, Color1);
  GUI_MEMDEV_Select(0);
  //
  // Use gradient for replacing colors
  //
  _ReplaceColorsGradient(hMemCircle, hMemGradient);
  GUI_MEMDEV_Delete(hMemGradient);
  return hMemCircle;
}

/*********************************************************************
*
*       _CreateRing
*/
static GUI_MEMDEV_Handle _CreateRing(int r, int w, U32 Color0, U32 Color1) {
  GUI_MEMDEV_Handle hMem, hMemGradient;
  int               xSize, ySize;

  xSize = ySize = r * 2 + 1;
  hMem         = GUI_MEMDEV_CreateFixed(0, 0, xSize, ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  hMemGradient = GUI_MEMDEV_CreateFixed(0, 0,     1, ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  GUI_MEMDEV_Select(hMem);
  //
  // Set all indices to 0
  //
  GUI_SetBkColorIndex(0);
  GUI_Clear();
  //
  // Draw arc
  //
  GUI_SetPenSize(w);
  GUI_SetColor(GUI_WHITE);
  GUI_AA_DisableHiRes();
  GUI_AA_DrawArc(r, r, r - w / 2, r - w / 2, 0, 360);
  //
  // Draw gradient
  //
  GUI_MEMDEV_Select(hMemGradient);
  GUI_DrawGradientV(0, 0, 0, ySize, Color0, Color1);
  GUI_MEMDEV_Select(0);
  //
  // Use gradient for replacing colors
  //
  _ReplaceColorsGradient(hMem, hMemGradient);
  GUI_MEMDEV_Delete(hMemGradient);
  return hMem;
}

/*********************************************************************
*
*       _CreateDoubleRing
*/
static GUI_MEMDEV_Handle _CreateDoubleRing(int r, int w0, int w1, U32 Color0, U32 Color1) {
  GUI_MEMDEV_Handle   hMem0, hMem1, hMem2;
  int                 xSize, ySize, RemPixels;
  U32               * pData0;
  U8                * pData2;

  xSize = ySize = r * 2 + 1;
  //
  // Create outer ring
  //
  hMem0 = _CreateRing(r,      w0, Color0, Color1);
  //
  // Create inner ring
  //
  hMem1 = _CreateRing(r - w0, w1, Color1, Color0);
  //
  // Create device for removing transparency effects in the middle of the rings
  //
  hMem2 = GUI_MEMDEV_CreateFixed(0, 0, xSize, ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_8, GUI_COLOR_CONV_8666);
  //
  // Combine outer and inner ring
  //
  GUI_MEMDEV_Select(hMem0);
  GUI_MEMDEV_WriteAt(hMem1, w0, w0);
  //
  // Draw circle in the overlapping region of the rings
  //
  GUI_MEMDEV_Select(hMem2);
  GUI_SetBkColorIndex(0);
  GUI_Clear();
  GUI_SetPenSize(2);
  GUI_AA_DrawArc(r, r, r - w0, r - w0, 0, 360);
  GUI_MEMDEV_Select(0);
  //
  // Remove transparency effect
  //
  pData0 = (U32 *)GUI_MEMDEV_GetDataPtr(hMem0);
  pData2 = (U8  *)GUI_MEMDEV_GetDataPtr(hMem2);
  RemPixels = xSize * ySize;
  do {
    if (*pData2++) {
      *pData0 &= 0xFFFFFF;
    }
    pData0++;
  } while (--RemPixels);
  //
  // Delete unused devices
  //
  GUI_MEMDEV_Delete(hMem1);
  GUI_MEMDEV_Delete(hMem2);
  //
  // Return the ready to use ring device
  //
  return hMem0;
}

/*********************************************************************
*
*       _MakeNumberStringEx
*/
static void _MakeNumberStringEx(int v, int NumDecs, char * acBuffer) {
  int i, a;

  NumDecs--;
  acBuffer[NumDecs - 0] = '0' + (a = v) % 10;
  for (i = 1; i <= NumDecs; i++) {
    acBuffer[NumDecs - i] = '0' + (a /= 10) % 10;
  }
  acBuffer[NumDecs + 1] = 0;
}

/*********************************************************************
*
*       _MakeNumberString
*/
static void _MakeNumberString(int v, char * acBuffer) {
  int NumDecs, i;

  for (i = v, NumDecs = (v == 0) ? 1 : 0; i; i /= 10, NumDecs++);
  _MakeNumberStringEx(v, NumDecs, acBuffer);
}

/*********************************************************************
*
*       _CreateReflex
*/
static GUI_MEMDEV_Handle _CreateReflex(int r, int dy, int a, U32 Color, U8 Intens) {
  GUI_MEMDEV_Handle   hMemGradient;
  GUI_MEMDEV_Handle   hMemReflex;
  GUI_MEMDEV_Handle   hMemRot;
  int                 Width, i, Index;
  U32               * pData;
  U32                 NumPixels;

  Width        = (r << 1) + 1;
  hMemGradient = GUI_MEMDEV_CreateFixed(0, 0, (64 << 1) + 1, (64 << 1) + 1, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  //
  // Initialize background
  //
  GUI_MEMDEV_Select(hMemGradient);
  GUI_SetBkColor(GUI_TRANSPARENT);
  GUI_Clear();
  //
  // Create circular gradient
  //
  GUI_SetPenSize(2);
  for (i = 64; i >= 0; i--) {
    Index = ((((64 - i) * 255) / 64) * Intens) / 255;
    GUI_SetColorIndex(Index);
    GUI_DrawArc(64, 64, i, i, 0, 360);
  }
  GUI_MEMDEV_Select(0);
  //
  // Replace indices with color and alpha value
  //
  pData     = (U32 *)GUI_MEMDEV_GetDataPtr(hMemGradient);
  NumPixels = 129 * 129;
  do {
    *pData = ((0xFF - *pData) << 24) | Color;
    *pData++;
  } while (--NumPixels);
  //
  // Create reflexion device
  //
  hMemReflex = GUI_MEMDEV_CreateFixed(0, 0, Width, Width, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  GUI_MEMDEV_Select(hMemReflex);
  GUI_SetBkColor(GUI_TRANSPARENT);
  GUI_Clear();
  //
  // Magnify gradient device to desired size
  //
  GUI_MEMDEV_DrawPerspectiveX(hMemGradient, 0, dy, Width, Width, Width, 0);
  GUI_MEMDEV_Select(0);
  GUI_MEMDEV_Delete(hMemGradient);
  //
  // Rotate if required
  //
  if (a) {
    hMemRot = GUI_MEMDEV_CreateFixed(0, 0, Width, Width, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
    GUI_MEMDEV_Select(hMemRot);
    GUI_SetBkColor(GUI_TRANSPARENT);
    GUI_Clear();
    GUI_MEMDEV_RotateHQ(hMemReflex, hMemRot, 0, 0, a * 1000, 1000);
    GUI_MEMDEV_Select(0);
    GUI_MEMDEV_Delete(hMemReflex);
    hMemReflex = hMemRot;
  }
  return hMemReflex;
}

/*********************************************************************
*
*       _RemoveTransparencyEffectCirc
*/
static void _RemoveTransparencyEffectCirc(GUI_MEMDEV_Handle hMem, int r, U32 AndMask) {
  GUI_MEMDEV_Handle   hMemCirc;
  U32               * pData;
  U8                * pDataCirc;
  int                 xSize, ySize;
  U32                 NumPixels;

  xSize    = ySize = r * 2 + 1;
  hMemCirc = GUI_MEMDEV_CreateFixed(0, 0, xSize, ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_8, GUI_COLOR_CONV_8666);
  GUI_MEMDEV_Select(hMemCirc);
  GUI_SetBkColorIndex(0);
  GUI_Clear();
  GUI_SetColorIndex(0xFF);
  GUI_FillCircle(r, r, r);
  pDataCirc = (U8 *)GUI_MEMDEV_GetDataPtr(hMemCirc);
  pData     = (U32 *)GUI_MEMDEV_GetDataPtr(hMem);
  NumPixels = xSize * ySize;
  do {
    if (*pDataCirc++) {
      *pData++ &= AndMask;
    } else {
      *pData++ = 0xFF000000;
    }
  } while (--NumPixels);
  GUI_MEMDEV_Delete(hMemCirc);
}

/*********************************************************************
*
*       _CreateScale
*/
static GUI_MEMDEV_Handle _CreateScale(
  int rRing,
  int rScale,
  int rCheckMarks,
  int rDigits,
  int rKnob,
  U32 ColorScale0,
  U32 ColorScale1,
  U32 ColorCheckmarks,
  U32 ColorDigits,
  U32 ColorRing0,
  U32 ColorRing1,
  int wRing0,
  int wRing1,
  int LenCheck0,
  int LenCheck1,
  int yPosLabel
) {
  GUI_MEMDEV_Handle hMemDoubleRing;
  GUI_MEMDEV_Handle hMemKnob;
  GUI_MEMDEV_Handle hMemOverlap;
  GUI_MEMDEV_Handle hMemRect;
  GUI_MEMDEV_Handle hMemReflex;
  GUI_MEMDEV_Handle hMemRing;
  GUI_MEMDEV_Handle hMemRot;
  GUI_MEMDEV_Handle hMemScale;
  GUI_MEMDEV_Handle hMemText;
  GUI_RECT          Rect;
  GUI_POINT         aPoint0Org[4];
  GUI_POINT         aPoint0Rot[4];
  GUI_POINT         aPoint1Org[4];
  GUI_POINT         aPoint1Rot[4];
  char              acBuffer[] = "km/h";
  int               i, xPos, yPos, a, xSizeText, ySizeFont, SizeMem, xSize, ySize, RemPixels;
  I32               SinHQ, CosHQ;
  float             af;
  U32             * pData0;
  U8              * pData2;

  //
  // Create device
  //
  hMemScale = _CreateCircle(rRing, ColorScale0, ColorScale1);
  GUI_MEMDEV_Select(hMemScale);
  //
  // Draw km/h
  //
  GUI_SetTextMode(GUI_TM_TRANS);
  GUI_SetFont(&GUI_FontRounded16);
  GUI_SetColor(ColorDigits);
  GUI_DispStringHCenterAt(acBuffer, rRing, yPosLabel);
  //
  // Draw digits
  //
  GUI_SetTextMode(GUI_TM_TRANS);
  GUI_SetFont(&GUI_FontDigit11);
  GUI_SetColor(GUI_WHITE);
  ySizeFont = GUI_GetFontSizeY();
  for (i = 0; i <= MAX_SPEED; i+= 20) {
    //
    // Calculate angle
    //
    a     = (210 - i) * 1000;
    SinHQ = GUI__SinHQ(a);
    CosHQ = GUI__CosHQ(a);
    //
    // Calculate position on radius
    //
    xPos = rRing + ((rDigits * CosHQ) >> 16);
    yPos = rRing - ((rDigits * SinHQ) >> 16);
    //
    // Get text rectangle
    //
    _MakeNumberString(i, acBuffer);
    xSizeText = GUI_GetStringDistX(acBuffer);
    Rect.x0   = Rect.y0 = 0;
    //
    // Create quadratic memory device for text
    //
    SizeMem  = (int)(1.414f * ((xSizeText > ySizeFont) ? xSizeText : ySizeFont));
    hMemText = GUI_MEMDEV_CreateFixed(0, 0, SizeMem, SizeMem, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
    //
    // Fill with 0
    //
    GUI_MEMDEV_Select(hMemText);
    GUI_SetBkColorIndex(0);
    GUI_Clear();
    //
    // Draw text
    //
    Rect.x1 = SizeMem - 1;
    Rect.y1 = SizeMem - 1;
    GUI_DispStringInRect(acBuffer, &Rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
    //
    // Rotate
    //
    hMemRot = GUI_MEMDEV_CreateFixed(0, 0, SizeMem, SizeMem, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
    GUI_MEMDEV_Select(hMemRot);
    GUI_SetBkColorIndex(0);
    GUI_Clear();
    GUI_MEMDEV_RotateHQ(hMemText, hMemRot, 0, 0, ((210 - i) - 90) * 1000, 1000);
    GUI_MEMDEV_Delete(hMemText);
    GUI_MEMDEV_Select(0);
    _ReplaceColors(hMemRot, ColorDigits);
    //
    // Draw
    //
    GUI_MEMDEV_Select(hMemScale);
    GUI_MEMDEV_WriteAt(hMemRot, xPos - (SizeMem >> 1), yPos - (SizeMem >> 1));
    GUI_MEMDEV_Delete(hMemRot);
  }
  //
  // Set magnification factor and color
  //
  GUI_SetColor(ColorCheckmarks);
  //
  // Initialize reference check mark
  //
  aPoint0Org[0].x = (rCheckMarks - LenCheck0) * MAG;
  aPoint0Org[0].y = 5;
  aPoint0Org[1].x = rCheckMarks * MAG;
  aPoint0Org[1].y = 5;
  aPoint0Org[2].x = rCheckMarks * MAG;
  aPoint0Org[2].y = -5;
  aPoint0Org[3].x = (rCheckMarks - LenCheck0) * MAG;
  aPoint0Org[3].y = -5;
  //
  // Enable high resolution
  //
  GUI_AA_EnableHiRes();
  GUI_AA_SetFactor(MAG);
  //
  // Draw check marks
  //
  for (i = 0; i <= MAX_SPEED; i+= 20) {
    //
    // Calculate angle
    //
    a = (210 - i) * 1000;
    SinHQ = GUI__SinHQ(a);
    CosHQ = GUI__CosHQ(a);
    //
    // Rotate polygon
    //
    af = ((210 - i) * 3.1415926f) / 180;
    GUI_RotatePolygon(aPoint0Rot, aPoint0Org, GUI_COUNTOF(aPoint0Org), af);
    //
    // Draw polygon
    //
    GUI_AA_FillPolygon(aPoint0Rot, GUI_COUNTOF(aPoint0Org), (rRing + 1) * MAG, (rRing + 1) * MAG);
  }
  //
  // Initialize reference check mark
  //
  aPoint1Org[0].x = (rCheckMarks - LenCheck1) * MAG;
  aPoint1Org[0].y = 5;
  aPoint1Org[1].x = rCheckMarks * MAG;
  aPoint1Org[1].y = 5;
  aPoint1Org[2].x = rCheckMarks * MAG;
  aPoint1Org[2].y = -5;
  aPoint1Org[3].x = (rCheckMarks - LenCheck1) * MAG;
  aPoint1Org[3].y = -5;
  //
  // Draw rect check marks
  //
  GUI_SetColor(ColorCheckmarks);
  for (i = 10; i <= 230; i+= 20) {
    //
    // Calculate angle
    //
    a = (210 - i) * 1000;
    SinHQ = GUI__SinHQ(a);
    CosHQ = GUI__CosHQ(a);
    //
    // Rotate polygon
    //
    af = ((210 - i) * 3.1415926f) / 180;
    GUI_RotatePolygon(aPoint1Rot, aPoint1Org, GUI_COUNTOF(aPoint1Org), af);
    //
    // Draw polygon
    //
    GUI_AA_FillPolygon(aPoint1Rot, GUI_COUNTOF(aPoint1Org), (rRing + 1) * MAG, (rRing + 1) * MAG);
  }
  hMemRect = _CreateRoundedRect(60, 30, 6, COLOR_DISPLAY0, COLOR_DISPLAY1);
  GUI_MEMDEV_Select(hMemScale);
  GUI_MEMDEV_WriteAt(hMemRect, rRing - 30 + 1, rRing + 50);
  GUI_MEMDEV_Delete(hMemRect);
  GUI_MEMDEV_Select(0);
  //
  // Large inner ring
  //
  hMemRing = _CreateRing(40, 3, 0x000000, 0x888888);
  GUI_MEMDEV_Select(hMemScale);
  GUI_MEMDEV_WriteAt(hMemRing, rRing - 40 + 1, rRing - 40 + 1);
  GUI_MEMDEV_Delete(hMemRing);
  GUI_MEMDEV_Select(0);
  //
  // Reflexion
  //
  hMemReflex = _CreateReflex(rRing, -rRing, 20, 0xFFFFFF, 0x99);
  GUI_MEMDEV_Select(hMemScale);
  GUI_MEMDEV_WriteAt(hMemReflex, 0, 0);
  GUI_MEMDEV_Delete(hMemReflex);
  GUI_MEMDEV_Select(0);
  //
  // Knob
  //
  hMemKnob = _CreateCircle(rKnob, 0x666666, 0x111111);
  GUI_MEMDEV_Select(hMemScale);
  GUI_MEMDEV_WriteAt(hMemKnob, rRing - rKnob + 1, rRing - rKnob + 1);
  GUI_MEMDEV_Delete(hMemKnob);
  GUI_MEMDEV_Select(0);
  //
  // Reflexion on knob
  //
  hMemReflex = _CreateReflex(rKnob, -rKnob, 20, 0xFFFFFF, 0x99);
  _RemoveTransparencyEffectCirc(hMemReflex, rKnob, 0xFFFFFFFF);
  GUI_MEMDEV_Select(hMemScale);
  GUI_MEMDEV_WriteAt(hMemReflex, rRing - rKnob + 1, rRing - rKnob + 1);
  GUI_MEMDEV_Delete(hMemReflex);
  GUI_MEMDEV_Select(0);
  //
  // Circle
  //
  GUI_MEMDEV_Select(hMemScale);
  GUI_SetPenSize(1);
  GUI_SetColor(0x0000cc);
  GUI_AA_DrawArc(rRing, rRing, rScale - 3, rScale - 3, 0, 360);
  //
  // Remove transparency effects
  //
  _RemoveTransparencyEffectCirc(hMemScale, rRing, 0xFFFFFF);
  //
  // Make sure that border of scale is transparent before adding double ring
  //
  GUI_MEMDEV_Select(hMemScale);
  GUI_SetColor(GUI_TRANSPARENT);
  GUI_SetPenSize(4);
  GUI_DrawArc(rRing, rRing, rRing, rRing, 0, 360);
  GUI_MEMDEV_Select(0);
  //
  // Add double ring
  //
  hMemDoubleRing = _CreateDoubleRing(rRing, wRing0, wRing1, ColorRing0, ColorRing1);
  GUI_MEMDEV_Select(hMemScale);
  GUI_MEMDEV_Write(hMemDoubleRing);
  GUI_MEMDEV_Delete(hMemDoubleRing);
  GUI_MEMDEV_Select(0);
  //
  // Create device for removing transparency effect between double ring and scale
  //
  xSize = ySize = rRing * 2 + 1;
  hMemOverlap   = GUI_MEMDEV_CreateFixed(0, 0, xSize, ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_8, GUI_COLOR_CONV_8666);
  //
  // Draw circle in the overlapping region of the rings
  //
  GUI_MEMDEV_Select(hMemOverlap);
  GUI_SetBkColorIndex(0);
  GUI_Clear();
  GUI_SetPenSize(2);
  GUI_AA_DrawArc(rRing, rRing, rRing - wRing0 - wRing1, rRing - wRing0 - wRing1, 0, 360);
  GUI_MEMDEV_Select(0);
  //
  // Remove transparency effect between double ring and scale
  //
  pData0    = (U32 *)GUI_MEMDEV_GetDataPtr(hMemScale);
  pData2    = (U8  *)GUI_MEMDEV_GetDataPtr(hMemOverlap);
  RemPixels = xSize * ySize;
  do {
    if (*pData2++) {
      *pData0 &= 0xFFFFFF;
    }
    pData0++;
  } while (--RemPixels);
  GUI_MEMDEV_Delete(hMemOverlap);
  //
  // Return
  //
  GUI_MEMDEV_Select(0);
  return hMemScale;
}

/*********************************************************************
*
*       _DrawCentered
*/
static void _DrawCentered(GUI_MEMDEV_Handle hMem, int xOff, int yOff) {
  int xSizeDevice, ySizeDevice, xSize, ySize;

  xSize       = LCD_GetXSize();
  ySize       = LCD_GetYSize();
  xSizeDevice = GUI_MEMDEV_GetXSize(hMem);
  ySizeDevice = GUI_MEMDEV_GetYSize(hMem);
  GUI_MEMDEV_WriteAt(hMem, xOff + (xSize - xSizeDevice) / 2, yOff + (ySize - ySizeDevice) / 2);
}

/*********************************************************************
*
*       _GetFontColor
*/
static GUI_COLOR _GetFontColor(PARAM * pParam, U32 Color0, U32 Color1) {
  GUI_MEMDEV_Handle   hMemOld;
  GUI_COLOR           FontColor;
  U32               * pData;

  if (pParam->hMemColor == 0) {
    pParam->hMemColor = GUI_MEMDEV_CreateFixed(0, 0, 10, 10, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  }
  hMemOld   = GUI_MEMDEV_Select(pParam->hMemColor);
  GUI_DrawGradientV(0, (int)pParam->Speed - MAX_SPEED, 0, (int)pParam->Speed, Color0, Color1);
  GUI_MEMDEV_Select(hMemOld);
  pData     = (U32 *)GUI_MEMDEV_GetDataPtr(pParam->hMemColor);
  FontColor = *pData;
  return FontColor;
}

/*********************************************************************
*
*       _DrawNeedleAndSpeed
*/
static void _DrawNeedleAndSpeed(PARAM * pParam, int mx, int my) {
  GUI_COLOR FontColor;

  //
  // Draw needle
  //
  GUI_SetColor(COLOR_NEEDLE);
  GUI_AA_FillPolygon(pParam->aPoints, GUI_COUNTOF(pParam->aPoints), mx * MAG, my * MAG);
  //
  // Draw speed
  //
  GUI_SetFont(&GUI_FontDigit19);
  FontColor = pParam->FontColor;
  GUI_SetColor(FontColor);
  GUI_SetTextAlign(GUI_TA_RIGHT);
  GUI_GotoXY(mx + 23, my + 49);
  GUI_DispDecMin((int)pParam->Speed);
}

/*********************************************************************
*
*       _Draw
*/
static void _Draw(void * p) {
  PARAM * pParam;

  pParam = (PARAM *)p;
  if (pParam->AutoDevInfo.DrawFixed) {
    GUI_MEMDEV_Write(pParam->hMemBk);
    _DrawCentered(pParam->hScale, 0, 0);
  }
  _DrawNeedleAndSpeed(pParam, pParam->xSize >> 1, pParam->ySize >> 1);
  if (pParam->Speed >= 200) {
    pParam->Speed++;
  }
}

/*********************************************************************
*
*       _CalcXIn
*
*  Purpose: Moves to the right getting slower
*/
static void _CalcXIn(int tDiff, int tMax, int xSize, int * pix, int * piSpin) {
  float fx;
  int   xSize_LCD;

  xSize_LCD = LCD_GetXSize();
  fx        = 1 - (float)tDiff / tMax;
  fx        = fx * fx * fx * fx;
  *pix      = ((xSize_LCD - xSize) >> 1) - (int)(fx * xSize_LCD);
  *piSpin   = (int)(fx * 1000 * xSize);
}

/*********************************************************************
*
*       _CalcXOut
*
*  Purpose: Moves to the right getting faster
*/
static void _CalcXOut(int tDiff, int tMax, int xSize, int * pix, int * piSpin) {
  float fx;
  int   xSize_LCD;

  xSize_LCD = LCD_GetXSize();
  fx        = (float)tDiff / tMax;
  fx        = fx * fx * fx * fx;
  *pix      = ((xSize_LCD - xSize) >> 1) + (int)(fx * xSize_LCD);
  *piSpin   = (int)(0 - fx * 1000 * xSize);
}

/*********************************************************************
*
*       _FillBkDev
*/
static void _FillBkDev(PARAM * pParam) {
  GUI_MEMDEV_Select(pParam->hMemBk);
  GUIDEMO_DrawBk(1);
  GUI_SetFont(&GUI_FontRounded16);
  GUI_SetTextMode(GUI_TM_TRANS);
  GUI_SetColor(GUI_WHITE);
  GUI_DispStringHCenterAt("STemWin", pParam->xSize - 40, 15);
  GUI_MEMDEV_Select(0);
}

/*********************************************************************
*
*       _Roll
*/
static int _Roll(PARAM * pParam, int tMax, void (* pfCalcX)(int, int, int, int *, int *)) {
  GUI_MEMDEV_Handle hDst;
  int ix,     iSpin;
  int tStart, tNow;
  int tDiff,  tUsed;
  int Size_DevRotate;

  hDst = GUI_MEMDEV_CreateFixed(0, 0, pParam->xSize, pParam->ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  Size_DevRotate = GUI_MEMDEV_GetXSize(pParam->hScaleRot);
  tStart = tNow = GUIDEMO_GetTime();
  do {
    tDiff = tNow - tStart;
    tDiff = (tDiff < tMax) ? tDiff : tMax;
    if (tDiff) {
      GUI_MEMDEV_Select(hDst);
      GUI_MEMDEV_Write(pParam->hMemBk);
      pfCalcX(tDiff, tMax, Size_DevRotate, &ix, &iSpin);
      GUI_MEMDEV_Rotate(pParam->hScaleRot, hDst, ix , (pParam->ySize - Size_DevRotate) >> 1, iSpin, 1000);
      GUI_MEMDEV_Select(0);
      GUI_MEMDEV_Write(hDst);
    }
    tUsed = GUIDEMO_GetTime() - tNow;
    tNow += tUsed;
    if (tUsed < T_MIN_FRAME_ROLL) {
      GUI_Delay(T_MIN_FRAME_ROLL - tUsed);
    } else {
      GUI_Exec();
    }
    if (GUIDEMO_CheckCancel()) {
      GUI_MEMDEV_Delete(hDst);
      return 1;
    }
  } while (tDiff < tMax);
  GUI_MEMDEV_Delete(hDst);
  return 0;
}

/*********************************************************************
*
*       _RollOut
*/
static int _RollOut(PARAM * pParam, int tMax) {
  return _Roll(pParam, tMax, _CalcXOut);
}

/*********************************************************************
*
*       _RollIn
*/
static int _RollIn(PARAM * pParam, int tMax) {
  return _Roll(pParam, tMax, _CalcXIn);
}

/*********************************************************************
*
*       _KillMemdevs
*/
static void _KillMemdevs(PARAM * pParam) {
  GUI_MEMDEV_Delete(pParam->hMemBk);
  GUI_MEMDEV_Delete(pParam->hMemColor);
  GUI_MEMDEV_Delete(pParam->hScale);
  GUI_MEMDEV_Delete(pParam->hScaleRot);
  pParam->hScale    = 0;
  pParam->hMemBk    = 0;
  pParam->hMemColor = 0;
  pParam->hScaleRot = 0;
}

/*********************************************************************
*
*       _ShowTitle
*/
static int _ShowTitle(GUI_MEMDEV_Handle hTitle, GUI_MEMDEV_Handle hTitleBk) {
  int TimeStart, TimeDiff;

  GUI_MEMDEV_Write(hTitleBk);
  //
  // Fade in Title and check if the user wants to cancel the demo
  //
  GUI_MEMDEV_FadeDevices(hTitle,   hTitleBk, TIME_TITLE_FADE);
  if (GUIDEMO_CheckCancel() == 1) {
    return 1;
  }
  //
  // Pause after fade in
  //
  TimeStart = GUIDEMO_GetTime();
  do {
    GUI_Delay(10);
    TimeDiff = GUIDEMO_GetTime() - TimeStart;
    if (GUIDEMO_CheckCancel() == 1) {
      return 1;
    }
  } while (TimeDiff < DELAY_TITLE_FADEIN);
  //
  // Fade out Title and check if the user wants to cancel the demo
  //
  GUI_MEMDEV_FadeDevices(hTitleBk, hTitle, TIME_TITLE_FADE);
  if (GUIDEMO_CheckCancel() == 1) {
    return 1;
  }
  //
  // Pause after fade in
  //
  TimeStart = GUIDEMO_GetTime();
  do {
    GUI_Delay(10);
    TimeDiff = GUIDEMO_GetTime() - TimeStart;
    if (GUIDEMO_CheckCancel() == 1) {
      return 1;
    }
  } while (TimeDiff < DELAY_TITLE_FADEOUT);
  return 0;
}

/*********************************************************************
*
*       _SpeedometerDemo
*/
static void _SpeedometerDemo(void) {
  GUI_MEMDEV_Handle hTitle, hTitleBk;
  GUI_AUTODEV       AutoDev;
  PARAM             Param;
  int               t0, tStart, tDiff, tUsed;
  float             f;
  GUI_RECT          Rect;

  //
  // Create devices
  //
  Param.hScale    = _CreateScale(R_RING, R_SCALE, R_CHECK, R_DIGIT, R_KNOB,
                                 COLOR_SCALE0, COLOR_SCALE1, COLOR_CMARK, COLOR_DIGIT, COLOR_RING0, COLOR_RING1,
                                 W_RING0, W_RING1,
                                 L_CHECK0, L_CHECK1, YPOS_LABEL);
  Param.hScaleRot = GUI_MEMDEV_CreateFixed(0, 0, R_RING * 2 + 1, R_RING * 2 + 1, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  Param.hMemColor = 0;
  //
  // Initialize high resolution anti aliasing
  //
  GUI_AA_EnableHiRes();
  GUI_AA_SetFactor(MAG);
  //
  // Create scale for rotation
  //
  GUI_MEMDEV_Select(Param.hScaleRot);
  GUI_SetBkColor(GUI_TRANSPARENT);
  GUI_Clear();
  GUI_MEMDEV_Write(Param.hScale);
  //
  // Draw needle and speed into device
  //
  Param.Speed     = 0;
  Param.Angle     = (210 - (Param.Speed)) * 3.1415926 / 180;
  Param.FontColor = _GetFontColor(&Param, COLOR_NEEDLE, GUI_WHITE);
  GUI_RotatePolygon(Param.aPoints, _aNeedle, GUI_COUNTOF(_aNeedle), Param.Angle);
  _DrawNeedleAndSpeed(&Param, R_RING + 1, R_RING + 1);
  GUI_MEMDEV_Select(0);
  //
  // Get display size
  //
  Param.xSize = LCD_GetXSize();
  Param.ySize = LCD_GetYSize();
  //
  // Create background device
  //
  Param.hMemBk = GUI_MEMDEV_CreateFixed(0, 0, Param.xSize, Param.ySize, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  _FillBkDev(&Param);
  GUI_MEMDEV_Write(Param.hMemBk);
  //
  // Initialize auto device
  //
  GUI_MEMDEV_CreateAuto(&AutoDev);
  //
  // Fill Title MEMDEVs
  //
  GUI_SetFont(&GUI_FontRounded33);
  GUI_GotoXY(0, 0);
  GUI_GetTextExtend(&Rect, _acText, sizeof(_acText));
  GUI_MoveRect(&Rect, (Param.xSize - Rect.x1) >> 1, (Param.ySize - Rect.y1) >> 1);
  hTitle   = GUI_MEMDEV_CreateFixed(Rect.x0, Rect.y0, Rect.x1 - Rect.x0 + 1, Rect.y1 - Rect.y0 + 1, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  hTitleBk = GUI_MEMDEV_CreateFixed(Rect.x0, Rect.y0, Rect.x1 - Rect.x0 + 1, Rect.y1 - Rect.y0 + 1, GUI_MEMDEV_NOTRANS, GUI_MEMDEV_APILIST_32, GUI_COLOR_CONV_8888);
  GUI_MEMDEV_Select(hTitle);
  GUI_MEMDEV_Write(Param.hMemBk);
  _DrawCentered(Param.hScaleRot, 0, 0);
  GUI_SetColor(GUI_WHITE);
  GUI_DispStringInRect(_acText, &Rect, GUI_TA_HCENTER | GUI_TA_VCENTER);
  GUI_MEMDEV_Select(hTitleBk);
  GUI_MEMDEV_Write(Param.hMemBk);
  _DrawCentered(Param.hScaleRot, 0, 0);
  GUI_MEMDEV_Select(0);
  //
  // Let the speedometer roll into the center of the screen
  //
  if (_RollIn(&Param, T_ROLL)) {
    GUI_MEMDEV_DeleteAuto(&AutoDev);
    GUI_MEMDEV_Delete(hTitle);
    GUI_MEMDEV_Delete(hTitleBk);
    _KillMemdevs(&Param);
    return;
  }
  //
  // Show Title
  //
  if (_ShowTitle(hTitle, hTitleBk)) {
    GUI_MEMDEV_DeleteAuto(&AutoDev);
    GUI_MEMDEV_Delete(hTitle);
    GUI_MEMDEV_Delete(hTitleBk);
    _KillMemdevs(&Param);
    return;
  }
  //
  // Accelerate and brake
  //
  t0 = GUIDEMO_GetTime();
  while ((tDiff = GUIDEMO_GetTime() - t0) < T_MAX) {
    tStart = GUIDEMO_GetTime();
    //
    // Calculate angle dependent on time
    //
    if (tDiff < (T_MAX >> 1)) {
      f = 1 - (float)tDiff / (T_MAX >> 1);
      f = f * f * f;
      Param.Speed = MAX_SPEED - MAX_SPEED * f;
      Param.Angle = ((210 - (Param.Speed)) * 3.1415926f) / 180;
    } else {
      f = 1 - ((float)tDiff - (T_MAX >> 1)) / (T_MAX >> 1);
      f = f * f * f;
      Param.Speed = MAX_SPEED * f;
      Param.Angle = ((-30 + (MAX_SPEED - Param.Speed)) * 3.1415926f) / 180;
    }
    Param.FontColor = _GetFontColor(&Param, COLOR_NEEDLE, GUI_WHITE);
    //
    // Rotate polygon
    //
    GUI_RotatePolygon(Param.aPoints, _aNeedle, GUI_COUNTOF(_aNeedle), Param.Angle);
    //
    // Draw scene
    //
    GUI_MEMDEV_DrawAuto(&AutoDev, &Param.AutoDevInfo, &_Draw, &Param);
    //
    // Wait a while
    //
    tUsed = GUIDEMO_GetTime() - tStart;
    if (tUsed < T_MIN_FRAME_NEEDLE) {
      GUI_Delay(T_MIN_FRAME_NEEDLE - tUsed);
    }
    if (GUIDEMO_CheckCancel()) {
      GUI_MEMDEV_DeleteAuto(&AutoDev);
      GUI_MEMDEV_Delete(hTitle);
      GUI_MEMDEV_Delete(hTitleBk);
      _KillMemdevs(&Param);
      return;
    }
  }
  _RollOut(&Param, T_ROLL);
  GUI_MEMDEV_DeleteAuto(&AutoDev);
  GUI_MEMDEV_Delete(hTitle);
  GUI_MEMDEV_Delete(hTitleBk);
  _KillMemdevs(&Param);
}

/*********************************************************************
*
*       Public code
*
**********************************************************************
*/
/*********************************************************************
*
*       GUIDEMO_Speedometer
*/
void GUIDEMO_Speedometer(void) {
  GUIDEMO_ShowIntro("Speedometer",
                    "Shows acceleration and\n"
                    "deceleration on a speedometer");
  _SpeedometerDemo();
  GUI_AA_DisableHiRes();
}

#else

void GUIDEMO_Speedometer(void) {}

#endif

/*************************** End of file ****************************/
