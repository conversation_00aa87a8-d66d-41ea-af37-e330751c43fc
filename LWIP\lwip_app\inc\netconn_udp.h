#ifndef __NETCONN_UDP_H
#define __NETCONN_UDP_H
#include "sys.h"
#include "includes.h"
//////////////////////////////////////////////////////////////////////////////////	 
//本程序只供学习使用，未经作者许可，不得用于其它任何用途
//ALIENTEK STM32F4&F7开发板
//NETCONN API编程方式的UDP测试代码	   
//正点原子@ALIENTEK
//技术论坛:www.openedv.com
//创建日期:2016/2/29
//版本：V1.0
//版权所有，盗版必究。
//Copyright(C) 广州市星翼电子科技有限公司 2009-2019
//All rights reserved									  
//*******************************************************************************
//修改信息
//无
////////////////////////////////////////////////////////////////////////////////// 	   
 
 
#define UDP_DEMO_RX_BUFSIZE		2000	//定义udp最大接收数据长度
#define UDP_DEMO_PORT			8089	//定义udp连接的本地端口号
#define UDP_BATTERY_PORT		8088	//定义电池数据UDP端口号
#define LWIP_SEND_DATA			0X80    //定义有数据发送

// TI Smart Lithium Battery Data Structure
typedef struct {
    uint16_t voltage_mv;        // Battery voltage in millivolts
    int16_t current_ma;         // Battery current in milliamps (positive = charging, negative = discharging)
    int16_t temperature_c;      // Battery temperature in Celsius * 10 (e.g., 250 = 25.0°C)
    uint8_t state_of_charge;    // State of charge percentage (0-100)
    uint8_t state_of_health;    // State of health percentage (0-100)
    uint16_t cycle_count;       // Number of charge/discharge cycles
    uint8_t status_flags;       // Battery status flags (bit field)
    uint8_t protection_flags;   // Protection status flags (bit field)
    uint32_t timestamp;         // Timestamp of the data
    uint8_t reserved[3];        // Reserved for future use (total size = 20 bytes)
} __attribute__((packed)) TI_Battery_Data_t;

// Battery status flag definitions
#define BATTERY_STATUS_CHARGING     0x01
#define BATTERY_STATUS_DISCHARGING  0x02
#define BATTERY_STATUS_FULL         0x04
#define BATTERY_STATUS_EMPTY        0x08
#define BATTERY_STATUS_ERROR        0x80

// Battery protection flag definitions
#define BATTERY_PROT_OVERVOLTAGE    0x01
#define BATTERY_PROT_UNDERVOLTAGE   0x02
#define BATTERY_PROT_OVERCURRENT    0x04
#define BATTERY_PROT_OVERTEMP       0x08
#define BATTERY_PROT_UNDERTEMP      0x10

extern u8 udp_flag;		//UDP数据发送标志位
extern u8 udp_battery_flag;	//UDP电池数据发送标志位

u8 udp_demo_init(void);
u8 udp_battery_init(void);
uint8_t udp_senddata(uint8_t *buf, uint16_t len);
uint8_t udp_send_battery_data(TI_Battery_Data_t *battery_data);
#endif

